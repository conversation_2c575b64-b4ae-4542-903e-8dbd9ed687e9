import * as React from 'react';
import { useCallback } from 'react';
import { createStackNavigator, StackNavigationProp } from '@react-navigation/stack';
import { useNavigation } from '@react-navigation/native';
import { Alert, View, TouchableOpacity, Text } from 'react-native';
import { useNotifications } from '../hooks/useNotifications';
import { auth } from '../firebase';
import { RootStackParamList } from '../types/navigation';
import { NativeModules } from 'react-native';
import { CardStyleInterpolators } from '@react-navigation/stack';
import { Ionicons } from '@expo/vector-icons';

// Import all screens
import CreatePostingScreen from '../screens/CreatePostingScreen';
import ForgotPassword from '../screens/ForgotPassword';
import HomeScreen from '../screens/HomeScreen';
import PostingDetail from '../screens/PostingDetail';
import LoginScreen from '../screens/LoginScreen';
import MakeOffer from '../screens/MakeOffer';
import MyFavoritesScreen from '../screens/MyFavoritesScreen';
import MyOffersScreen from '../screens/MyOffersScreen';
import MyPostingsScreen from '../screens/MyPostingsScreen';
import ProfileScreen from '../screens/ProfileScreen';
import SettingsScreen from '../screens/SettingsScreen';
import SignUp from '../screens/SignUp';
import SignUpConfirmation from '../screens/SignUpConfirmation';
import EditPostingScreen from '../screens/EditPostingScreen';
import OfferDetail from '../screens/OfferDetail';
import EditOffer from '../screens/EditOffer';
import LocationSettings from '../screens/LocationSettings';
import NotificationSettingsScreen from '../screens/NotificationSettingsScreen';
import NotificationsScreen from '../screens/NotificationsScreen';

const Stack = createStackNavigator<RootStackParamList>();

interface BackButtonProps {
  navigation: StackNavigationProp<RootStackParamList>;
}

const BackButton: React.FC<BackButtonProps> = React.memo(({ navigation }) => {
  console.log('[BackButton] Rendering back button');
  
  const handlePress = useCallback(() => {
    console.log('[BackButton] Back button pressed');
    navigation.goBack();
  }, [navigation]);

  return (
    <TouchableOpacity 
      onPress={handlePress}
      style={{ 
        marginLeft: 16, 
        padding: 8,
        backgroundColor: 'transparent',
        minWidth: 44,
        minHeight: 44,
        justifyContent: 'center',
        alignItems: 'center'
      }}
      accessibilityLabel="Go back"
      accessibilityRole="button"
    >
      <Ionicons name="chevron-back" size={28} color="#000" />
    </TouchableOpacity>
  );
});

BackButton.displayName = 'BackButton';

export default function AppNavigator() {
  const { permissionStatus } = useNotifications();

  return (
    <Stack.Navigator
      initialRouteName="LoginScreen"
      screenOptions={({ navigation, route }) => {
        console.log('[Navigation] Configuring screen:', route.name);
        console.log('[Navigation] Can go back:', navigation.canGoBack());
        
        return {
          headerShown: true,
          headerBackTitleVisible: false,
          headerMode: 'float',
          headerLeft: (props) => {
            console.log('[Navigation] Rendering headerLeft for:', route.name);
            console.log('[Navigation] Can go back status:', navigation.canGoBack());
            
            // Force back button to show except for specific screens
            const hideBackButtonScreens = ['Home', 'LoginScreen'];
            if (hideBackButtonScreens.includes(route.name)) {
              console.log('[Navigation] Hiding back button for:', route.name);
              return null;
            }
            
            // Always show back button for other screens
            return <BackButton navigation={navigation} />;
          },
          headerStyle: {
            backgroundColor: '#fff',
            elevation: 4, // Android shadow
            shadowOpacity: 0.3, // iOS shadow
          },
          headerTintColor: '#000',
          headerTitleStyle: {
            fontWeight: 'bold',
          },
        };
      }}
    >
      <Stack.Screen 
        name="LoginScreen" 
        component={LoginScreen} 
        options={{
          headerLeft: () => null,
        }}
      />
      <Stack.Screen 
        name="Home" 
        component={HomeScreen} 
        options={{ headerShown: false }} 
      />
      <Stack.Screen name="CreatePosting" component={CreatePostingScreen} />
      <Stack.Screen name="EditPosting" component={EditPostingScreen} />
      <Stack.Screen 
        name="PostingDetail" 
        component={PostingDetail}
        options={{
          headerShown: true,
          headerTitle: 'PostingDetails',
          // Remove any headerLeft override if it exists
        }}
      />
      <Stack.Screen name="Profile" component={ProfileScreen} />
      <Stack.Screen name="MyFavorites" component={MyFavoritesScreen} />
      <Stack.Screen name="MyPostings" component={MyPostingsScreen} />
      <Stack.Screen name="MyOffers" component={MyOffersScreen} />
      <Stack.Screen name="Settings" component={SettingsScreen} />
      <Stack.Screen name="SignUp" component={SignUp} />
      <Stack.Screen name="SignUpConfirmation" component={SignUpConfirmation} />
      <Stack.Screen name="ForgotPassword" component={ForgotPassword} />
      <Stack.Screen name="MakeOffer" component={MakeOffer} />
      <Stack.Screen 
        name="OfferDetail" 
        component={OfferDetail}
        options={{
          freezeOnBlur: true,
          detachPreviousScreen: true
        }}
      />
      <Stack.Screen name="EditOffer" component={EditOffer} />
      <Stack.Screen name="LocationSettings" component={LocationSettings} />
      <Stack.Screen 
        name="NotificationSettings" 
        component={NotificationSettingsScreen}
        options={{
          title: 'Notification Settings',
          headerBackTitle: 'Back'
        }} 
      />
      <Stack.Screen 
        name="Notifications" 
        component={NotificationsScreen}
        options={{ title: 'Notifications' }}
      />
    </Stack.Navigator>
  );
}

//export default AppNavigator; 
