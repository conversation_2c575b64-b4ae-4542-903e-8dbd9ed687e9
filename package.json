{"name": "sat<PERSON>", "version": "1.0.0", "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest --config jest/config/jest.config.js --detectOpenHandles --forceExit", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint .", "metro": "react-native start --reset-cache"}, "dependencies": {"@babel/runtime": "^7.26.0", "@modelcontextprotocol/sdk": "^1.7.0", "@react-native-async-storage/async-storage": "1.18.2", "@react-native-community/netinfo": "^11.4.1", "@react-native-community/push-notification-ios": "^1.11.0", "@react-native-firebase/app": "17.4.2", "@react-native-firebase/auth": "17.4.2", "@react-native-firebase/firestore": "17.4.2", "@react-native-firebase/messaging": "17.4.2", "@react-native-masked-view/masked-view": "0.2.9", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "boost": "^0.63.1", "crypto-browserify": "^3.12.1", "expo": "~49.0.15", "expo-location": "~16.1.0", "expo-splash-screen": "~0.20.5", "expo-status-bar": "~1.6.0", "firebase": "^10.7.1", "firebase-admin": "^13.0.1", "firebase-functions": "^6.1.2", "react": "18.2.0", "react-native": "0.71.8", "react-native-gesture-handler": "~2.12.0", "react-native-maps": "1.7.1", "react-native-reanimated": "^2.17.0", "react-native-safe-area-context": "^5.0.0", "react-native-screens": "~3.22.0", "react-native-vector-icons": "^10.2.0", "stream-browserify": "^3.0.0"}, "devDependencies": {"@babel/core": "^7.26.0", "@babel/preset-env": "^7.26.0", "@babel/preset-typescript": "^7.26.0", "@jest/globals": "^29.7.0", "@react-native-community/cli": "^11.3.7", "@react-native/babel-preset": "^0.76.6", "@testing-library/jest-dom": "^5.16.5", "@testing-library/jest-native": "^5.4.3", "@testing-library/react": "^13.4.0", "@testing-library/react-native": "^12.3.2", "@tsconfig/react-native": "^3.0.5", "@types/jest": "^29.5.0", "@types/react": "~18.2.14", "@types/react-dom": "^18.2.0", "@types/react-native": "^0.72.8", "@types/react-native-vector-icons": "^6.4.18", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-react-native": "^18.0.0", "react-dom": "18.2.0", "react-native-dotenv": "^3.4.11", "react-native-svg-transformer": "^1.5.0", "react-test-renderer": "^18.2.0", "typescript": "^5.1.3"}, "private": true}