import React from 'react';
import { View, TextInput, TouchableOpacity, Text, StyleSheet } from 'react-native';

interface MessageInputProps {
  newMessage: string;
  setNewMessage: (message: string) => void;
  handleSendMessage: () => void;
  isAuthorized: boolean;
  offerStatus: string;
}

export function MessageInput({
  newMessage,
  setNewMessage,
  handleSendMessage,
  isAuthorized,
  offerStatus,
}: MessageInputProps) {
  if (!isAuthorized || offerStatus === 'withdrawn') {
    return (
      <View style={styles.disabledInputContainer}>
        <Text style={styles.disabledText}>
          {offerStatus === 'withdrawn' 
            ? 'This offer has been withdrawn. Messages are disabled.'
            : 'Only the posting owner and offer owner can send messages.'}
        </Text>
      </View>
    );
  }

  return (
    <View style={styles.inputContainer}>
      <TextInput
        style={styles.input}
        value={newMessage}
        onChangeText={setNewMessage}
        placeholder="Type a message..."
        multiline
      />
      <TouchableOpacity 
        style={[styles.sendButton, !newMessage.trim() && styles.sendButtonDisabled]}
        onPress={handleSendMessage}
        disabled={!newMessage.trim()}
      >
        <Text style={styles.sendButtonText}>Send</Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  inputContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'white',
    borderTopWidth: 1,
    borderTopColor: '#eee',
    padding: 10,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
    zIndex: 1000,
  },
  input: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 20,
    paddingHorizontal: 15,
    paddingVertical: 8,
    marginRight: 10,
    maxHeight: 100,
    backgroundColor: 'white',
  },
  sendButton: {
    backgroundColor: '#007AFF',
    borderRadius: 20,
    paddingVertical: 8,
    paddingHorizontal: 15,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sendButtonDisabled: {
    opacity: 0.5,
  },
  sendButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  disabledInputContainer: {
    backgroundColor: '#F2F2F2',
    padding: 16,
    alignItems: 'center',
    borderTopWidth: 1,
    borderTopColor: '#E5E5E5',
    
  },
  disabledText: {
    color: '#999',
    fontSize: 14,
  },
}); 