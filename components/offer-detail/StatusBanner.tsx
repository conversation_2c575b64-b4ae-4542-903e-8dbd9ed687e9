import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

interface StatusBannerProps {
  status: string;
  message?: string;
}

export function StatusBanner({ status, message }: StatusBannerProps) {
  if (status !== 'withdrawn') return null;
  
  return (
    <View style={styles.statusBanner}>
      <Text style={styles.statusText}>
        {message || 'This offer has been withdrawn'}
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  statusBanner: {
    backgroundColor: '#FFE5E5',
    padding: 10,
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#FFB5B5',
  },
  statusText: {
    color: '#FF3B30',
    fontWeight: '600',
    fontSize: 14,
  },
}); 