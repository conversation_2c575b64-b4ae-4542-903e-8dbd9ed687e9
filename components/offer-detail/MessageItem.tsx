import React from 'react';
import { View, Text, StyleSheet, Platform } from 'react-native';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import { getRelativeTime } from '../../utils/dateUtils';
import type { Message } from '../../types/firebase';

interface MessageItemProps {
  item: Message;
  currentUserId: string;
  offerOwnerId: string;
}

function ReadReceipt({ item, currentUserId }: { item: Message; currentUserId: string }) {
  if (item.senderId !== currentUserId) return null;

  const getReadAtTime = () => {
    try {
      if (item.read && item.readAt && item.readAt.seconds) {
        const date = new Date(item.readAt.seconds * 1000);
        return getRelativeTime(date);
      }
      return '';
    } catch (error) {
      console.error('Error getting readAt time:', error);
      return '';
    }
  };

  return (
    <View style={styles.readReceiptContainer}>
      <MaterialIcons 
        name={item.read ? "done-all" : "done"} 
        size={16} 
        color={item.read ? "#4CAF50" : "#999"}
      />
      {item.read && item.readAt && (
        <Text style={styles.readAtTime}>
          Read {getReadAtTime()}
        </Text>
      )}
    </View>
  );
}

export function MessageItem({ item, currentUserId, offerOwnerId }: MessageItemProps) {
  const getMessageTime = () => {
    try {
      if (item.timestamp && item.timestamp.seconds) {
        const date = new Date(item.timestamp.seconds * 1000);
        return getRelativeTime(date);
      }
      console.warn('Invalid timestamp format:', item.timestamp);
      return '';
    } catch (error) {
      console.error('Error getting message time:', error);
      return '';
    }
  };

  return (
    <View style={[
      styles.messageContainer,
      item.senderId === offerOwnerId
        ? styles.offerOwnerMessage
        : styles.postingOwnerMessage,
      Platform.select({
        ios: {
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 1 },
          shadowOpacity: 0.1,
          shadowRadius: 2,
        },
        android: {
          elevation: 2,
        },
      }),
    ]}>
      <Text style={styles.messageText}>{item.text}</Text>
      <View style={styles.messageFooter}>
        <Text style={styles.messageTime}>
          {getMessageTime()}
        </Text>
        <ReadReceipt item={item} currentUserId={currentUserId} />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  messageContainer: {
    padding: 10,
    marginVertical: 5,
    borderRadius: 10,
    maxWidth: '80%',
  },
  postingOwnerMessage: {
    backgroundColor: '#d1f0ff',
    alignSelf: 'flex-end',
  },
  offerOwnerMessage: {
    backgroundColor: '#f1f1f1',
    alignSelf: 'flex-start',
  },
  messageText: {
    fontSize: 16,
  },
  messageFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    marginTop: 4,
  },
  readReceiptContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 6,
  },
  readAtTime: {
    fontSize: 12,
    color: '#666',
    marginLeft: 4,
  },
  messageTime: {
    fontSize: 12,
    color: '#666',
    marginTop: 0,
  },
}); 