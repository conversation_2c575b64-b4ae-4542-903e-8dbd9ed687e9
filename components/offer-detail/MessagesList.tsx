import React, { useEffect } from 'react';
import { View, FlatList, Text, StyleSheet, ActivityIndicator } from 'react-native';
import { MessageItem } from './MessageItem';
import useDiscussions from '../../hooks/useDiscussions';
import type { Message } from '../../types/firebase';

interface MessagesListProps {
  offerId: string;
  currentUserId: string;
  offerOwnerId: string;
  onMessagesUpdate?: (messages: Message[]) => void;
}

export function MessagesList({ 
  offerId, 
  currentUserId, 
  offerOwnerId,
  onMessagesUpdate 
}: MessagesListProps) {
  const { messages, loading, error } = useDiscussions(offerId);

  // Notify parent component when messages change
  useEffect(() => {
    if (onMessagesUpdate) {
      onMessagesUpdate(messages);
    }
  }, [messages, onMessagesUpdate]);

  if (loading) {
    return (
      <View style={styles.centerContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.centerContainer}>
        <Text style={styles.errorText}>Error loading messages</Text>
        <Text style={styles.errorDetails}>{error.message}</Text>
      </View>
    );
  }

  if (!messages.length) {
    return (
      <View style={styles.centerContainer}>
        <Text style={styles.noMessagesText}>No messages yet</Text>
      </View>
    );
  }

  return (
    <FlatList
      data={messages}
      keyExtractor={(item) => item.id || `${item.senderId}_${Date.now()}`}
      renderItem={({ item }) => (
        <MessageItem 
          item={item}
          currentUserId={currentUserId}
          offerOwnerId={offerOwnerId}
        />
      )}
      contentContainerStyle={styles.listContent}
      inverted={false}
    />
  );
}

const styles = StyleSheet.create({
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  listContent: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  noMessagesText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  errorText: {
    fontSize: 16,
    color: '#FF3B30',
    textAlign: 'center',
    marginBottom: 8,
  },
  errorDetails: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },
});