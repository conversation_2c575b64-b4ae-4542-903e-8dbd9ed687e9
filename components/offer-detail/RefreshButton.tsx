import React, { useEffect, useRef } from 'react';
import { TouchableOpacity, StyleSheet, Animated, Easing } from 'react-native';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';

interface RefreshButtonProps {
  onPress: () => void;
  isLoading?: boolean;
  testID?: string;
}

export function RefreshButton({ onPress, isLoading, testID }: RefreshButtonProps) {
  // Add rotation animation
  const rotateAnim = useRef(new Animated.Value(0)).current;

  // Start rotation animation when loading
  useEffect(() => {
    if (isLoading) {
      Animated.loop(
        Animated.timing(rotateAnim, {
          toValue: 1,
          duration: 1000,
          easing: Easing.linear,
          useNativeDriver: true
        })
      ).start();
    } else {
      rotateAnim.setValue(0);
    }
  }, [isLoading, rotateAnim]);

  // Create the rotation interpolation
  const spin = rotateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg']
  });

  return (
    <TouchableOpacity
      testID={testID}
      style={styles.button}
      onPress={onPress}
      disabled={isLoading}
    >
      <Animated.View style={{ transform: [{ rotate: spin }] }}>
        <MaterialIcons
          name="refresh"
          size={24}
          color={isLoading ? '#999' : '#007AFF'}
        />
      </Animated.View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  button: {
    padding: 8,
    marginLeft: 8,
  }
}); 