import React, { useEffect, useState } from 'react';
import { View, ScrollView, StyleSheet, Platform, Text, Dimensions } from 'react-native';

interface ScrollableContentProps {
  isExpanded: boolean;
  children: React.ReactNode;
  isLoading?: boolean;
}

export function ScrollableContent({ 
  isExpanded, 
  children,
  isLoading = false,
}: ScrollableContentProps) {
  const [maxHeight, setMaxHeight] = useState(0);
  
  useEffect(() => {
    // Get screen height
    const screenHeight = Dimensions.get('window').height;
    // Calculate max height by subtracting:
    // - input container height (~60px)
    // - extra padding for visibility (100px)
    // - status bar height (~44px on iOS)
    // - header height (~44px)
    const calculatedHeight = screenHeight - 200; // Adjusted total offset
    setMaxHeight(calculatedHeight);
  }, []);

  if (!isExpanded) {
    return null;
  }

  return (
    <View style={[styles.container, { maxHeight }]}>
      <ScrollView 
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={true}
        bounces={Platform.OS === 'ios'}
        alwaysBounceVertical={false}
        scrollEventThrottle={16}
        nestedScrollEnabled={true}
      >
        {isLoading ? (
          <View style={styles.placeholder}>
            <Text style={styles.placeholderText}>Loading...</Text>
          </View>
        ) : (
          <View style={styles.contentContainer}>
            {children}
          </View>
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    width: '100%',
  },
  scrollView: {
    width: '100%',
  },
  scrollContent: {
    flexGrow: 0,
    paddingBottom: 0, // Add significant bottom padding to ensure content is visible
  },
  contentContainer: {
    padding: 16,
    paddingTop: 8,
  },
  placeholder: {
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  placeholderText: {
    color: '#999',
    fontSize: 14,
  }
}); 