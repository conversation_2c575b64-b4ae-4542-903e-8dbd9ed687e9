// MapMarker.tsx

import React, { forwardRef } from 'react';
import { Marker, Callout } from 'react-native-maps';
import { View, Text, StyleSheet } from 'react-native';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';

interface MapMarkerProps {
  identifier: string;
  coordinate: {
    latitude: number;
    longitude: number;
  };
  title: string;
  description: string;
  selected?: boolean;
  isFavorite?: boolean;
  onCalloutPress?: () => void;
}

const MapMarker = forwardRef<typeof Marker, MapMarkerProps>((props, ref) => {
  const { identifier, coordinate, title, description, selected, isFavorite, onCalloutPress } = props;

  return (
    <Marker
      ref={ref}
      identifier={identifier}
      coordinate={coordinate}
      title={title}
      description={description}
      pinColor={selected ? 'blue' : 'red'}
    >
      <Callout onPress={onCalloutPress}>
        <View style={styles.calloutContainer}>
          <View style={styles.calloutHeader}>
            <Text style={styles.calloutTitle}>{title}</Text>
            {isFavorite && (
              <MaterialIcons name="favorite" size={16} color="#FF4081" style={styles.heartIcon} />
            )}
          </View>
          <Text style={styles.calloutDescription} numberOfLines={2}>{description}</Text>
        </View>
      </Callout>
    </Marker>
  );
});

const styles = StyleSheet.create({
  calloutContainer: {
    width: 150,
    padding: 5,
  },
  calloutHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  calloutTitle: {
    fontWeight: 'bold',
    flex: 1,
    marginRight: 4,
  },
  calloutDescription: {
    fontSize: 12,
    color: '#666',
  },
  heartIcon: {
    marginLeft: 4,
  },
});

export default MapMarker;