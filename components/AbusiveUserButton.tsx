import React, { useState } from 'react';
import { TouchableOpacity, Text, StyleSheet, Alert } from 'react-native';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';

interface AbusiveUserButtonProps {
  onMarkAbusive: () => Promise<void>;
  onNavigate?: () => void;
  style?: object;
  userId?: string | null;
  targetUserId?: string | null;
}

export function AbusiveUserButton({ 
  onMarkAbusive, 
  onNavigate, 
  style,
  userId,
  targetUserId 
}: AbusiveUserButtonProps) {
  const [isLoading, setIsLoading] = useState(false);

  const showWorkInProgressAlert = () => {
    console.log('[AbusiveUserButton] Showing work in progress alert', {
      userId,
      targetUserId,
      timestamp: new Date().toISOString()
    });

    Alert.alert(
      'Feature in Development',
      'This feature is currently under development and will be available soon.',
      [
        {
          text: 'OK',
          onPress: () => {
            console.log('[AbusiveUserButton] Work in progress alert acknowledged, navigating...', {
              userId,
              targetUserId,
              timestamp: new Date().toISOString()
            });
            onNavigate?.();
          },
        },
      ]
    );
  };

  const handlePress = async () => {
    console.log('[AbusiveUserButton] Button pressed', {
      userId,
      targetUserId,
      timestamp: new Date().toISOString()
    });

    Alert.alert(
      'Mark User as Abusive',
      'Are you sure you want to mark this user as abusive? This will restrict them from submitting offers to your postings for 30 days.',
      [
        {
          text: 'Cancel',
          style: 'cancel',
          onPress: () => {
            console.log('[AbusiveUserButton] Mark as abusive cancelled', {
              userId,
              targetUserId,
              timestamp: new Date().toISOString()
            });
          },
        },
        {
          text: 'Confirm',
          onPress: async () => {
            setIsLoading(true);
            try {
              console.log('[AbusiveUserButton] Mark as abusive confirmed', {
                userId,
                targetUserId,
                timestamp: new Date().toISOString()
              });
              
              await onMarkAbusive();
              
              console.log('[AbusiveUserButton] Mark as abusive API call successful', {
                userId,
                targetUserId,
                timestamp: new Date().toISOString()
              });
              
              // Show work in progress alert
              showWorkInProgressAlert();
            } catch (error) {
              console.error('[AbusiveUserButton] Error marking user as abusive:', {
                error,
                userId,
                targetUserId,
                timestamp: new Date().toISOString()
              });
              Alert.alert(
                'Error',
                'Failed to mark user as abusive. Please try again.'
              );
            } finally {
              setIsLoading(false);
            }
          },
        },
      ],
      { cancelable: true }
    );
  };

  return (
    <TouchableOpacity
      style={[styles.button, style]}
      onPress={handlePress}
      disabled={isLoading}
    >
      <MaterialIcons name="report" size={20} color="#fff" style={styles.icon} />
      <Text style={styles.text}>
        {isLoading ? 'Marking as Abusive...' : 'Mark as Abusive'}
      </Text>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#D32F2F',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  icon: {
    marginRight: 8,
  },
  text: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
}); 