// /Users/<USER>/Desktop/satbana/components/ListItem.tsx

import React from 'react';
import { TouchableOpacity, View, Text, StyleSheet } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootStackParamList } from '../types/navigation';
import { Posting } from '../types/posting';

interface ListItemProps {
  posting: Posting;
}

type ListItemNavigationProp = StackNavigationProp<RootStackParamList>;

const ListItem = React.memo(({ posting }: ListItemProps) => {
  const navigation = useNavigation<ListItemNavigationProp>();

  const handlePress = () => {
    navigation.navigate('PostingDetail', {
      postingId: posting.id,
      itemName: posting.title,
      itemLocation: posting.location,
      userId: posting.userId,
    });
  };
  
  if (!posting || typeof posting.title === 'undefined') {
    return null;
  }
  
  return (
    <TouchableOpacity 
      onPress={handlePress}
      accessibilityLabel={`Open details for ${posting.title || 'Untitled'}`}
    >
      <View style={styles.listItem}>
        <Text style={styles.title}>{posting.title || 'Untitled'}</Text>
      </View>
    </TouchableOpacity>
  );
});

const styles = StyleSheet.create({
  listItem: {
    padding: 15,
    backgroundColor: 'white',
    borderRadius: 8,
    marginVertical: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },

});

export default ListItem;