// FormInput.tsx

import React from 'react';
import { View, TextInput, Text, StyleSheet, StyleProp, ViewStyle, TextStyle } from 'react-native';

interface FormInputProps {
  label?: string;
  value: string;
  onChangeText: (text: string) => void;
  placeholder: string;
  multiline?: boolean;
  keyboardType?: 'default' | 'numeric';
  style?: StyleProp<ViewStyle>;
  testID?: string;
}

function FormInput({ 
  label, 
  value, 
  onChangeText, 
  placeholder, 
  multiline = false, 
  keyboardType = 'default',
  style,
  testID
}: FormInputProps): React.JSX.Element {
  return (
    <View style={[styles.container, style]}>
      {label ? <Text style={styles.label}>{label}</Text> : null}
      <TextInput
        style={[styles.input, multiline ? styles.multilineInput : null]}
        value={value}
        onChangeText={onChangeText}
        placeholder={placeholder}
        multiline={multiline}
        keyboardType={keyboardType}
        testID={testID}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 20,
    width: '100%',
  },
  label: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  input: {
    width: '100%',
    padding: 10,
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 5,
    backgroundColor: 'white'
  },
  multilineInput: {
    height: 100,
    textAlignVertical: 'top',
  },
});

export default FormInput;