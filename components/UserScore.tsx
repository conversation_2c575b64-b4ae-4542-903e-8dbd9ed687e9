import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';

interface UserScoreProps {
  score: number;
  size?: 'small' | 'medium' | 'large';
  showIcon?: boolean;
}

export function UserScore({ score, size = 'medium', showIcon = true }: UserScoreProps) {
  const getScoreColor = (score: number) => {
    if (score >= 8) return '#4CAF50'; // Green for high scores
    if (score >= 5) return '#FFC107'; // Yellow for medium scores
    return '#F44336'; // Red for low scores
  };

  const getFontSize = (size: 'small' | 'medium' | 'large') => {
    switch (size) {
      case 'small': return 12;
      case 'large': return 18;
      default: return 14;
    }
  };

  const getIconSize = (size: 'small' | 'medium' | 'large') => {
    switch (size) {
      case 'small': return 14;
      case 'large': return 20;
      default: return 16;
    }
  };

  return (
    <View style={styles.container}>
      {showIcon && (
        <MaterialIcons 
          name="star" 
          size={getIconSize(size)} 
          color={getScoreColor(score)} 
          style={styles.icon}
        />
      )}
      <Text style={[
        styles.score,
        { fontSize: getFontSize(size), color: getScoreColor(score) }
      ]}>
        {score.toFixed(1)}
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 4,
  },
  icon: {
    marginRight: 2,
  },
  score: {
    fontWeight: '600',
  },
}); 