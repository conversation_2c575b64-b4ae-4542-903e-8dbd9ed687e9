import React, { useEffect, useState, useCallback } from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { auth } from '../firebase';
import { setupNotificationListener } from '../services/notificationService';
import { listenerRegistry } from '../utils/listenerRegistry';

const NotificationBell = () => {
  const navigation = useNavigation();
  const [unreadCount, setUnreadCount] = useState(0);

  // Callback to handle unread count updates
  const handleUnreadCountUpdate = useCallback((count: number) => {
    setUnreadCount(count);
  }, []);

  useEffect(() => {
    let unsubscribe: (() => void) | undefined;

    const setupListener = async () => {
      const user = auth.currentUser;
      if (user) {
        console.log('Setting up notification bell listener for user:', user.uid);
        unsubscribe = setupNotificationListener(user.uid, handleUnreadCountUpdate);
      }
    };

    setupListener();

    // Cleanup function
    return () => {
      console.log('Cleaning up notification bell listener');
      if (unsubscribe) {
        try {
          unsubscribe();
          console.log('Successfully cleaned up notification bell listener');
        } catch (error) {
          console.warn('Error cleaning up notification bell listener:', error);
        }
      }
    };
  }, [handleUnreadCountUpdate]);

  const handlePress = () => {
    navigation.navigate('Notifications');
  };

  return (
    <TouchableOpacity onPress={handlePress} style={styles.container}>
      <View style={styles.bellContainer}>
        <Ionicons name="notifications-outline" size={24} color="#000" />
        {unreadCount > 0 && (
          <View style={styles.badge}>
            <Text style={styles.badgeText}>
              {unreadCount > 99 ? '99+' : unreadCount}
            </Text>
          </View>
        )}
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 10,
    marginRight: 0,
    marginTop: 50,
  },
  bellContainer: {
    position: 'relative',
  },
  badge: {
    position: 'absolute',
    right: -6,
    top: -3,
    backgroundColor: 'red',
    borderRadius: 9,
    minWidth: 18,
    height: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  badgeText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
});

export default NotificationBell; 
