import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';

interface RestrictionMessageProps {
  restrictionEndDate: Date;
  style?: object;
}

export function RestrictionMessage({ restrictionEndDate, style }: RestrictionMessageProps) {
  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <View style={[styles.container, style]}>
      <MaterialIcons name="warning" size={20} color="#F44336" style={styles.icon} />
      <Text style={styles.text}>
        This user is restricted from submitting offers to your postings until{' '}
        <Text style={styles.date}>{formatDate(restrictionEndDate)}</Text>
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: 12,
    backgroundColor: '#FFEBEE',
    borderRadius: 8,
    marginVertical: 8,
  },
  icon: {
    marginRight: 8,
    marginTop: 2,
  },
  text: {
    flex: 1,
    color: '#D32F2F',
    fontSize: 14,
    lineHeight: 20,
  },
  date: {
    fontWeight: '600',
  },
}); 