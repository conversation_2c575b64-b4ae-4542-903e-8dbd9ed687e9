import React, { useEffect, useRef, useState } from 'react';
import { View, PanResponder, Animated, StyleSheet, Keyboard, Platform, KeyboardEvent, Dimensions } from 'react-native';

interface DraggableListContainerProps {
  children: React.ReactNode;
  searchFieldHeight: number;
  onScroll?: (event: any) => void;
}

const HANDLE_HEIGHT = 5;
const POSTING_HEIGHT = 100; // Height of one posting item
const HANDLE_AREA_HEIGHT = 30; // Height of handle area including margins

export function DraggableListContainer({ children, searchFieldHeight, onScroll }: DraggableListContainerProps) {
  const pan = useRef(new Animated.ValueXY()).current;
  const MIN_CONTAINER_HEIGHT = POSTING_HEIGHT + HANDLE_AREA_HEIGHT;
  const MAX_CONTAINER_HEIGHT = Dimensions.get('window').height - searchFieldHeight - 100;
  
  const [containerHeight, setContainerHeight] = useState(MIN_CONTAINER_HEIGHT);
  const [keyboardHeight, setKeyboardHeight] = useState(0);
  const lastGestureDy = useRef(0);
  const isExpanded = useRef(false);

  useEffect(() => {
    const keyboardWillShow = (event: KeyboardEvent) => {
      setKeyboardHeight(event.endCoordinates.height);
      const newValue = lastGestureDy.current - event.endCoordinates.height;
      
      Animated.spring(pan.y, {
        toValue: newValue,
        useNativeDriver: false,
        bounciness: 0,
      }).start();
      
      lastGestureDy.current = newValue;
    };

    const keyboardWillHide = () => {
      setKeyboardHeight(0);
      const newValue = lastGestureDy.current + keyboardHeight;
      
      Animated.spring(pan.y, {
        toValue: newValue,
        useNativeDriver: false,
        bounciness: 0,
      }).start();
      
      lastGestureDy.current = newValue;
    };

    const showListener = Platform.OS === 'ios' 
      ? Keyboard.addListener('keyboardWillShow', keyboardWillShow)
      : Keyboard.addListener('keyboardDidShow', keyboardWillShow);
      
    const hideListener = Platform.OS === 'ios'
      ? Keyboard.addListener('keyboardWillHide', keyboardWillHide)
      : Keyboard.addListener('keyboardDidHide', keyboardWillHide);

    return () => {
      showListener.remove();
      hideListener.remove();
    };
  }, [keyboardHeight]);

  const toggleExpansion = () => {
    const toValue = isExpanded.current ? 0 : -(MAX_CONTAINER_HEIGHT - MIN_CONTAINER_HEIGHT);
    
    Animated.spring(pan.y, {
      toValue,
      useNativeDriver: false,
      bounciness: 0,
    }).start();

    lastGestureDy.current = toValue;
    isExpanded.current = !isExpanded.current;
  };

  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onPanResponderGrant: () => {
        pan.setOffset({
          x: 0,
          y: lastGestureDy.current,
        });
      },
      onPanResponderMove: (_, gesture) => {
        const newY = gesture.dy + lastGestureDy.current;
        if (newY <= 0 && newY >= -(MAX_CONTAINER_HEIGHT - MIN_CONTAINER_HEIGHT)) {
          pan.y.setValue(gesture.dy);
        }
      },
      onPanResponderRelease: (_, gesture) => {
        pan.flattenOffset();
        lastGestureDy.current += gesture.dy;

        // Determine whether to snap to min or max height
        const shouldExpand = lastGestureDy.current < -(MAX_CONTAINER_HEIGHT - MIN_CONTAINER_HEIGHT) / 2;
        
        const finalValue = shouldExpand ? 
          -(MAX_CONTAINER_HEIGHT - MIN_CONTAINER_HEIGHT) : 
          0;

        Animated.spring(pan.y, {
          toValue: finalValue,
          useNativeDriver: false,
          bounciness: 0,
        }).start();

        lastGestureDy.current = finalValue;
        isExpanded.current = shouldExpand;
      },
    })
  ).current;

  const animatedHeight = Animated.add(
    MIN_CONTAINER_HEIGHT,
    Animated.multiply(pan.y, -1)
  ).interpolate({
    inputRange: [MIN_CONTAINER_HEIGHT, MAX_CONTAINER_HEIGHT],
    outputRange: [MIN_CONTAINER_HEIGHT, MAX_CONTAINER_HEIGHT],
    extrapolate: 'clamp'
  });

  const containerStyle = {
    height: animatedHeight,
    bottom: keyboardHeight,
  };

  return (
    <Animated.View
      style={[styles.container, containerStyle]}
      onLayout={(event) => {
        const { height } = event.nativeEvent.layout;
        setContainerHeight(height);
      }}
    >
      <View {...panResponder.panHandlers} style={styles.handleArea}>
        <View style={styles.handle} />
      </View>
      <View style={styles.content}>
        {children}
      </View>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'white',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  handleArea: {
    height: HANDLE_AREA_HEIGHT,
    justifyContent: 'center',
    alignItems: 'center',
  },
  handle: {
    width: 40,
    height: HANDLE_HEIGHT,
    backgroundColor: '#DDDDDD',
    borderRadius: HANDLE_HEIGHT / 2,
  },
  content: {
    flex: 1,
  },
}); 