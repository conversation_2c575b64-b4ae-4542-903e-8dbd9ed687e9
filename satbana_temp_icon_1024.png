# Solar Trackers

# Executive Summary:

Trackers are increasingly becoming an integral part of the
utility scale solar power plants. The widespread adoption of
bifacial technology in the utility segment has also favored
the growing popularity of trackers, given the synergies
resulting from this combination. Trackers, as a segment, is
continuing to advance in all areas, and so is our coverage
of the topic as will be evident as you read through this
2nd Edition of the Market Survey on Solar Trackers. The
survey features 46 products from 21 tracker suppliers.
The basic function of trackers is to follow the sun. While
manual trackers have been in existence for several
decades, and even the first automatic tracker for
concentrated PV was developed in the 1970s, today’s
solar trackers are generally used for standard PV modules
in utility-scale systems – and are smart and sophisticated.
Broken down, there are 3 types of trackers. Singleaxis trackers have only one degree of freedom; it can
be horizontal or vertical. Dual-axis trackers (DAT) have
two degrees of freedom. However, today’s utility-scale
PV plants almost exclusively use singe-axis trackers,
specifically horizontal-single axis-trackers. So HSATs are
the state of the art at present. These trackers use motors,
drives, sensors and controllers to position the solar
modules optimally towards the sun.
Trackers as a segment was hit hard by supply disruptions
and raw material price increases. According to IHS Markit,
the segment still registered around 39 GW worth of
installations in 2021. And maintaining its growth trajectory,
the segment is expected to reach about 46 GW in 2022.
Total shipments are estimated to be over 50 GW, and
the top 3 tracker suppliers together – Nextracker, Array
Technolgies and PVH – control more than 50% of the
market.
Being the key enabler for trackers, complementing or
being compatible with bifacial is the top priority for the
tracker segment (which is why we called a TaiyangNews
event in July 2022 Bifacial & Solar Trackers Conference).
A lot has been achieved on this front. Avoiding torquetube shading is the basic design aspect, and several such
torque tube designs are available today. 2P configurations
are also gaining more popularity on this account.
The industry is also focusing more and more on tracking
algorithms. Companies are developing proprietary
algorithms that provide site-specific inclination angles for
dynamic weather conditions. The aim of such software is
primarily to improve the diffused light absorption, especially
during cloudy and rainy days. The algorithms also take
the bifacial aspect of the module into consideration and
position the panels in optimal inclination. The second
where algorithms become important is in finding the
optimal position for the so-called backtracking in order to
avoid row-to-row shading. Based on 3D mapping of the
site and powered by advanced simulation tools, a few
companies offer such backtracking strategies to maximize
energy generation.
Adapting to complex terrains is another topic being more
and more prominently discussed during these days where
flat lands is not always easily accessible for very large
PV plants. One solution is to grade the land to make it
flat to install the trackers. Alternatively, increasing the pier
height helps in maintaining the slope of the trackers, which
obviously requires extra materials. Some companies are
addressing this issue with their terrain following tracker
design. These designs are also being improved to sustain
wind loads and larger form factor modules. Separately,
trackers are also finding new applications such as
agrivoltaics, which, though at the very beginning, might be
a key to tapping the full potential of solar in the long run.
In addition to summarizing these key developments,
the survey also lists key specifications of products (in
tables; see p. 44 - 62) and corresponding detailed product
descriptions (see chapter 5).

## Techniques summary

# Techniques Used in Solar Trackers:
1. Sun-tracking algorithms for optimal panel positioning
2. Bifacial module compatibility techniques
3. Torque tube shading reduction designs
4. 2P (two-portrait) module configurations for enhanced energy yield and reduced shading
5. Site-specific inclination angle calculations
6. Diffused light absorption optimization
7. Backtracking strategies to prevent row-to-row shading
8. 3D mapping and simulation for site optimization
9. Terrain-following tracker designs
10. Wind load resistance engineering
11. Agrivoltaics integration methods
12. Pier height adjustment for slope management