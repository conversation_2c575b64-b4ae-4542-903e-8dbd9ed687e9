import React, { useEffect, useRef, useState } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import AppNavigator from './navigation/AppNavigator';
import { StyleSheet, LogBox, Platform, AppState, AppStateStatus } from 'react-native';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import type { NavigationContainerRef } from '@react-navigation/native';
import { useNotificationSetup } from './hooks/useNotificationSetup';
import { requestNotificationPermissions } from './services/notificationService';

LogBox.ignoreLogs([
  '`new NativeEventEmitter()`',
  'EventEmitter.removeListener',
]);

const App: React.FC = () => {
  const navigationRef = useRef<NavigationContainerRef<any>>(null);
  const appState = useRef<AppStateStatus>(AppState.currentState);
  const [appStateVisible, setAppStateVisible] = useState<AppStateStatus>(appState.current);
  useNotificationSetup();

  useEffect(() => {
    const setupPermissions = async () => {
      try {
        await requestNotificationPermissions();
      } catch (error) {
        console.error('Error setting up notification permissions:', error);
      }
    };
    
    setupPermissions();
  }, []);

  useEffect(() => {
    const subscription = AppState.addEventListener('change', nextAppState => {
      console.log('App state changed from', appState.current, 'to', nextAppState);
      appState.current = nextAppState;
      setAppStateVisible(nextAppState);
    });

    return () => {
      subscription.remove();
    };
  }, []);

  return (
    <SafeAreaProvider>
      <GestureHandlerRootView style={styles.container}>
        <NavigationContainer ref={navigationRef}>
          <AppNavigator />
        </NavigationContainer>
      </GestureHandlerRootView>
    </SafeAreaProvider>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default App;
