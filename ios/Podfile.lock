PODS:
  - abseil (1.20211102.0):
    - abseil/algorithm (= 1.20211102.0)
    - abseil/base (= 1.20211102.0)
    - abseil/cleanup (= 1.20211102.0)
    - abseil/container (= 1.20211102.0)
    - abseil/debugging (= 1.20211102.0)
    - abseil/flags (= 1.20211102.0)
    - abseil/functional (= 1.20211102.0)
    - abseil/hash (= 1.20211102.0)
    - abseil/memory (= 1.20211102.0)
    - abseil/meta (= 1.20211102.0)
    - abseil/numeric (= 1.20211102.0)
    - abseil/profiling (= 1.20211102.0)
    - abseil/random (= 1.20211102.0)
    - abseil/status (= 1.20211102.0)
    - abseil/strings (= 1.20211102.0)
    - abseil/synchronization (= 1.20211102.0)
    - abseil/time (= 1.20211102.0)
    - abseil/types (= 1.20211102.0)
    - abseil/utility (= 1.20211102.0)
  - abseil/algorithm (1.20211102.0):
    - abseil/algorithm/algorithm (= 1.20211102.0)
    - abseil/algorithm/container (= 1.20211102.0)
  - abseil/algorithm/algorithm (1.20211102.0):
    - abseil/base/config
  - abseil/algorithm/container (1.20211102.0):
    - abseil/algorithm/algorithm
    - abseil/base/core_headers
    - abseil/meta/type_traits
  - abseil/base (1.20211102.0):
    - abseil/base/atomic_hook (= 1.20211102.0)
    - abseil/base/base (= 1.20211102.0)
    - abseil/base/base_internal (= 1.20211102.0)
    - abseil/base/config (= 1.20211102.0)
    - abseil/base/core_headers (= 1.20211102.0)
    - abseil/base/dynamic_annotations (= 1.20211102.0)
    - abseil/base/endian (= 1.20211102.0)
    - abseil/base/errno_saver (= 1.20211102.0)
    - abseil/base/fast_type_id (= 1.20211102.0)
    - abseil/base/log_severity (= 1.20211102.0)
    - abseil/base/malloc_internal (= 1.20211102.0)
    - abseil/base/pretty_function (= 1.20211102.0)
    - abseil/base/raw_logging_internal (= 1.20211102.0)
    - abseil/base/spinlock_wait (= 1.20211102.0)
    - abseil/base/strerror (= 1.20211102.0)
    - abseil/base/throw_delegate (= 1.20211102.0)
  - abseil/base/atomic_hook (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
  - abseil/base/base (1.20211102.0):
    - abseil/base/atomic_hook
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/log_severity
    - abseil/base/raw_logging_internal
    - abseil/base/spinlock_wait
    - abseil/meta/type_traits
  - abseil/base/base_internal (1.20211102.0):
    - abseil/base/config
    - abseil/meta/type_traits
  - abseil/base/config (1.20211102.0)
  - abseil/base/core_headers (1.20211102.0):
    - abseil/base/config
  - abseil/base/dynamic_annotations (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
  - abseil/base/endian (1.20211102.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
  - abseil/base/errno_saver (1.20211102.0):
    - abseil/base/config
  - abseil/base/fast_type_id (1.20211102.0):
    - abseil/base/config
  - abseil/base/log_severity (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
  - abseil/base/malloc_internal (1.20211102.0):
    - abseil/base/base
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/raw_logging_internal
  - abseil/base/pretty_function (1.20211102.0)
  - abseil/base/raw_logging_internal (1.20211102.0):
    - abseil/base/atomic_hook
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/log_severity
  - abseil/base/spinlock_wait (1.20211102.0):
    - abseil/base/base_internal
    - abseil/base/core_headers
    - abseil/base/errno_saver
  - abseil/base/strerror (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/errno_saver
  - abseil/base/throw_delegate (1.20211102.0):
    - abseil/base/config
    - abseil/base/raw_logging_internal
  - abseil/cleanup (1.20211102.0):
    - abseil/cleanup/cleanup (= 1.20211102.0)
    - abseil/cleanup/cleanup_internal (= 1.20211102.0)
  - abseil/cleanup/cleanup (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/cleanup/cleanup_internal
  - abseil/cleanup/cleanup_internal (1.20211102.0):
    - abseil/base/base_internal
    - abseil/base/core_headers
    - abseil/utility/utility
  - abseil/container (1.20211102.0):
    - abseil/container/btree (= 1.20211102.0)
    - abseil/container/common (= 1.20211102.0)
    - abseil/container/compressed_tuple (= 1.20211102.0)
    - abseil/container/container_memory (= 1.20211102.0)
    - abseil/container/fixed_array (= 1.20211102.0)
    - abseil/container/flat_hash_map (= 1.20211102.0)
    - abseil/container/flat_hash_set (= 1.20211102.0)
    - abseil/container/hash_function_defaults (= 1.20211102.0)
    - abseil/container/hash_policy_traits (= 1.20211102.0)
    - abseil/container/hashtable_debug (= 1.20211102.0)
    - abseil/container/hashtable_debug_hooks (= 1.20211102.0)
    - abseil/container/hashtablez_sampler (= 1.20211102.0)
    - abseil/container/have_sse (= 1.20211102.0)
    - abseil/container/inlined_vector (= 1.20211102.0)
    - abseil/container/inlined_vector_internal (= 1.20211102.0)
    - abseil/container/layout (= 1.20211102.0)
    - abseil/container/node_hash_map (= 1.20211102.0)
    - abseil/container/node_hash_policy (= 1.20211102.0)
    - abseil/container/node_hash_set (= 1.20211102.0)
    - abseil/container/raw_hash_map (= 1.20211102.0)
    - abseil/container/raw_hash_set (= 1.20211102.0)
  - abseil/container/btree (1.20211102.0):
    - abseil/base/core_headers
    - abseil/base/throw_delegate
    - abseil/container/common
    - abseil/container/compressed_tuple
    - abseil/container/container_memory
    - abseil/container/layout
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/strings/cord
    - abseil/strings/strings
    - abseil/types/compare
    - abseil/utility/utility
  - abseil/container/common (1.20211102.0):
    - abseil/meta/type_traits
    - abseil/types/optional
  - abseil/container/compressed_tuple (1.20211102.0):
    - abseil/utility/utility
  - abseil/container/container_memory (1.20211102.0):
    - abseil/base/config
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/utility/utility
  - abseil/container/fixed_array (1.20211102.0):
    - abseil/algorithm/algorithm
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/throw_delegate
    - abseil/container/compressed_tuple
    - abseil/memory/memory
  - abseil/container/flat_hash_map (1.20211102.0):
    - abseil/algorithm/container
    - abseil/container/container_memory
    - abseil/container/hash_function_defaults
    - abseil/container/raw_hash_map
    - abseil/memory/memory
  - abseil/container/flat_hash_set (1.20211102.0):
    - abseil/algorithm/container
    - abseil/base/core_headers
    - abseil/container/container_memory
    - abseil/container/hash_function_defaults
    - abseil/container/raw_hash_set
    - abseil/memory/memory
  - abseil/container/hash_function_defaults (1.20211102.0):
    - abseil/base/config
    - abseil/hash/hash
    - abseil/strings/cord
    - abseil/strings/strings
  - abseil/container/hash_policy_traits (1.20211102.0):
    - abseil/meta/type_traits
  - abseil/container/hashtable_debug (1.20211102.0):
    - abseil/container/hashtable_debug_hooks
  - abseil/container/hashtable_debug_hooks (1.20211102.0):
    - abseil/base/config
  - abseil/container/hashtablez_sampler (1.20211102.0):
    - abseil/base/base
    - abseil/base/core_headers
    - abseil/container/have_sse
    - abseil/debugging/stacktrace
    - abseil/memory/memory
    - abseil/profiling/exponential_biased
    - abseil/profiling/sample_recorder
    - abseil/synchronization/synchronization
    - abseil/utility/utility
  - abseil/container/have_sse (1.20211102.0)
  - abseil/container/inlined_vector (1.20211102.0):
    - abseil/algorithm/algorithm
    - abseil/base/core_headers
    - abseil/base/throw_delegate
    - abseil/container/inlined_vector_internal
    - abseil/memory/memory
  - abseil/container/inlined_vector_internal (1.20211102.0):
    - abseil/base/core_headers
    - abseil/container/compressed_tuple
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/types/span
  - abseil/container/layout (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/strings/strings
    - abseil/types/span
    - abseil/utility/utility
  - abseil/container/node_hash_map (1.20211102.0):
    - abseil/algorithm/container
    - abseil/container/container_memory
    - abseil/container/hash_function_defaults
    - abseil/container/node_hash_policy
    - abseil/container/raw_hash_map
    - abseil/memory/memory
  - abseil/container/node_hash_policy (1.20211102.0):
    - abseil/base/config
  - abseil/container/node_hash_set (1.20211102.0):
    - abseil/algorithm/container
    - abseil/container/hash_function_defaults
    - abseil/container/node_hash_policy
    - abseil/container/raw_hash_set
    - abseil/memory/memory
  - abseil/container/raw_hash_map (1.20211102.0):
    - abseil/base/throw_delegate
    - abseil/container/container_memory
    - abseil/container/raw_hash_set
  - abseil/container/raw_hash_set (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/container/common
    - abseil/container/compressed_tuple
    - abseil/container/container_memory
    - abseil/container/hash_policy_traits
    - abseil/container/hashtable_debug_hooks
    - abseil/container/hashtablez_sampler
    - abseil/container/have_sse
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/utility/utility
  - abseil/debugging (1.20211102.0):
    - abseil/debugging/debugging_internal (= 1.20211102.0)
    - abseil/debugging/demangle_internal (= 1.20211102.0)
    - abseil/debugging/examine_stack (= 1.20211102.0)
    - abseil/debugging/failure_signal_handler (= 1.20211102.0)
    - abseil/debugging/leak_check (= 1.20211102.0)
    - abseil/debugging/leak_check_disable (= 1.20211102.0)
    - abseil/debugging/stacktrace (= 1.20211102.0)
    - abseil/debugging/symbolize (= 1.20211102.0)
  - abseil/debugging/debugging_internal (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/errno_saver
    - abseil/base/raw_logging_internal
  - abseil/debugging/demangle_internal (1.20211102.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
  - abseil/debugging/examine_stack (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/debugging/stacktrace
    - abseil/debugging/symbolize
  - abseil/debugging/failure_signal_handler (1.20211102.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/errno_saver
    - abseil/base/raw_logging_internal
    - abseil/debugging/examine_stack
    - abseil/debugging/stacktrace
  - abseil/debugging/leak_check (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
  - abseil/debugging/leak_check_disable (1.20211102.0):
    - abseil/base/config
  - abseil/debugging/stacktrace (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/debugging/debugging_internal
  - abseil/debugging/symbolize (1.20211102.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/malloc_internal
    - abseil/base/raw_logging_internal
    - abseil/debugging/debugging_internal
    - abseil/debugging/demangle_internal
    - abseil/strings/strings
  - abseil/flags (1.20211102.0):
    - abseil/flags/commandlineflag (= 1.20211102.0)
    - abseil/flags/commandlineflag_internal (= 1.20211102.0)
    - abseil/flags/config (= 1.20211102.0)
    - abseil/flags/flag (= 1.20211102.0)
    - abseil/flags/flag_internal (= 1.20211102.0)
    - abseil/flags/marshalling (= 1.20211102.0)
    - abseil/flags/parse (= 1.20211102.0)
    - abseil/flags/path_util (= 1.20211102.0)
    - abseil/flags/private_handle_accessor (= 1.20211102.0)
    - abseil/flags/program_name (= 1.20211102.0)
    - abseil/flags/reflection (= 1.20211102.0)
    - abseil/flags/usage (= 1.20211102.0)
    - abseil/flags/usage_internal (= 1.20211102.0)
  - abseil/flags/commandlineflag (1.20211102.0):
    - abseil/base/config
    - abseil/base/fast_type_id
    - abseil/flags/commandlineflag_internal
    - abseil/strings/strings
    - abseil/types/optional
  - abseil/flags/commandlineflag_internal (1.20211102.0):
    - abseil/base/config
    - abseil/base/fast_type_id
  - abseil/flags/config (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/flags/path_util
    - abseil/flags/program_name
    - abseil/strings/strings
    - abseil/synchronization/synchronization
  - abseil/flags/flag (1.20211102.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/flags/config
    - abseil/flags/flag_internal
    - abseil/flags/reflection
    - abseil/strings/strings
  - abseil/flags/flag_internal (1.20211102.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/flags/commandlineflag
    - abseil/flags/commandlineflag_internal
    - abseil/flags/config
    - abseil/flags/marshalling
    - abseil/flags/reflection
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/strings/strings
    - abseil/synchronization/synchronization
    - abseil/utility/utility
  - abseil/flags/marshalling (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/log_severity
    - abseil/strings/str_format
    - abseil/strings/strings
  - abseil/flags/parse (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/flags/commandlineflag
    - abseil/flags/commandlineflag_internal
    - abseil/flags/config
    - abseil/flags/flag
    - abseil/flags/flag_internal
    - abseil/flags/private_handle_accessor
    - abseil/flags/program_name
    - abseil/flags/reflection
    - abseil/flags/usage
    - abseil/flags/usage_internal
    - abseil/strings/strings
    - abseil/synchronization/synchronization
  - abseil/flags/path_util (1.20211102.0):
    - abseil/base/config
    - abseil/strings/strings
  - abseil/flags/private_handle_accessor (1.20211102.0):
    - abseil/base/config
    - abseil/flags/commandlineflag
    - abseil/flags/commandlineflag_internal
    - abseil/strings/strings
  - abseil/flags/program_name (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/flags/path_util
    - abseil/strings/strings
    - abseil/synchronization/synchronization
  - abseil/flags/reflection (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/container/flat_hash_map
    - abseil/flags/commandlineflag
    - abseil/flags/commandlineflag_internal
    - abseil/flags/config
    - abseil/flags/private_handle_accessor
    - abseil/strings/strings
    - abseil/synchronization/synchronization
  - abseil/flags/usage (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/flags/usage_internal
    - abseil/strings/strings
    - abseil/synchronization/synchronization
  - abseil/flags/usage_internal (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/container/flat_hash_map
    - abseil/flags/commandlineflag
    - abseil/flags/config
    - abseil/flags/flag
    - abseil/flags/flag_internal
    - abseil/flags/path_util
    - abseil/flags/private_handle_accessor
    - abseil/flags/program_name
    - abseil/flags/reflection
    - abseil/strings/strings
  - abseil/functional (1.20211102.0):
    - abseil/functional/bind_front (= 1.20211102.0)
    - abseil/functional/function_ref (= 1.20211102.0)
  - abseil/functional/bind_front (1.20211102.0):
    - abseil/base/base_internal
    - abseil/container/compressed_tuple
    - abseil/meta/type_traits
    - abseil/utility/utility
  - abseil/functional/function_ref (1.20211102.0):
    - abseil/base/base_internal
    - abseil/base/core_headers
    - abseil/meta/type_traits
  - abseil/hash (1.20211102.0):
    - abseil/hash/city (= 1.20211102.0)
    - abseil/hash/hash (= 1.20211102.0)
    - abseil/hash/low_level_hash (= 1.20211102.0)
  - abseil/hash/city (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
  - abseil/hash/hash (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/container/fixed_array
    - abseil/hash/city
    - abseil/hash/low_level_hash
    - abseil/meta/type_traits
    - abseil/numeric/int128
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/variant
    - abseil/utility/utility
  - abseil/hash/low_level_hash (1.20211102.0):
    - abseil/base/config
    - abseil/base/endian
    - abseil/numeric/bits
    - abseil/numeric/int128
  - abseil/memory (1.20211102.0):
    - abseil/memory/memory (= 1.20211102.0)
  - abseil/memory/memory (1.20211102.0):
    - abseil/base/core_headers
    - abseil/meta/type_traits
  - abseil/meta (1.20211102.0):
    - abseil/meta/type_traits (= 1.20211102.0)
  - abseil/meta/type_traits (1.20211102.0):
    - abseil/base/config
  - abseil/numeric (1.20211102.0):
    - abseil/numeric/bits (= 1.20211102.0)
    - abseil/numeric/int128 (= 1.20211102.0)
    - abseil/numeric/representation (= 1.20211102.0)
  - abseil/numeric/bits (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
  - abseil/numeric/int128 (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/numeric/bits
  - abseil/numeric/representation (1.20211102.0):
    - abseil/base/config
  - abseil/profiling (1.20211102.0):
    - abseil/profiling/exponential_biased (= 1.20211102.0)
    - abseil/profiling/periodic_sampler (= 1.20211102.0)
    - abseil/profiling/sample_recorder (= 1.20211102.0)
  - abseil/profiling/exponential_biased (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
  - abseil/profiling/periodic_sampler (1.20211102.0):
    - abseil/base/core_headers
    - abseil/profiling/exponential_biased
  - abseil/profiling/sample_recorder (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/synchronization/synchronization
    - abseil/time/time
  - abseil/random (1.20211102.0):
    - abseil/random/bit_gen_ref (= 1.20211102.0)
    - abseil/random/distributions (= 1.20211102.0)
    - abseil/random/internal (= 1.20211102.0)
    - abseil/random/random (= 1.20211102.0)
    - abseil/random/seed_gen_exception (= 1.20211102.0)
    - abseil/random/seed_sequences (= 1.20211102.0)
  - abseil/random/bit_gen_ref (1.20211102.0):
    - abseil/base/core_headers
    - abseil/base/fast_type_id
    - abseil/meta/type_traits
    - abseil/random/internal/distribution_caller
    - abseil/random/internal/fast_uniform_bits
    - abseil/random/random
  - abseil/random/distributions (1.20211102.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/random/internal/distribution_caller
    - abseil/random/internal/fast_uniform_bits
    - abseil/random/internal/fastmath
    - abseil/random/internal/generate_real
    - abseil/random/internal/iostream_state_saver
    - abseil/random/internal/traits
    - abseil/random/internal/uniform_helper
    - abseil/random/internal/wide_multiply
    - abseil/strings/strings
  - abseil/random/internal (1.20211102.0):
    - abseil/random/internal/distribution_caller (= 1.20211102.0)
    - abseil/random/internal/fast_uniform_bits (= 1.20211102.0)
    - abseil/random/internal/fastmath (= 1.20211102.0)
    - abseil/random/internal/generate_real (= 1.20211102.0)
    - abseil/random/internal/iostream_state_saver (= 1.20211102.0)
    - abseil/random/internal/mock_helpers (= 1.20211102.0)
    - abseil/random/internal/nanobenchmark (= 1.20211102.0)
    - abseil/random/internal/nonsecure_base (= 1.20211102.0)
    - abseil/random/internal/pcg_engine (= 1.20211102.0)
    - abseil/random/internal/platform (= 1.20211102.0)
    - abseil/random/internal/pool_urbg (= 1.20211102.0)
    - abseil/random/internal/randen (= 1.20211102.0)
    - abseil/random/internal/randen_engine (= 1.20211102.0)
    - abseil/random/internal/randen_hwaes (= 1.20211102.0)
    - abseil/random/internal/randen_hwaes_impl (= 1.20211102.0)
    - abseil/random/internal/randen_slow (= 1.20211102.0)
    - abseil/random/internal/salted_seed_seq (= 1.20211102.0)
    - abseil/random/internal/seed_material (= 1.20211102.0)
    - abseil/random/internal/traits (= 1.20211102.0)
    - abseil/random/internal/uniform_helper (= 1.20211102.0)
    - abseil/random/internal/wide_multiply (= 1.20211102.0)
  - abseil/random/internal/distribution_caller (1.20211102.0):
    - abseil/base/config
    - abseil/base/fast_type_id
    - abseil/utility/utility
  - abseil/random/internal/fast_uniform_bits (1.20211102.0):
    - abseil/base/config
    - abseil/meta/type_traits
  - abseil/random/internal/fastmath (1.20211102.0):
    - abseil/numeric/bits
  - abseil/random/internal/generate_real (1.20211102.0):
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/random/internal/fastmath
    - abseil/random/internal/traits
  - abseil/random/internal/iostream_state_saver (1.20211102.0):
    - abseil/meta/type_traits
    - abseil/numeric/int128
  - abseil/random/internal/mock_helpers (1.20211102.0):
    - abseil/base/fast_type_id
    - abseil/types/optional
  - abseil/random/internal/nanobenchmark (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/random/internal/platform
    - abseil/random/internal/randen_engine
  - abseil/random/internal/nonsecure_base (1.20211102.0):
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/random/internal/pool_urbg
    - abseil/random/internal/salted_seed_seq
    - abseil/random/internal/seed_material
    - abseil/types/optional
    - abseil/types/span
  - abseil/random/internal/pcg_engine (1.20211102.0):
    - abseil/base/config
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/random/internal/fastmath
    - abseil/random/internal/iostream_state_saver
  - abseil/random/internal/platform (1.20211102.0):
    - abseil/base/config
  - abseil/random/internal/pool_urbg (1.20211102.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/raw_logging_internal
    - abseil/random/internal/randen
    - abseil/random/internal/seed_material
    - abseil/random/internal/traits
    - abseil/random/seed_gen_exception
    - abseil/types/span
  - abseil/random/internal/randen (1.20211102.0):
    - abseil/base/raw_logging_internal
    - abseil/random/internal/platform
    - abseil/random/internal/randen_hwaes
    - abseil/random/internal/randen_slow
  - abseil/random/internal/randen_engine (1.20211102.0):
    - abseil/base/endian
    - abseil/meta/type_traits
    - abseil/random/internal/iostream_state_saver
    - abseil/random/internal/randen
  - abseil/random/internal/randen_hwaes (1.20211102.0):
    - abseil/base/config
    - abseil/random/internal/platform
    - abseil/random/internal/randen_hwaes_impl
  - abseil/random/internal/randen_hwaes_impl (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/numeric/int128
    - abseil/random/internal/platform
  - abseil/random/internal/randen_slow (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/numeric/int128
    - abseil/random/internal/platform
  - abseil/random/internal/salted_seed_seq (1.20211102.0):
    - abseil/container/inlined_vector
    - abseil/meta/type_traits
    - abseil/random/internal/seed_material
    - abseil/types/optional
    - abseil/types/span
  - abseil/random/internal/seed_material (1.20211102.0):
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/raw_logging_internal
    - abseil/random/internal/fast_uniform_bits
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/span
  - abseil/random/internal/traits (1.20211102.0):
    - abseil/base/config
  - abseil/random/internal/uniform_helper (1.20211102.0):
    - abseil/base/config
    - abseil/meta/type_traits
    - abseil/random/internal/traits
  - abseil/random/internal/wide_multiply (1.20211102.0):
    - abseil/base/config
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/random/internal/traits
  - abseil/random/random (1.20211102.0):
    - abseil/random/distributions
    - abseil/random/internal/nonsecure_base
    - abseil/random/internal/pcg_engine
    - abseil/random/internal/pool_urbg
    - abseil/random/internal/randen_engine
    - abseil/random/seed_sequences
  - abseil/random/seed_gen_exception (1.20211102.0):
    - abseil/base/config
  - abseil/random/seed_sequences (1.20211102.0):
    - abseil/container/inlined_vector
    - abseil/random/internal/nonsecure_base
    - abseil/random/internal/pool_urbg
    - abseil/random/internal/salted_seed_seq
    - abseil/random/internal/seed_material
    - abseil/random/seed_gen_exception
    - abseil/types/span
  - abseil/status (1.20211102.0):
    - abseil/status/status (= 1.20211102.0)
    - abseil/status/statusor (= 1.20211102.0)
  - abseil/status/status (1.20211102.0):
    - abseil/base/atomic_hook
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/container/inlined_vector
    - abseil/debugging/stacktrace
    - abseil/debugging/symbolize
    - abseil/functional/function_ref
    - abseil/strings/cord
    - abseil/strings/str_format
    - abseil/strings/strings
    - abseil/types/optional
  - abseil/status/statusor (1.20211102.0):
    - abseil/base/base
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/meta/type_traits
    - abseil/status/status
    - abseil/strings/strings
    - abseil/types/variant
    - abseil/utility/utility
  - abseil/strings (1.20211102.0):
    - abseil/strings/cord (= 1.20211102.0)
    - abseil/strings/cord_internal (= 1.20211102.0)
    - abseil/strings/cordz_functions (= 1.20211102.0)
    - abseil/strings/cordz_handle (= 1.20211102.0)
    - abseil/strings/cordz_info (= 1.20211102.0)
    - abseil/strings/cordz_sample_token (= 1.20211102.0)
    - abseil/strings/cordz_statistics (= 1.20211102.0)
    - abseil/strings/cordz_update_scope (= 1.20211102.0)
    - abseil/strings/cordz_update_tracker (= 1.20211102.0)
    - abseil/strings/internal (= 1.20211102.0)
    - abseil/strings/str_format (= 1.20211102.0)
    - abseil/strings/str_format_internal (= 1.20211102.0)
    - abseil/strings/strings (= 1.20211102.0)
  - abseil/strings/cord (1.20211102.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/raw_logging_internal
    - abseil/container/fixed_array
    - abseil/container/inlined_vector
    - abseil/functional/function_ref
    - abseil/meta/type_traits
    - abseil/strings/cord_internal
    - abseil/strings/cordz_functions
    - abseil/strings/cordz_info
    - abseil/strings/cordz_statistics
    - abseil/strings/cordz_update_scope
    - abseil/strings/cordz_update_tracker
    - abseil/strings/internal
    - abseil/strings/str_format
    - abseil/strings/strings
    - abseil/types/optional
  - abseil/strings/cord_internal (1.20211102.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/raw_logging_internal
    - abseil/base/throw_delegate
    - abseil/container/compressed_tuple
    - abseil/container/inlined_vector
    - abseil/container/layout
    - abseil/functional/function_ref
    - abseil/meta/type_traits
    - abseil/strings/strings
    - abseil/types/span
  - abseil/strings/cordz_functions (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/profiling/exponential_biased
  - abseil/strings/cordz_handle (1.20211102.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/raw_logging_internal
    - abseil/synchronization/synchronization
  - abseil/strings/cordz_info (1.20211102.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/container/inlined_vector
    - abseil/debugging/stacktrace
    - abseil/strings/cord_internal
    - abseil/strings/cordz_functions
    - abseil/strings/cordz_handle
    - abseil/strings/cordz_statistics
    - abseil/strings/cordz_update_tracker
    - abseil/synchronization/synchronization
    - abseil/types/span
  - abseil/strings/cordz_sample_token (1.20211102.0):
    - abseil/base/config
    - abseil/strings/cordz_handle
    - abseil/strings/cordz_info
  - abseil/strings/cordz_statistics (1.20211102.0):
    - abseil/base/config
    - abseil/strings/cordz_update_tracker
  - abseil/strings/cordz_update_scope (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/strings/cord_internal
    - abseil/strings/cordz_info
    - abseil/strings/cordz_update_tracker
  - abseil/strings/cordz_update_tracker (1.20211102.0):
    - abseil/base/config
  - abseil/strings/internal (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/raw_logging_internal
    - abseil/meta/type_traits
  - abseil/strings/str_format (1.20211102.0):
    - abseil/strings/str_format_internal
  - abseil/strings/str_format_internal (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/functional/function_ref
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/numeric/representation
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/span
  - abseil/strings/strings (1.20211102.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/raw_logging_internal
    - abseil/base/throw_delegate
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/strings/internal
  - abseil/synchronization (1.20211102.0):
    - abseil/synchronization/graphcycles_internal (= 1.20211102.0)
    - abseil/synchronization/kernel_timeout_internal (= 1.20211102.0)
    - abseil/synchronization/synchronization (= 1.20211102.0)
  - abseil/synchronization/graphcycles_internal (1.20211102.0):
    - abseil/base/base
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/malloc_internal
    - abseil/base/raw_logging_internal
  - abseil/synchronization/kernel_timeout_internal (1.20211102.0):
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/time/time
  - abseil/synchronization/synchronization (1.20211102.0):
    - abseil/base/atomic_hook
    - abseil/base/base
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/malloc_internal
    - abseil/base/raw_logging_internal
    - abseil/debugging/stacktrace
    - abseil/debugging/symbolize
    - abseil/synchronization/graphcycles_internal
    - abseil/synchronization/kernel_timeout_internal
    - abseil/time/time
  - abseil/time (1.20211102.0):
    - abseil/time/internal (= 1.20211102.0)
    - abseil/time/time (= 1.20211102.0)
  - abseil/time/internal (1.20211102.0):
    - abseil/time/internal/cctz (= 1.20211102.0)
  - abseil/time/internal/cctz (1.20211102.0):
    - abseil/time/internal/cctz/civil_time (= 1.20211102.0)
    - abseil/time/internal/cctz/time_zone (= 1.20211102.0)
  - abseil/time/internal/cctz/civil_time (1.20211102.0):
    - abseil/base/config
  - abseil/time/internal/cctz/time_zone (1.20211102.0):
    - abseil/base/config
    - abseil/time/internal/cctz/civil_time
  - abseil/time/time (1.20211102.0):
    - abseil/base/base
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/numeric/int128
    - abseil/strings/strings
    - abseil/time/internal/cctz/civil_time
    - abseil/time/internal/cctz/time_zone
  - abseil/types (1.20211102.0):
    - abseil/types/any (= 1.20211102.0)
    - abseil/types/bad_any_cast (= 1.20211102.0)
    - abseil/types/bad_any_cast_impl (= 1.20211102.0)
    - abseil/types/bad_optional_access (= 1.20211102.0)
    - abseil/types/bad_variant_access (= 1.20211102.0)
    - abseil/types/compare (= 1.20211102.0)
    - abseil/types/optional (= 1.20211102.0)
    - abseil/types/span (= 1.20211102.0)
    - abseil/types/variant (= 1.20211102.0)
  - abseil/types/any (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/fast_type_id
    - abseil/meta/type_traits
    - abseil/types/bad_any_cast
    - abseil/utility/utility
  - abseil/types/bad_any_cast (1.20211102.0):
    - abseil/base/config
    - abseil/types/bad_any_cast_impl
  - abseil/types/bad_any_cast_impl (1.20211102.0):
    - abseil/base/config
    - abseil/base/raw_logging_internal
  - abseil/types/bad_optional_access (1.20211102.0):
    - abseil/base/config
    - abseil/base/raw_logging_internal
  - abseil/types/bad_variant_access (1.20211102.0):
    - abseil/base/config
    - abseil/base/raw_logging_internal
  - abseil/types/compare (1.20211102.0):
    - abseil/base/core_headers
    - abseil/meta/type_traits
  - abseil/types/optional (1.20211102.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/types/bad_optional_access
    - abseil/utility/utility
  - abseil/types/span (1.20211102.0):
    - abseil/algorithm/algorithm
    - abseil/base/core_headers
    - abseil/base/throw_delegate
    - abseil/meta/type_traits
  - abseil/types/variant (1.20211102.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/types/bad_variant_access
    - abseil/utility/utility
  - abseil/utility (1.20211102.0):
    - abseil/utility/utility (= 1.20211102.0)
  - abseil/utility/utility (1.20211102.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/meta/type_traits
  - boost (1.76.0)
  - BoringSSL-GRPC (0.0.24):
    - BoringSSL-GRPC/Implementation (= 0.0.24)
    - BoringSSL-GRPC/Interface (= 0.0.24)
  - BoringSSL-GRPC/Implementation (0.0.24):
    - BoringSSL-GRPC/Interface (= 0.0.24)
  - BoringSSL-GRPC/Interface (0.0.24)
  - DoubleConversion (1.1.6)
  - EXApplication (5.3.1):
    - ExpoModulesCore
  - EXConstants (14.4.2):
    - ExpoModulesCore
  - EXFileSystem (15.4.5):
    - ExpoModulesCore
  - EXFont (11.4.0):
    - ExpoModulesCore
  - EXLocation (16.1.0):
    - ExpoModulesCore
  - Expo (49.0.23):
    - ExpoModulesCore
  - ExpoKeepAwake (12.3.0):
    - ExpoModulesCore
  - ExpoModulesCore (1.5.13):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
    - React-RCTAppDelegate
    - ReactCommon/turbomodule/core
  - EXSplashScreen (0.20.5):
    - ExpoModulesCore
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - FBLazyVector (0.71.8)
  - FBReactNativeSpec (0.71.8):
    - RCT-Folly (= 2021.07.22.00)
    - RCTRequired (= 0.71.8)
    - RCTTypeSafety (= 0.71.8)
    - React-Core (= 0.71.8)
    - React-jsi (= 0.71.8)
    - ReactCommon/turbomodule/core (= 0.71.8)
  - Firebase (10.7.0):
    - Firebase/Core (= 10.7.0)
  - Firebase/Auth (10.7.0):
    - Firebase/CoreOnly
    - FirebaseAuth (~> 10.7.0)
  - Firebase/Core (10.7.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 10.7.0)
  - Firebase/CoreOnly (10.7.0):
    - FirebaseCore (= 10.7.0)
  - Firebase/Firestore (10.7.0):
    - Firebase/CoreOnly
    - FirebaseFirestore (~> 10.7.0)
  - Firebase/Messaging (10.7.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 10.7.0)
  - FirebaseAnalytics (10.7.0):
    - FirebaseAnalytics/AdIdSupport (= 10.7.0)
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/MethodSwizzler (~> 7.8)
    - GoogleUtilities/Network (~> 7.8)
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - FirebaseAnalytics/AdIdSupport (10.7.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleAppMeasurement (= 10.7.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/MethodSwizzler (~> 7.8)
    - GoogleUtilities/Network (~> 7.8)
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - FirebaseAuth (10.7.0):
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GTMSessionFetcher/Core (< 4.0, >= 2.1)
  - FirebaseCore (10.7.0):
    - FirebaseCoreInternal (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Logger (~> 7.8)
  - FirebaseCoreExtension (10.7.0):
    - FirebaseCore (~> 10.0)
  - FirebaseCoreInternal (10.7.0):
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseFirestore (10.7.0):
    - abseil/algorithm (~> 1.20211102.0)
    - abseil/base (~> 1.20211102.0)
    - abseil/container/flat_hash_map (~> 1.20211102.0)
    - abseil/memory (~> 1.20211102.0)
    - abseil/meta (~> 1.20211102.0)
    - abseil/strings/strings (~> 1.20211102.0)
    - abseil/time (~> 1.20211102.0)
    - abseil/types (~> 1.20211102.0)
    - FirebaseCore (~> 10.0)
    - "gRPC-C++ (~> 1.44.0)"
    - leveldb-library (~> 1.22)
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - FirebaseInstallations (10.7.0):
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - PromisesObjC (~> 2.1)
  - FirebaseMessaging (10.7.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Reachability (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - fmt (6.2.1)
  - glog (0.3.5)
  - GoogleAppMeasurement (10.7.0):
    - GoogleAppMeasurement/AdIdSupport (= 10.7.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/MethodSwizzler (~> 7.8)
    - GoogleUtilities/Network (~> 7.8)
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleAppMeasurement/AdIdSupport (10.7.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 10.7.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/MethodSwizzler (~> 7.8)
    - GoogleUtilities/Network (~> 7.8)
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (10.7.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/MethodSwizzler (~> 7.8)
    - GoogleUtilities/Network (~> 7.8)
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleDataTransport (9.4.1):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/AppDelegateSwizzler (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (7.13.3):
    - GoogleUtilities/Privacy
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (7.13.3):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.13.3)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (7.13.3)
  - GoogleUtilities/Reachability (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - "gRPC-C++ (1.44.0)":
    - "gRPC-C++/Implementation (= 1.44.0)"
    - "gRPC-C++/Interface (= 1.44.0)"
  - "gRPC-C++/Implementation (1.44.0)":
    - abseil/base/base (= 1.20211102.0)
    - abseil/base/core_headers (= 1.20211102.0)
    - abseil/container/flat_hash_map (= 1.20211102.0)
    - abseil/container/inlined_vector (= 1.20211102.0)
    - abseil/functional/bind_front (= 1.20211102.0)
    - abseil/hash/hash (= 1.20211102.0)
    - abseil/memory/memory (= 1.20211102.0)
    - abseil/random/random (= 1.20211102.0)
    - abseil/status/status (= 1.20211102.0)
    - abseil/status/statusor (= 1.20211102.0)
    - abseil/strings/cord (= 1.20211102.0)
    - abseil/strings/str_format (= 1.20211102.0)
    - abseil/strings/strings (= 1.20211102.0)
    - abseil/synchronization/synchronization (= 1.20211102.0)
    - abseil/time/time (= 1.20211102.0)
    - abseil/types/optional (= 1.20211102.0)
    - abseil/types/variant (= 1.20211102.0)
    - abseil/utility/utility (= 1.20211102.0)
    - "gRPC-C++/Interface (= 1.44.0)"
    - gRPC-Core (= 1.44.0)
  - "gRPC-C++/Interface (1.44.0)"
  - gRPC-Core (1.44.0):
    - gRPC-Core/Implementation (= 1.44.0)
    - gRPC-Core/Interface (= 1.44.0)
  - gRPC-Core/Implementation (1.44.0):
    - abseil/base/base (= 1.20211102.0)
    - abseil/base/core_headers (= 1.20211102.0)
    - abseil/container/flat_hash_map (= 1.20211102.0)
    - abseil/container/inlined_vector (= 1.20211102.0)
    - abseil/functional/bind_front (= 1.20211102.0)
    - abseil/hash/hash (= 1.20211102.0)
    - abseil/memory/memory (= 1.20211102.0)
    - abseil/random/random (= 1.20211102.0)
    - abseil/status/status (= 1.20211102.0)
    - abseil/status/statusor (= 1.20211102.0)
    - abseil/strings/cord (= 1.20211102.0)
    - abseil/strings/str_format (= 1.20211102.0)
    - abseil/strings/strings (= 1.20211102.0)
    - abseil/synchronization/synchronization (= 1.20211102.0)
    - abseil/time/time (= 1.20211102.0)
    - abseil/types/optional (= 1.20211102.0)
    - abseil/types/variant (= 1.20211102.0)
    - abseil/utility/utility (= 1.20211102.0)
    - BoringSSL-GRPC (= 0.0.24)
    - gRPC-Core/Interface (= 1.44.0)
    - Libuv-gRPC (= 0.0.10)
  - gRPC-Core/Interface (1.44.0)
  - GTMSessionFetcher/Core (3.5.0)
  - hermes-engine (0.71.8):
    - hermes-engine/Pre-built (= 0.71.8)
  - hermes-engine/Pre-built (0.71.8)
  - leveldb-library (1.22.6)
  - libevent (2.1.12)
  - Libuv-gRPC (0.0.10):
    - Libuv-gRPC/Implementation (= 0.0.10)
    - Libuv-gRPC/Interface (= 0.0.10)
  - Libuv-gRPC/Implementation (0.0.10):
    - Libuv-gRPC/Interface (= 0.0.10)
  - Libuv-gRPC/Interface (0.0.10)
  - nanopb (2.30909.1):
    - nanopb/decode (= 2.30909.1)
    - nanopb/encode (= 2.30909.1)
  - nanopb/decode (2.30909.1)
  - nanopb/encode (2.30909.1)
  - PromisesObjC (2.4.0)
  - RCT-Folly (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Default (= 2021.07.22.00)
  - RCT-Folly/Default (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
  - RCT-Folly/Futures (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - libevent
  - RCTRequired (0.71.8)
  - RCTTypeSafety (0.71.8):
    - FBLazyVector (= 0.71.8)
    - RCTRequired (= 0.71.8)
    - React-Core (= 0.71.8)
  - React (0.71.8):
    - React-Core (= 0.71.8)
    - React-Core/DevSupport (= 0.71.8)
    - React-Core/RCTWebSocket (= 0.71.8)
    - React-RCTActionSheet (= 0.71.8)
    - React-RCTAnimation (= 0.71.8)
    - React-RCTBlob (= 0.71.8)
    - React-RCTImage (= 0.71.8)
    - React-RCTLinking (= 0.71.8)
    - React-RCTNetwork (= 0.71.8)
    - React-RCTSettings (= 0.71.8)
    - React-RCTText (= 0.71.8)
    - React-RCTVibration (= 0.71.8)
  - React-callinvoker (0.71.8)
  - React-Codegen (0.71.8):
    - FBReactNativeSpec
    - hermes-engine
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-jsi
    - React-jsiexecutor
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-Core (0.71.8):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.71.8)
    - React-cxxreact (= 0.71.8)
    - React-hermes
    - React-jsi (= 0.71.8)
    - React-jsiexecutor (= 0.71.8)
    - React-perflogger (= 0.71.8)
    - Yoga
  - React-Core/CoreModulesHeaders (0.71.8):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.8)
    - React-hermes
    - React-jsi (= 0.71.8)
    - React-jsiexecutor (= 0.71.8)
    - React-perflogger (= 0.71.8)
    - Yoga
  - React-Core/Default (0.71.8):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-cxxreact (= 0.71.8)
    - React-hermes
    - React-jsi (= 0.71.8)
    - React-jsiexecutor (= 0.71.8)
    - React-perflogger (= 0.71.8)
    - Yoga
  - React-Core/DevSupport (0.71.8):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.71.8)
    - React-Core/RCTWebSocket (= 0.71.8)
    - React-cxxreact (= 0.71.8)
    - React-hermes
    - React-jsi (= 0.71.8)
    - React-jsiexecutor (= 0.71.8)
    - React-jsinspector (= 0.71.8)
    - React-perflogger (= 0.71.8)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.71.8):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.8)
    - React-hermes
    - React-jsi (= 0.71.8)
    - React-jsiexecutor (= 0.71.8)
    - React-perflogger (= 0.71.8)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.71.8):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.8)
    - React-hermes
    - React-jsi (= 0.71.8)
    - React-jsiexecutor (= 0.71.8)
    - React-perflogger (= 0.71.8)
    - Yoga
  - React-Core/RCTBlobHeaders (0.71.8):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.8)
    - React-hermes
    - React-jsi (= 0.71.8)
    - React-jsiexecutor (= 0.71.8)
    - React-perflogger (= 0.71.8)
    - Yoga
  - React-Core/RCTImageHeaders (0.71.8):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.8)
    - React-hermes
    - React-jsi (= 0.71.8)
    - React-jsiexecutor (= 0.71.8)
    - React-perflogger (= 0.71.8)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.71.8):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.8)
    - React-hermes
    - React-jsi (= 0.71.8)
    - React-jsiexecutor (= 0.71.8)
    - React-perflogger (= 0.71.8)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.71.8):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.8)
    - React-hermes
    - React-jsi (= 0.71.8)
    - React-jsiexecutor (= 0.71.8)
    - React-perflogger (= 0.71.8)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.71.8):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.8)
    - React-hermes
    - React-jsi (= 0.71.8)
    - React-jsiexecutor (= 0.71.8)
    - React-perflogger (= 0.71.8)
    - Yoga
  - React-Core/RCTTextHeaders (0.71.8):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.8)
    - React-hermes
    - React-jsi (= 0.71.8)
    - React-jsiexecutor (= 0.71.8)
    - React-perflogger (= 0.71.8)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.71.8):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.8)
    - React-hermes
    - React-jsi (= 0.71.8)
    - React-jsiexecutor (= 0.71.8)
    - React-perflogger (= 0.71.8)
    - Yoga
  - React-Core/RCTWebSocket (0.71.8):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.71.8)
    - React-cxxreact (= 0.71.8)
    - React-hermes
    - React-jsi (= 0.71.8)
    - React-jsiexecutor (= 0.71.8)
    - React-perflogger (= 0.71.8)
    - Yoga
  - React-CoreModules (0.71.8):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.71.8)
    - React-Codegen (= 0.71.8)
    - React-Core/CoreModulesHeaders (= 0.71.8)
    - React-jsi (= 0.71.8)
    - React-RCTBlob
    - React-RCTImage (= 0.71.8)
    - ReactCommon/turbomodule/core (= 0.71.8)
  - React-cxxreact (0.71.8):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.71.8)
    - React-jsi (= 0.71.8)
    - React-jsinspector (= 0.71.8)
    - React-logger (= 0.71.8)
    - React-perflogger (= 0.71.8)
    - React-runtimeexecutor (= 0.71.8)
  - React-hermes (0.71.8):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - RCT-Folly/Futures (= 2021.07.22.00)
    - React-cxxreact (= 0.71.8)
    - React-jsi
    - React-jsiexecutor (= 0.71.8)
    - React-jsinspector (= 0.71.8)
    - React-perflogger (= 0.71.8)
  - React-jsi (0.71.8):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
  - React-jsiexecutor (0.71.8):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-cxxreact (= 0.71.8)
    - React-jsi (= 0.71.8)
    - React-perflogger (= 0.71.8)
  - React-jsinspector (0.71.8)
  - React-logger (0.71.8):
    - glog
  - react-native-maps (1.7.1):
    - React-Core
  - react-native-netinfo (11.4.1):
    - React-Core
  - react-native-safe-area-context (5.1.0):
    - React-Core
  - React-perflogger (0.71.8)
  - React-RCTActionSheet (0.71.8):
    - React-Core/RCTActionSheetHeaders (= 0.71.8)
  - React-RCTAnimation (0.71.8):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.71.8)
    - React-Codegen (= 0.71.8)
    - React-Core/RCTAnimationHeaders (= 0.71.8)
    - React-jsi (= 0.71.8)
    - ReactCommon/turbomodule/core (= 0.71.8)
  - React-RCTAppDelegate (0.71.8):
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - ReactCommon/turbomodule/core
  - React-RCTBlob (0.71.8):
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Codegen (= 0.71.8)
    - React-Core/RCTBlobHeaders (= 0.71.8)
    - React-Core/RCTWebSocket (= 0.71.8)
    - React-jsi (= 0.71.8)
    - React-RCTNetwork (= 0.71.8)
    - ReactCommon/turbomodule/core (= 0.71.8)
  - React-RCTImage (0.71.8):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.71.8)
    - React-Codegen (= 0.71.8)
    - React-Core/RCTImageHeaders (= 0.71.8)
    - React-jsi (= 0.71.8)
    - React-RCTNetwork (= 0.71.8)
    - ReactCommon/turbomodule/core (= 0.71.8)
  - React-RCTLinking (0.71.8):
    - React-Codegen (= 0.71.8)
    - React-Core/RCTLinkingHeaders (= 0.71.8)
    - React-jsi (= 0.71.8)
    - ReactCommon/turbomodule/core (= 0.71.8)
  - React-RCTNetwork (0.71.8):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.71.8)
    - React-Codegen (= 0.71.8)
    - React-Core/RCTNetworkHeaders (= 0.71.8)
    - React-jsi (= 0.71.8)
    - ReactCommon/turbomodule/core (= 0.71.8)
  - React-RCTSettings (0.71.8):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.71.8)
    - React-Codegen (= 0.71.8)
    - React-Core/RCTSettingsHeaders (= 0.71.8)
    - React-jsi (= 0.71.8)
    - ReactCommon/turbomodule/core (= 0.71.8)
  - React-RCTText (0.71.8):
    - React-Core/RCTTextHeaders (= 0.71.8)
  - React-RCTVibration (0.71.8):
    - RCT-Folly (= 2021.07.22.00)
    - React-Codegen (= 0.71.8)
    - React-Core/RCTVibrationHeaders (= 0.71.8)
    - React-jsi (= 0.71.8)
    - ReactCommon/turbomodule/core (= 0.71.8)
  - React-runtimeexecutor (0.71.8):
    - React-jsi (= 0.71.8)
  - ReactCommon/turbomodule/bridging (0.71.8):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.71.8)
    - React-Core (= 0.71.8)
    - React-cxxreact (= 0.71.8)
    - React-jsi (= 0.71.8)
    - React-logger (= 0.71.8)
    - React-perflogger (= 0.71.8)
  - ReactCommon/turbomodule/core (0.71.8):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.71.8)
    - React-Core (= 0.71.8)
    - React-cxxreact (= 0.71.8)
    - React-jsi (= 0.71.8)
    - React-logger (= 0.71.8)
    - React-perflogger (= 0.71.8)
  - RNCAsyncStorage (1.18.2):
    - React-Core
  - RNCMaskedView (0.2.9):
    - React-Core
  - RNCPushNotificationIOS (1.11.0):
    - React-Core
  - RNFBApp (17.4.2):
    - Firebase/CoreOnly (= 10.7.0)
    - React-Core
  - RNFBAuth (17.4.2):
    - Firebase/Auth (= 10.7.0)
    - React-Core
    - RNFBApp
  - RNFBFirestore (17.4.2):
    - Firebase/Firestore (= 10.7.0)
    - nanopb (< 2.30910.0, >= 2.30908.0)
    - React-Core
    - RNFBApp
  - RNFBMessaging (17.4.2):
    - Firebase/Messaging (= 10.7.0)
    - FirebaseCoreExtension (= 10.7.0)
    - React-Core
    - RNFBApp
  - RNGestureHandler (2.12.1):
    - React-Core
  - RNReanimated (2.17.0):
    - DoubleConversion
    - FBLazyVector
    - FBReactNativeSpec
    - glog
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-callinvoker
    - React-Core
    - React-Core/DevSupport
    - React-Core/RCTWebSocket
    - React-CoreModules
    - React-cxxreact
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-RCTActionSheet
    - React-RCTAnimation
    - React-RCTBlob
    - React-RCTImage
    - React-RCTLinking
    - React-RCTNetwork
    - React-RCTSettings
    - React-RCTText
    - ReactCommon/turbomodule/core
    - Yoga
  - RNScreens (3.22.1):
    - React-Core
    - React-RCTImage
  - RNVectorIcons (10.2.0):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - Yoga (1.14.0)

DEPENDENCIES:
  - abseil (= 1.20211102.0)
  - boost (from `../node_modules/react-native/third-party-podspecs/boost.podspec`)
  - BoringSSL-GRPC (= 0.0.24)
  - DoubleConversion (from `../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - EXApplication (from `../node_modules/expo-application/ios`)
  - EXConstants (from `../node_modules/expo-constants/ios`)
  - EXFileSystem (from `../node_modules/expo-file-system/ios`)
  - EXFont (from `../node_modules/expo-font/ios`)
  - EXLocation (from `../node_modules/expo-location/ios`)
  - Expo (from `../node_modules/expo`)
  - ExpoKeepAwake (from `../node_modules/expo-keep-awake/ios`)
  - ExpoModulesCore (from `../node_modules/expo-modules-core`)
  - EXSplashScreen (from `../node_modules/expo-splash-screen/ios`)
  - FBLazyVector (from `../node_modules/react-native/Libraries/FBLazyVector`)
  - FBReactNativeSpec (from `../node_modules/react-native/React/FBReactNativeSpec`)
  - Firebase (= 10.7.0)
  - Firebase/Auth (= 10.7.0)
  - Firebase/Firestore (= 10.7.0)
  - Firebase/Messaging (= 10.7.0)
  - FirebaseCore (= 10.7.0)
  - FirebaseCoreInternal (= 10.7.0)
  - FirebaseInstallations (= 10.7.0)
  - glog (from `../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - "gRPC-C++ (= 1.44.0)"
  - gRPC-Core (= 1.44.0)
  - hermes-engine (from `../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec`)
  - libevent (~> 2.1.12)
  - RCT-Folly (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTRequired (from `../node_modules/react-native/Libraries/RCTRequired`)
  - RCTTypeSafety (from `../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../node_modules/react-native/`)
  - React-callinvoker (from `../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Codegen (from `build/generated/ios`)
  - React-Core (from `../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native/`)
  - React-CoreModules (from `../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native/ReactCommon/cxxreact`)
  - React-hermes (from `../node_modules/react-native/ReactCommon/hermes`)
  - React-jsi (from `../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native/ReactCommon/jsinspector`)
  - React-logger (from `../node_modules/react-native/ReactCommon/logger`)
  - react-native-maps (from `../node_modules/react-native-maps`)
  - "react-native-netinfo (from `../node_modules/@react-native-community/netinfo`)"
  - react-native-safe-area-context (from `../node_modules/react-native-safe-area-context`)
  - React-perflogger (from `../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-RCTActionSheet (from `../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTAppDelegate (from `../node_modules/react-native/Libraries/AppDelegate`)
  - React-RCTBlob (from `../node_modules/react-native/Libraries/Blob`)
  - React-RCTImage (from `../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native/Libraries/Network`)
  - React-RCTSettings (from `../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native/Libraries/Vibration`)
  - React-runtimeexecutor (from `../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native/ReactCommon`)
  - "RNCAsyncStorage (from `../node_modules/@react-native-async-storage/async-storage`)"
  - "RNCMaskedView (from `../node_modules/@react-native-masked-view/masked-view`)"
  - "RNCPushNotificationIOS (from `../node_modules/@react-native-community/push-notification-ios`)"
  - "RNFBApp (from `../node_modules/@react-native-firebase/app`)"
  - "RNFBAuth (from `../node_modules/@react-native-firebase/auth`)"
  - "RNFBFirestore (from `../node_modules/@react-native-firebase/firestore`)"
  - "RNFBMessaging (from `../node_modules/@react-native-firebase/messaging`)"
  - RNGestureHandler (from `../node_modules/react-native-gesture-handler`)
  - RNReanimated (from `../node_modules/react-native-reanimated`)
  - RNScreens (from `../node_modules/react-native-screens`)
  - RNVectorIcons (from `../node_modules/react-native-vector-icons`)
  - Yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - abseil
    - BoringSSL-GRPC
    - Firebase
    - FirebaseAnalytics
    - FirebaseAuth
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseFirestore
    - FirebaseInstallations
    - FirebaseMessaging
    - fmt
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleUtilities
    - "gRPC-C++"
    - gRPC-Core
    - GTMSessionFetcher
    - leveldb-library
    - libevent
    - Libuv-gRPC
    - nanopb
    - PromisesObjC

EXTERNAL SOURCES:
  boost:
    :podspec: "../node_modules/react-native/third-party-podspecs/boost.podspec"
  DoubleConversion:
    :podspec: "../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  EXApplication:
    :path: "../node_modules/expo-application/ios"
  EXConstants:
    :path: "../node_modules/expo-constants/ios"
  EXFileSystem:
    :path: "../node_modules/expo-file-system/ios"
  EXFont:
    :path: "../node_modules/expo-font/ios"
  EXLocation:
    :path: "../node_modules/expo-location/ios"
  Expo:
    :path: "../node_modules/expo"
  ExpoKeepAwake:
    :path: "../node_modules/expo-keep-awake/ios"
  ExpoModulesCore:
    :path: "../node_modules/expo-modules-core"
  EXSplashScreen:
    :path: "../node_modules/expo-splash-screen/ios"
  FBLazyVector:
    :path: "../node_modules/react-native/Libraries/FBLazyVector"
  FBReactNativeSpec:
    :path: "../node_modules/react-native/React/FBReactNativeSpec"
  glog:
    :podspec: "../node_modules/react-native/third-party-podspecs/glog.podspec"
  hermes-engine:
    :podspec: "../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec"
  RCT-Folly:
    :podspec: "../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTRequired:
    :path: "../node_modules/react-native/Libraries/RCTRequired"
  RCTTypeSafety:
    :path: "../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../node_modules/react-native/"
  React-callinvoker:
    :path: "../node_modules/react-native/ReactCommon/callinvoker"
  React-Codegen:
    :path: build/generated/ios
  React-Core:
    :path: "../node_modules/react-native/"
  React-CoreModules:
    :path: "../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native/ReactCommon/cxxreact"
  React-hermes:
    :path: "../node_modules/react-native/ReactCommon/hermes"
  React-jsi:
    :path: "../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native/ReactCommon/jsinspector"
  React-logger:
    :path: "../node_modules/react-native/ReactCommon/logger"
  react-native-maps:
    :path: "../node_modules/react-native-maps"
  react-native-netinfo:
    :path: "../node_modules/@react-native-community/netinfo"
  react-native-safe-area-context:
    :path: "../node_modules/react-native-safe-area-context"
  React-perflogger:
    :path: "../node_modules/react-native/ReactCommon/reactperflogger"
  React-RCTActionSheet:
    :path: "../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTAppDelegate:
    :path: "../node_modules/react-native/Libraries/AppDelegate"
  React-RCTBlob:
    :path: "../node_modules/react-native/Libraries/Blob"
  React-RCTImage:
    :path: "../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native/Libraries/Network"
  React-RCTSettings:
    :path: "../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native/Libraries/Vibration"
  React-runtimeexecutor:
    :path: "../node_modules/react-native/ReactCommon/runtimeexecutor"
  ReactCommon:
    :path: "../node_modules/react-native/ReactCommon"
  RNCAsyncStorage:
    :path: "../node_modules/@react-native-async-storage/async-storage"
  RNCMaskedView:
    :path: "../node_modules/@react-native-masked-view/masked-view"
  RNCPushNotificationIOS:
    :path: "../node_modules/@react-native-community/push-notification-ios"
  RNFBApp:
    :path: "../node_modules/@react-native-firebase/app"
  RNFBAuth:
    :path: "../node_modules/@react-native-firebase/auth"
  RNFBFirestore:
    :path: "../node_modules/@react-native-firebase/firestore"
  RNFBMessaging:
    :path: "../node_modules/@react-native-firebase/messaging"
  RNGestureHandler:
    :path: "../node_modules/react-native-gesture-handler"
  RNReanimated:
    :path: "../node_modules/react-native-reanimated"
  RNScreens:
    :path: "../node_modules/react-native-screens"
  RNVectorIcons:
    :path: "../node_modules/react-native-vector-icons"
  Yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  abseil: ebe5b5529fb05d93a8bdb7951607be08b7fa71bc
  boost: 7dcd2de282d72e344012f7d6564d024930a6a440
  BoringSSL-GRPC: 3175b25143e648463a56daeaaa499c6cb86dad33
  DoubleConversion: 5189b271737e1565bdce30deb4a08d647e3f5f54
  EXApplication: 042aa2e3f05258a16962ea1a9914bf288db9c9a1
  EXConstants: ce5bbea779da8031ac818c36bea41b10e14d04e1
  EXFileSystem: f8b838a880254de42a5a7da20ed5ce12e2697c1b
  EXFont: 738c44c390953ebcbab075a4848bfbef025fd9ee
  EXLocation: a425ace19c0445e7ea9702982fc6b9a402791510
  Expo: ba9abdf444dc6d2c05a82c4c1b51a2400beb6167
  ExpoKeepAwake: be4cbd52d9b177cde0fd66daa1913afa3161fc1d
  ExpoModulesCore: f0581cd745335dd7a68117f6fedd22b98e2c73f0
  EXSplashScreen: c0e7f2d4a640f3b875808ed0b88575538daf6d82
  FBLazyVector: f637f31eacba90d4fdeff3fa41608b8f361c173b
  FBReactNativeSpec: 0d9a4f4de7ab614c49e98c00aedfd3bfbda33d59
  Firebase: 0219acf760880eeec8ce479895bd7767466d9f81
  FirebaseAnalytics: f8133442ee6f8512e28ff19e62ce15398bfaeace
  FirebaseAuth: dd64c01631df724b09f33e584625775c52f7d71f
  FirebaseCore: e317665b9d744727a97e623edbbed009320afdd7
  FirebaseCoreExtension: f17247ba8c61e4d3c8d136b5e2de3cb4ac6a85b6
  FirebaseCoreInternal: 8845798510aae74703467480f71ac613788d0696
  FirebaseFirestore: 3963a6edd1c84b4748dab3e2c62624a29d03eca1
  FirebaseInstallations: 59c0e4c7a816a0f76710d83f77e5369b3e45eb96
  FirebaseMessaging: ac9062bcc35ed56e15a0241d8fd317022499baf8
  fmt: ff9d55029c625d3757ed641535fd4a75fedc7ce9
  glog: 04b94705f318337d7ead9e6d17c019bd9b1f6b1b
  GoogleAppMeasurement: fe17c92a32207dd5cdd4e8d742767f2da74857f6
  GoogleDataTransport: 6c09b596d841063d76d4288cc2d2f42cc36e1e2a
  GoogleUtilities: ea963c370a38a8069cc5f7ba4ca849a60b6d7d15
  "gRPC-C++": 9675f953ace2b3de7c506039d77be1f2e77a8db2
  gRPC-Core: 943e491cb0d45598b0b0eb9e910c88080369290b
  GTMSessionFetcher: 5aea5ba6bd522a239e236100971f10cb71b96ab6
  hermes-engine: 47986d26692ae75ee7a17ab049caee8864f855de
  leveldb-library: cc8b8f8e013647a295ad3f8cd2ddf49a6f19be19
  libevent: 4049cae6c81cdb3654a443be001fb9bdceff7913
  Libuv-gRPC: 55e51798e14ef436ad9bc45d12d43b77b49df378
  nanopb: d4d75c12cd1316f4a64e3c6963f879ecd4b5e0d5
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  RCT-Folly: 424b8c9a7a0b9ab2886ffe9c3b041ef628fd4fb1
  RCTRequired: 8af6a32dfc2b65ec82193c2dee6e1011ff22ac2a
  RCTTypeSafety: bee9dd161c175896c680d47ef1d9eaacf2b587f4
  React: d850475db9ba8006a8b875d79e1e0d6ac8a0f8b6
  React-callinvoker: 6a0c75475ddc17c9ed54e4ff0478074a18fd7ab5
  React-Codegen: 786571642e87add634e7f4d299c85314ec6cc158
  React-Core: 1adfab153f59e4f56e09b97a153089f466d7b8aa
  React-CoreModules: 958d236715415d4ccdd5fa35c516cf0356637393
  React-cxxreact: 2e7a6283807ce8755c3d501735acd400bec3b5cd
  React-hermes: 8102c3112ba32207c3052619be8cfae14bf99d84
  React-jsi: dd29264f041a587e91f994e4be97e86c127742b2
  React-jsiexecutor: 747911ab5921641b4ed7e4900065896597142125
  React-jsinspector: c712f9e3bb9ba4122d6b82b4f906448b8a281580
  React-logger: 342f358b8decfbf8f272367f4eacf4b6154061be
  react-native-maps: 667f9b975549c6fa9b1631bf859440f68ebd3b8f
  react-native-netinfo: f0a9899081c185db1de5bb2fdc1c88c202a059ac
  react-native-safe-area-context: 04803a01f39f31cc6605a5531280b477b48f8a88
  React-perflogger: d21f182895de9d1b077f8a3cd00011095c8c9100
  React-RCTActionSheet: 0151f83ef92d2a7139bba7dfdbc8066632a6d47b
  React-RCTAnimation: 5ec9c0705bb2297549c120fe6473aa3e4a01e215
  React-RCTAppDelegate: 9895fd1b6d1176d88c4b10ddc169b2e1300c91f0
  React-RCTBlob: f3634eb45b6e7480037655e1ca93d1136ac984dd
  React-RCTImage: 3c12cb32dec49549ae62ed6cba4018db43841ffc
  React-RCTLinking: 310e930ee335ef25481b4a173d9edb64b77895f9
  React-RCTNetwork: b6837841fe88303b0c04c1e3c01992b30f1f5498
  React-RCTSettings: 600d91fe25fa7c16b0ff891304082440f2904b89
  React-RCTText: a0a19f749088280c6def5397ed6211b811e7eef3
  React-RCTVibration: 43ffd976a25f6057a7cf95ea3648ba4e00287f89
  React-runtimeexecutor: 7c51ae9d4b3e9608a2366e39ccaa606aa551b9ed
  ReactCommon: 9957c7530467adbdf4a4a1476d636bd00b2e4dd8
  RNCAsyncStorage: ddc4ee162bfd41b0d2c68bf2d95acd81dd7f1f93
  RNCMaskedView: 949696f25ec596bfc697fc88e6f95cf0c79669b6
  RNCPushNotificationIOS: 64218f3c776c03d7408284a819b2abfda1834bc8
  RNFBApp: 55dbfc574e5ae414f2984394934f79f87eb4b050
  RNFBAuth: d30acc0f5f169bc28c7fcbf1b45cd061cc647590
  RNFBFirestore: c505e6f96e70fb029ae1961c73965fd13ce92db5
  RNFBMessaging: d365361650cc697294227e1280923f35a31d2501
  RNGestureHandler: c0d04458598fcb26052494ae23dda8f8f5162b13
  RNReanimated: f186e85d9f28c9383d05ca39e11dd194f59093ec
  RNScreens: 50ffe2fa2342eabb2d0afbe19f7c1af286bc7fb3
  RNVectorIcons: 084d874504f21a5452744e400537eb96b45060b6
  Yoga: 065f0b74dba4832d6e328238de46eb72c5de9556

PODFILE CHECKSUM: 425601999082ffc486e9c7fdf0abc44f514a02b3

COCOAPODS: 1.16.2
