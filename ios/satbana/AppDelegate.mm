#import "AppDelegate.h"
#import <Firebase.h>
#import <UserNotifications/UserNotifications.h>
#import <RNCPushNotificationIOS.h>
#import <React/RCTBundleURLProvider.h>
#import <RNGestureHandler/RNGestureHandlerManager.h>

@interface AppDelegate () <UNUserNotificationCenterDelegate>
@end

@implementation AppDelegate

- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions
{
  // Set module name first
  self.moduleName = @"com.company.satbana";
  self.initialProps = @{};

  // Initialize Firebase before anything else
  if ([FIRApp defaultApp] == nil) {
    NSString *filePath = [[NSBundle mainBundle] pathForResource:@"GoogleService-Info" ofType:@"plist"];
    FIROptions *options = [[FIROptions alloc] initWithContentsOfFile:filePath];
    [FIRApp configureWithOptions:options];
  }
  
  // Initialize UNUserNotificationCenter with proper error handling
  UNUserNotificationCenter *center = [UNUserNotificationCenter currentNotificationCenter];
  center.delegate = self;

  [center requestAuthorizationWithOptions:(UNAuthorizationOptionAlert |
                                         UNAuthorizationOptionSound |
                                         UNAuthorizationOptionBadge)
                      completionHandler:^(BOOL granted, NSError * _Nullable error) {
    if (error) {
      NSLog(@"Error requesting notification permission: %@", error);
    } else if (granted) {
      NSLog(@"Notification permission granted");
      dispatch_async(dispatch_get_main_queue(), ^{
        [application registerForRemoteNotifications];
        
        // Log the APNs environment
        NSString *environment = [[[[NSBundle mainBundle] bundleURL] pathExtension] isEqualToString:@"app"] ? @"Development" : @"Production";
        NSLog(@"APNs Environment: %@", environment);
      });
    } else {
      NSLog(@"Notification permission denied");
    }
  }];

  return [super application:application didFinishLaunchingWithOptions:launchOptions];
}

// Add this method for React Native bridge
- (NSURL *)sourceURLForBridge:(RCTBridge *)bridge
{
#if DEBUG
  return [[RCTBundleURLProvider sharedSettings] jsBundleURLForBundleRoot:@"index"];
#else
  return [[NSBundle mainBundle] URLForResource:@"main" withExtension:@"jsbundle"];
#endif
}

// Required for the register event.
- (void)application:(UIApplication *)application didRegisterForRemoteNotificationsWithDeviceToken:(NSData *)deviceToken
{
  const unsigned char *tokenBytes = (const unsigned char *)[deviceToken bytes];
  NSMutableString *hexToken = [NSMutableString string];
  
  for (NSInteger i = 0; i < deviceToken.length; i++) {
    [hexToken appendFormat:@"%02x", tokenBytes[i]];
  }
  
  NSLog(@"APNs device token: %@", hexToken);
  [RNCPushNotificationIOS didRegisterForRemoteNotificationsWithDeviceToken:deviceToken];
}

// Required for the notification event.
- (void)application:(UIApplication *)application didReceiveRemoteNotification:(NSDictionary *)userInfo
fetchCompletionHandler:(void (^)(UIBackgroundFetchResult))completionHandler
{
  [RNCPushNotificationIOS didReceiveRemoteNotification:userInfo fetchCompletionHandler:completionHandler];
}

// Required for the registrationError event.
- (void)application:(UIApplication *)application didFailToRegisterForRemoteNotificationsWithError:(NSError *)error
{
  NSLog(@"Failed to register for remote notifications: %@", error);
  [RNCPushNotificationIOS didFailToRegisterForRemoteNotificationsWithError:error];
}

// Required for localNotification event
- (void)userNotificationCenter:(UNUserNotificationCenter *)center
didReceiveNotificationResponse:(UNNotificationResponse *)response
         withCompletionHandler:(void (^)(void))completionHandler
{
  [RNCPushNotificationIOS didReceiveNotificationResponse:response];
  completionHandler();
}

// Called when a notification is delivered to a foreground app.
-(void)userNotificationCenter:(UNUserNotificationCenter *)center
      willPresentNotification:(UNNotification *)notification
        withCompletionHandler:(void (^)(UNNotificationPresentationOptions options))completionHandler
{
  NSDictionary *userInfo = notification.request.content.userInfo;
  [RNCPushNotificationIOS didReceiveRemoteNotification:userInfo];
  
  // Set base options
  UNNotificationPresentationOptions options = 
    UNNotificationPresentationOptionBadge | 
    UNNotificationPresentationOptionSound;
  
  // Add banner/alert based on iOS version
  if (@available(iOS 14.0, *)) {
    options |= UNNotificationPresentationOptionBanner | 
               UNNotificationPresentationOptionList;
  } else {
    options |= UNNotificationPresentationOptionAlert;
  }
  
  #if TARGET_IPHONE_SIMULATOR
    NSLog(@"Presenting notification in Simulator");
    // Force all presentation options in simulator
    options = UNNotificationPresentationOptionBadge | 
              UNNotificationPresentationOptionSound | 
              UNNotificationPresentationOptionBanner | 
              UNNotificationPresentationOptionList;
  #else
    NSLog(@"Presenting notification in Device");
  #endif
  
  completionHandler(options);
}

// Add helper method for token formatting
- (NSString *)stringWithDeviceToken:(NSData *)deviceToken {
  const char *bytes = (const char*)[deviceToken bytes];
  NSMutableString *token = [NSMutableString string];
  
  for (NSUInteger i = 0; i < [deviceToken length]; i++) {
    [token appendFormat:@"%02.2hhX", bytes[i]];
  }
  
  return [token copy];
}

@end
