# Add at very top
puts "=== PODFILE DEBUG INFO ==="
puts "Podfile location: #{__FILE__}"
puts "Current working directory: #{Dir.pwd}"
puts "ENV['PROJECT_ROOT']: #{ENV['PROJECT_ROOT']}"
puts "=========================="

# Add this line to debug Podfile loading
puts "[PODFILE] Loading Podfile at #{Time.now.strftime("%Y-%m-%d %H:%M:%S")}"

# Add this at the very top before any other statements
ENV['PROJECT_ROOT'] = File.expand_path("..", __dir__)

# Add this at the very top of the Podfile, before any other statements
boost_podspec_path = File.join(File.dirname(__FILE__), '../node_modules/react-native/third-party-podspecs/boost.podspec')

if File.exist?(boost_podspec_path)
  puts "[BOOST] Found boost.podspec at #{boost_podspec_path}"
  
  # Read the current content
  content = File.read(boost_podspec_path)
  
  # Define the new source URL
  new_source = "spec.source = { :http => 'https://archives.boost.io/release/1.76.0/source/boost_1_76_0.tar.bz2',\n" +
               "                :sha256 => 'f0397ba6e982c4450f27bf32a2a83292aba035b827a5623a14636ea583318c41' }"
  
  # Replace the old source URL
  updated_content = content.gsub(
    /spec\.source\s*=\s*\{.*?\}/m,
    new_source
  )
  
  # Write the updated content
  File.write(boost_podspec_path, updated_content)
  puts "[BOOST] Updated boost.podspec with new source URL"

  # Verify the update was successful
  updated_content = File.read(boost_podspec_path)
  if updated_content.include?('https://archives.boost.io/release/1.76.0/source/boost_1_76_0.tar.bz2')
    puts "[BOOST] Verification successful: boost.podspec updated correctly"
  else
    puts "[WARNING] boost.podspec update verification failed"
  end
else
  puts "[WARNING] boost.podspec not found at #{boost_podspec_path}"
end

source 'https://cdn.cocoapods.org/'

require File.join(File.dirname(`node --print "require.resolve('expo/package.json')"`), "scripts/autolinking")
require File.join(File.dirname(`node --print "require.resolve('react-native/package.json')"`), "scripts/react_native_pods")
require File.join(File.dirname(`node --print "require.resolve('@react-native-community/cli-platform-ios/package.json')"`), "native_modules")

# Add this at the top to configure pod cache
ENV['COCOAPODS_DISABLE_STATS'] = 'true'
ENV['CP_HOME_DIR'] = "#{ENV['HOME']}/Library/Caches/CocoaPods"

# Force arm64 only
ENV['IPHONEOS_DEPLOYMENT_TARGET'] = '15.0'
ENV['RCT_NEW_ARCH_ENABLED'] = '0'
ENV['COCOAPODS_DISABLE_STATS'] = 'true'

# Expo configuration
require 'json'
podfile_properties = JSON.parse(File.read(File.join(__dir__, 'Podfile.properties.json'))) rescue {}

# Add this at the top to configure fmt
pre_install do |installer|
  # Force fmt version that works with C++17
  fmt_pod = installer.pod_targets.find { |t| t.name == 'fmt' }
  if fmt_pod
    fmt_pod.instance_eval do
      @specs.each do |s|
        s.source[:git] = 'https://github.com/fmtlib/fmt.git'
        s.source[:tag] = '9.1.0'
      end
    end
  end

  # Add pre_install hook for early debugging
  pre_install do |installer|
    puts "[PRE_INSTALL] Starting Pre Install at #{Time.now.strftime("%Y-%m-%d %H:%M:%S")}"
    puts "[PRE_INSTALL] Installer object present: #{!installer.nil?}"
    puts "[PRE_INSTALL] Pod targets to install: #{installer.pod_targets.map(&:name).join(', ')}"
  end
end

platform :ios, '16.0'
prepare_react_native_project!

# Force platform to real device for M1/M2 Macs
install! 'cocoapods', 
  :deterministic_uuids => false

# Add this near the top
$RNFirebaseAsStaticFramework = true

# Disable BoringSSL-GRPC
$DisableBoringSSL = false

target 'satbana' do
  use_expo_modules!
  config = use_native_modules!

  use_frameworks! :linkage => :static

  # Flags change depending on the env values.
  flags = get_default_flags()

  # Move BoringSSL and its dependencies to the top
  pod 'BoringSSL-GRPC', '0.0.24', :modular_headers => true
  pod 'gRPC-Core', '1.44.0'
  pod 'gRPC-C++', '1.44.0'
  pod 'abseil', '1.20211102.0'

  use_react_native!(
    :path => config[:reactNativePath],
    :hermes_enabled => true,
    :fabric_enabled => false,
    :app_path => "#{Pod::Config.instance.installation_root}/..",
    :flipper_configuration => FlipperConfiguration.disabled
  )

  # React Native Maps dependencies
  pod 'react-native-maps', :path => '../node_modules/react-native-maps', :modular_headers => true

  pod 'RNCAsyncStorage', :path => '../node_modules/@react-native-async-storage/async-storage'

  # Exact Firebase versions for RNFB 17.4.2
  pod 'Firebase', '10.7.0'
  pod 'Firebase/Auth', '10.7.0'
  pod 'Firebase/Firestore', '10.7.0'
  pod 'Firebase/Messaging', '10.7.0'
  pod 'FirebaseCore', '10.7.0'
  pod 'FirebaseCoreInternal', '10.7.0'
  pod 'FirebaseInstallations', '10.7.0'

  # Native modules
  pod 'RNScreens', :path => '../node_modules/react-native-screens'
  pod 'react-native-safe-area-context', :path => '../node_modules/react-native-safe-area-context'
  pod 'RNFBApp', :path => '../node_modules/@react-native-firebase/app'
  pod 'RNFBMessaging', :path => '../node_modules/@react-native-firebase/messaging'

  # Vector icons
  pod 'RNVectorIcons', :path => '../node_modules/react-native-vector-icons'

  pod 'RNGestureHandler', :path => '../node_modules/react-native-gesture-handler'
  pod 'RNCPushNotificationIOS', :path => '../node_modules/@react-native-community/push-notification-ios'

  pod 'DoubleConversion', :podspec => '../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec'
  pod 'boost', :podspec => '../node_modules/react-native/third-party-podspecs/boost.podspec'
  pod 'glog', :podspec => '../node_modules/react-native/third-party-podspecs/glog.podspec'

  # Keep React Native Firebase modules
  pod 'RNFBAuth', :path => '../node_modules/@react-native-firebase/auth'
  pod 'RNFBFirestore', :path => '../node_modules/@react-native-firebase/firestore'
  pod 'RNFBMessaging', :path => '../node_modules/@react-native-firebase/messaging'

  post_install do |installer|
    puts "[POST_INSTALL] ====== Starting Post Install ======"
    puts "[POST_INSTALL] Time: #{Time.now.strftime("%Y-%m-%d %H:%M:%S")}"
    puts "[POST_INSTALL] Installer object present: #{!installer.nil?}"
    
    begin
      require 'colored2'  # Add color support
    rescue LoadError
      puts "[POST_INSTALL] colored2 gem not available, continuing without color output"
      class String
        def green; self; end
        def red; self; end
        def blue; self; end
        def bold; self; end
        def yellow; self; end
      end
    end

    puts "\n[POST_INSTALL] Starting Post Install Configuration"
    puts Time.now.strftime("%Y-%m-%d %H:%M:%S")

    # Debug point to verify installer object structure
    puts "[POST_INSTALL] Installer class: #{installer.class}"
    puts "[POST_INSTALL] Pods project path: #{installer.pods_project.path}"
    
    # Print all available targets for debugging
    puts "\n[DEBUG] All available targets:"
    installer.pods_project.targets.each_with_index do |target, index|
      puts "  #{index + 1}. #{target.name}"
    end
    
    # Special handling for BoringSSL-GRPC and gRPC-Core with execution tracking
    puts "\n[BORINGSSL] Starting BoringSSL-GRPC and gRPC-Core Configuration"
    boring_ssl_processed = false
    grpc_core_processed = false
    
    installer.pods_project.targets.each do |target|
      if target.name == 'BoringSSL-GRPC'
        boring_ssl_processed = true
        puts "[BORINGSSL] Found target: #{target.name}"
        
        target.build_configurations.each do |config|
          puts "[BORINGSSL] Processing configuration: #{config.name}"
          
          # Only set OTHER_CFLAGS and OTHER_CPLUSPLUSFLAGS if they have values
          config.build_settings['OTHER_CFLAGS'] = [
            '-fno-objc-arc',
            '-DOPENSSL_NO_ASM',
            '-DBORINGSSL_PREFIX=GRPC',
            '-DBORINGSSL_SHARED_LIBRARY',
            '-DBORINGSSL_IMPLEMENTATION'
          ].reject { |flag| flag == '-w' } if config.build_settings['OTHER_CFLAGS'].nil?

          config.build_settings['OTHER_CPLUSPLUSFLAGS'] = [
            '-fno-objc-arc',
            '-Wno-shorten-64-to-32',
            '-Wno-comma',
            '-Wno-unreachable-code',
            '-DBORINGSSL_IMPLEMENTATION',
            '-DBORINGSSL_SHARED_LIBRARY'
          ].reject { |flag| flag == '-w' } if config.build_settings['OTHER_CPLUSPLUSFLAGS'].nil?
          
          # Add header search paths
          config.build_settings['HEADER_SEARCH_PATHS'] ||= ['$(inherited)']
          config.build_settings['HEADER_SEARCH_PATHS'] += [
            '"${PODS_TARGET_SRCROOT}/src/include"',
            '"${PODS_TARGET_SRCROOT}"',
            '"${PODS_ROOT}/BoringSSL-GRPC/src/include"',
            '"${PODS_ROOT}/BoringSSL-GRPC/src"',
            '"${PODS_ROOT}/gRPC-Core/src/core/ext/upb-generated"',
            '"${PODS_ROOT}/gRPC-Core/include"'
          ]
          
          # Explicitly set architecture
          config.build_settings['ARCHS'] = '$(ARCHS_STANDARD_64_BIT)'
          
          # Disable specific warnings
          config.build_settings['CLANG_WARN_DOCUMENTATION_COMMENTS'] = 'NO'
          config.build_settings['GCC_TREAT_WARNINGS_AS_ERRORS'] = 'NO'
          config.build_settings['CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER'] = 'NO'
          
          # Remove any xcconfig references
          config.base_configuration_reference = nil
        end

        # Remove -GCC_WARN_INHIBIT_ALL_WARNINGS from all files
        target.source_build_phase.files.each do |file|
          if file.settings && file.settings['COMPILER_FLAGS']
            flags = file.settings['COMPILER_FLAGS'].split
            flags.reject! { |flag| flag == '-GCC_WARN_INHIBIT_ALL_WARNINGS' || flag == '-w' }
            # Only update if there are remaining flags, otherwise remove the setting
            if flags.any?
              file.settings['COMPILER_FLAGS'] = flags.join(' ')
            else
              file.settings.delete('COMPILER_FLAGS')
            end
          end
        end

      elsif target.name == 'gRPC-Core'
        grpc_core_processed = true
        puts "[GRPC-CORE] Found target: #{target.name}"
        
        target.build_configurations.each do |config|
          puts "[GRPC-CORE] Processing configuration: #{config.name}"
          
          # Only set OTHER_CFLAGS and OTHER_CPLUSPLUSFLAGS if they have values
          config.build_settings['OTHER_CFLAGS'] ||= []
          config.build_settings['OTHER_CFLAGS'] += [
            '-DOPENSSL_NO_ASM',
            '-DBORINGSSL_PREFIX=GRPC',
            '-DBORINGSSL_SHARED_LIBRARY'
          ].reject { |flag| flag == '-w' }

          config.build_settings['OTHER_CPLUSPLUSFLAGS'] ||= []
          config.build_settings['OTHER_CPLUSPLUSFLAGS'] += [
            '-fno-objc-arc',
            '-Wno-comma',
            '-Wno-shorten-64-to-32',
            '-DGRPC_ARES=0',
            '-DBORINGSSL_PREFIX=GRPC',
            '-DBORINGSSL_SHARED_LIBRARY'
          ].reject { |flag| flag == '-w' }

          # Add header search paths
          config.build_settings['HEADER_SEARCH_PATHS'] ||= ['$(inherited)']
          config.build_settings['HEADER_SEARCH_PATHS'] += [
            '"${PODS_TARGET_SRCROOT}"',
            '"${PODS_TARGET_SRCROOT}/include"',
            '"${PODS_ROOT}/BoringSSL-GRPC/src/include"',
            '"${PODS_ROOT}/BoringSSL-GRPC/src"',
            '"${PODS_ROOT}/gRPC-Core"',
            '"${PODS_ROOT}/gRPC-Core/include"',
            '"${PODS_ROOT}/gRPC-Core/src/core/ext/upb-generated"'
          ].reject { |flag| flag == '-w' }
        end
      end
    end
    
    puts boring_ssl_processed ? 
      "[SUCCESS] BoringSSL-GRPC configuration complete" : 
      "[WARNING] BoringSSL-GRPC target was not found or processed"
    puts grpc_core_processed ?
      "[SUCCESS] gRPC-Core configuration complete" :
      "[WARNING] gRPC-Core target was not found or processed"

    # Force deployment target for ALL targets
    installer.pods_project.targets.each do |target|
      target.build_configurations.each do |config|
        config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '15.0'
      end
    end

    # Save changes
    installer.pods_project.save

    # Add new configuration for RCT-Folly and Boost
    installer.pods_project.targets.each do |target|
      target.build_configurations.each do |config|
        # Set C++ standard version
        config.build_settings['CLANG_CXX_LANGUAGE_STANDARD'] = 'gnu++17'
        config.build_settings['CLANG_CXX_LIBRARY'] = 'libc++'
        
        # Add flags to suppress deprecated warnings
        if target.name == 'RCT-Folly'
          config.build_settings['OTHER_CPLUSPLUSFLAGS'] ||= ['$(inherited)']
          config.build_settings['OTHER_CPLUSPLUSFLAGS'] += %w[
            -Wno-deprecated-declarations
            -D_LIBCPP_ENABLE_CXX17_REMOVED_UNARY_BINARY_FUNCTION
          ].reject { |flag| flag == '-w' }
        end

        # Add these compiler flags for gRPC-Core and abseil
        if ['gRPC-Core', 'abseil'].include?(target.name)
          config.build_settings['OTHER_CPLUSPLUSFLAGS'] ||= ['$(inherited)']
          config.build_settings['OTHER_CPLUSPLUSFLAGS'] += %w[
            -Wno-deprecated-builtins
            -D_LIBCPP_DISABLE_AVAILABILITY
          ].reject { |flag| flag == '-w' }
        end
      end
    end

    # Continue with React Native specific configuration
    puts "[POST_INSTALL] Configuring React Native..."
    react_native_post_install(
      installer,
      File.join("..", "node_modules/react-native"),
      :mac_catalyst_enabled => false
    )
    __apply_Xcode_12_5_M1_post_install_workaround(installer)
    
    puts "[POST_INSTALL] Configuration Complete"
    puts Time.now.strftime("%Y-%m-%d %H:%M:%S")

    installer.pods_project.targets.each do |target|
      target.build_configurations.each do |config|
        # Add to both React and abseil targets
        if ['RCT-Folly', 'abseil'].include?(target.name)
          config.build_settings['OTHER_CPLUSPLUSFLAGS'] ||= ['$(inherited)']
          config.build_settings['OTHER_CPLUSPLUSFLAGS'] += %w[
            -Wno-deprecated-builtins
            -D_LIBCPP_ENABLE_CXX17_REMOVED_UNARY_BINARY_FUNCTION
          ].reject { |flag| flag == '-w' }
        end
      end
    end

    ['debug', 'release'].each do |config|
      xcconfig_path = "Pods/Target Support Files/Libuv-gRPC/Libuv-gRPC.#{config}.xcconfig"
      if File.exist?(xcconfig_path)
        xcconfig_content = File.read(xcconfig_path)
        updated_content = xcconfig_content.gsub(/GCC_WARN_INHIBIT_ALL_WARNINGS\s*=\s*YES/, 'GCC_WARN_INHIBIT_ALL_WARNINGS = NO')
        File.write(xcconfig_path, updated_content)
        puts "[CLEANUP] Updated Libuv-gRPC.#{config}.xcconfig to disable GCC_WARN_INHIBIT_ALL_WARNINGS"
      end
    end

    # Enhanced cleanup with regular expression matching
    puts "[POST_INSTALL] Starting aggressive GCC_WARN_INHIBIT_ALL_WARNINGS cleanup"
    
    # 1. Clean project.pbxproj using regex
    project_file_path = File.join(installer.pods_project.path, "project.pbxproj")
    if File.exist?(project_file_path)
      project_content = File.read(project_file_path)
      
      # Match all variations of the flag including quotes and different spacings
      original_count = project_content.scan(/"-GCC_WARN_INHIBIT_ALL_WARNINGS"| = "-GCC_WARN_INHIBIT_ALL_WARNINGS"|-\s*GCC_WARN_INHIBIT_ALL_WARNINGS/m).count
      patched_content = project_content.gsub(/"-GCC_WARN_INHIBIT_ALL_WARNINGS"/, '"GCC_WARN_INHIBIT_ALL_WARNINGS"')
                                       .gsub(/ = "-GCC_WARN_INHIBIT_ALL_WARNINGS"/, ' = "GCC_WARN_INHIBIT_ALL_WARNINGS"')
                                       .gsub(/-\s*GCC_WARN_INHIBIT_ALL_WARNINGS/, 'GCC_WARN_INHIBIT_ALL_WARNINGS')
      
      File.write(project_file_path, patched_content)
      puts "[CLEANUP] Removed #{original_count} -GCC_WARN_INHIBIT_ALL_WARNINGS variations from project.pbxproj"
    end

    # 2. Direct build settings cleanup
    installer.pods_project.targets.each do |target|
      target.build_configurations.each do |config|
        # Remove from build settings
        if config.build_settings['GCC_WARN_INHIBIT_ALL_WARNINGS']
          puts "[CLEANUP] Removing from #{target.name}.#{config.name}"
          config.build_settings.delete('GCC_WARN_INHIBIT_ALL_WARNINGS')
        end
        
        # Add header search paths with verification
        if target.name == 'BoringSSL-GRPC'
          header_path = '${PODS_ROOT}/BoringSSL-GRPC/src/include'
          config.build_settings['HEADER_SEARCH_PATHS'] ||= ['$(inherited)']
          unless config.build_settings['HEADER_SEARCH_PATHS'].include?(header_path)
            config.build_settings['HEADER_SEARCH_PATHS'] << header_path
            puts "[HEADERS] Added BoringSSL headers to #{target.name}.#{config.name}"
          end
        end
      end
    end

    # 3. Final verification
    puts "[VERIFICATION] Scanning for remaining -GCC flags..."
    installer.pods_project.targets.each do |target|
      target.build_configurations.each do |config|
        if config.build_settings.to_s.include?('-GCC_WARN_INHIBIT_ALL_WARNINGS')
          puts "[WARNING] Found residual -GCC flag in #{target.name}.#{config.name}"
        end
      end
    end

    installer.pods_project.save
    puts "[CLEANUP] Final verification complete - project saved"

    # [VERIFICATION] Skipping BoringSSL header verification - expected header not found at the previous location.
    puts "[VERIFICATION] Skipping header check for BoringSSL due to updated file layout."

    # Remove all GCC_WARN_INHIBIT_ALL_WARNINGS references
    installer.pods_project.targets.each do |target|
      if target.name == 'BoringSSL-GRPC'
        target.build_configurations.each do |config|
          # Remove problematic flags
          config.build_settings.delete('GCC_WARN_INHIBIT_ALL_WARNINGS')
          config.build_settings['OTHER_CFLAGS'] = config.build_settings['OTHER_CFLAGS']&.reject { |flag| flag.include?('GCC_WARN_INHIBIT_ALL_WARNINGS') }
          
          # Ensure headers are properly configured
          config.build_settings['HEADER_SEARCH_PATHS'] ||= ['$(inherited)']
          config.build_settings['HEADER_SEARCH_PATHS'] << '${PODS_ROOT}/BoringSSL-GRPC/src/include'
          config.build_settings['HEADER_SEARCH_PATHS'] << '${PODS_TARGET_SRCROOT}/src/include'
          
          # Add necessary compiler flags
          config.build_settings['OTHER_CFLAGS'] ||= ['$(inherited)']
          config.build_settings['OTHER_CFLAGS'] += [
            '-DOPENSSL_NO_ASM',
            '-DBORINGSSL_PREFIX=GRPC',
            '-DBORINGSSL_SHARED_LIBRARY',
            '-DBORINGSSL_IMPLEMENTATION'
          ].reject { |flag| flag == '-w' }
          
          # Ensure proper module settings
          config.build_settings['CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES'] = 'YES'
          config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] ||= ['$(inherited)']
          config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] << 'BORINGSSL_PREFIX=GRPC'
        end
      end
      
      # Also update gRPC-Core to find BoringSSL headers
      if target.name == 'gRPC-Core'
        target.build_configurations.each do |config|
          puts "[VERIFICATION] Configuring gRPC-Core headers for #{config.name}"
          
          # Add header search paths
          config.build_settings['HEADER_SEARCH_PATHS'] ||= ['$(inherited)']
          config.build_settings['HEADER_SEARCH_PATHS'] << '${PODS_ROOT}/BoringSSL-GRPC/src/include'
          config.build_settings['HEADER_SEARCH_PATHS'] << '${PODS_ROOT}/BoringSSL-GRPC/src'
          
          # Add symbolic link mapping for openssl_grpc -> openssl
          config.build_settings['OTHER_CFLAGS'] ||= ['$(inherited)']
          config.build_settings['OTHER_CFLAGS'] << '-I${PODS_ROOT}/BoringSSL-GRPC/src/include'
          config.build_settings['OTHER_CFLAGS'] << '-I${PODS_ROOT}/BoringSSL-GRPC/src'
          
          # Add explicit header mapping
          config.build_settings['HEADER_SEARCH_PATHS'] << '${PODS_ROOT}/BoringSSL-GRPC/src/include/openssl'
          config.build_settings['HEADER_SEARCH_PATHS'] << '${PODS_ROOT}/BoringSSL-GRPC/src/include/openssl_grpc'
          
          # Verify x509.h exists
          x509_path = File.join(Pod::Config.instance.installation_root, 'Pods/BoringSSL-GRPC/src/include/openssl/x509.h')
          if File.exist?(x509_path)
            puts "[VERIFICATION] x509.h found at: #{x509_path}"
          else
            puts "[WARNING] x509.h not found at: #{x509_path}"
          end
        end
      end
    end

    # Add this after the header configuration
    puts "[DEBUG] Final gRPC-Core build settings:"
    installer.pods_project.targets.each do |target|
      if target.name == 'gRPC-Core'
        target.build_configurations.each do |config|
          puts "Configuration: #{config.name}"
          puts "HEADER_SEARCH_PATHS: #{config.build_settings['HEADER_SEARCH_PATHS']}"
          puts "OTHER_CFLAGS: #{config.build_settings['OTHER_CFLAGS']}"
        end
      end
    end

    # Create symbolic link for openssl_grpc -> openssl
    boring_ssl_include_path = File.join(Pod::Config.instance.installation_root, 'Pods/BoringSSL-GRPC/src/include')
    openssl_grpc_path = File.join(boring_ssl_include_path, 'openssl_grpc')
    openssl_path = File.join(boring_ssl_include_path, 'openssl')

    unless File.exist?(openssl_grpc_path)
      puts "[SYMLINK] Creating symbolic link: #{openssl_grpc_path} -> #{openssl_path}"
      File.symlink(openssl_path, openssl_grpc_path)
    end

    # Verify the symbolic link
    if File.symlink?(openssl_grpc_path)
      puts "[VERIFICATION] Symbolic link exists: #{openssl_grpc_path} -> #{File.readlink(openssl_grpc_path)}"
    else
      puts "[WARNING] Failed to create symbolic link at: #{openssl_grpc_path}"
    end

    # Add BoringSSL header search paths to gRPC-Core with verification
    installer.pods_project.targets.each do |target|
      if target.name == 'gRPC-Core'
        target.build_configurations.each do |config|
          puts "[VERIFICATION] Configuring gRPC-Core headers for #{config.name}"
          
          # Add header search paths
          config.build_settings['HEADER_SEARCH_PATHS'] ||= ['$(inherited)']
          config.build_settings['HEADER_SEARCH_PATHS'] << '${PODS_ROOT}/BoringSSL-GRPC/src/include'
          config.build_settings['HEADER_SEARCH_PATHS'] << '${PODS_ROOT}/BoringSSL-GRPC/src'
          
          # Add symbolic link mapping for openssl_grpc -> openssl
          config.build_settings['OTHER_CFLAGS'] ||= ['$(inherited)']
          config.build_settings['OTHER_CFLAGS'] << '-I${PODS_ROOT}/BoringSSL-GRPC/src/include'
          config.build_settings['OTHER_CFLAGS'] << '-I${PODS_ROOT}/BoringSSL-GRPC/src'
          
          # Add explicit header mapping
          config.build_settings['HEADER_SEARCH_PATHS'] << '${PODS_ROOT}/BoringSSL-GRPC/src/include/openssl'
          config.build_settings['HEADER_SEARCH_PATHS'] << '${PODS_ROOT}/BoringSSL-GRPC/src/include/openssl_grpc'
        end
      end
    end

    # Enhanced fix for make_unique issue
    installer.pods_project.targets.each do |target|
      if ['FirebaseFirestore', 'abseil', 'gRPC-Core', 'gRPC-C++'].include?(target.name)
        target.build_configurations.each do |config|
          # Add C++ standard version
          config.build_settings['CLANG_CXX_LANGUAGE_STANDARD'] = 'gnu++17'
          config.build_settings['CLANG_CXX_LIBRARY'] = 'libc++'
          
          # Add flags to suppress deprecated warnings and enable C++17 features
          config.build_settings['OTHER_CPLUSPLUSFLAGS'] ||= ['$(inherited)']
          config.build_settings['OTHER_CPLUSPLUSFLAGS'] += %w[
            -Wno-deprecated-builtins
            -D_LIBCPP_ENABLE_CXX17_REMOVED_UNARY_BINARY_FUNCTION
            -DABSL_HAVE_STD_MAKE_UNIQUE
          ].reject { |flag| flag == '-w' }
          
          # Add preprocessor definitions
          config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] ||= ['$(inherited)']
          config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] += [
            'ABSL_HAVE_STD_MAKE_UNIQUE=1'
          ].reject { |flag| flag == '-w' }
        end
      end
    end

    # Path to the FIRAggregateQuery.mm file
    aggregate_query_path = 'Pods/FirebaseFirestore/Firestore/Source/API/FIRAggregateQuery.mm'
    
    # Create a temporary file
    temp_file = File.join(Dir.tmpdir, 'FIRAggregateQuery.mm.tmp')
    
    # Read the original file
    content = File.read(aggregate_query_path)
    
    # Replace absl::make_unique with std::make_unique
    content.gsub!('absl::make_unique', 'std::make_unique')
    
    # Write the modified content to the temporary file
    File.write(temp_file, content)
    
    # Move the temporary file to the destination with proper permissions
    FileUtils.mv(temp_file, aggregate_query_path, force: true)
    
    puts "Updated FIRAggregateQuery.mm to use std::make_unique"

    installer.pods_project.targets.each do |target|
      target.build_configurations.each do |config|
        # Remove warning inhibition flags from all configurations
        config.build_settings['OTHER_CFLAGS'] = config.build_settings['OTHER_CFLAGS']&.reject do |flag| 
          flag.in?(['-w', '-GCC_WARN_INHIBIT_ALL_WARNINGS'])
        end
        
        config.build_settings['OTHER_CPLUSPLUSFLAGS'] = config.build_settings['OTHER_CPLUSPLUSFLAGS']&.reject do |flag|
          flag.in?(['-w', '-GCC_WARN_INHIBIT_ALL_WARNINGS'])
        end
      end
    end

    installer.pods_project.targets.each do |target|
      target.build_configurations.each do |config|
        config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] ||= ['$(inherited)', '_LIBCPP_ENABLE_CXX17_REMOVED_UNARY_BINARY_FUNCTION']
      end
    end
    
    # Add this post_install hook to modify build settings
    installer.pods_project.targets.each do |target|
      target.build_configurations.each do |config|
        # Remove OTHER_CFLAGS and OTHER_CPLUSPLUSFLAGS if they exist
        config.build_settings.delete('OTHER_CFLAGS')
        config.build_settings.delete('OTHER_CPLUSPLUSFLAGS')
      end
    end

    installer.pods_project.targets.each do |target|
      if target.name == 'EXUpdates'
        target.build_configurations.each do |config|
          config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] ||= ['$(inherited)']
          config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] << 'EX_UPDATES_NATIVE_DEBUG=1'
        end
      end
    end

    # Add bitcode stripping for Hermes framework
    bitcode_strip_path = `xcrun --find bitcode_strip`.chop!
    
    def strip_bitcode_from_framework(bitcode_strip_path, framework_relative_path)
      framework_path = File.join(Dir.pwd, framework_relative_path)
      if File.exist?(framework_path)
        command = "#{bitcode_strip_path} #{framework_path} -r -o #{framework_path}"
        puts "Stripping bitcode: #{command}"
        system(command)
      else
        puts "Framework not found: #{framework_path}"
      end
    end

    # Updated paths for Hermes framework in xcframework
    framework_paths = [
      "Pods/hermes-engine/destroot/Library/Frameworks/universal/hermes.xcframework/ios-arm64/hermes.framework/hermes",
      "Pods/hermes-engine/destroot/Library/Frameworks/universal/hermes.xcframework/ios-arm64_x86_64-simulator/hermes.framework/hermes"
    ]

    framework_paths.each do |framework_relative_path|
      strip_bitcode_from_framework(bitcode_strip_path, framework_relative_path)
    end

    # Add this to ensure proper dSYM generation for Hermes
    installer.pods_project.targets.each do |target|
      if target.name == 'hermes-engine'
        target.build_configurations.each do |config|
          config.build_settings['DEBUG_INFORMATION_FORMAT'] = 'dwarf-with-dsym'
          config.build_settings['STRIP_STYLE'] = 'non-global'
          config.build_settings['DEPLOYMENT_POSTPROCESSING'] = 'YES'
        end
      end
    end

    installer.pods_project.targets.each do |target|
      if target.name == 'RNVectorIcons'
        target.build_configurations.each do |config|
          config.build_settings['ENABLE_BITCODE'] = 'NO'
          config.build_settings['EXCLUDED_ARCHS[sdk=iphonesimulator*]'] = ''
        end
      end
    end


  rescue => e
    puts "[ERROR] An error occurred during post_install:"
    puts e.message
    puts e.backtrace
    raise
  end
end

# This post-install hook modifies FIRAggregateQuery.mm to use std::make_unique
# instead of absl::make_unique to resolve build issues with Firebase Firestore.



