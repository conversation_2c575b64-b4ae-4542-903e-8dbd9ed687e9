// hooks/useUserOffers.js

import { useState, useEffect } from 'react';
import { fetchUserOffers } from '../services/firebaseService';

const useUserOffers = (userId) => {
  const [offers, setOffers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (!userId) {
      setError('No user is logged in');
      setLoading(false);
      return;
    }

    const unsubscribe = fetchUserOffers(userId, (offersData) => {
      setOffers(offersData);
      setLoading(false);
    });

    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
    };
  }, [userId]);

  return { offers, loading, error };
};

export default useUserOffers;