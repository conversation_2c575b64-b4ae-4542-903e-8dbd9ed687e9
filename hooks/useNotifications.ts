import { useState, useEffect } from 'react';
import { fcmService } from '../services/fcmService';
import messaging from '@react-native-firebase/messaging';
import firestore from '@react-native-firebase/firestore';

export const useNotifications = (user) => {
  const [permissionStatus, setPermissionStatus] = useState<boolean>(false);
  const [unreadCount, setUnreadCount] = useState(0);

  useEffect(() => {
    const checkPermission = async () => {
      const hasPermission = await fcmService.requestUserPermission();
      setPermissionStatus(hasPermission);
    };

    checkPermission();

    // Initialize messaging
    const unsubscribe = messaging().onMessage(async remoteMessage => {
      console.log('Received foreground message:', remoteMessage);
    });

    if (!user) return;

    const unsubscribeNotifications = db.collection('notifications')
      .where('recipientId', '==', user.uid)
      .where('read', '==', false)
      .onSnapshot((snapshot) => {
        setUnreadCount(snapshot.size);
      });

    return () => {
      unsubscribe();
      unsubscribeNotifications();
    };
  }, [user]);

  return { permissionStatus, unreadCount };
};

export default useNotifications; 