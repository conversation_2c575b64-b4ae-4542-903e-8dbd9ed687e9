// hooks/useWithdrawOffer.js

import { doc, updateDoc, serverTimestamp, getDoc } from 'firebase/firestore';
import { db } from '../firebase';
import { createNotification } from '../services/firebaseService';
import { NotificationType } from '../types/notifications';
import { useAuthUser } from './useAuthUser';

const useWithdrawOffer = () => {
  const currentUser = useAuthUser();

  const withdrawOffer = async (offerId) => {
    try {
      const offerRef = doc(db, 'offers', offerId);
      
      // Get offer details first to get posting owner ID
      const offerDoc = await getDoc(offerRef);
      const offerData = offerDoc.data();
      
      // Update offer status
      await updateDoc(offerRef, {
        status: 'withdrawn',
        lastUpdated: serverTimestamp()
      });

      // Get posting details to get posting owner ID
      const postingRef = doc(db, 'postings', offerData.postingId);
      const postingDoc = await getDoc(postingRef);
      const postingData = postingDoc.data();

      // Create notification for posting owner
      const notificationData = {
        type: NotificationType.OFFER_STATUS_CHANGE,
        recipientId: postingData.userId,
        senderId: currentUser.uid,
        offerId,
        postingId: offerData.postingId,
        title: 'Offer Withdrawn',
        body: `An offer for your posting has been withdrawn`,
        data: {
          currentStatus: 'withdrawn',
          currentPrice: offerData.price,
          currentDescription: offerData.description,
          timestamp: Date.now(),
        }
      };

      await createNotification(notificationData);
      console.log('Withdrawal notification sent to posting owner');

      return true;
    } catch (error) {
      console.error('Error withdrawing offer:', error);
      throw error;
    }
  };

  return { withdrawOffer };
};

export default useWithdrawOffer;