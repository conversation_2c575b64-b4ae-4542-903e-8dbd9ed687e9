// useFavorites.js

import { useState, useEffect, useCallback } from 'react';
import { getUserFavorites, addToFavorites, removeFromFavorites } from '../services/firebaseService';
import { useFocusEffect } from '@react-navigation/native';

const useFavorites = (postingId, userId, initialState = null) => {
  const [isFavorite, setIsFavorite] = useState(initialState ?? false);
  
  // Add effect to handle initialState changes
  useEffect(() => {
    if (initialState !== null) {
      setIsFavorite(initialState);
    }
  }, [initialState]);

  const [loading, setLoading] = useState(initialState === null);

  const fetchFavoriteStatus = useCallback(async () => {
    if (userId) {
      setLoading(true);
      try {
        const favorites = await getUserFavorites(userId);
        setIsFavorite(favorites?.includes(postingId) ?? false);
      } catch (error) {
        console.error('[useFavorites][Error] Error fetching favorites status:', error);
      }
      setLoading(false);
    }
  }, [userId, postingId]);

  // Add focus effect to refetch status when screen is focused
  useFocusEffect(
    useCallback(() => {
      console.log('[useFavorites][Focus] Screen focused, fetching favorite status');
      fetchFavoriteStatus();
    }, [fetchFavoriteStatus])
  );

  const handleAddToFavorites = async () => {
    if (userId) {
      try {
        await addToFavorites(userId, postingId);
        setIsFavorite(true);
      } catch (error) {
        console.error('[useFavorites][Error] Error adding to favorites:', error);
      }
    }
  };

  const handleRemoveFromFavorites = async () => {
    if (userId) {
      try {
        await removeFromFavorites(userId, postingId);
        setIsFavorite(false);
      } catch (error) {
        console.error('[useFavorites][Error] Error removing from favorites:', error);
      }
    }
  };

  return {
    isFavorite,
    addToFavorites: handleAddToFavorites,
    removeFromFavorites: handleRemoveFromFavorites,
    loading
  };
};

export default useFavorites;
