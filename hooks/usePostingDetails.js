// usePostingDetails.js

import { useReducer, useEffect, useCallback, useRef } from 'react';
import { getFirestore, collection, doc, onSnapshot } from 'firebase/firestore';
import { db } from '../firebase';
import { createAndRegisterListener } from '../services/firebaseService';
import { useFocusEffect } from '@react-navigation/native';

// Change to named export
export const usePostingDetails = (postingId, componentId) => {
  const metrics = useRef({
    startTime: Date.now(),
    updates: 0,
    lastUpdate: null
  });

  // Add cleanup state tracking
  const cleanupState = useRef({
    isCleaningUp: false,
    cleanupStartTime: null,
    lastCleanupDuration: null
  });

  // Add Firebase listener tracking ref
  const firebaseListenerRef = useRef({
    active: false,
    id: null,
    startTime: null,
    cleanupPromise: null,
    unsubscribe: null  // Add unsubscribe function storage
  });

  console.log('[usePostingDetails][Init] Starting with postingId:', {
    postingId,
    metrics: {
      startTime: new Date(metrics.current.startTime).toISOString()
    },
    timestamp: new Date().toISOString()
  });

  const [state, dispatch] = useReducer((state, action) => {
    metrics.current.updates++;
    metrics.current.lastUpdate = Date.now();

    console.log('[usePostingDetails][Reducer] Processing action:', {
      type: action.type,
      currentState: state,
      metrics: {
        updateCount: metrics.current.updates,
        timeSinceStart: Date.now() - metrics.current.startTime,
        timestamp: new Date().toISOString()
      }
    });

    switch (action.type) {
      case 'FETCH_START':
        return {
          ...state,
          loading: true,
          error: null
        };
      case 'FETCH_SUCCESS':
        return {
          ...state,
          loading: false,
          postingDetails: action.payload,
          error: null
        };
      case 'FETCH_ERROR':
        return {
          ...state,
          loading: false,
          error: action.payload
        };
      case 'UPDATE_POSTING':
        return {
          ...state,
          loading: false,
          postingDetails: action.payload,
          error: null
        };
      default:
        return state;
    }
  }, {
    loading: true,
    postingDetails: null,
    error: null
  });

  // Setup subscription function
  const setupSubscription = useCallback(() => {
    if (!db || !postingId) {
      console.error('[usePostingDetails][Fatal] Missing required parameters');
      dispatch({
        type: 'FETCH_ERROR',
        payload: 'Missing required parameters'
      });
      return;
    }

    console.log('[usePostingDetails][Setup] Creating references');
    const postingsRef = collection(db, 'postings');
    const postingDocRef = doc(postingsRef, postingId);

    console.log('[usePostingDetails][Setup] Pre-snapshot listener setup:', {
      postingId,
      ref: postingDocRef.path,
      timestamp: new Date().toISOString()
    });

    try {
      dispatch({ type: 'FETCH_START' });

      const listenerResult = createAndRegisterListener(
        'postings',
        postingDocRef,
        (docSnapshot) => {
          if (!firebaseListenerRef.current.active) {
            console.log('[usePostingDetails][Snapshot] First listener activation:', {
              postingId,
              listenerId: listenerResult.listenerId,
              timestamp: new Date().toISOString(),
              snapshot: {
                exists: docSnapshot.exists(),
                path: docSnapshot.ref.path
              }
            });
          }

          if (docSnapshot.exists()) {
            const data = docSnapshot.data();
            dispatch({
              type: 'UPDATE_POSTING',
              payload: {
                id: docSnapshot.id,
                ...data
              }
            });
          } else {
            console.log('[usePostingDetails][Snapshot] Document does not exist');
            dispatch({
              type: 'FETCH_ERROR',
              payload: 'No such document'
            });
          }
        },
        (error) => {
          console.error('[usePostingDetails][Error] Listener error:', error);
          dispatch({
            type: 'FETCH_ERROR',
            payload: error.message
          });
        },
        componentId
      );

      firebaseListenerRef.current = {
        active: true,
        id: listenerResult.listenerId,
        startTime: Date.now(),
        unsubscribe: listenerResult.unsubscribe
      };

      return listenerResult.unsubscribe;
    } catch (error) {
      console.error('[usePostingDetails][Fatal] Setup failed:', error);
      dispatch({
        type: 'FETCH_ERROR',
        payload: error.message
      });
    }
  }, [postingId, componentId]);

  // RESTORED: useFocusEffect for immediate data availability on navigation
  // This ensures posting details are available immediately when user navigates to screen
  useFocusEffect(
    useCallback(() => {
      console.log('[usePostingDetails][Focus] Screen focused, setting up subscription');

      // Set up subscription immediately on focus for immediate data availability
      const unsubscribe = setupSubscription();
      if (unsubscribe) {
        firebaseListenerRef.current.unsubscribe = unsubscribe;
      }

      return () => {
        console.log('[usePostingDetails][Focus] Screen unfocused, cleaning up');
        if (firebaseListenerRef.current.unsubscribe) {
          firebaseListenerRef.current.unsubscribe();
          firebaseListenerRef.current.active = false;
        }
      };
    }, [setupSubscription])
  );

  return {
    ...state,
    postingId,
    refetchPostingDetails: useCallback(() => {
      console.log('[usePostingDetails] Manually refetching:', {
        postingId,
        metrics: {
          timeSinceStart: Date.now() - metrics.current.startTime,
          updates: metrics.current.updates
        },
        timestamp: new Date().toISOString()
      });
      setupSubscription();
    }, [postingId, setupSubscription]),
    listenerState: {
      active: firebaseListenerRef.current.active,
      id: firebaseListenerRef.current.id,
      uptime: firebaseListenerRef.current.startTime 
        ? Date.now() - firebaseListenerRef.current.startTime 
        : null,
      lastCleanupDuration: cleanupState.current.lastCleanupDuration
    },
    ensureCleanup: useCallback(async () => {
      if (firebaseListenerRef.current.unsubscribe) {
        firebaseListenerRef.current.unsubscribe();
        firebaseListenerRef.current.active = false;
      }
    }, []),
    unsubscribe: () => {
      if (firebaseListenerRef.current.unsubscribe) {
        console.log('[usePostingDetails][Unsubscribe] Cleaning up listener:', {
          postingId,
          listenerId: firebaseListenerRef.current.id,
          timestamp: new Date().toISOString()
        });
        firebaseListenerRef.current.unsubscribe();
        firebaseListenerRef.current.active = false;
      }
    }
  };
};

// Add default export to maintain backward compatibility
export default usePostingDetails;
