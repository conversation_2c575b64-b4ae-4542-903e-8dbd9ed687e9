// useFavoritesData.js
import { useState, useEffect } from 'react';
import { db } from '../firebase';
import { doc, getDoc } from 'firebase/firestore';

const useFavoritesData = (userId) => {
  const [favorites, setFavorites] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchFavorites = async () => {
      if (!userId) {
        console.error('User must be logged in to view favorites.');
        return;
      }

      try {
        const userRef = doc(db, 'users', userId);
        const userDoc = await getDoc(userRef);
        if (userDoc.exists()) {
          const userFavorites = userDoc.data().favorites || [];
          const favoritePostings = await Promise.all(
            userFavorites.map(async (postingId) => {
              const postingRef = doc(db, 'postings', postingId);
              const postingDoc = await getDoc(postingRef);
              if (postingDoc.exists()) {
                return { id: postingDoc.id, ...postingDoc.data() };
              }
              return null;
            })
          );
          setFavorites(favoritePostings.filter((item) => item !== null));
        }
      } catch (err) {
        console.error('Error fetching favorites:', err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchFavorites();
  }, [userId]);

  return { favorites, loading, error };
};

export default useFavoritesData;