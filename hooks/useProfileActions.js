// useProfileActions.js

import { useNavigation, CommonActions } from '@react-navigation/native';
import { signOut } from '../services/authService';
import { useCallback } from 'react';
import { clearNotificationListeners } from '../services/notificationService';

export const useProfileActions = () => {
  const navigation = useNavigation();

  const options = [
    { id: '1', title: 'Create Posting', navigateTo: 'CreatePosting' },
    { id: '2', title: 'My Postings', navigateTo: 'MyPostings' },
    { id: '3', title: 'My Offers', navigateTo: 'MyOffers' },
    { id: '4', title: 'My Favorites', navigateTo: 'MyFavorites' },
    { id: '5', title: 'Settings', navigateTo: 'Settings' },
    { id: '6', title: 'Sign Out', navigateTo: null },
  ];

  const handleOptionPress = useCallback(async (option) => {
    if (option.navigateTo) {
      console.log(`Navigating to ${option.navigateTo}`);
      navigation.navigate(option.navigateTo);
    } else if (option.title === 'Sign Out') {
      console.log('=== Starting Sign Out Flow ===');
      try {
        // First cleanup notification listeners
        console.log('Step 1: Cleaning up notification listeners...');
        await clearNotificationListeners();
        
        // Add a small delay after cleanup
        await new Promise(resolve => setTimeout(resolve, 100));
        
        console.log('Step 2: Initiating sign out...');
        await signOut();
        
        console.log('Step 3: Preparing for navigation reset...');
        const resetAction = CommonActions.reset({
          index: 0,
          routes: [{ name: 'LoginScreen' }],
        });
        
        console.log('Step 4: Executing navigation reset...');
        navigation.dispatch(resetAction);
        
        console.log('=== Sign Out Flow Complete ===');
      } catch (error) {
        console.error('Error in sign out flow:', error);
        console.error('Error stack:', error.stack);
      }
    }
  }, [navigation]);

  return { options, handleOptionPress };
};