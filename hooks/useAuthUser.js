// useAuthUser.js

import { useEffect, useState } from 'react';
import { auth } from '../firebase';

export const useAuthUser = () => {
  const [currentUser, setCurrentUser] = useState(auth.currentUser);

  useEffect(() => {
    console.log('[useAuthUser] Setting up auth state listener', {
      hasAuth: !!auth,
      hasOnAuthStateChanged: !!auth?.onAuthStateChanged,
      currentUser: auth?.currentUser
    });

    const unsubscribe = auth.onAuthStateChanged((user) => {
      console.log('[useAuthUser] Auth state changed', { user });
      setCurrentUser(user);
    });

    return () => {
      console.log('[useAuthUser] Cleaning up auth state listener');
      unsubscribe();
    };
  }, []);

  return currentUser;
};