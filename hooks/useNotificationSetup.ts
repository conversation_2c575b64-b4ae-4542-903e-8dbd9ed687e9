import { useEffect } from 'react';
import { Platform } from 'react-native';
import messaging from '@react-native-firebase/messaging';
import { handleForegroundNotification } from '../services/notificationService';

export const useNotificationSetup = () => {
  useEffect(() => {
    const setupNotifications = async () => {
      console.log('=== Setting up notification listeners ===');
      
      if (Platform.OS === 'ios') {
        try {
          const authStatus = await messaging().requestPermission();
          console.log('Permission status:', authStatus);
          
          if (authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
              authStatus === messaging.AuthorizationStatus.PROVISIONAL) {
            console.log('Notification permissions granted');
          }
        } catch (error) {
          console.error('Failed to get notification permissions:', error);
        }
      }
    };

    setupNotifications();
    
    // Handle foreground messages
    const unsubscribeForeground = messaging().onMessage(async remoteMessage => {
      console.log('Received foreground message:', remoteMessage);
      handleForegroundNotification(remoteMessage);
    });

    // Handle background/quit state messages
    messaging().setBackgroundMessageHandler(async remoteMessage => {
      console.log('Received background message:', remoteMessage);
      return Promise.resolve();
    });

    return () => {
      console.log('Cleaning up notification listeners');
      unsubscribeForeground();
    };
  }, []);
}; 