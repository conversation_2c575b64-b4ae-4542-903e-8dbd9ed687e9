// useFormValidation.js

import { useState } from 'react';

export const useFormValidation = (fields) => {
  const [validationErrors, setValidationErrors] = useState([]);

  const validate = () => {
    const errors = [];

    if (!fields.itemName.trim()) {
      errors.push('Item name is required.');
    }
    if (!fields.description.trim()) {
      errors.push('Description is required.');
    }

    setValidationErrors(errors);
    return errors.length === 0;
  };

  return { validate, validationErrors };
};