// useLocationInitialization.js


import { useState, useEffect } from 'react';
import { fetchUserLocation } from '../services/firebaseService';

export const useLocationInitialization = (mapRef, route) => {
    const [isInitialLoad, setIsInitialLoad] = useState(true);
    const [initialLocation, setInitialLocation] = useState(null);

    useEffect(() => {
        const initializeLocation = async () => {
            if (route?.params?.userLocation) {
                setInitialLocation(route.params.userLocation);

                if (mapRef && mapRef.current) { // Added check for mapRef
                    mapRef.current.animateCamera({ 
                        center: route.params.userLocation, 
                        zoom: 14 
                    });
                }
                setIsInitialLoad(false);
                return;
            }

            if (isInitialLoad) {
                try {
                    const location = await fetchUserLocation();
                    if (location) {
                        setInitialLocation(location);

                        if (mapRef && mapRef.current) { // Added check for mapRef
                            mapRef.current.animateCamera({ 
                                center: location, 
                                zoom: 14 
                            });
                        }
                        setIsInitialLoad(false);
                    }
                } catch (error) {
                    console.error('Location initialization failed', error);
                }
            }
        };

        initializeLocation();
    }, [isInitialLoad, mapRef, route]);

    return { initialLocation, isInitialLoad };
};