// useFetchUserPostings.js

import { useEffect, useState } from 'react';
import { collection, query, where, onSnapshot } from 'firebase/firestore';
import { db, auth } from '../firebase';

export const useFetchUserPostings = () => {
  const [activePostings, setActivePostings] = useState([]);
  const [deletedPostings, setDeletedPostings] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const currentUser = auth.currentUser;

  useEffect(() => {
    console.log('[useFetchUserPostings] Hook initialized', {
      isUserLoggedIn: !!currentUser,
      userId: currentUser?.uid
    });

    if (!currentUser) {
      console.error('[useFetchUserPostings] No user is logged in');
      setLoading(false);
      return;
    }

    try {
      console.log('[useFetchUserPostings] Setting up postings subscription for user:', currentUser.uid);
      
      // Subscribe to active postings
      const activeQuery = query(
        collection(db, 'postings'),
        where('userId', '==', currentUser.uid),
        where('postingStatus', '==', 'Active')
      );

      // Subscribe to deleted postings
      const deletedQuery = query(
        collection(db, 'postings'),
        where('userId', '==', currentUser.uid),
        where('postingStatus', '==', 'Deleted')
      );

      const unsubscribeActive = onSnapshot(activeQuery, (snapshot) => {
        const active = snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
        console.log('[useFetchUserPostings] Active postings updated:', {
          count: active.length,
          postingIds: active.map(p => p.id)
        });
        setActivePostings(active);
        setLoading(false);
      }, (error) => {
        console.error('[useFetchUserPostings] Error in active postings subscription:', error);
        setError(error);
        setLoading(false);
      });

      const unsubscribeDeleted = onSnapshot(deletedQuery, (snapshot) => {
        const deleted = snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
        console.log('[useFetchUserPostings] Deleted postings updated:', {
          count: deleted.length,
          postingIds: deleted.map(p => p.id)
        });
        setDeletedPostings(deleted);
        setLoading(false);
      }, (error) => {
        console.error('[useFetchUserPostings] Error in deleted postings subscription:', error);
        setError(error);
        setLoading(false);
      });

      // Cleanup function to unsubscribe when the component unmounts
      return () => {
        console.log('[useFetchUserPostings] Cleaning up subscriptions');
        unsubscribeActive();
        unsubscribeDeleted();
      };
    } catch (error) {
      console.error('[useFetchUserPostings] Error setting up subscriptions:', error);
      setError(error);
      setLoading(false);
    }
  }, [currentUser]);

  return { activePostings, deletedPostings, loading, error };
};