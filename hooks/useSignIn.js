// useSignIn.js

import { useState } from 'react';
import { auth } from '../firebase';
import { signInWithEmailAndPassword } from 'firebase/auth';
import { initializeNotifications } from '../services/notificationService';

export const useSignIn = () => {
  const [loading, setLoading] = useState(false);

  const handleSignIn = async (email, password) => {
    setLoading(true);
    console.log('=== Starting Sign In Process ===');
    
    try {
      console.log('Attempting authentication...');
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      console.log('Authentication successful for user:', userCredential.user.uid);

      // Initialize notifications after successful auth
      console.log('Initializing notifications...');
      const notificationResult = await initializeNotifications();
      console.log('Notification initialization result:', notificationResult);

    } catch (error) {
      console.error('Sign in error:', {
        code: error.code,
        message: error.message,
        stack: error.stack
      });
      throw error;
    } finally {
      setLoading(false);
      console.log('=== Sign In Process Complete ===');
    }
  };

  return { handleSignIn, loading };
};