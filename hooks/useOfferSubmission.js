// hooks/useOfferSubmission.js

import { useState } from 'react';
import { addOfferAndDiscussion, addToFavorites, getPostingOwner } from '../services/firebaseService';
import { auth } from '../firebase';

const useOfferSubmission = (postingId, navigation, itemName, itemDescription, itemLocation) => {
  const [price, setPrice] = useState('');
  const [description, setDescription] = useState('');
  const [postingOwnerId, setPostingOwnerId] = useState('');
  const currentUser = auth.currentUser;

  const fetchPostingOwner = async () => {
    try {
      const ownerId = await getPostingOwner(postingId);
      if (ownerId) {
        setPostingOwnerId(ownerId);
      } else {
        console.error('Error: No posting found for the provided postingId:', postingId);
      }
    } catch (error) {
      console.error('Error fetching posting owner:', error);
    }
  };

  const handlePriceChange = (value) => {
    if (/^\d*$/.test(value)) {
      setPrice(value);
    }
  };

  const handleSubmitOffer = async () => {
    if (!price.trim() || parseInt(price) <= 0) {
      throw new Error('Please enter a valid offer price greater than zero.');
    }
    if (!description.trim()) {
      throw new Error('Please enter a description for your offer.');
    }

    try {
      const offerData = {
        postingId,
        userId: currentUser.uid,
        price,
        description,
        status: 'Pending',
        timestamp: new Date(),
      };

      await addOfferAndDiscussion(offerData, postingOwnerId);
      await addToFavorites(currentUser.uid, postingId);

      navigation.navigate('PostingDetail', {
        postingId,
        itemName,
        itemDescription,
        itemLocation: itemLocation || { latitude: 0, longitude: 0 },
        userId: postingOwnerId,
      });
    } catch (error) {
      throw new Error('There was an error submitting your offer. Please try again.');
    }
  };

  return {
    price,
    description,
    postingOwnerId,
    setPostingOwnerId,
    handlePriceChange,
    setDescription,
    handleSubmitOffer,
    fetchPostingOwner,
  };
};

export default useOfferSubmission;