// useOffers.js

import { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { collection, query, where, orderBy } from 'firebase/firestore';
import { db } from '../firebase';
import { createAndRegisterListener } from '../services/firebaseService';
import { useFocusEffect } from '@react-navigation/native';

const useOffers = (postingId, componentId) => {
  // Stable state management
  const [state, setState] = useState({
    offers: [],
    error: null,
    isLoading: true
  });

  // Stable refs
  const mountedRef = useRef(false);
  const subscriptionRef = useRef({
    unsubscribe: null,
    id: `offers-${postingId}`,
    active: false,
    startTime: null,
    lastUpdate: null
  });
  
  // Add cleanup state tracking
  const cleanupState = useRef({
    isCleaningUp: false,
    cleanupStartTime: null,
    lastCleanupDuration: null,
    cleanupPromise: null
  });

  // Memoized query - only changes with postingId
  const offersQuery = useMemo(() => {
    if (!postingId || !db) return null;
    return query(
      collection(db, 'offers'),
      where('postingId', '==', postingId),
      orderBy('createdAt', 'desc')
    );
  }, [postingId]);

  // Stable update handler
  const handleOffersUpdate = useCallback((snapshot) => {
    if (!mountedRef.current) return;

    try {
      const offersData = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      console.log('[useOffers][Update]', {
        postingId,
        count: offersData.length,
        changes: snapshot.docChanges().map(change => ({
          type: change.type,
          id: change.doc.id,
          timestamp: new Date().toISOString()
        })),
        timestamp: new Date().toISOString()
      });

      subscriptionRef.current.lastUpdate = Date.now();

      setState(prev => ({
        ...prev,
        offers: offersData,
        isLoading: false
      }));
    } catch (err) {
      console.error('[useOffers][Error]', {
        error: err.message,
        postingId,
        timestamp: new Date().toISOString()
      });

      setState(prev => ({
        ...prev,
        error: err,
        isLoading: false
      }));
    }
  }, [postingId]);

  // Setup subscription function
  const setupSubscription = useCallback(() => {
    if (!postingId || !offersQuery) {
      setState(prev => ({ ...prev, isLoading: false }));
      return;
    }

    console.log('[useOffers][Subscribe] Setting up subscription', {
      postingId,
      timestamp: new Date().toISOString()
    });

    setState(prev => ({ ...prev, isLoading: true }));

    try {
      const { unsubscribe, listenerId } = createAndRegisterListener(
        'offers',
        offersQuery,
        handleOffersUpdate,
        (err) => {
          if (!mountedRef.current) return;
          console.error('[useOffers][Error]', err);
          setState(prev => ({
            ...prev,
            error: err,
            isLoading: false
          }));
        },
        componentId
      );

      subscriptionRef.current = {
        unsubscribe,
        id: listenerId,
        active: true,
        startTime: Date.now(),
        lastUpdate: null
      };

      return unsubscribe;
    } catch (err) {
      console.error('[useOffers][SetupError]', err);
      setState(prev => ({
        ...prev,
        error: err,
        isLoading: false
      }));
    }
  }, [postingId, offersQuery, handleOffersUpdate, componentId]);

  // RESTORED: useFocusEffect for immediate data availability on navigation
  // This ensures offers are available immediately when user returns from MakeOffer
  useFocusEffect(
    useCallback(() => {
      console.log('[useOffers][Focus] Screen focused, setting up subscription');
      mountedRef.current = true;

      // Set up subscription immediately on focus for immediate data availability
      const unsubscribe = setupSubscription();
      if (unsubscribe) {
        subscriptionRef.current.unsubscribe = unsubscribe;
      }

      return () => {
        console.log('[useOffers][Focus] Screen unfocused, cleaning up');
        if (subscriptionRef.current.unsubscribe) {
          subscriptionRef.current.unsubscribe();
        }
        mountedRef.current = false;
      };
    }, [setupSubscription])
  );

  // Mount effect for initial setup
  useEffect(() => {
    mountedRef.current = true;
    return () => {
      mountedRef.current = false;
    };
  }, []);

  return {
    ...state,
    hasSubscription: subscriptionRef.current.active,
    subscriptionId: subscriptionRef.current.id,
    cleanupState: {
      isCleaningUp: cleanupState.current.isCleaningUp,
      lastCleanupDuration: cleanupState.current.lastCleanupDuration
    },
    ensureCleanup: useCallback(async () => {
      if (subscriptionRef.current.unsubscribe) {
        subscriptionRef.current.unsubscribe();
        subscriptionRef.current.active = false;
      }
    }, []),
    unsubscribe: () => {
      if (subscriptionRef.current.unsubscribe) {
        console.log('[useOffers][Unsubscribe] Cleaning up subscription:', {
          postingId,
          subscriptionId: subscriptionRef.current.id,
          timestamp: new Date().toISOString()
        });
        subscriptionRef.current.unsubscribe();
        subscriptionRef.current.active = false;
      }
    }
  };
};

export default useOffers;
