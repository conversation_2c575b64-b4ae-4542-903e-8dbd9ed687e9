import { useState, useEffect } from 'react';
import { Alert } from 'react-native';
import { doc, getDoc, updateDoc } from 'firebase/firestore';
import { auth, db } from '../firebase';
import { NotificationType, NotificationSettings, NotificationSettingsHookResult } from '../types/notifications';

const defaultSettings: NotificationSettings = {
  [NotificationType.NEW_OFFER]: true,
  [NotificationType.OFFER_STATUS_CHANGE]: true,
  [NotificationType.NEW_MESSAGE]: true,
  [NotificationType.FAVORITE_POSTING_UPDATE]: true,
};

export const useNotificationSettings = (): NotificationSettingsHookResult => {
  const [settings, setSettings] = useState<NotificationSettings>(defaultSettings);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      const user = auth.currentUser;
      if (!user) {
        console.error('No user logged in');
        setError(new Error('No user logged in'));
        return;
      }

      const userRef = doc(db, 'users', user.uid);
      const userDoc = await getDoc(userRef);
      
      if (userDoc.exists()) {
        const userData = userDoc.data();
        if (userData.notificationSettings) {
          setSettings(userData.notificationSettings);
        } else {
          // If no settings exist, set defaults
          await updateDoc(userRef, {
            notificationSettings: defaultSettings
          });
          setSettings(defaultSettings);
        }
      }
    } catch (error) {
      console.error('Error loading notification settings:', error);
      setError(error instanceof Error ? error : new Error('Failed to load settings'));
      Alert.alert('Error', 'Failed to load notification settings');
    } finally {
      setLoading(false);
    }
  };

  const updateSetting = async (type: NotificationType, value: boolean) => {
    try {
      const user = auth.currentUser;
      if (!user) {
        console.error('No user logged in');
        setError(new Error('No user logged in'));
        return;
      }

      console.log(`Updating ${type} notification setting to ${value}`);

      const userRef = doc(db, 'users', user.uid);
      const newSettings = {
        ...settings,
        [type]: value
      };

      await updateDoc(userRef, {
        notificationSettings: newSettings
      });

      setSettings(newSettings);
      console.log(`Successfully updated ${type} setting to ${value}`);
    } catch (error) {
      console.error('Error updating notification setting:', error);
      setError(error instanceof Error ? error : new Error('Failed to update setting'));
      Alert.alert('Error', 'Failed to update notification setting');
    }
  };

  return {
    settings,
    updateSetting,
    loading,
    error
  };
};

export default useNotificationSettings; 