import { useState, useCallback, useRef } from 'react';
import { fetchPostingsPreview, fetchPostingsBySearch } from '../services/firebaseService';
import { addSubscription } from '../utils/firebaseCleanup';

export function useControlledPostings() {
  const [postings, setPostings] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const lastFetchRegion = useRef(null);
  const NUMBER_OF_RESULTS = 10;

  const getMapBounds = (region) => {
    return {
      north: region.latitude + (region.latitudeDelta / 2),
      south: region.latitude - (region.latitudeDelta / 2),
      east: region.longitude + (region.longitudeDelta / 2),
      west: region.longitude - (region.longitudeDelta / 2)
    };
  };

  const fetchPostings = useCallback(async (region, searchTerm = '') => {
    setIsLoading(true);
    console.log('[useControlledPostings] Fetching postings for region:', region);
    
    try {
      const mapBounds = getMapBounds(region);
      const result = await fetchPostingsBySearch(
        searchTerm,
        null,
        mapBounds,
        NUMBER_OF_RESULTS,
        !!searchTerm
      );

      console.log('[useControlledPostings] Fetched postings:', result.postings.length);
      setPostings(result.postings);
      lastFetchRegion.current = region;
    } catch (err) {
      console.error('[useControlledPostings] Error:', err);
      setError(err);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const searchPostings = useCallback(async (searchTerm, region) => {
    console.log('[useControlledPostings] Searching postings:', searchTerm);
    return fetchPostings(region, searchTerm);
  }, [fetchPostings]);

  return {
    postings,
    isLoading,
    error,
    fetchPostings,
    searchPostings
  };
}
