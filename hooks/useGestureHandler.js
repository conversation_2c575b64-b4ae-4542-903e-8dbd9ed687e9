import { useState, useCallback, useRef, useEffect } from 'react';
import { 
  PanGestureHandlerGestureEvent, 
  State as GestureState 
} from 'react-native-gesture-handler';
import { Animated, Platform } from 'react-native';

/**
 * @typedef {Object} GestureHandlerResult
 * @property {function(import('react-native-gesture-handler').PanGestureHandlerGestureEvent)} handleGesture
 * @property {import('react-native-reanimated').Value} listHeight
 */

/**
 * @param {number} [initialHeight=250]
 * @param {number} [maxHeight=600]
 * @returns {GestureHandlerResult}
 */
export const useGestureHandler = (initialHeight = 250, maxHeight = 600) => {
  const listHeightRef = useRef(/** @type {Animated.Value|undefined} */ (undefined));

  useEffect(() => {
    if (!listHeightRef.current) {
      listHeightRef.current = new Animated.Value(initialHeight);
    }
  }, [initialHeight]);

  const [currentHeight, setCurrentHeight] = useState(initialHeight);
  
  const gestureState = useRef({
    startHeight: initialHeight,
    lastY: 0
  }).current;

  const handleGesture = useCallback((event) => {
    if (!listHeightRef.current) return;

    const { translationY, state } = event.nativeEvent;

    switch (state) {
      case GestureState.BEGAN:
        gestureState.startHeight = currentHeight;
        gestureState.lastY = 0;
        break;

      case GestureState.ACTIVE:
        const deltaY = translationY - gestureState.lastY;
        const newHeight = Math.max(
          initialHeight, 
          Math.min(
            gestureState.startHeight - deltaY, 
            maxHeight
          )
        );

        Animated.spring(listHeightRef.current, {
          toValue: newHeight,
          useNativeDriver: false,
          tension: 40,
          friction: 8
        }).start();

        setCurrentHeight(newHeight);
        gestureState.lastY = translationY;
        break;

      case GestureState.END:
        const snapHeights = [initialHeight, maxHeight / 2, maxHeight];
        const closestSnapHeight = snapHeights.reduce((prev, curr) => 
          Math.abs(curr - currentHeight) < Math.abs(prev - currentHeight) 
            ? curr 
            : prev
        );

        Animated.spring(listHeightRef.current, {
          toValue: closestSnapHeight,
          useNativeDriver: false,
          tension: 40,
          friction: 8
        }).start();

        setCurrentHeight(closestSnapHeight);
        break;
    }
  }, [currentHeight, initialHeight, maxHeight]);

  /** @type {GestureHandlerResult} */
  return {
    handleGesture,
    listHeight: listHeightRef.current
  };
};