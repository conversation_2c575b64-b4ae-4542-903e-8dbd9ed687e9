// useAddPosting.js

import { useState } from 'react';
import { addPosting as addPostingService } from '../services/firebaseService';

export const useAddPosting = () => {
  const [loading, setLoading] = useState(false);

  const addPosting = async (postingData) => {
    setLoading(true);
    try {
      await addPostingService(postingData);
    } catch (error) {
      setLoading(false);
      throw error;
    }
    setLoading(false);
  };

  return { addPosting, loading };
};