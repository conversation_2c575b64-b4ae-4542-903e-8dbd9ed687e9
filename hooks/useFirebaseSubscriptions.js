import { useEffect, useCallback, useState } from 'react';
import { fetchAllPostings } from '../services/firebaseService';

export function useFirebaseSubscriptions() {
  const [postings, setPostings] = useState([]);
  const NUMBER_OF_RESULTS = 10;

  const fetchInitialData = useCallback(async () => {
    try {
      const fetchedPostings = await fetchAllPostings(NUMBER_OF_RESULTS);
      
      if (!Array.isArray(fetchedPostings)) {
        setPostings([]);
        return;
      }

      const transformedPostings = fetchedPostings.map(posting => ({
        ...posting,
        location: {
          latitude: posting.latitude,
          longitude: posting.longitude
        }
      }));

      const validPostings = transformedPostings.filter(posting => {
        const isValid = posting && 
          posting.location && 
          typeof posting.location.latitude === 'number' && 
          typeof posting.location.longitude === 'number';
        return isValid;
      });
      
      setPostings(validPostings);
    } catch (error) {
      console.error('Error fetching initial data:', error);
      setPostings([]);
    }
  }, []);

  const handleLoadMore = () => {
    // Implement load more logic here
  };

  useEffect(() => {
    fetchInitialData();
  }, [fetchInitialData]);

  return { postings, handleLoadMore, setPostings };
}