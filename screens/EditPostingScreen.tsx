// EditPostingsScreen.tsx

import React, { useState, useEffect, useRef } from 'react';
import { View, Text, Button, StyleSheet, Alert, ActivityIndicator, Keyboard, TouchableWithoutFeedback } from 'react-native';
import MapView, { Marker, Region } from 'react-native-maps';
import { useNavigation, useRoute } from '@react-navigation/native';
import { StackNavigationProp, RouteProp } from '@react-navigation/stack';
import { RootStackParamList, NavigationFlowMetrics } from '../types/navigation';
import { updatePostingDetails } from '../services/firebaseService';
import { usePostingDetails } from '../hooks/usePostingDetails';
import FormInput from '../components/FormInput';

type EditPostingScreenProps = {
  navigation: StackNavigationProp<RootStackParamList, 'EditPosting'>;
  route: RouteProp<RootStackParamList, 'EditPosting'>;
};

interface LocationState {
  latitude: number;
  longitude: number;
  latitudeDelta: number;
  longitudeDelta: number;
}

interface PostingDetails {
  title: string;
  description: string;
  latitude: number;
  longitude: number;
}

interface MapPressEvent {
  nativeEvent: {
    coordinate: {
      latitude: number;
      longitude: number;
    };
  };
}

const EditPostingScreen: React.FC<EditPostingScreenProps> = ({ route, navigation }) => {
  // Update metrics ref to use performance.now() for high-precision timing
  const metrics = useRef({
    navigationComplete: performance.now(),
    dataLoadStart: 0,
    dataLoadComplete: 0,
    flowMetrics: route.params.flowMetrics,
    events: [] as Array<{
      name: string;
      timestamp: number;
      duration?: number;
      preciseTimestamp: number;
    }>
  });

  // Enhanced metrics logging effect
  useEffect(() => {
    const mountTime = performance.now();
    const navigationDuration = mountTime - (route.params.flowMetrics?.startTime || mountTime);
    
    console.log('[EditPostingScreen][Navigation] Screen mounted:', {
      incoming: {
        flowMetrics: route.params.flowMetrics,
        navigationDuration: navigationDuration.toFixed(3), // milliseconds with 3 decimal places
        preciseTime: {
          start: route.params.flowMetrics?.startTime,
          mount: mountTime
        }
      },
      screen: {
        mountTime: mountTime.toFixed(3),
        postingId: route.params.postingId
      },
      timestamp: new Date().toISOString()
    });

    // Track data loading with precise timing
    metrics.current.dataLoadStart = performance.now();
    
    return () => {
      const cleanupTime = performance.now();
      console.log('[EditPostingScreen][Navigation] Screen cleanup:', {
        flowMetrics: {
          ...metrics.current,
          totalDuration: (cleanupTime - metrics.current.navigationComplete).toFixed(3)
        },
        timestamp: new Date().toISOString()
      });
    };
  }, [route.params]);

  // Add defensive checks and proper initialization
  const postingId = route?.params?.postingId || '';

  // Replace usePostingDetails with initial data from navigation params
  const { initialData } = route.params;

  // Use local state instead of real-time hook
  const [postingDetails, setPostingDetails] = useState(initialData);

  // Enhanced data load completion tracking
  useEffect(() => {
    if (postingDetails) {
      const loadCompleteTime = performance.now();
      const loadDuration = loadCompleteTime - metrics.current.dataLoadStart;
      
      metrics.current.dataLoadComplete = loadCompleteTime;
      metrics.current.events.push({
        name: 'dataLoad',
        timestamp: Date.now(),
        preciseTimestamp: loadCompleteTime,
        duration: loadDuration
      });

      console.log('[EditPostingScreen][Data] Load complete:', {
        timing: {
          duration: loadDuration.toFixed(3),
          start: metrics.current.dataLoadStart.toFixed(3),
          complete: loadCompleteTime.toFixed(3)
        },
        postingId: route.params.postingId,
        timestamp: new Date().toISOString()
      });
    }
  }, [postingDetails]);

  
  


  // Add debug logging
  console.log('[EditPostingScreen][Debug] Hook initialization:', {
    hookSetup: {
      hookImport: typeof usePostingDetails,
      postingId,
      hasPostingDetails: !!postingDetails,
      loading: false,
      error: false
    },
    timestamp: new Date().toISOString()
  });

  // Defensive parameter extraction
  const params = route?.params ?? {};
  const {
    itemName: initialName,
    itemDescription: initialDesc,
    itemLocation: initialLocation
  } = params;

  console.log('[EditPostingScreen] Extracted params:', {
    postingId,
    hasName: !!initialName,
    hasDesc: !!initialDesc,
    hasLocation: !!initialLocation,
    timestamp: new Date().toISOString()
  });

  // Initialize hooks with defensive checks
  const [title, setTitle] = useState(initialName ?? '');
  const [description, setDescription] = useState(initialDesc ?? '');
  const [location, setLocation] = useState<LocationState>({
    latitude: initialLocation?.latitude ?? 0,
    longitude: initialLocation?.longitude ?? 0,
    latitudeDelta: 0.05,
    longitudeDelta: 0.05
  });

  // Effect to update state if posting details change
  useEffect(() => {
    if (postingDetails) {
      console.log('[EditPostingScreen] Updating from posting details:', {
        hasTitle: Boolean(postingDetails.title),
        hasDesc: Boolean(postingDetails.description),
        timestamp: new Date().toISOString()
      });

      setTitle(postingDetails.title);
      setDescription(postingDetails.description);
      setLocation(prev => ({
        ...prev,
        latitude: postingDetails.latitude,
        longitude: postingDetails.longitude,
      }));
    }
  }, [postingDetails]);

  // Add missing state declarations
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Define formData based on current state
  const formData = {
    title,
    description,
    latitude: location.latitude,
    longitude: location.longitude
  };

  // Replace loading check with proper state
  if (!postingDetails) {
    return <ActivityIndicator testID="loading-indicator" size="large" color="#0000ff" />;
  }

  const handleMapPress = (event: MapPressEvent) => {
    const { latitude, longitude } = event.nativeEvent.coordinate;
    setLocation((prevLocation) => ({
      ...prevLocation,
      latitude,
      longitude,
    }));
  };

  const handleSaveChanges = async () => {
    try {
      setIsSaving(true);
      setError(null);
      
      console.log('[EditPostingScreen][Save] Starting update:', {
        postingId,
        updates: formData,
        timestamp: new Date().toISOString()
      });

      // Use existing updatePostingDetails function
      await updatePostingDetails(postingId, formData);

      console.log('[EditPostingScreen][Save] Update successful:', {
        postingId,
        timestamp: new Date().toISOString()
      });

      navigation.goBack();
    } catch (err) {
      console.error('[EditPostingScreen][Save] Update failed:', {
        error: err instanceof Error ? {
          message: err.message,
          stack: err.stack
        } : err,
        postingId,
        timestamp: new Date().toISOString()
      });
      setError('Failed to update posting. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
      <View style={styles.container}>
        <Text style={styles.header}>Edit Posting</Text>

        <FormInput
          label="Title:"
          value={title}
          onChangeText={setTitle}
          placeholder="Enter item name"
        />

        <FormInput
          label="Description:"
          value={description}
          onChangeText={setDescription}
          placeholder="Enter item description"
        />

        <Text style={styles.label}>Update Location:</Text>
        {location.latitude !== 0 && location.longitude !== 0 ? (
          <MapView
            style={styles.map}
            initialRegion={location}
            onRegionChangeComplete={(region: Region) => {
              setLocation((prevLocation) => ({
                ...prevLocation,
                latitudeDelta: region.latitudeDelta,
                longitudeDelta: region.longitudeDelta,
              }));
            }}
            onPress={handleMapPress}
          >
            <Marker
              coordinate={location}
              draggable
              onDragEnd={(e) =>
                setLocation((prevLocation) => ({
                  ...prevLocation,
                  latitude: e.nativeEvent.coordinate.latitude,
                  longitude: e.nativeEvent.coordinate.longitude,
                }))
              }
            />
          </MapView>
        ) : (
          <Text>Loading map...</Text>
        )}

        <Button title="Save Changes" onPress={handleSaveChanges} />
      </View>
    </TouchableWithoutFeedback>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: 'white',
  },
  header: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    marginBottom: 8,
  },
  map: {
    width: '100%',
    height: 200,
    marginBottom: 16,
  },
});

export default EditPostingScreen;
