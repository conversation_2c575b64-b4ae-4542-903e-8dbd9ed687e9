// CreatePostingScreen.tsx

import React, { useState } from 'react';
import { View, Text, Button, StyleSheet, Alert, Keyboard, TouchableWithoutFeedback } from 'react-native';
import MapView, { Marker } from 'react-native-maps';
import { useNavigation } from '@react-navigation/native';
import FormInput from '../components/FormInput';
import { useAddPosting } from '../hooks/useAddPosting'; // Custom hook for adding posting
import { useFormValidation } from '../hooks/useFormValidation'; // Custom hook for form validation
import { useAuthUser } from '../hooks/useAuthUser'; // Custom hook to get current user

function CreatePostingScreen(): React.JSX.Element {
  const [itemName, setItemName] = useState('');
  const [description, setDescription] = useState('');
  const [location, setLocation] = useState({
    latitude: 37.78825,
    longitude: -122.4324,
  });
  const navigation = useNavigation();
  const currentUser = useAuthUser(); // Get current user using a custom hook
  const { addPosting, loading } = useAddPosting(); // Using the custom hook for adding posting

  // Use the form validation hook
  const { validate, validationErrors } = useFormValidation({
    itemName,
    description,
  });

  const handleMapPress = (event) => {
    const { latitude, longitude } = event.nativeEvent.coordinate;
    setLocation({ latitude, longitude });
  };

  const handleSubmitPosting = async () => {
    if (!validate()) {
      Alert.alert('Validation Error', validationErrors.join('\n'));
      return;
    }

    if (!currentUser) {
      console.error('No user is logged in');
      return;
    }

    try {
      await addPosting({
        title: itemName,
        description,
        latitude: location.latitude,
        longitude: location.longitude,
        userId: currentUser.uid,
        postingStatus: 'Active',
      });
      Alert.alert('Success', 'Your posting has been successfully created.', [
        { text: 'OK', onPress: () => navigation.navigate('Home') },
      ]);
    } catch (error) {
      Alert.alert('Error', 'There was an error adding the posting. Please try again.');
    }
  };

  return (
    <TouchableWithoutFeedback onPress={() => Keyboard.dismiss()}>
      <View style={styles.container}>
        <FormInput
          label="Item Name:"
          value={itemName}
          onChangeText={setItemName}
          placeholder="Enter item name"
        />

        <FormInput
          label="Description:"
          value={description}
          onChangeText={setDescription}
          placeholder="Enter item description"
          multiline
        />

        <Text style={styles.label}>Pick Location:</Text>
        <MapView
          style={styles.map}
          initialRegion={{
            latitude: location.latitude,
            longitude: location.longitude,
            latitudeDelta: 0.0922,
            longitudeDelta: 0.0421,
          }}
          onPress={handleMapPress}
        >
          <Marker coordinate={location} />
        </MapView>

        <Button title={loading ? 'Submitting...' : 'Submit Posting'} onPress={handleSubmitPosting} />
      </View>
    </TouchableWithoutFeedback>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 16,
  },
  map: {
    width: '100%',
    height: 200,
    marginTop: 16,
  },
});

export default CreatePostingScreen;