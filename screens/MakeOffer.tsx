// MakeOffer.tsx

import React, { useState, useEffect, useRef } from 'react';
import { View, Text, Button, StyleSheet, Alert, KeyboardAvoidingView, Platform, ScrollView } from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { auth, db } from '../firebase';
import { getPostingOwner, addOfferAndDiscussion, getListenerStats } from '../services/firebaseService';
import FormInput from '../components/FormInput';
import { doc, getDoc } from 'firebase/firestore';
import { RestrictionMessage } from '../components/RestrictionMessage';

interface RouteParams {
  postingId: string;
  itemName: string;
  itemDescription: string;
  itemLocation?: {
    latitude: number;
    longitude: number;
  };
  userId: string;
}

function MakeOffer(): React.JSX.Element {
  const route = useRoute();
  const { postingId, itemName, itemDescription, itemLocation, userId } = route.params as RouteParams;
  const [price, setPrice] = useState('');
  const [description, setDescription] = useState('');
  const navigation = useNavigation();
  const currentUser = auth.currentUser;
  const [postingOwnerId, setPostingOwnerId] = useState(userId);
  const [restrictionEndDate, setRestrictionEndDate] = useState<Date | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Add cleanup verification state
  const cleanupVerification = useRef({
    lastVerified: Date.now(),
    hasActiveListeners: false,
    pendingCleanup: false
  });

  // Add cleanup verification effect
  useEffect(() => {
    const verifyCleanup = () => {
      const stats = getListenerStats();
      console.log('[MakeOffer][Verify] Checking for active listeners:', {
        stats,
        timestamp: new Date().toISOString()
      });

      cleanupVerification.current = {
        lastVerified: Date.now(),
        hasActiveListeners: stats.totalListeners > 0,
        pendingCleanup: false
      };

      if (stats.totalListeners > 0) {
        console.warn('[MakeOffer][Verify] Found active listeners:', {
          count: stats.totalListeners,
          byCollection: stats.byCollection,
          timestamp: new Date().toISOString()
        });
      }
    };

    // Initial verification
    verifyCleanup();

    // Set up periodic verification in development
    if (process.env.NODE_ENV === 'development') {
      const interval = setInterval(verifyCleanup, 5000);
      return () => clearInterval(interval);
    }
  }, []);

  useEffect(() => {
    const checkUserRestrictions = async () => {
      if (!currentUser) return;

      try {
        console.log('[MakeOffer] Checking user restrictions for:', currentUser.uid);
        const userDoc = await getDoc(doc(db, 'users', currentUser.uid));
        
        if (userDoc.exists()) {
          const userData = userDoc.data();
          if (userData.restrictionEndDate) {
            const endDate = userData.restrictionEndDate.toDate();
            if (endDate > new Date()) {
              console.log('[MakeOffer] User is restricted until:', endDate);
              setRestrictionEndDate(endDate);
            }
          }
        }
      } catch (error) {
        console.error('[MakeOffer] Error checking user restrictions:', {
          error: error instanceof Error ? {
            message: error.message,
            stack: error.stack
          } : error,
          userId: currentUser.uid
        });
      } finally {
        setIsLoading(false);
      }
    };

    checkUserRestrictions();
  }, [currentUser]);

  useEffect(() => {
    if (!postingId) {
      console.error('[MakeOffer] Error: postingId is undefined');
      return;
    }

    const fetchPostingOwner = async () => {
      try {
        console.log('[MakeOffer] Fetching posting owner for postingId:', postingId);
        const ownerId = await getPostingOwner(postingId);
        if (ownerId) {
          console.log('[MakeOffer] Found posting owner:', ownerId);
          setPostingOwnerId(ownerId);
        } else {
          console.error('[MakeOffer] Error: No posting found for postingId:', postingId);
        }
      } catch (error) {
        console.error('[MakeOffer] Error fetching posting owner:', {
          error: error instanceof Error ? {
            message: error.message,
            stack: error.stack
          } : error,
          postingId
        });
      }
    };

    if (!postingOwnerId) {
      fetchPostingOwner();
    }
  }, [postingId, postingOwnerId]);

  const handlePriceChange = (value: string) => {
    if (/^\d*$/.test(value)) {
      setPrice(value);
    }
  };

  const handleSubmitOffer = async () => {
    if (restrictionEndDate && restrictionEndDate > new Date()) {
      Alert.alert(
        'Restricted Access',
        'You are currently restricted from submitting offers. Please try again after the restriction period ends.'
      );
      return;
    }

    console.log('[MakeOffer] Submitting offer:', {
      postingId,
      price,
      description: description.length > 50 ? description.substring(0, 50) + '...' : description
    });

    if (!price.trim() || parseInt(price) <= 0) {
      Alert.alert('Validation Error', 'Please enter a valid offer price greater than zero.');
      return;
    }
    if (!description.trim()) {
      Alert.alert('Validation Error', 'Please enter a description for your offer.');
      return;
    }
  
    try {
      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      const offerData = {
        postingId,
        userId: currentUser.uid,
        price: parseInt(price),
        description,
        status: 'pending',
        timestamp: new Date(),
      };
  
      console.log('[MakeOffer] Creating offer with data:', {
        postingId,
        price: offerData.price,
        postingOwnerId
      });

      // Add offer and initialize discussion
      const offerId = await addOfferAndDiscussion(offerData, postingOwnerId);
      console.log('[MakeOffer] Offer created successfully:', { offerId });
  
      // Verify cleanup before navigation
      const stats = getListenerStats();
      if (stats.totalListeners > 0) {
        console.warn('[MakeOffer] Active listeners detected before navigation:', {
          count: stats.totalListeners,
          byCollection: stats.byCollection,
          timestamp: new Date().toISOString()
        });
      }

      // Navigate back to PostingDetail screen
      navigation.navigate('PostingDetail', {
        postingId,
        itemLocation,
        userId,
        itemName
      });
    } catch (error) {
      console.error('[MakeOffer] Error submitting offer:', {
        error: error instanceof Error ? {
          message: error.message,
          stack: error.stack
        } : error,
        postingId,
        price
      });
      Alert.alert('Error', 'There was an error submitting your offer. Please try again.');
    }
  };

  if (isLoading) {
    return (
      <View style={[styles.container, styles.loadingContainer]}>
        <Text>Checking user status...</Text>
      </View>
    );
  }

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={{ flex: 1 }}
    >
      <ScrollView contentContainerStyle={styles.container}>
        <Text style={styles.title}>Make an Offer</Text>

        {restrictionEndDate && restrictionEndDate > new Date() ? (
          <RestrictionMessage
            restrictionEndDate={restrictionEndDate}
            style={styles.restrictionMessage}
          />
        ) : (
          <>
            <FormInput
              label="Offer Price"
              value={price}
              onChangeText={handlePriceChange}
              placeholder="Enter your offer price"
              keyboardType="numeric"
            />
            <FormInput
              label="Description"
              value={description}
              onChangeText={setDescription}
              placeholder="Enter a description for your offer"
              multiline
            />
            <Button title="Submit Offer" onPress={handleSubmitOffer} />
          </>
        )}
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flexGrow: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
    backgroundColor: 'white',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  restrictionMessage: {
    marginVertical: 24,
    width: '100%',
  },
});

export default MakeOffer;
