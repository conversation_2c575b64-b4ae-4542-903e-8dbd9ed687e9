import React, { useState, useEffect } from 'react';
import { View, FlatList, StyleSheet, TouchableOpacity, Text, ActivityIndicator, Alert } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NotificationData } from '../types/firebase';
import { subscribeToNotifications, markNotificationAsRead } from '../services/notificationService';
import Ionicons from 'react-native-vector-icons/Ionicons';
import useNotifications from '../hooks/useNotifications';
import { NotificationType } from '../types/notifications';
import { getRelativeTime } from '../utils/dateUtils';

const NotificationsScreen = () => {
  const [notifications, setNotifications] = useState<NotificationData[]>([]);
  const [loading, setLoading] = useState(true);
  const navigation = useNavigation();

  useEffect(() => {
    let unsubscribe: (() => void) | undefined;

    const setupSubscription = async () => {
      try {
        console.log('Setting up notifications subscription');
        unsubscribe = await subscribeToNotifications((updatedNotifications) => {
          const processedNotifications = updatedNotifications.map(notification => ({
            ...notification,
            createdAt: notification.createdAt || new Date(),
            title: notification.title || '',
            body: notification.body || '',
            read: !!notification.read,
          }));

          console.log(`Received ${processedNotifications.length} notifications`);
          setNotifications(processedNotifications);
          setLoading(false);
        });
      } catch (error) {
        console.error('Error in notifications subscription:', error);
        setLoading(false);
      }
    };

    setupSubscription();

    return () => {
      try {
        if (unsubscribe) {
          console.log('Cleaning up notifications subscription');
          unsubscribe();
        }
      } catch (error) {
        console.error('Error cleaning up notifications subscription:', error);
      }
    };
  }, []);

  const handleNotificationPress = async (notification: NotificationData) => {
    try {
      console.log('[NotificationsScreen][handleNotificationPress] Starting with:', {
        notificationId: notification.id,
        type: notification.type,
        offerId: notification.offerId,
        hasData: !!notification.data,
        stack: new Error().stack
      });

      if (!notification.read) {
        await markNotificationAsRead(notification.id);
      }

      switch (notification.type) {
        case NotificationType.NEW_OFFER:
        case NotificationType.OFFER_STATUS_CHANGE:
        case NotificationType.NEW_MESSAGE:
          if (!notification.offerId) {
            console.warn('[NotificationsScreen] Missing offerId:', {
              type: notification.type,
              notification: notification
            });
            return;
          }

          console.log('[NotificationsScreen] Navigating to OfferDetail:', {
            offerId: notification.offerId,
            type: notification.type
          });

          navigation.navigate('OfferDetail', {
            offerId: notification.offerId,
            postingId: notification.postingId || '',
            offerOwnerId: notification.data?.offerOwnerId || notification.senderId,
            postingOwnerId: notification.data?.postingOwnerId
          });
          break;

        case NotificationType.FAVORITE_POSTING_UPDATE:
          if (!notification.postingId) {
            console.warn('Missing postingId for posting update notification:', notification);
            return;
          }
          navigation.navigate('PostingDetail', {
            postingId: notification.postingId,
            itemLocation: null,
            userId: notification.senderId,
            itemName: '',
            itemDescription: '',
          });
          break;

        case NotificationType.SYSTEM_WITHDRAWAL:
          console.log('[NotificationsScreen] Processing SYSTEM_WITHDRAWAL notification:', {
            notification: {
              id: notification.id,
              type: notification.type,
              postingId: notification.postingId,
              offerId: notification.offerId,
              senderId: notification.senderId,
              recipientId: notification.recipientId,
              createdAt: notification.createdAt,
              read: notification.read
            },
            timestamp: new Date().toISOString()
          });

          if (!notification.postingId) {
            console.warn('[NotificationsScreen] Missing postingId in SYSTEM_WITHDRAWAL notification:', {
              type: notification.type,
              notification: notification,
              timestamp: new Date().toISOString()
            });
            return;
          }

          if (!notification.offerId) {
            console.warn('[NotificationsScreen] Missing offerId in SYSTEM_WITHDRAWAL notification:', {
              type: notification.type,
              notification: notification,
              timestamp: new Date().toISOString()
            });
            // Continue anyway as we'll navigate to posting
          }

          // For SYSTEM_WITHDRAWAL, we should navigate to OfferDetail if we have offerId
          if (notification.offerId) {
            console.log('[NotificationsScreen] Navigating to OfferDetail for withdrawn offer:', {
              offerId: notification.offerId,
              postingId: notification.postingId,
              type: notification.type,
              timestamp: new Date().toISOString()
            });

            navigation.navigate('OfferDetail', {
              offerId: notification.offerId,
              postingId: notification.postingId,
              postingOwnerId: notification.senderId,
              offerOwnerId: notification.recipientId
            });
          } else {
            // Fallback to PostingDetail if no offerId
            console.log('[NotificationsScreen] Navigating to PostingDetail (fallback):', {
              postingId: notification.postingId,
              type: notification.type,
              timestamp: new Date().toISOString()
            });

            navigation.navigate('PostingDetail', {
              postingId: notification.postingId,
              itemLocation: null,
              userId: notification.senderId,
              itemName: '',
              itemDescription: '',
            });
          }
          break;

        default:
          console.warn(`Unhandled notification type: ${notification.type}`);
      }
    } catch (error) {
      console.error('Error handling notification:', error);
      Alert.alert(
        'Error',
        'There was a problem processing this notification. Please try again.'
      );
    }
  };

  const getNotificationIcon = (type: NotificationType): string => {
    switch (type) {
      case NotificationType.NEW_OFFER:
        return 'pricetag-outline';
      case NotificationType.NEW_MESSAGE:
        return 'chatbubble-outline';
      case NotificationType.FAVORITE_POSTING_UPDATE:
        return 'heart-outline';
      case NotificationType.OFFER_STATUS_CHANGE:
        return 'refresh-outline';
      case NotificationType.SYSTEM_WITHDRAWAL:
        return 'alert-circle-outline';
      default:
        return 'notifications-outline';
    }
  };

  const renderNotification = ({ item }: { item: NotificationData & { id: string } }) => {
    if (!item) {
      console.warn('Received invalid notification item');
      return null;
    }

    // Handle Firestore timestamp
    let timestamp = '';
    if (item.createdAt) {
      const date = item.createdAt.toDate ? item.createdAt.toDate() : new Date(item.createdAt);
      timestamp = getRelativeTime(date);
    }

    return (
      <TouchableOpacity
        style={[styles.notificationItem, !item.read && styles.unread]}
        onPress={() => handleNotificationPress(item)}
      >
        <View style={styles.iconContainer}>
          <Ionicons
            name={getNotificationIcon(item.type)}
            size={24}
            color={item.read ? '#666' : '#007AFF'}
          />
        </View>
        <View style={styles.contentContainer}>
          <Text style={styles.title}>{item.title || ''}</Text>
          <Text style={styles.body} numberOfLines={2}>
            {item.body || ''}
          </Text>
          {timestamp ? <Text style={styles.timestamp}>{timestamp}</Text> : null}
        </View>
      </TouchableOpacity>
    );
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator testID="loading-indicator" size="large" />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <FlatList
        data={notifications}
        renderItem={renderNotification}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContent}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>No notifications yet</Text>
          </View>
        }
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  listContent: {
    padding: 16,
  },
  notificationItem: {
    flexDirection: 'row',
    backgroundColor: 'white',
    padding: 16,
    marginBottom: 8,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
  },
  unread: {
    backgroundColor: '#f0f9ff',
  },
  iconContainer: {
    marginRight: 16,
    justifyContent: 'center',
  },
  contentContainer: {
    flex: 1,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  body: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  timestamp: {
    fontSize: 12,
    color: '#999',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 32,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
  },
});

export default NotificationsScreen;