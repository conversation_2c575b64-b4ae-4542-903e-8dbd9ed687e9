// LocationSettings.tsx



import React, { useState } from 'react';
import { View, Text, Button, StyleSheet, Alert } from 'react-native';
import MapView, { Marker } from 'react-native-maps';
import { useNavigation } from '@react-navigation/native';
import { saveUserLocation } from '../services/firebaseService';

const LocationSettings = () => {
  const [selectedLocation, setSelectedLocation] = useState(null);
  const navigation = useNavigation();

  const handleMapPress = (event) => {
    const { latitude, longitude } = event.nativeEvent.coordinate;
    setSelectedLocation({ latitude, longitude });
  };

  const confirmLocation = async () => {
    if (selectedLocation) {
      try {
        await saveUserLocation(selectedLocation);
        Alert.alert("Location Saved", "Your location has been updated.");
        navigation.navigate('Home', { userLocation: selectedLocation }); // Pass the location as a parameter
      } catch (error) {
        Alert.alert("Error", "Failed to save location. Please try again.");
      }
    } else {
      Alert.alert("No Location Selected", "Please select a location on the map.");
    }
  };

  const cancelSelection = () => {
    navigation.navigate('Home');
  };

  return (
    <View style={styles.container}>
      <Text style={styles.header}>Mark your preferred location on the map</Text>
      <MapView
        style={styles.map}
        initialRegion={{
          latitude: 37.7749, // Default to San Francisco
          longitude: -122.4194,
          latitudeDelta: 0.09,
          longitudeDelta: 0.04,
        }}
        onPress={handleMapPress}
      >
        {selectedLocation && (
          <Marker
            coordinate={selectedLocation}
            title="Selected Location"
            description="This is your chosen location."
          />
        )}
      </MapView>
      <View style={styles.buttonContainer}>
        <Button title="Confirm Location" onPress={confirmLocation} />
        <Button title="Cancel" onPress={cancelSelection} color="gray" />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    fontSize: 18,
    textAlign: 'center',
    marginVertical: 10,
  },
  map: {
    flex: 1,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: 10,
  },
});

export default LocationSettings;