// ProfileScreen.tsx

import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, FlatList } from 'react-native';
import { useProfileActions } from '../hooks/useProfileActions';

function ProfileScreen(): React.JSX.Element {
  const { options, handleOptionPress } = useProfileActions();

  return (
    <View testID="profile-container" style={styles.container}>
      <FlatList
        data={options}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <TouchableOpacity
            testID="profile-list-item"
            onPress={() => handleOptionPress(item)}
            style={styles.touchable}
          >
            <View style={styles.listItem}>
              <Text testID="profile-list-item-text" style={styles.listItemText}>
                {item.title}
              </Text>
            </View>
          </TouchableOpacity>
        )}
        contentContainerStyle={styles.listContainer}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 20,
    backgroundColor: 'white',
  },
  listContainer: {
    flexGrow: 1,
  },
  touchable: {
    opacity: 1,
  },
  listItem: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#ccc',
  },
  listItemText: {
    fontSize: 16,
  },
});

export default ProfileScreen;