// EditOffer.tsx

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { 
  View, 
  Text, 
  Button, 
  StyleSheet, 
  Alert, 
  Keyboard, 
  TouchableWithoutFeedback, 
  ScrollView, 
  TouchableOpacity,
  TextInput,
  StyleProp,
  ViewStyle
} from 'react-native';
import { useRoute, useNavigation, RouteProp, ParamListBase } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { doc, getDoc } from 'firebase/firestore';
import { db } from '../firebase';
import { Ionicons } from '@expo/vector-icons';
import { auth } from '../firebase';
import useOfferDetails from '../hooks/useOfferDetails';
import { useAuthUser } from '../hooks/useAuthUser';
import useWithdrawOffer from '../hooks/useWithdrawOffer';
import FormInput from '../components/FormInput';
import { NotificationType } from '../types/notifications';
import { updateOffer } from '../services/firebaseService';

// Update FormInput props interface
interface FormInputProps {
  label: string;
  value: string;
  onChangeText: (text: string) => void;
  placeholder: string;
  multiline?: boolean;
  keyboardType?: 'default' | 'numeric';
  style?: StyleProp<ViewStyle>;
  testID?: string;
}

// Update OfferDetails interface
interface OfferDetails {
  id: string;
  price: string | number;
  description: string;
  postingId: string;
  postingOwnerId: string;
  status: string;
  updatedAt?: Date;
  [key: string]: any;
}

// Update route params interface
interface RootStackParamList extends ParamListBase {
  EditOffer: {
    offerId: string;
  };
  OfferDetail: {
    offerId: string;
    postingId?: string;
    offerOwnerId?: string;
    refresh?: boolean;
    initialOffer?: Partial<OfferDetails>;
  };
}

type EditOfferScreenRouteProp = RouteProp<RootStackParamList, 'EditOffer'>;
type EditOfferScreenNavigationProp = StackNavigationProp<RootStackParamList, 'EditOffer'>;

function EditOffer(): React.JSX.Element {
  const route = useRoute<EditOfferScreenRouteProp>();
  const navigation = useNavigation<EditOfferScreenNavigationProp>();
  const { offerId } = route.params;
  const currentUser = useAuthUser();
  
  const { offerDetails, loading, error: offerError, cleanup: cleanupOfferDetails } = useOfferDetails(offerId);
  const { withdrawOffer } = useWithdrawOffer();
  
  const [price, setPrice] = useState<string>('');
  const [description, setDescription] = useState<string>('');
  const [isUpdating, setIsUpdating] = useState<boolean>(false);

  // Navigation state refs
  const navigationReadyRef = useRef<boolean>(false);
  const cleanupRef = useRef<boolean>(false);
  const navigationStateRef = useRef<{
    isNavigating: boolean;
    navigationPromise: Promise<void> | null;
  }>({
    isNavigating: false,
    navigationPromise: null
  });

  // Lifecycle tracking ref
  const lifecycleRef = useRef<{
    mountCount: number;
    cleanupStarted: boolean;
    navigationStarted: boolean;
    lastAction: string;
    errors: string[];
  }>({
    mountCount: 0,
    cleanupStarted: false,
    navigationStarted: false,
    lastAction: '',
    errors: []
  });

  // Update logging function
  const logLifecycle = useCallback((action: string, data?: Record<string, unknown>) => {
    const upperAction = action.toUpperCase();
    lifecycleRef.current.lastAction = upperAction;
    console.log(`[EditOffer][${upperAction}]`, {
      timestamp: new Date().toISOString(),
      mountCount: lifecycleRef.current.mountCount,
      cleanupStarted: cleanupRef.current,
      navigationStarted: lifecycleRef.current.navigationStarted,
      lastAction: upperAction,
      errors: lifecycleRef.current.errors,
      ...data
    });
  }, []);

  // Add mount effect
  useEffect(() => {
    // Reset cleanup state on mount
    cleanupRef.current = false;
    lifecycleRef.current.cleanupStarted = false;
    lifecycleRef.current.mountCount++;
    logLifecycle('MOUNT');

    return () => {
      cleanupRef.current = true;
      lifecycleRef.current.cleanupStarted = true;
      logLifecycle('CLEANUP');
      
      // Only reset state if we're navigating away
      if (navigationStateRef.current.isNavigating) {
        setPrice('');
        setDescription('');
        setIsUpdating(false);
      }
      
      // Cleanup navigation state
      navigationStateRef.current.isNavigating = false;
      navigationStateRef.current.navigationPromise = null;
      
      // Call cleanup function from useOfferDetails
      if (cleanupOfferDetails) {
        cleanupOfferDetails();
      }
    };
  }, [logLifecycle, cleanupOfferDetails]);

  // Update offer details effect
  useEffect(() => {
    try {
      if (offerDetails && typeof offerDetails === 'object' && 'price' in offerDetails) {
        const details = offerDetails as OfferDetails;
        let priceValue = '';
        
        if (details.price !== undefined && details.price !== null) {
          priceValue = typeof details.price === 'string' 
            ? details.price 
            : details.price.toString();
        }
        
        setPrice(priceValue);
        setDescription(details.description || '');
      }
    } catch (error) {
      const err = error instanceof Error ? error : new Error('Unknown error');
      console.error('Error processing offer details:', err);
      console.error('Error stack:', err.stack);
      setPrice('');
      setDescription('');
    }
  }, [offerDetails]);

  // Update notification body function
  const getUpdateNotificationBody = (
    originalData: OfferDetails,
    updatedData: Partial<OfferDetails>
  ): string => {
    const changes: string[] = [];
    if (originalData.price !== updatedData.price) {
      changes.push(`price from $${originalData.price} to $${updatedData.price}`);
    }
    if (originalData.description !== updatedData.description) {
      changes.push('description');
    }
    return `Offer updated (${changes.join(' and ')})`;
  };

  // Add handler functions
  const handlePriceChange = (value: string) => {
    // Only allow numeric input
    if (/^\d*$/.test(value)) {
      setPrice(value);
    } else {
      // Keep the current value if input is invalid
      setPrice(price);
    }
  };

  const handleDescriptionChange = (value: string) => {
    setDescription(value);
  };

  const handleBack = useCallback(() => {
    if (!navigationStateRef.current.isNavigating && navigation) {
      navigationStateRef.current.isNavigating = true;
      setPrice('');
      setDescription('');
      navigation.goBack();
    }
  }, [navigation]);

  const handleUpdateOffer = async () => {
    try {
      if (!price.trim()) {
        Alert.alert('Error', 'Please enter a valid price');
        return;
      }

      const numericPrice = parseFloat(price);
      if (isNaN(numericPrice) || numericPrice <= 0) {
        Alert.alert('Error', 'Please enter a valid price');
        return;
      }

      setIsUpdating(true);

      console.log('[EditOffer][handleUpdateOffer] Updating offer:', {
        offerId,
        price: numericPrice,
        description: description.trim()
      });

      // Update the offer using the existing updateOffer function which handles notifications
      const updatedOffer = await updateOffer(offerId, {
        price: numericPrice,
        description: description.trim()
      });

      console.log('[EditOffer][handleUpdateOffer] Offer updated:', {
        offerId: updatedOffer.id,
        price: updatedOffer.price,
        description: updatedOffer.description
      });

      Alert.alert('Success', 'Offer updated successfully', [
        {
          text: 'OK',
          onPress: () => {
            navigationStateRef.current.isNavigating = true;
            navigation.navigate('OfferDetail', {
              offerId: offerId,
              offerOwnerId: offerDetails?.offerOwnerId,
              postingOwnerId: offerDetails?.postingOwnerId,
              postingId: offerDetails?.postingId,
              refresh: true,
              initialOffer: updatedOffer,
              initialPosting: offerDetails?.posting
            });
          }
        }
      ]);
    } catch (error) {
      const err = error instanceof Error ? error : new Error('Unknown error');
      console.error('[EditOffer][handleUpdateOffer] Error:', {
        error: err.message,
        stack: err.stack,
        offerId
      });
      Alert.alert('Error', 'Failed to update offer');
    } finally {
      setIsUpdating(false);
    }
  };

  const handleWithdrawOffer = () => {
    Alert.alert(
      'Withdraw Offer',
      'Are you sure you want to withdraw this offer?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Withdraw',
          style: 'destructive',
          onPress: async () => {
            try {
              await withdrawOffer(offerId);
              Alert.alert('Success', 'Offer withdrawn successfully', [
                {
                  text: 'OK',
                  onPress: () => {
                    navigation.navigate('OfferDetail', {
                      offerId,
                      refresh: true
                    });
                  }
                }
              ]);
            } catch (error) {
              const err = error instanceof Error ? error : new Error('Unknown error');
              console.error('Error withdrawing offer:', err);
              Alert.alert('Error', 'Failed to withdraw offer');
            }
          }
        }
      ]
    );
  };

  // Loading state
  if (loading || isUpdating) {
    return (
      <View style={styles.loadingContainer} testID="edit-offer-loading-indicator">
        <Text>{isUpdating ? 'Updating offer...' : 'Loading offer details...'}</Text>
      </View>
    );
  }

  // Error state
  if (offerError) {
    return (
      <View style={styles.errorContainer}>
        <Text>Error: {offerError}</Text>
      </View>
    );
  }

  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
      <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
        <TouchableOpacity 
          onPress={handleBack}
          style={styles.backButton}
          testID="edit-offer-back-button"
        >
          
        </TouchableOpacity>

        <Text style={styles.title}>Edit Offer</Text>
        <FormInput
          label="Offer Price:"
          value={price}
          onChangeText={handlePriceChange}
          placeholder="Enter your offer price"
          keyboardType="numeric"
          testID="edit-offer-price-input"
        />
        <FormInput
          label="Description:"
          value={description}
          onChangeText={handleDescriptionChange}
          placeholder="Enter a description for your offer"
          multiline
          style={styles.descriptionInput}
          testID="edit-offer-description-input"
        />
        <View style={styles.buttonContainer}>
          <View style={styles.button}>
            <Button 
              title="Update Offer" 
              onPress={handleUpdateOffer} 
              color="#007AFF"
              testID="edit-offer-update-button"
            />
          </View>
          <View style={styles.button}>
            <Button 
              title="Withdraw Offer" 
              onPress={handleWithdrawOffer} 
              color="#FF3B30"
              testID="edit-offer-withdraw-button"
            />
          </View>
        </View>
      </ScrollView>
    </TouchableWithoutFeedback>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
  },
  contentContainer: {
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  descriptionInput: {
    minHeight: 100,
    textAlignVertical: 'top',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
  },
  button: {
    flex: 1,
    marginHorizontal: 5,
  },
  backButton: {
    paddingHorizontal: 15,
    paddingVertical: 10,
  },
});

export default EditOffer;