// MyOffersScreen.tsx

import React, { useState, useMemo } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, Button, Alert } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { auth } from '../firebase';
import useUserOffers from '../hooks/useUserOffers';
import useWithdrawOffer from '../hooks/useWithdrawOffer';

type TabType = 'active' | 'withdrawn';

function MyOffersScreen(): React.JSX.Element {
  const navigation = useNavigation();
  const currentUser = auth.currentUser;
  const [activeTab, setActiveTab] = useState<TabType>('active');

  const { offers, loading, error } = useUserOffers(currentUser?.uid);
  const { withdrawOffer } = useWithdrawOffer();

  // Filter offers based on active tab
  const filteredOffers = useMemo(() => {
    return offers.filter(offer => {
      if (activeTab === 'active') {
        return offer.status !== 'withdrawn';
      } else {
        return offer.status === 'withdrawn';
      }
    });
  }, [offers, activeTab]);

  const handleOfferPress = (offer) => {
    navigation.navigate('OfferDetail', {
      offerId: offer.id,
      postingId: offer.postingId,
      postingOwnerId: offer.postingOwnerId,
      offerOwnerId: currentUser.uid,
    });
  };

  const handleWithdrawOffer = (offerId) => {
    Alert.alert(
      'Withdraw Offer',
      'Are you sure you want to withdraw this offer?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Withdraw',
          style: 'destructive',
          onPress: () => {
            withdrawOffer(offerId);
          },
        },
      ],
      { cancelable: true }
    );
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <Text>Loading offers...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Text>Error: {error}</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Tab Navigation */}
      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === 'active' && styles.activeTab
          ]}
          onPress={() => setActiveTab('active')}
        >
          <Text style={[
            styles.tabText,
            activeTab === 'active' && styles.activeTabText
          ]}>
            Active ({offers.filter(o => o.status !== 'withdrawn').length})
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === 'withdrawn' && styles.activeTab
          ]}
          onPress={() => setActiveTab('withdrawn')}
        >
          <Text style={[
            styles.tabText,
            activeTab === 'withdrawn' && styles.activeTabText
          ]}>
            Withdrawn ({offers.filter(o => o.status === 'withdrawn').length})
          </Text>
        </TouchableOpacity>
      </View>

      <FlatList
        data={filteredOffers}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <View style={styles.listItem}>
            <TouchableOpacity 
              onPress={() => handleOfferPress(item)} 
              style={styles.offerContent}
              accessibilityLabel={`Open details for ${item.title || 'Untitled'}`}
            >
              <Text style={styles.listItemText}>Posting: {item.title || 'Untitled'}</Text>
              <Text style={styles.descriptionText}>Description: {item.description || 'No description available'}</Text>
              {item.postingStatus === 'Deleted' && (
                <Text style={styles.deletedText}>This posting has been deleted by the owner</Text>
              )}
              <Text style={styles.priceText}>Price: ${item.price}</Text>
              <Text style={[
                styles.statusText,
                item.status === 'withdrawn' && styles.withdrawnText
              ]}>
                Status: {item.status || 'Pending'}
              </Text>
            </TouchableOpacity>
            {activeTab === 'active' && (
              <Button 
                title="Withdraw Offer" 
                onPress={() => handleWithdrawOffer(item.id)} 
                color="red" 
              />
            )}
          </View>
        )}
        ListEmptyComponent={() => (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>
              {activeTab === 'active' 
                ? 'No active offers' 
                : 'No withdrawn offers'}
            </Text>
          </View>
        )}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
  },
  tabContainer: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E5',
    backgroundColor: 'white',
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: '#007AFF',
  },
  tabText: {
    fontSize: 16,
    color: '#666',
  },
  activeTabText: {
    color: '#007AFF',
    fontWeight: '600',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  listItem: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E5',
  },
  offerContent: {
    marginBottom: 8,
  },
  listItemText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  descriptionText: {
    fontSize: 14,
    color: 'gray',
    marginTop: 4,
  },
  deletedText: {
    fontSize: 14,
    color: 'red',
    marginTop: 4,
  },
  priceText: {
    fontSize: 14,
    marginTop: 4,
  },
  statusText: {
    fontSize: 14,
    color: 'gray',
    marginTop: 4,
  },
  withdrawnText: {
    color: '#FF3B30',
  },
  emptyContainer: {
    padding: 20,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
  },
});

export default MyOffersScreen;