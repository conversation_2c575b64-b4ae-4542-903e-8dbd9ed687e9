// MyPostingsScreen.tsx

import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, ActivityIndicator } from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { useFetchUserPostings } from '../hooks/useFetchUserPostings';
import Icon from 'react-native-vector-icons/Ionicons';

interface Posting {
  id: string;
  title?: string;
  description?: string;
  latitude: number;
  longitude: number;
  userId: string;
  postingStatus?: string;
}

interface TabProps {
  label: string;
  isActive: boolean;
  onPress: () => void;
}

interface RouteParams {
  fromEdit?: boolean;
}

const Tab: React.FC<TabProps> = ({ label, isActive, onPress }) => (
  <TouchableOpacity
    style={[styles.tab, isActive && styles.activeTab]}
    onPress={() => {
      console.log(`[MyPostingsScreen] Tab pressed: ${label}`);
      onPress();
    }}
    accessibilityLabel={`Switch to ${label} tab`}
    testID={`${label.toLowerCase()}-tab`}
  >
    <Text style={[styles.tabText, isActive && styles.activeTabText]}>{label}</Text>
  </TouchableOpacity>
);

const PostingList: React.FC<{ postings: Posting[] }> = ({ postings }) => {
  const navigation = useNavigation();
  console.log('[MyPostingsScreen] Rendering PostingList:', {
    postingsCount: postings.length,
    postingIds: postings.map(p => p.id),
    postingStatuses: postings.map(p => p.postingStatus)
  });

  const handleItemPress = (item: Posting) => {
    console.log('[MyPostingsScreen] Posting item pressed:', {
      postingId: item.id,
      title: item.title,
      status: item.postingStatus
    });
    
    navigation.navigate('PostingDetail', {
      postingId: item.id,
      itemName: item.title || 'Untitled',
      itemDescription: item.description || 'No description available',
      itemLocation: { latitude: item.latitude, longitude: item.longitude },
      userId: item.userId,
    });
  };

  return (
    <FlatList
      data={postings}
      keyExtractor={(item) => item.id}
      renderItem={({ item }) => (
        <TouchableOpacity
          style={styles.postingItem}
          onPress={() => handleItemPress(item)}
          testID={`posting-item-${item.id}`}
        >
          <Text style={styles.postingTitle}>{item.title || 'Untitled'}</Text>
          <Text style={styles.postingDescription} numberOfLines={2}>
            {item.description || 'No description available'}
          </Text>
        </TouchableOpacity>
      )}
      ListEmptyComponent={
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>No postings found</Text>
        </View>
      }
    />
  );
};

const MyPostingsScreen: React.FC = () => {
  const [activeTab, setActiveTab] = useState('Active');
  const { activePostings, deletedPostings, loading, error } = useFetchUserPostings();
  const navigation = useNavigation();
  const route = useRoute();
  const { fromEdit } = route.params as RouteParams || {};

  useEffect(() => {
    console.log('[MyPostingsScreen] Setting up navigation header:', {
      fromEdit,
      activeTab
    });

    // Only modify the header if coming from edit
    // This preserves the default back button behavior in other cases (e.g., from Profile)
    if (fromEdit) {
      navigation.setOptions({
        headerLeft: () => (
          <TouchableOpacity
            onPress={() => {
              console.log('[MyPostingsScreen] Home button pressed, navigating to Home');
              navigation.navigate('Home');
            }}
            style={styles.headerButton}
            testID="home-button"
          >
            <Icon name="home-outline" size={24} color="#007AFF" />
          </TouchableOpacity>
        )
      });
    }
  }, [navigation, fromEdit]);

  console.log('[MyPostingsScreen] Screen rendered:', {
    activeTab,
    activePostingsCount: activePostings.length,
    deletedPostingsCount: deletedPostings.length,
    isLoading: loading,
    hasError: !!error,
    fromEdit
  });

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" />
      </View>
    );
  }

  if (error) {
    // Since error can be any type coming from the JS hook
    const errorMessage = typeof error === 'object' && error !== null
      ? (error as Error).message || 'An error occurred'
      : typeof error === 'string'
        ? error
        : 'An error occurred';

    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Error: {errorMessage}</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.tabContainer}>
        <Tab
          label="Active"
          isActive={activeTab === 'Active'}
          onPress={() => setActiveTab('Active')}
        />
        <Tab
          label="Deleted"
          isActive={activeTab === 'Deleted'}
          onPress={() => setActiveTab('Deleted')}
        />
      </View>
      <PostingList
        postings={activeTab === 'Active' ? activePostings : deletedPostings}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  tabContainer: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: '#007AFF',
  },
  tabText: {
    fontSize: 16,
    color: '#666',
  },
  activeTabText: {
    color: '#007AFF',
    fontWeight: '600',
  },
  postingItem: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  postingTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  postingDescription: {
    fontSize: 14,
    color: '#666',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  errorText: {
    color: 'red',
    textAlign: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  headerButton: {
    paddingHorizontal: 15,
    justifyContent: 'center',
    alignItems: 'center'
  }
});

export default MyPostingsScreen;