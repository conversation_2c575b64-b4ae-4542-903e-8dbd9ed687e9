import React from 'react';
import { View, Text, StyleSheet, Switch, ActivityIndicator, Alert } from 'react-native';
import { useNotificationSettings } from '../hooks/useNotificationSettings';
import { NotificationType } from '../types/notifications';

function NotificationSettingsScreen(): React.JSX.Element {
  const { settings, updateSetting, loading, error } = useNotificationSettings();

  const handleSettingUpdate = async (type: NotificationType, value: boolean) => {
    try {
      await updateSetting(type, value);
    } catch (error) {
      console.error('Error updating notification setting:', error);
      Alert.alert('Error', 'Failed to update notification setting');
    }
  };

  const notificationOptions = [
    {
      type: NotificationType.NEW_OFFER,
      title: 'New Offers',
      description: 'Receive notifications when someone makes an offer on your posting'
    },
    {
      type: NotificationType.OFFER_STATUS_CHANGE,
      title: 'Offer Updates',
      description: 'Receive notifications when someone updates their offer price or description'
    },
    {
      type: NotificationType.NEW_MESSAGE,
      title: 'Messages',
      description: 'Receive notifications when you get new messages in offer discussions'
    },
    {
      type: NotificationType.FAVORITE_POSTING_UPDATE,
      title: 'Posting Updates',
      description: 'Receive notifications when postings in your favorites list are updated'
    }
  ];

  if (loading) {
    return (
      <View 
        style={styles.centerContainer} 
        testID="loading-indicator"
        accessibilityLabel="Loading notification settings"
        accessibilityRole="progressbar"
      >
        <ActivityIndicator size="large" color="#007AFF" />
      </View>
    );
  }

  if (error) {
    return (
      <View 
        style={styles.centerContainer}
        testID="error-container"
        accessibilityRole="alert"
      >
        <Text 
          style={styles.errorText}
          testID="error-message"
          accessibilityLabel={`Error: ${error.message}`}
        >
          Error: {error.message}
        </Text>
      </View>
    );
  }

  return (
    <View 
      style={styles.container}
      accessibilityRole="list"
      accessibilityLabel="Notification settings"
    >
      {notificationOptions.map((option) => (
        <View 
          key={option.type} 
          style={styles.settingItem}
          testID="notification-option"
          accessibilityRole="none"
          accessibilityLabel={`Toggle ${option.title} notifications`}
          accessibilityHint={option.description}
        >
          <View style={styles.settingText}>
            <Text 
              style={styles.settingTitle}
              accessibilityRole="header"
            >
              {option.title}
            </Text>
            <Text 
              style={styles.settingDescription}
              accessibilityRole="text"
            >
              {option.description}
            </Text>
          </View>
          <Switch
            testID="notification-switch"
            value={settings[option.type]}
            onValueChange={(value) => handleSettingUpdate(option.type, value)}
            trackColor={{ false: '#767577', true: '#81b0ff' }}
            thumbColor={settings[option.type] ? '#007AFF' : '#f4f3f4'}
            accessibilityRole="switch"
            accessibilityState={{ checked: settings[option.type] }}
            accessibilityLabel={`${option.title} notifications`}
            accessibilityHint={`Double tap to ${settings[option.type] ? 'disable' : 'enable'} ${option.title} notifications`}
          />
        </View>
      ))}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
    padding: 16,
  },
  centerContainer: {
    flex: 1,
    backgroundColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E5',
  },
  settingText: {
    flex: 1,
    marginRight: 16,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  settingDescription: {
    fontSize: 14,
    color: '#666',
  },
  errorText: {
    color: 'red',
    fontSize: 16,
  },
});

export default NotificationSettingsScreen; 