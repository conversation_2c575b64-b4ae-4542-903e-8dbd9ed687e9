// PostingDetail.tsx

import React, { useEffect, useState, useCallback, useMemo, useReducer, useRef } from 'react';
import { View, Text, Button, StyleSheet, Alert, FlatList, TouchableOpacity, ScrollView, ActivityIndicator, BackHandler } from 'react-native';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { auth } from '../firebase';
import { deletePosting, registerListener, unregisterListener, getListenerStats, getListenerRegistry, getActiveListenerCount, getActiveListenersForComponent, getCleanupCoordinator } from '../services/firebaseService';
import { usePostingDetails } from '../hooks/usePostingDetails';
import useOffers from '../hooks/useOffers';
import useFavorites from '../hooks/useFavorites';
import AntDesign from 'react-native-vector-icons/AntDesign';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { RootStackParamList, NavigationFlowMetrics } from '../types/navigation';

// Add these interfaces at the top of the file
interface ListenerMetadata {
  collection: string;
  type: string;
  source: string;
  startTime?: number;
  componentId?: string;
}

interface SubscriptionEntry {
  id: string;
  type: string;
  active: boolean;
  registeredAt: number;
  unsubscribe?: () => void;
  metadata?: ListenerMetadata;
}

interface ListenerRegistry {
  cleanup: Map<string, {
    cleanup: () => void;
    createdAt: number;
    lastActive: number;
    active: boolean;
    collection: string;
    id: string;
  }>;
  stats: {
    totalListeners: number;
    activeScreens: Set<string>;
    lastCleanup: number | null;
  };
}

interface ComponentListenerCollection {
  offers?: number;
  postings?: number;
  [key: string]: number | undefined;
}

// Add type assertion for getListenerRegistry
const getTypedListenerRegistry = (): ListenerRegistry => getListenerRegistry();

interface SubscriptionManagerRef {
  subscriptions: Map<string, SubscriptionEntry>;
  metrics: {
    startTime: number;
    subscriptionCount: number;
    lastUpdate: number;
  };
}

interface SubscriptionMetrics {
  startTime: number;
  subscriptionCount: number;
  lastUpdate: number;
}

interface SubscriptionStats {
  total: number;
  active: number;
  subscriptions: Array<{
    id: string;
    active: boolean;
    collection: string;
    registeredAt: number;
  }>;
}

interface PostingSubscriptionManager {
  subscriptions: Map<string, any>;
  metrics: SubscriptionMetrics;
  verifySubscriptions: () => {
    local: SubscriptionStats;
    timestamp: string;
  };
}

const isDev = __DEV__;
console.log('[PostingDetail] Running in:', isDev ? 'development' : 'production');

interface PostingDetailProps {
  route: {
    params: {
      postingId: string;
      itemLocation: {
        latitude: number;
        longitude: number;
      };
      userId: string;
    };
  };
}

// Add type definitions at the top
interface Offer {
  id: string;
  userId: string;
  price: number;
  description: string;
  status: 'pending' | 'accepted' | 'rejected' | 'withdrawn';
  isDeleted: boolean;
  createdAt: Date;
  updatedAt: Date;
}

interface OffersHookResult {
  offers: Offer[];
  error: Error | null;
  isLoading: boolean;
  hasSubscription: boolean;
  subscriptionId: string;
  cleanupState: {
    isCleaningUp: boolean;
    lastCleanupDuration: number | null;
  };
  ensureCleanup: () => Promise<void>;
  unsubscribe: () => void;
}

interface PostingDetails {
  id: string;
  title: string;
  description: string;
  userId: string;
  latitude: number;
  longitude: number;
  postingStatus: 'Active' | 'Deleted';
}

type NavigationProp = StackNavigationProp<RootStackParamList>;

// Add at the top of the file
type PostingDetailState = {
  isDescriptionExpanded: boolean;
  activeTab: 'active' | 'deleted';
  isOwner: boolean;
  userOffer: { id: string; status: string } | null;
  offersCount: number;
  currentUserId: string | null;
  postingDetailsId: string;
  lastUpdate: number;
};

type PostingDetailAction = 
  | { type: 'UPDATE_OFFERS'; payload: { userOffer: { id: string; status: string } | null; offersCount: number } }
  | { type: 'POSTING_LOADED'; payload: { currentUserId: string | null; isOwner: boolean; postingDetailsId: string } }
  | { type: 'SWITCH_TAB'; payload: 'active' | 'deleted' }
  | { type: 'TOGGLE_DESCRIPTION' }; // New action type

function postingDetailReducer(state: PostingDetailState, action: PostingDetailAction): PostingDetailState {
  switch (action.type) {
    case 'SWITCH_TAB':
      return {
        ...state,
        activeTab: action.payload,
        lastUpdate: Date.now()
      };
    case 'UPDATE_OFFERS':
      return {
        ...state,
        userOffer: action.payload.userOffer,
        offersCount: action.payload.offersCount,
        lastUpdate: Date.now()
      };
    case 'POSTING_LOADED':
      return {
        ...state,
        currentUserId: action.payload.currentUserId,
        isOwner: action.payload.isOwner,
        postingDetailsId: action.payload.postingDetailsId,
        lastUpdate: Date.now()
      };
    case 'TOGGLE_DESCRIPTION':
      return {
        ...state,
        isDescriptionExpanded: !state.isDescriptionExpanded,
        lastUpdate: Date.now()
      };
    default:
      return state;
  }
}

// Add this utility function at the top of the file
const getTimestamp = () => new Date().toISOString();

// Add cleanup manager type
interface CleanupManager {
  isCleaningUp: boolean;
  pendingNavigation: (() => void) | null;
  cleanupPromise: Promise<void> | null;
  timeoutId: NodeJS.Timeout | null;
}

// Add utility functions at the top
  const verifySubscriptions = () => {
  const stats = getListenerStats();
    return {
      local: {
      total: 0,
      active: stats.totalListeners,
      subscriptions: []
      },
      timestamp: new Date().toISOString()
    };
  };

  const trackListener = (id: string, type: string, unsubscribe: () => void) => {
  console.log('[PostingDetail][Listener] Tracking:', { id, type });
};

const cleanupListener = (id: string) => {
  console.log('[PostingDetail][Listener] Cleaning up:', { id });
};

function PostingDetail({ route }: PostingDetailProps): React.JSX.Element {
  const { postingId, itemLocation, userId } = route.params;
  const componentId = `PostingDetail_${postingId}`;
  const navigation = useNavigation<NavigationProp>();
  const currentUser = auth.currentUser;
  
  // 1. All refs first
  const subscriptionManager = useRef<SubscriptionManagerRef>({
    subscriptions: new Map(),
    metrics: {
      startTime: Date.now(),
      subscriptionCount: 0,
      lastUpdate: Date.now()
    }
  });

  const cleanupManager = useRef<CleanupManager>({
    isCleaningUp: false,
    pendingNavigation: null,
    cleanupPromise: null,
    timeoutId: null
  });

  const subscriptions = useRef<Set<string>>(new Set());
  const mountTimestamp = useRef(Date.now());
  const initializationRef = useRef({
    initialized: false,
    startTime: Date.now()
  });

  // 2. All reducers and state
  const [state, dispatch] = useReducer(postingDetailReducer, {
    isDescriptionExpanded: false,
    activeTab: 'active',
    isOwner: false,
    userOffer: null,
    offersCount: 0,
    currentUserId: null,
    postingDetailsId: '',
    lastUpdate: Date.now()
  });

  const [expandedOffers, setExpandedOffers] = useState(new Set());

  // 3. All hooks that depend on refs/state
  const { 
    postingDetails, 
    loading: postingLoading, 
    error: postingError,
    refetchPostingDetails,
    unsubscribe: unsubscribePosting,
    ensureCleanup: ensurePostingCleanup 
  } = usePostingDetails(postingId, componentId);

  const offersResult = useOffers(postingId, componentId);
  const { 
    offers: rawOffers, 
    error: offersError,
    unsubscribe: unsubscribeOffers,
    ensureCleanup: ensureOffersCleanup 
  } = offersResult;

  const offers = rawOffers as Offer[];

  const initialFavoriteStatus = null;  // Use null instead of false
  const { 
    isFavorite,
    addToFavorites: addToFavoritesHook,
    removeFromFavorites: removeFromFavoritesHook,
  } = useFavorites(postingId, currentUser?.uid, initialFavoriteStatus);

  // 4. All memoized values
  const filteredOffers = useMemo(() => {
    return (offers as Offer[]).filter((offer: Offer) => {
      if (state.activeTab === 'active') {
        return !offer.isDeleted && offer.status !== 'withdrawn';
      } else {
        return offer.isDeleted || offer.status === 'withdrawn';
      }
    }).sort((a: Offer, b: Offer) => a.price - b.price);
  }, [offers, state.activeTab]);

  // 5. All callbacks
  const handleCleanupComplete = useCallback(() => {
    if (cleanupManager.current.pendingNavigation) {
      const navigate = cleanupManager.current.pendingNavigation;
      cleanupManager.current.pendingNavigation = null;
      navigate();
    }
  }, []);

  const safeNavigate = useCallback(async (navigateAction: () => void) => {
    console.log('[PostingDetail][Navigation] Attempting safe navigation');
    
    if (cleanupManager.current.isCleaningUp) {
      console.log('[PostingDetail][Navigation] Cleanup in progress, queueing navigation');
      cleanupManager.current.pendingNavigation = navigateAction;
      return;
    }

    // Get actual listener stats and ensure hooks are ready for cleanup
    const listenerStats = getListenerStats();
    
    console.log('[PostingDetail][Navigation] Checking listeners:', {
      activeCount: listenerStats.totalListeners,
      byCollection: listenerStats.byCollection,
      timestamp: new Date().toISOString()
    });

    // Only proceed with cleanup if there are actually active listeners
    if (listenerStats.totalListeners === 0) {
      console.log('[PostingDetail][Navigation] No active listeners, proceeding with navigation');
      navigateAction();
      return;
    }
    
    try {
      cleanupManager.current.isCleaningUp = true;
      
      // Explicitly cleanup both hooks first
      console.log('[PostingDetail][Navigation] Cleaning up hooks');
      
      // Clean up posting listener first
      if (unsubscribePosting) {
        console.log('[PostingDetail] Unsubscribing from posting');
        try {
          unsubscribePosting();
          console.log('[PostingDetail] Successfully unsubscribed from posting');
        } catch (error) {
          console.error('[PostingDetail] Error unsubscribing from posting:', error);
        }
      }

      // Then clean up offers listener
      if (unsubscribeOffers) {
        console.log('[PostingDetail] Unsubscribing from offers');
        try {
          unsubscribeOffers();
          console.log('[PostingDetail] Successfully unsubscribed from offers');
        } catch (error) {
          console.error('[PostingDetail] Error unsubscribing from offers:', error);
        }
      }

      // Wait a short time for cleanup to propagate
      await new Promise(resolve => setTimeout(resolve, 100));

      // Verify cleanup
      const postCleanupStats = getListenerStats();
      console.log('[PostingDetail][Navigation] Post-cleanup verification:', {
        activeListeners: postCleanupStats.totalListeners,
        byCollection: postCleanupStats.byCollection,
        timestamp: new Date().toISOString()
      });

      // If cleanup was not successful, force cleanup
      if (postCleanupStats.totalListeners > 0) {
        console.warn('[PostingDetail][Navigation] Cleanup incomplete, forcing cleanup');
        
        const cleanupPromises = [];

        // Force cleanup of posting if still active
        if (postCleanupStats.byCollection.postings > 0 && ensurePostingCleanup) {
          console.log('[PostingDetail] Forcing cleanup of posting');
          cleanupPromises.push(ensurePostingCleanup());
        }

        // Force cleanup of offers if still active
        if (postCleanupStats.byCollection.offers > 0 && ensureOffersCleanup) {
          console.log('[PostingDetail] Forcing cleanup of offers');
          cleanupPromises.push(ensureOffersCleanup());
        }

        // Wait for all forced cleanups to complete
        if (cleanupPromises.length > 0) {
          await Promise.all(cleanupPromises);
          console.log('[PostingDetail] Successfully forced cleanup');
        }
      }

      // Final verification
      const finalStats = getListenerStats();
      if (finalStats.totalListeners > 0) {
        console.error('[PostingDetail][Navigation] Final cleanup failed:', {
          activeListeners: finalStats.totalListeners,
          byCollection: finalStats.byCollection,
        timestamp: new Date().toISOString()
      });
      } else {
        console.log('[PostingDetail][Navigation] All listeners cleaned up successfully');
      }

      // Clear subscription tracking
      cleanupManager.current.isCleaningUp = false;
      cleanupManager.current.pendingNavigation = null;

      // Proceed with navigation
      navigateAction();
    } catch (error) {
      console.error('[PostingDetail][Navigation] Error during cleanup:', error);
      // Still proceed with navigation even if cleanup failed
      navigateAction();
    }
  }, [unsubscribePosting, unsubscribeOffers, ensurePostingCleanup, ensureOffersCleanup]);

  const handleMapMarkerPress = useCallback(() => {
    if (!postingDetails) return;

    safeNavigate(() => {
      navigation.reset({
        index: 0,
        routes: [{
          name: 'Home',
          params: {
            focusLocation: {
              latitude: postingDetails.latitude,
              longitude: postingDetails.longitude,
              latitudeDelta: 0.0922,
              longitudeDelta: 0.0421,
            },
            selectedPosting: {
              id: postingDetails.id,
              title: postingDetails.title,
              description: postingDetails.description,
              userId: postingDetails.userId,
              latitude: postingDetails.latitude,
              longitude: postingDetails.longitude,
              postingStatus: postingDetails.postingStatus
            },
            showCallout: true
          }
        }]
      });
    });
  }, [postingDetails, navigation, safeNavigate]);

  const handleMakeOffer = useCallback(() => {
    if (postingDetails) {
      safeNavigate(() => {
        navigation.navigate('MakeOffer', {
          postingId,
          itemName: postingDetails.title,
          itemDescription: postingDetails.description,
          itemLocation,
          userId,
        });
      });
    }
  }, [postingDetails, postingId, itemLocation, userId, navigation, safeNavigate]);

  const handleEditOffer = useCallback(() => {
    if (state.userOffer) {
      navigation.navigate('EditOffer', {
        offerId: state.userOffer.id,
        postingId,
        ownerId: userId,
      });
    }
  }, [state.userOffer, postingId, userId, navigation]);

  const handleDeletePosting = useCallback(() => {
    Alert.alert(
      'Confirm Delete',
      'Are you sure you want to delete this posting? All active offers will be withdrawn.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              console.log('=== Starting Posting Deletion Flow ===');
              console.log('PostingId:', postingId);
              
              await deletePosting(postingId);
              console.log('Posting deleted successfully');
              
              Alert.alert(
                'Success',
                'Posting deleted successfully. All offer owners have been notified.',
                [
                  {
                    text: 'OK',
                    onPress: () => {
                      console.log('Navigating to MyPostings screen...');
                      navigation.navigate('MyPostings');
                    }
                  }
                ]
              );
            } catch (error) {
              console.error('Error deleting posting:', error);
              Alert.alert('Error', 'Failed to delete posting');
            }
          },
        },
      ],
      { cancelable: true }
    );
  }, [postingId, navigation]);

  const handleEditPosting = useCallback(() => {
    console.log('[PostingDetail][Navigation] Navigating to edit:', {
      postingId,
      postingDetails,
      timestamp: new Date().toISOString()
    });

    navigation.navigate('EditPosting', {
      postingId,
      initialData: postingDetails
    });
  }, [navigation, postingId, postingDetails]);

  const handleAddToFavorites = useCallback(async () => {
    if (!currentUser) {
      console.error('User must be logged in to add to favorites.');
      return;
    }

    try {
      await addToFavoritesHook();
      console.log('[PostingDetail] Added to favorites successfully');
    } catch (error) {
      console.error('[PostingDetail] Error adding to favorites:', error);
    }
  }, [currentUser, addToFavoritesHook]);

  const handleRemoveFromFavorites = useCallback(async () => {
    if (!currentUser) {
      console.error('User must be logged in to remove from favorites.');
      return;
    }

    try {
      await removeFromFavoritesHook();
      console.log('[PostingDetail] Removed from favorites successfully');
    } catch (error) {
      console.error('[PostingDetail] Error removing from favorites:', error);
    }
  }, [currentUser, removeFromFavoritesHook]);

  const handleOfferPress = useCallback((offer: Offer) => {
    safeNavigate(() => {
      navigation.navigate('OfferDetail', {
        offerId: offer.id,
        postingId: postingId,
        postingOwnerId: userId,
        offerOwnerId: offer.userId
      });
    });
  }, [navigation, postingId, userId, safeNavigate]);

  const toggleOfferExpansion = useCallback((offerId: string) => {
    setExpandedOffers(prev => {
      const newSet = new Set(prev);
      if (newSet.has(offerId)) {
        newSet.delete(offerId);
      } else {
        newSet.add(offerId);
      }
      return newSet;
    });
  }, []);

  // 6. All effects
  const setupSubscriptions = useCallback(async () => {
    if (subscriptions.current.size > 0) {
      console.log('[PostingDetail][Init] Cleaning up existing subscriptions before setup');
      subscriptions.current.forEach(id => {
        cleanupListener(id);
      });
      subscriptions.current.clear();
    }

    console.log('[PostingDetail][Init] Starting centralized coordination', {
      postingId,
      timeSinceStart: Date.now() - initializationRef.current.startTime,
      timestamp: getTimestamp()
    });

    // Hooks now handle their own immediate initialization
    // We just coordinate and track them for cleanup purposes

    // Track listeners that should be active (hooks initialize themselves)
    const postingSubId = `posting_${postingId}_${Date.now()}`;
    const offersSubId = `offers_${postingId}_${Date.now()}`;

    // Track the unsubscribe functions for cleanup coordination
    if (unsubscribePosting) {
      trackListener(postingSubId, 'posting_details', unsubscribePosting);
      subscriptions.current.add(postingSubId);
    }

    if (unsubscribeOffers) {
      trackListener(offersSubId, 'offers_list', unsubscribeOffers);
      subscriptions.current.add(offersSubId);
    }

    console.log('[PostingDetail][Init] Coordination complete', {
      postingSubId: unsubscribePosting ? postingSubId : 'not_ready',
      offersSubId: unsubscribeOffers ? offersSubId : 'not_ready',
      totalTime: Date.now() - initializationRef.current.startTime
    });
  }, [postingId, unsubscribePosting, unsubscribeOffers]);

  useEffect(() => {
    // For iOS, intercept navigation events instead of hardware back button
    const unsubscribe = navigation.addListener('beforeRemove', (e) => {
      // Prevent default navigation behavior
      e.preventDefault();
      
      // Use safeNavigate to ensure listeners are cleaned up
      safeNavigate(() => {
        // Resume the prevented navigation action
        navigation.dispatch(e.data.action);
      });
    });

    return unsubscribe;
  }, [navigation, safeNavigate]);

  useEffect(() => {
    if (initializationRef.current.initialized) {
      console.log('[PostingDetail][Init] Preventing duplicate coordination');
      return;
    }

    initializationRef.current.initialized = true;

    console.log('[PostingDetail][Init] Starting coordination', {
      postingId,
      timestamp: new Date().toISOString(),
      timeSinceStart: Date.now() - initializationRef.current.startTime
    });

    // Hooks handle their own immediate focus-based initialization
    // We just coordinate cleanup tracking here
    setupSubscriptions().catch(error => {
      console.error('[PostingDetail][Init] Coordination failed:', error);
    });

    return () => {
      console.log('[PostingDetail][Cleanup] Starting', {
        subscriptionCount: subscriptions.current.size,
        subscriptions: Array.from(subscriptions.current)
      });

      subscriptions.current.forEach(id => {
        cleanupListener(id);
      });
      subscriptions.current.clear();
      initializationRef.current.initialized = false;

      console.log('[PostingDetail][Cleanup] Complete', {
        timestamp: new Date().toISOString()
      });
    };
  }, [postingId, setupSubscriptions]);

  // REMOVED: Component-level focus handler that was causing conflicts
  // Hooks now handle their own focus effects for immediate data availability
  // This eliminates the infinite loop while preserving immediate data loading

  /* useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      const verificationInterval = setInterval(() => {
        const stats = getListenerStats();
        const localStats = verifySubscriptions(); // Use the helper function instead

        console.log('[PostingDetail][Monitor] Subscription check:', {
          timestamp: new Date().toISOString(),
          subscriptions: {
            registry: {
              total: stats.totalListeners,
              byCollection: stats.byCollection,
              activeCleanups: stats.activeCleanups
            },
            local: localStats.local,
            activeListeners: {
              posting: !!unsubscribePosting,
              offers: !!unsubscribeOffers
            }
          }
        });

        // Enhanced mismatch detection
        if (stats.totalListeners !== localStats.local.active) {
          console.warn('[PostingDetail][Monitor] Registry mismatch:', {
            registryTotal: stats.totalListeners,
            localActive: localStats.local.active,
            details: {
              registry: stats,
              local: localStats
            }
          });
        }
      }, 3000);

      return () => {
        console.log('[PostingDetail][Monitor] Stopping');
        clearInterval(verificationInterval);
      };
    }
  }, [postingId, unsubscribePosting, unsubscribeOffers]); */

  useEffect(() => {
    return () => {
      console.log('[PostingDetail][Cleanup] Starting final cleanup');
      
      const activeListeners = Array.from(subscriptions.current);
      if (activeListeners.length === 0) return;

      const coordinator = getCleanupCoordinator();
      const groupId = `posting_detail_${postingId}_${Date.now()}`;
      
      coordinator.createGroup(groupId, activeListeners);
      coordinator.coordinateCleanup(groupId)
        .then(() => {
          console.log('[PostingDetail][Cleanup] Final cleanup complete');
          handleCleanupComplete();
        })
        .catch(error => {
          console.error('[PostingDetail][Cleanup] Final cleanup failed:', error);
          handleCleanupComplete();
        });
    };
  }, [postingId, handleCleanupComplete]);

  useEffect(() => {
    console.log('[PostingDetail][StateSync] Checking state update conditions:', {
      postingLoading,
      hasDetails: !!postingDetails,
      currentUser: currentUser?.uid,
      userId,
      offersCount: offers?.length,
      timestamp: getTimestamp()
    });

    if (!postingLoading && postingDetails && currentUser) {
      console.log('[PostingDetail][StateSync] Updating state with:', {
        postingId,
        userId,
        currentUserId: currentUser.uid,
        isOwnerCheck: currentUser.uid === userId,
        offersCount: offers?.length,
        timestamp: getTimestamp()
      });

      // First update ownership and user state
      dispatch({
        type: 'POSTING_LOADED',
        payload: {
          currentUserId: currentUser.uid,
          isOwner: currentUser.uid === userId,
          postingDetailsId: postingId
        }
      });

      // Then process offers if they're loaded
      if (offers) {
        console.log('[PostingDetail][StateSync] Processing offers:', {
          count: offers.length,
          userOffers: offers.filter(offer => 
            offer.userId === currentUser.uid && 
            !offer.isDeleted && 
            offer.status !== 'withdrawn'
          ).length,
          timestamp: getTimestamp()
        });

        const userOffer = offers.find(
          offer => offer.userId === currentUser.uid && 
          !offer.isDeleted && 
          offer.status !== 'withdrawn'
        );
        
        dispatch({
          type: 'UPDATE_OFFERS',
          payload: {
            userOffer: userOffer ? { id: userOffer.id, status: userOffer.status } : null,
            offersCount: offers.length
          }
        });
      }

      console.log('[PostingDetail][StateSync] State updated:', {
        isOwner: currentUser.uid === userId,
        postingId,
        hasUserOffer: !!offers?.find(o => o.userId === currentUser.uid),
        timestamp: getTimestamp()
      });
    }
  }, [postingLoading, postingDetails, currentUser, offers, postingId, userId]);

  useEffect(() => {
    console.log('[PostingDetail][StateVerification]', {
      state,
      auth: {
        currentUser: currentUser?.uid,
        expectedOwner: userId
      },
      posting: {
        id: postingId,
        loaded: !!postingDetails
      },
      timestamp: getTimestamp()
    });
  }, [state, currentUser, userId, postingId, postingDetails]);

  useEffect(() => {
    console.log('[PostingDetail][StateDebug]', {
      timestamp: getTimestamp(),
      state,
      subscriptions: {
        types: Array.from(subscriptionManager.current.subscriptions.keys()),
        metrics: {
          totalTime: Date.now() - subscriptionManager.current.metrics.startTime,
          subscriptionCount: subscriptionManager.current.metrics.subscriptionCount,
          lastUpdate: new Date(subscriptionManager.current.metrics.lastUpdate).toISOString()
        }
      },
      props: { postingId, userId },
      auth: { currentUserId: currentUser?.uid },
      performance: {
        timeSinceLastUpdate: Date.now() - state.lastUpdate
      }
    });
  }, [state, postingId, userId, currentUser?.uid]);

  // Memoize header actions
  const headerActions = useMemo(() => (
    <HeaderActions
      isOwner={state.isOwner}
      userOffer={state.userOffer}
      currentUserId={state.currentUserId}
      postingId={postingId}
      onDelete={handleDeletePosting}
      onEdit={handleEditPosting}
      onLocation={handleMapMarkerPress}
    />
  ), [
    state.isOwner,
    state.userOffer,
    state.currentUserId,
    postingId,
    handleDeletePosting,
    handleEditPosting,
    handleMapMarkerPress
  ]);

  // error boundary logging to track the state when the error occurs
  useEffect(() => {
    if (postingDetails) {
      console.log('[PostingDetail] Posting details loaded for edit:', {
        hasDetails: !!postingDetails,
        hasLocation: !!itemLocation,
        isOwner: state.isOwner,
        timestamp: new Date().toISOString()
      });
    }
  }, [postingDetails, itemLocation, state.isOwner]);

  console.log('[PostingDetail] Component state:', {
    hasPostingDetails: !!postingDetails,
    loading: postingLoading,
    error: postingError,
    timestamp: new Date().toISOString()
  });

  // 4. Loading and error states AFTER all hooks
  if (postingLoading) {
    return (
      <View style={styles.loadingContainer} testID="loading-container">
        <ActivityIndicator size="large" color="#0000ff" />
      </View>
    );
  }

  if (postingError) {
    return (
      <View style={styles.errorContainer} testID="error-container">
        <Text style={styles.errorText}>
          {postingError === 'No such document' 
            ? 'This posting no longer exists'
            : `Error loading posting: ${postingError}`}
        </Text>
        <TouchableOpacity 
          onPress={refetchPostingDetails}
          style={styles.retryButton}
        >
          <Text style={styles.retryText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  // 5. Main render AFTER all hooks and conditions
  const renderHeaderActions = () => {
    console.log('[PostingDetail][Render] HeaderActions state:', {
      timestamp: new Date().toISOString(),
      isOwner: state.isOwner,
      userOffer: state.userOffer,
      isFavorite,  // This will now reflect the correct status
      currentUserId: state.currentUserId,
      postingId: state.postingDetailsId,
    });
    
    return (
      <View style={styles.headerActions}>
        {state.isOwner ? (
          // Owner Actions
          <>
            {postingDetails?.postingStatus !== 'Deleted' && (
              <>
                <TouchableOpacity 
                  testID="edit-posting-button"
                  style={styles.headerButton} 
                  onPress={handleEditPosting}
                >
                  <Ionicons name="create-outline" size={24} color="#007AFF" />
                </TouchableOpacity>
                <TouchableOpacity 
                  testID="delete-posting-button"
                  style={styles.headerButton} 
                  onPress={handleDeletePosting}
                >
                  <Ionicons name="trash-outline" size={24} color="#FF3B30" />
                </TouchableOpacity>
              </>
            )}
          </>
        ) : (
          // Offer Owner/Viewer Actions
          <>
            {!state.isOwner && postingDetails?.postingStatus !== 'Deleted' && (
              <>
                {!state.userOffer ? (
                  <TouchableOpacity 
                    testID="make-offer-button"
                    style={styles.headerButton} 
                    onPress={handleMakeOffer}
                  >
                    <Ionicons name="add-circle-outline" size={24} color="#007AFF" />
                  </TouchableOpacity>
                ) : (
                  <TouchableOpacity 
                    testID="edit-offer-button"
                    style={styles.headerButton} 
                    onPress={handleEditOffer}
                  >
                    <Ionicons name="create-outline" size={24} color="#007AFF" />
                  </TouchableOpacity>
                )}
              </>
            )}
            <TouchableOpacity 
              testID="favorite-button"
              style={styles.headerButton} 
              onPress={isFavorite ? handleRemoveFromFavorites : handleAddToFavorites}
            >
              <Ionicons 
                name={isFavorite ? "heart" : "heart-outline"} 
                size={24} 
                color={isFavorite ? "red" : "#007AFF"} 
              />
            </TouchableOpacity>
          </>
        )}
        {/* Location button always visible for all users */}
        <TouchableOpacity 
          testID="location-button"
          style={styles.headerButton}
          onPress={handleMapMarkerPress}
        >
          <Ionicons name="location" size={24} color="#007AFF" />
        </TouchableOpacity>
      </View>
    );
  };

  const renderOffer = ({ item }: { item: Offer }) => (
    <TouchableOpacity 
      style={styles.offerItem}
      onPress={() => handleOfferPress(item)}
    >
      <View style={styles.offerHeader}>
        <Text style={styles.offerPrice}>Price: ${item.price}</Text>
      </View>
      <Text 
        style={styles.offerDescription}
      >
        {item.description}
      </Text>
    </TouchableOpacity>
  );

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>{postingDetails?.title}</Text>
        {renderHeaderActions()}
      </View>

      {postingDetails?.postingStatus === 'Deleted' && (
        <Text style={styles.deletedNotice}>This posting was deleted by the owner.</Text>
      )}

      <View 
        testID="description-container"
        style={styles.descriptionContainer}
      >
        <Text
          testID="description-text"
          numberOfLines={state.isDescriptionExpanded ? undefined : 3}
          style={styles.description}
          onPress={() => dispatch({ type: 'TOGGLE_DESCRIPTION' })}
        >
          {postingDetails?.description}
        </Text>
        {(postingDetails?.description?.length || 0) > 100 && (
          <TouchableOpacity 
            onPress={() => dispatch({ type: 'TOGGLE_DESCRIPTION' })}
            style={styles.expandButton}
          >
            <Text style={styles.expandButtonText}>
              {state.isDescriptionExpanded ? 'Show Less' : 'Show More'}
            </Text>
          </TouchableOpacity>
        )}
      </View>

      <Text style={styles.offersTitle}>Offers:</Text>
      
      <View style={styles.tabContainer}>
        <TouchableOpacity 
          style={[styles.tab, state.activeTab === 'active' && styles.activeTab]}
          onPress={() => dispatch({ type: 'SWITCH_TAB', payload: 'active' })}
          testID="active-tab"
        >
          <Text style={[styles.tabText, state.activeTab === 'active' && styles.activeTabText]}>Active</Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={[styles.tab, state.activeTab === 'deleted' && styles.activeTab]}
          onPress={() => dispatch({ type: 'SWITCH_TAB', payload: 'deleted' })}
          testID="deleted-tab"
        >
          <Text style={[styles.tabText, state.activeTab === 'deleted' && styles.activeTabText]}>Deleted</Text>
        </TouchableOpacity>
      </View>

      {filteredOffers.length === 0 ? (
        <Text style={styles.noOffersText}>No {state.activeTab} offers available</Text>
      ) : (
        <FlatList
          data={filteredOffers}
          keyExtractor={(item) => item.id}
          renderItem={renderOffer}
          scrollEnabled={false}
        />
      )}
    </ScrollView>
  );
}

interface HeaderActionsProps {
  isOwner: boolean | null;
  userOffer: { id: string; status: string } | null;
  currentUserId: string | null;
  postingId: string | undefined;
  onDelete: () => void;
  onEdit: () => void;
  onLocation: () => void;
}

const HeaderActions: React.FC<HeaderActionsProps> = ({
  isOwner,
  userOffer,
  currentUserId,
  postingId,
  onDelete,
  onEdit,
  onLocation
}) => {
  console.log('[PostingDetail][HeaderActions] Rendering:', {
    isOwner,
    hasUserOffer: !!userOffer,
    currentUserId,
    timestamp: new Date().toISOString()
  });

  if (!currentUserId || !postingId) return null;

  return (
    <View style={styles.headerActionsContainer}>
      {isOwner && (
        <>
          <TouchableOpacity onPress={onEdit} style={styles.headerButton}>
            <AntDesign name="edit" size={24} color="#007AFF" />
          </TouchableOpacity>
          <TouchableOpacity onPress={onDelete} style={styles.headerButton}>
            <AntDesign name="delete" size={24} color="#FF3B30" />
          </TouchableOpacity>
        </>
      )}
      <TouchableOpacity onPress={onLocation} style={styles.headerButton}>
        <Ionicons name="location" size={24} color="#007AFF" />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    color: '#FF3B30',
    textAlign: 'center',
    fontSize: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    flex: 1,
    marginRight: 10,
  },
  locationButton: {
    padding: 8,
  },
  descriptionContainer: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  description: {
    fontSize: 16,
  },
  expandButton: {
    marginTop: 8,
  },
  expandButtonText: {
    color: '#007AFF',
    fontSize: 14,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerButton: {
    padding: 8,
    marginLeft: 8,
  },
  offerItem: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    backgroundColor: 'white',
  },
  offerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  offerPrice: {
    fontSize: 18,
    fontWeight: '600',
  },
  offerDescription: {
    fontSize: 16,
    color: '#666',
  },
  expandOfferButton: {
    padding: 4,
  },
  deletedNotice: {
    fontSize: 16,
    color: 'red',
    padding: 16,
    backgroundColor: '#ffebee',
  },
  offersTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    padding: 16,
    backgroundColor: '#f5f5f5',
  },
  tabContainer: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    marginHorizontal: 16,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  activeTab: {
    borderBottomColor: '#007AFF',
  },
  tabText: {
    fontSize: 16,
    color: '#666',
  },
  activeTabText: {
    color: '#007AFF',
    fontWeight: '600',
  },
  noOffersText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    padding: 20,
  },
  headerActionsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
  },
  retryButton: {
    marginTop: 16,
    padding: 12,
    backgroundColor: '#007AFF',
    borderRadius: 8,
  },
  retryText: {
    color: 'white',
    fontSize: 16,
    textAlign: 'center',
  },
});

export default PostingDetail;
