import { DraggableListContainer } from '../components/DraggableListContainer';
import React, { useState, useRef, useCallback, useEffect, useMemo, forwardRef, useImperativeHandle } from 'react';
import { NativeSyntheticEvent, NativeScrollEvent, Text, TouchableOpacity, Dimensions, Platform, Keyboard } from 'react-native';
import { View, StyleSheet, TextInput, ActivityIndicator, FlatList } from 'react-native';
import MapView, { Marker, Callout, Region as MapRegion } from 'react-native-maps';
import { useControlledPostings } from '../hooks/useControlledPostings';
import ListItem from '../components/ListItem';
import MapMarker from '../components/MapMarker';
import { Posting } from '../types/posting';
import AntDesign from 'react-native-vector-icons/AntDesign';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { useNavigation, useRoute } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootStackParamList } from '../types/navigation';
import { fetchPostingsBySearch, getUserFavorites } from '../services/firebaseService';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import SearchInput from '../components/SearchInput';
import NotificationBell from '../components/NotificationBell';
import { debounce } from 'lodash';
import { auth } from '../firebase';

type HomeScreenNavigationProp = StackNavigationProp<RootStackParamList>;

interface RenderItemProps {
  item: Posting;
}

interface MapRegion extends Region {
  latitude: number;
  longitude: number;
  latitudeDelta: number;
  longitudeDelta: number;
}

interface HomeScreenRouteParams {
  focusLocation?: {
    latitude: number;
    longitude: number;
    latitudeDelta: number;
    longitudeDelta: number;
  };
  selectedPosting?: any; // Replace 'any' with your posting type
  showCallout?: boolean;
}

export default function HomeScreen() {
  const navigation = useNavigation<HomeScreenNavigationProp>();
  const route = useRoute<{ params?: HomeScreenRouteParams }>();
  const [isLoading, setIsLoading] = useState(false);
  const { postings, isLoading: isPostingsLoading, fetchPostings, searchPostings } = useControlledPostings();
  const [isListScrollEnabled, setIsListScrollEnabled] = useState(true);
  const listRef = useRef<FlatList>(null);
  const mapRef = useRef<MapView>(null);
  const [region, setRegion] = useState<MapRegion>({
    latitude: 37.78825,
    longitude: -122.4324,
    latitudeDelta: 0.0922,
    longitudeDelta: 0.0421,
  });
  const [searchTerm, setSearchTerm] = useState('');
  const [showSearchButton, setShowSearchButton] = useState(false);
  const [isSearchMode, setIsSearchMode] = useState(false);
  const searchTimeoutRef = useRef(null);
  const lastPanTimeRef = useRef(Date.now());
  const NUMBER_OF_SEARCH_RESULTS = 10;
  const [markersKey] = useState(0);
  const [isMapReady, setIsMapReady] = useState(false);
  const insets = useSafeAreaInsets();
  const { height: screenHeight } = Dimensions.get('window');
  const [shouldUpdateMarkers, setShouldUpdateMarkers] = useState(false);
  const [selectedPosting, setSelectedPosting] = useState<Posting | null>(null);
  const [showCallout, setShowCallout] = useState(false);
  const [isCalloutVisible, setIsCalloutVisible] = useState(false);
  const currentUser = auth.currentUser;
  const [favoritePostings, setFavoritePostings] = useState<string[]>([]);

  // Calculate top position based on device
  const getTopPosition = () => {
    if (Platform.OS === 'ios') {
      // For iPhone models with notch or Dynamic Island
      if (insets.top > 20) {
        return insets.top + 10;
      }
      // For older iPhones
      return 40;
    }
    // For Android
    return insets.top + 10;
  };

  const handleListScroll = useCallback((event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const { contentOffset, velocity } = event.nativeEvent;
    
    if (contentOffset.y <= 0 && (velocity?.y ?? 0) > 0) {
      setIsListScrollEnabled(false);
    } else {
      setIsListScrollEnabled(true);
    }
  }, []);

  const renderItem = ({ item }: RenderItemProps) => {
    return <ListItem posting={item} />;
  };

  const renderEmptyComponent = () => (
    <View style={{ padding: 20, alignItems: 'center' }}>
      <Text>No postings found</Text>
    </View>
  );

  // Debounced region change handler
  const DEBOUNCE_DELAY = 300; // Reduce from 500ms to 300ms

  const debouncedRegionChange = useMemo(
    () => debounce((newRegion: MapRegion) => {
      console.log('Debounced region changed:', newRegion);
      setRegion(newRegion);
      
      if (searchTerm) {
        setShowSearchButton(true);
      } else {
        console.log('Fetching postings for new region');
        fetchPostings(newRegion);
      }
    }, DEBOUNCE_DELAY),
    [fetchPostings, searchTerm]
  );

  const handleRegionChange = useCallback((newRegion: MapRegion) => {
    console.log('Region changing:', newRegion);
    const now = Date.now();
    // Reduce the time threshold from 1000ms to 500ms
    if (now - lastPanTimeRef.current > 500) {
      debouncedRegionChange(newRegion);
    }
    lastPanTimeRef.current = now;
  }, [debouncedRegionChange]);

  // Clean up debounce on unmount
  useEffect(() => {
    return () => {
      debouncedRegionChange.cancel();
    };
  }, [debouncedRegionChange]);

  const handleSearch = useCallback(async (searchRegion: MapRegion, term: string) => {
    setIsLoading(true);
    try {
      await searchPostings(term, searchRegion);
    } finally {
      setIsLoading(false);
    }
  }, [searchPostings]);

  const handleSearchInputChange = (text: string) => {
    setSearchTerm(text);
    setShowSearchButton(!!text);
    setIsSearchMode(!!text);

    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    if (!text) {
      setIsSearchMode(false);
      handleSearch(region, '');
    }
  };

  useEffect(() => {
    console.log('Initial search with region:', region);
    handleSearch(region, '');
  }, []);

  useEffect(() => {
    if (shouldUpdateMarkers) {
      mapRef.current?.forceUpdate();
      setShouldUpdateMarkers(false);
    }
  }, [shouldUpdateMarkers]);

  // Fetch user's favorites when component mounts or user changes
  useEffect(() => {
    const fetchFavorites = async () => {
      if (currentUser) {
        try {
          const favorites = await getUserFavorites(currentUser.uid);
          setFavoritePostings(favorites);
        } catch (error) {
          console.error('Error fetching favorites:', error);
        }
      }
    };

    const unsubscribe = navigation.addListener('focus', () => {
      fetchFavorites();
    });

    return unsubscribe;
  }, [navigation, currentUser]);

  // Separate Marker component with correct interaction flow
  const PostingMarker = ({ posting, onCalloutPress, isFavorite }) => (
    <Marker
      key={posting.id}
      identifier={posting.id}
      coordinate={{
        latitude: posting.latitude,
        longitude: posting.longitude
      }}
    >
      <Callout
        onPress={() => {
          console.log('[HomeScreen] Callout pressed for posting:', posting.id);
          onCalloutPress();
        }}
      >
        <View style={styles.calloutContainer}>
          <Text style={styles.calloutTitle}>{posting.title}</Text>
          {isFavorite && (
            <Ionicons name="heart" size={16} color="red" />
          )}
        </View>
      </Callout>
    </Marker>
  );

  const mapMarkers = useMemo(() => (
    postings.map(posting => (
      <PostingMarker
        key={posting.id}
        posting={posting}
        isFavorite={favoritePostings.includes(posting.id)}
        onCalloutPress={() => {
          navigation.navigate('PostingDetail', {
            postingId: posting.id,
            itemLocation: {
              latitude: posting.latitude,
              longitude: posting.longitude
            },
            userId: posting.userId
          });
        }}
      />
    ))
  ), [postings, navigation, favoritePostings]);

  // Ensure map and list are always in sync
  const visiblePostings = useMemo(() => {
    return postings.filter(posting => 
      posting.latitude >= region.latitude - (region.latitudeDelta / 2) &&
      posting.latitude <= region.latitude + (region.latitudeDelta / 2) &&
      posting.longitude >= region.longitude - (region.longitudeDelta / 2) &&
      posting.longitude <= region.longitude + (region.longitudeDelta / 2)
    );
  }, [postings, region]);

  const styles = StyleSheet.create({
    container: {
      flex: 1,
    },
    map: {
      ...StyleSheet.absoluteFillObject,
    },
    headerContainer: {
      position: 'absolute',
      left: 0,
      right: 0,
      flexDirection: 'row',
      alignItems: 'flex-start',
      paddingHorizontal: 20,
      zIndex: 1,
      marginTop: -20,
    },
    searchContainer: {
      flex: 1,
      marginRight: 50,
      marginLeft: -10,
      backgroundColor: 'transparent',
      padding: 5,
      zIndex: 1,
    },
    searchInput: {
      backgroundColor: 'white',
      padding: 10,
      paddingRight: 30,
      borderRadius: 8,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5,
    },
    listContent: {
      paddingHorizontal: 20,
    },
    listContentContainer: {
      paddingBottom: 20,
      flexGrow: 1,
    },
    profileButton: {
      position: 'absolute',
      marginTop: 5,
      right: 20,
      padding: 1,
      backgroundColor: 'white',
      borderRadius: 30,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5,
    },
    searchButton: {
      position: 'absolute',
      left: '50%',
      transform: [{ translateX: -50 }],
      top: '50%',
      backgroundColor: 'white',
      padding: 8,
      borderRadius: 8,
      marginTop: 10,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5,
    },
    searchButtonText: {
      color: '#007AFF',
      fontWeight: '600',
    },
    clearButton: {
      position: 'absolute',
      right: 8,
      top: '50%',
      transform: [{ translateY: -10 }],
      padding: 4,
    },
    calloutContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: 8,
      minWidth: 120,
    },
    calloutTitle: {
      fontSize: 14,
      marginRight: 8,
      flex: 1,
    },
  });

  const dynamicStyles = {
    headerContainer: {
      ...styles.headerContainer,
      top: getTopPosition(),
    },
  };

  const handleClearSearch = useCallback(() => {
    setIsSearchMode(false);
    setShowSearchButton(false);
    setSearchTerm('');
    handleSearch(region, '');
  }, [region, handleSearch]);

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', () => {
      setIsListScrollEnabled(false);
    });

    const keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', () => {
      setIsListScrollEnabled(true);
    });

    // Cleanup listeners on unmount
    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);

  useEffect(() => {
    if (!route.params) return;

    const { focusLocation, selectedPosting, showCallout } = route.params;
    console.log('[HomeScreen] Handling route params:', { 
      hasFocusLocation: !!focusLocation,
      hasSelectedPosting: !!selectedPosting,
      showCallout 
    });

    if (focusLocation && mapRef.current) {
      // Add delay to ensure map is ready
      requestAnimationFrame(() => {
        mapRef.current?.animateToRegion(focusLocation, 1000);
        
        if (selectedPosting && showCallout) {
          console.log('[HomeScreen] Setting up callout display');
          setSelectedPosting(selectedPosting);
          setShowCallout(true);
        }
      });
    }
  }, [route.params]);

  useEffect(() => {
    if (selectedPosting && showCallout) {
      console.log('[HomeScreen] Attempting to show callout for:', selectedPosting.id);
      const marker = mapMarkers?.find(marker => 
        marker.props.identifier === selectedPosting.id
      );
      if (marker?.props.ref) {
        requestAnimationFrame(() => {
          try {
            marker.props.ref.showCallout();
          } catch (error) {
            console.error('[HomeScreen] Callout display failed:', error);
          }
        });
      }
    }
  }, [selectedPosting, showCallout, mapMarkers]);

  return (
    <View style={styles.container}>
      <MapView
        ref={mapRef}
        style={styles.map}
        region={region}
        onRegionChangeComplete={handleRegionChange}
        onPress={() => {
          Keyboard.dismiss();
          setIsCalloutVisible(false);
        }}
        onMarkerPress={(e) => {
          console.log('[HomeScreen] Marker pressed:', e.nativeEvent);
          setIsCalloutVisible(true);
        }}
        moveOnMarkerPress={false}
      >
        {mapMarkers}
      </MapView>

      <View style={dynamicStyles.headerContainer}>
        <View style={styles.searchContainer}>
          <SearchInput
            value={searchTerm}
            onChangeText={handleSearchInputChange}
            onClear={handleClearSearch}
            placeholder="Search..."
          />
          {showSearchButton && (
            <TouchableOpacity 
              style={styles.searchButton}
              onPress={() => handleSearch(region, searchTerm)}
            >
              <Text style={styles.searchButtonText}>Search This Area</Text>
            </TouchableOpacity>
          )}
        </View>
        <NotificationBell />
        <TouchableOpacity 
          style={styles.profileButton}
          onPress={() => navigation.navigate('Profile')}
        >
          <Ionicons name="person-circle-outline" size={32} color="#000" />
        </TouchableOpacity>
      </View>

      <DraggableListContainer
        searchFieldHeight={60}
        onScroll={handleListScroll}
      >
        <FlatList
          ref={listRef}
          data={visiblePostings} // Use visiblePostings instead of postings
          renderItem={renderItem}
          keyExtractor={(item) => item.id}
          ListEmptyComponent={renderEmptyComponent}
          contentContainerStyle={[styles.listContentContainer, styles.listContent]}
          scrollEnabled={isListScrollEnabled}
        />
      </DraggableListContainer>
    </View>
  );
}
