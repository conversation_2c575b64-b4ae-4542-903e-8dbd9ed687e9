// OfferDetail.tsx

import React, { useEffect, useState, useRef, useCallback, useMemo } from 'react';
import { View, Text, TextInput, Button, FlatList, StyleSheet, KeyboardAvoidingView, Platform, Keyboard, TouchableWithoutFeedback, ActivityIndicator, RefreshControl, TouchableOpacity, ScrollView, Alert, SectionList } from 'react-native';
import { useRoute, useNavigation, RouteProp, NavigationProp } from '@react-navigation/native';
import { auth } from '../firebase';
import { fetchMessagesForOffer, addMessageToDiscussion, markMessagesAsRead, fetchPaginatedMessages, getPostingIdForOffer } from '../services/firebaseService';
import { useWindowDimensions } from 'react-native';
import { withRetry, DEFAULT_RETRY_CONFIG } from '../utils/retryUtils';
import { RetryError } from '../utils/errorRecovery';
import useWithdrawOffer from '../hooks/useWithdrawOffer';
import cacheManager from '../utils/cacheManager';
import { onSnapshot, query, collection, where, orderBy, limit, serverTimestamp, doc, getDoc, updateDoc, increment, Timestamp, setDoc } from 'firebase/firestore';
import { db } from '../firebase';
import { MessageItem } from '../components/offer-detail/MessageItem';
import { PostingDetailsSection } from '../components/offer-detail/PostingDetailsSection';
import { MessageInput } from '../components/offer-detail/MessageInput';
import { OfferDetailsSection } from '../components/offer-detail/OfferDetailsSection';
import useOfferDetails from '../hooks/useOfferDetails';
import { StatusBanner } from '../components/offer-detail/StatusBanner';
import { Message, FirebaseTimestamp, FirebaseError, MessageResult } from '../types/firebase';
import { logError, ErrorContext, AppError } from '../utils/errorLogger';
import { validateTimestamp, isFirestoreTimestamp } from '../utils/timestampValidation';
import messaging from '@react-native-firebase/messaging';

const MESSAGES_PER_PAGE = 20;

interface OfferDetails {
  posting?: {
    id: string;
    title?: string;
    description?: string;
    postingStatus?: string;
    userId?: string;
  };
  price?: string;
  description?: string;
  status?: string;
  userId?: string;
  postingId?: string;
  userScore?: number;
  restrictionEndDate?: Date | null;
}

interface RouteParams {
  offerId: string;
  postingId: string;
  postingOwnerId: string;
  offerOwnerId: string;
  refresh?: boolean;
  initialOffer?: {
    id: string;
    userId: string;
    postingId: string;
    price?: string;
    description?: string;
    status?: string;
    [key: string]: any;
  } | null;
  initialPosting?: {
    id: string;
    userId: string;
    title?: string;
    description?: string;
    postingStatus?: string;
    [key: string]: any;
  } | null;
}

type RootStackParamList = {
  OfferDetail: RouteParams;
  PostingDetail: {
    postingId: string;
    userId?: string;
    refresh?: boolean;
  };
  EditOffer: { offerId: string };
};

interface PostingDetailsSectionProps {
  postingId: string;
  postingOwnerId: string | null;
  postingTitle: string;
  postingDescription: string;
  isDetailsExpanded: boolean;
  setIsDetailsExpanded: React.Dispatch<React.SetStateAction<boolean>>;
  navigation: NavigationProp<RootStackParamList>;
  isLoading: boolean;
  postingStatus: string;
  currentUserId: string | undefined;
  onMarkAbusive: () => Promise<void>;
  refetchPostingDetails: () => Promise<void>;
  showRefresh?: boolean;
  onRefreshComplete?: () => void;
}

interface OfferDetailsSectionProps {
  offerPrice: string;
  offerDescription: string;
  isOfferDetailsExpanded: boolean;
  setIsOfferDetailsExpanded: React.Dispatch<React.SetStateAction<boolean>>;
  currentUserId: string | undefined;
  offerOwnerId: string | null;
  offerStatus: string;
  offerId: string;
  navigation: NavigationProp<RootStackParamList>;
  onWithdraw: () => Promise<void>;
  isLoading: boolean;
  userScore?: number;
  isPostingOwner: boolean;
  onMarkAbusive: () => Promise<void>;
  restrictionEndDate?: Date | null;
  showRefresh?: boolean;
  onRefreshComplete?: () => void;
}

interface FirebaseMessageResponse {
  messages: Message[];
  lastDoc: any;
  hasMore: boolean;
  hasNewMessages?: boolean;
  newMessages?: Message[];
}

interface MessageSortable {
  timestamp: {
    toDate: () => Date;
  };
}

const compareMessages = (messageA: Message, messageB: Message): number => {
  return messageB.timestamp.toDate().getTime() - messageA.timestamp.toDate().getTime();
};

function safeStringify(obj: unknown): string {
  try {
    return JSON.stringify(obj);
  } catch (err) {
    return 'Error stringifying object';
  }
}

interface MessageValidationResult {
  isValid: boolean;
  message?: string;
}

// Type guard for FirebaseError
const isFirebaseError = (error: unknown): error is FirebaseError => {
  return error instanceof Error && 'code' in error;
};

const handleError = (error: unknown, context: Omit<ErrorContext, 'component'>) => {
  const appError: AppError = isFirebaseError(error)
    ? {
        message: error.message,
        code: error.code,
        stack: error.stack,
        name: error.name
      }
    : error instanceof Error
      ? {
          message: error.message,
          stack: error.stack,
          name: error.name
        }
      : {
          message: String(error)
        };

  logError(appError, {
    component: 'OfferDetail',
    ...context
  });
};

// Type guard for Message
const isMessage = (value: unknown): value is Message => {
  return value !== null &&
         typeof value === 'object' &&
         'senderId' in value &&
         'text' in value &&
         'timestamp' in value;
};

// Update validateMessage to use the new timestamp validation
const validateMessage = (msg: unknown): msg is Message => {
  const messageData = msg && typeof msg === 'object' ? msg as Record<string, unknown> : null;

  try {
    // Log message structure for debugging
    console.log('[validateMessage] Validating message:', {
      hasMessage: !!messageData,
      messageStructure: messageData ? Object.keys(messageData) : [],
      timestampType: messageData?.timestamp ? typeof messageData.timestamp : 'undefined'
    });

    // Basic required fields check
    if (!messageData || typeof messageData !== 'object') {
      handleError(new Error('Invalid message format: not an object'), {
        action: 'validateMessage',
        severity: 'medium',
        data: { messageData }
      });
      return false;
    }

    const { senderId, text, timestamp } = messageData;
    if (!senderId || !text || !timestamp) {
      handleError(new Error('Invalid message format: missing required fields'), {
        action: 'validateMessage',
        severity: 'medium',
        data: {
          hasSenderId: !!senderId,
          hasText: !!text,
          hasTimestamp: !!timestamp,
          messageData
        }
      });
      return false;
    }

    // Use the new timestamp validation
    const validatedTimestamp = validateTimestamp(timestamp, 'message.timestamp');
    if (!validatedTimestamp) {
      handleError(new Error('Invalid timestamp format'), {
        action: 'validateMessage',
        severity: 'medium',
        data: {
          messageId: messageData.id,
          timestamp,
          timestampType: typeof timestamp
        }
      });
      return false;
    }

    return true;
  } catch (error: unknown) {
    handleError(error, {
      action: 'validateMessage',
      severity: 'medium',
      data: {
        messageId: messageData?.id,
        error: error instanceof Error ? error.message : String(error)
      }
    });
    return false;
  }
};

const handleMessageError = (error: unknown, action: string, data?: Record<string, unknown>) => {
  if (isFirebaseError(error)) {
    handleError(error, {
      action,
      severity: 'medium',
      data: {
        ...data,
        errorCode: error.code,
        errorName: error.name,
        errorMessage: error.message,
        errorStack: error.stack
      }
    });
  } else if (error instanceof Error) {
    handleError(error, {
      action,
      severity: 'medium',
      data: {
        ...data,
        errorName: error.name,
        errorMessage: error.message,
        errorStack: error.stack
      }
    });
  } else {
    handleError(new Error(String(error)), { action, severity: 'medium', data });
  }
  return false;
};

function OfferDetail(): React.JSX.Element {
  const route = useRoute<RouteProp<RootStackParamList, 'OfferDetail'>>();
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  const {
    offerId,
    initialOffer = null,
    initialPosting = null
  } = route.params || {};
  const currentUser = auth.currentUser;

  // Restore state variables
  const [postingOwnerId, setPostingOwnerId] = useState<string | null>(null);
  const [offerOwnerId, setOfferOwnerId] = useState<string | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [error, setError] = useState<string | null>(null);

  const [loading, setLoading] = useState(true);
  const {
    loading: hookLoading,
    error: hookError,
    offerDetails,
    messages: hookMessages,
    setOfferDetails,
    refreshData,
    isRefreshing: hookRefreshing
  } = useOfferDetails(offerId, { initialOffer, initialPosting }) as {
    loading: boolean;
    error: string | null;
    offerDetails: OfferDetails | null;
    messages: Message[];
    setOfferDetails: React.Dispatch<React.SetStateAction<OfferDetails | null>>;
    refreshData: () => Promise<void>;
    isRefreshing: boolean;
  };

  // Sync loading states
  useEffect(() => {
    setLoading(hookLoading || hookRefreshing);
  }, [hookLoading, hookRefreshing]);

  // Update message handling
  useEffect(() => {
    if (hookMessages?.length > 0) {
      console.log('Updating messages from hook:', hookMessages.length);
      setMessages(prevMessages => {
        // Only update if we have new messages or no existing messages
        if (!prevMessages.length || prevMessages.length !== hookMessages.length) {
          return hookMessages;
        }
        return prevMessages;
      });
    }
  }, [hookMessages]);

  // Add cleanup handling
  useEffect(() => {
    return () => {
      console.log('Cleaning up OfferDetail component');
      // Use safe cleanup
      if (navigationRef.current.isMounted && !cleanupRef.current) {
        setMessages([]);
        setLoading(true);
      }
    };
  }, []);

  useEffect(() => {
    if (hookError) {
      setError(hookError);
    }
  }, [hookError]);

  const [newMessage, setNewMessage] = useState('');
  const [isDetailsExpanded, setIsDetailsExpanded] = useState(false);
  const [isOfferDetailsExpanded, setIsOfferDetailsExpanded] = useState(false);
  const flatListRef = useRef<FlatList>(null);
  const [isAtBottom, setIsAtBottom] = useState(true);
  const [isInitialLoad, setIsInitialLoad] = useState(true);

  // Memoize derived data
  const {
    postingTitle,
    postingDescription,
    postingStatus,
    offerPrice,
    offerDescription,
    offerStatus,
    postingOwnerId: hookPostingOwnerId,
    offerOwnerId: hookOfferOwnerId
  } = useMemo(() => {
    const result = {
      postingTitle: route.params?.initialPosting?.title || offerDetails?.posting?.title || '',
      postingDescription: route.params?.initialPosting?.description || offerDetails?.posting?.description || '',
      postingStatus: offerDetails?.posting?.postingStatus || 'Active',
      offerPrice: offerDetails?.price?.toString() || '',
      offerDescription: offerDetails?.description || '',
      offerStatus: offerDetails?.status || 'active',
      postingOwnerId: offerDetails?.posting?.userId,
      offerOwnerId: offerDetails?.userId
    };

    // Enhanced logging for offer status tracking
    console.log('[OfferDetail][STATUS_TRACKING]', {
      offerId,
      offerStatus: result.offerStatus,
      postingStatus: result.postingStatus,
      hasOfferDetails: !!offerDetails,
      hasInitialOffer: !!initialOffer,
      initialOfferStatus: initialOffer?.status,
      timestamp: new Date().toISOString()
    });

    return result;
  }, [offerDetails, route.params?.initialPosting, offerId, initialOffer]);

  const { height: screenHeight } = useWindowDimensions();
  const [refreshing, setRefreshing] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [hasMoreMessages, setHasMoreMessages] = useState(true);
  const [lastMessageDoc, setLastMessageDoc] = useState<any>(null);
  const [keyboardHeight, setKeyboardHeight] = useState(0);
  const { withdrawOffer: withdrawOfferFn } = useWithdrawOffer();

  const [isInitialized, setIsInitialized] = useState(false);
  const messageSubscriptionRef = useRef<(() => void) | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const refreshTimerRef = useRef<NodeJS.Timeout | null>(null);
  const mountedRef = useRef(true);
  const cleanupRef = useRef({
    isCleaningUp: false,
    isMounted: true
  });

  // Add navigation state tracking
  const navigationRef = useRef({
    isNavigating: false,
    isMounted: true
  });

  // Add lifecycle tracking ref
  const lifecycleRef = useRef({
    mountCount: 0,
    cleanupStarted: false,
    lastAction: '',
    errors: [] as string[]
  });

  // Add comprehensive logging function
  const logLifecycle = useCallback((action: string, data?: any) => {
    lifecycleRef.current.lastAction = action;
    console.log(`[OfferDetail][${action}]`, {
      timestamp: new Date().toISOString(),
      mountCount: lifecycleRef.current.mountCount,
      cleanupStarted: cleanupRef.current,
      lastAction: lifecycleRef.current.lastAction,
      ...data
    });
  }, []);

  const keyboardVerticalOffset = screenHeight > 800 ? 100 : 50;

  const isAuthorizedUser = currentUser?.uid === postingOwnerId || currentUser?.uid === offerOwnerId;

  useEffect(() => {
    if (messages.length > 0 && currentUser) {
      // Only mark messages as read if user is either the posting owner or offer owner
      const canMarkAsRead = currentUser.uid === postingOwnerId || currentUser.uid === offerOwnerId;

      if (canMarkAsRead) {
        console.log('Marking messages as read for authorized user:', {
          userId: currentUser.uid,
          isPostingOwner: currentUser.uid === postingOwnerId,
          isOfferOwner: currentUser.uid === offerOwnerId
        });
        markMessagesAsRead(offerId, currentUser.uid).catch(error => {
          console.error('Error marking messages as read:', error);
        });
      } else {
        console.log('User is not authorized to mark messages as read:', {
          userId: currentUser.uid,
          postingOwnerId,
          offerOwnerId
        });
      }
    }
  }, [messages, currentUser, offerId, postingOwnerId, offerOwnerId]);

  useEffect(() => {
    // Track mount status
    const isMounted = { current: true };

    // Log mount
    logLifecycle('MOUNT', { params: route.params });

    // Clear any existing messages and subscriptions
    setMessages([]);
    if (messageSubscriptionRef.current) {
      messageSubscriptionRef.current();
      messageSubscriptionRef.current = null;
    }

    // Set up new subscription if we have an offerId
    if (offerId) {
      console.log('[Messages Subscription] Setting up subscription for offerId:', offerId);

      const discussionsRef = collection(db, 'discussions');
      const q = query(
        discussionsRef,
        where('offerId', '==', offerId)
      );

      const unsubscribe = onSnapshot(
        q,
        (snapshot) => {
          if (!isMounted.current) return;

          console.log('[Messages Subscription] Received snapshot update:', {
            size: snapshot.size,
            empty: snapshot.empty,
            metadata: {
              hasPendingWrites: snapshot.metadata.hasPendingWrites,
              fromCache: snapshot.metadata.fromCache
            }
          });

          if (!snapshot.empty) {
            const discussionDoc = snapshot.docs[0];
            const discussionData = discussionDoc.data();
            console.log('[Messages Subscription] Processing discussion:', {
              id: discussionDoc.id,
              messageCount: discussionData.messages?.length || 0
            });

            if (discussionData.messages && discussionData.messages.length > 0) {
              console.log('[Messages Subscription] Raw messages:', {
                count: discussionData.messages.length,
                sampleMessage: discussionData.messages[0],
                messageKeys: discussionData.messages[0] ? Object.keys(discussionData.messages[0]) : []
              });

              const validMessages = discussionData.messages.filter(validateMessage);

              console.log('[Messages Subscription] Validation summary:', {
                totalMessages: discussionData.messages.length,
                validMessages: validMessages.length,
                invalidMessages: discussionData.messages.length - validMessages.length,
                firstValidMessage: validMessages[0] ? {
                  id: validMessages[0].id,
                  senderId: validMessages[0].senderId,
                  timestamp: validMessages[0].timestamp
                } : null
              });

              if (isMounted.current) {
                setMessages(prevMessages => {
                  const messageMap = new Map<string, Message>();

                  console.log('[Messages Subscription] Processing messages:', {
                    prevCount: prevMessages.length,
                    newCount: validMessages.length,
                    hasLegacyMessages: validMessages.some((msg: Message) => !msg.id),
                    messageTypes: validMessages.map((msg: Message) => ({
                      hasId: !!msg.id,
                      hasTimestamp: !!msg.timestamp,
                      timestampType: msg.timestamp ? typeof msg.timestamp : 'undefined'
                    }))
                  });

                  // Add existing messages to map
                  prevMessages.forEach((msg: Message) => {
                    const key = msg.id || `${msg.senderId}_${msg.timestamp?.toDate ? msg.timestamp.toDate().getTime() : (msg.timestamp.seconds * 1000 + Math.floor(msg.timestamp.nanoseconds / 1e6))}`;
                    messageMap.set(key, msg);
                  });

                  // Add new messages to map
                  validMessages.forEach((msg: Message) => {
                    const key = msg.id || `${msg.senderId}_${msg.timestamp?.toDate ? msg.timestamp.toDate().getTime() : (msg.timestamp.seconds * 1000 + Math.floor(msg.timestamp.nanoseconds / 1e6))}`;
                    messageMap.set(key, msg);
                  });

                  const allMessages = Array.from(messageMap.values());
                  const sortedMessages = allMessages.sort((a: Message, b: Message) => {
                    const timeA = a.timestamp?.toDate ? a.timestamp.toDate().getTime() : (a.timestamp.seconds * 1000 + Math.floor(a.timestamp.nanoseconds / 1e6));
                    const timeB = b.timestamp?.toDate ? b.timestamp.toDate().getTime() : (b.timestamp.seconds * 1000 + Math.floor(b.timestamp.nanoseconds / 1e6));
                    return timeB - timeA;
                  });

                  console.log('[Messages Subscription] Updated messages state:', {
                    previousCount: prevMessages.length,
                    newCount: sortedMessages.length,
                    firstMessage: sortedMessages[0] ? {
                      id: sortedMessages[0].id,
                      senderId: sortedMessages[0].senderId,
                      timestamp: sortedMessages[0].timestamp,
                      text: sortedMessages[0].text
                    } : null,
                    lastMessage: sortedMessages[sortedMessages.length - 1] ? {
                      id: sortedMessages[sortedMessages.length - 1].id,
                      senderId: sortedMessages[sortedMessages.length - 1].senderId,
                      timestamp: sortedMessages[sortedMessages.length - 1].timestamp,
                      text: sortedMessages[sortedMessages.length - 1].text
                    } : null,
                    hasLegacyMessages: sortedMessages.some((msg: Message) => !msg.id)
                  });

                  return sortedMessages;
                });
              }
            } else {
              console.log('[Messages Subscription] No messages found in discussion');
              if (isMounted.current) {
                setMessages([]);
              }
            }
          } else {
            console.log('[Messages Subscription] No discussion found');
            if (isMounted.current) {
              setMessages([]);
            }
          }
        },
        (error: unknown) => {
          if (error instanceof Error) {
            const errorDetails = {
              message: error.message,
              stack: error.stack,
              ...(error as FirebaseError).code ? { code: (error as FirebaseError).code } : {}
            };
            console.error('[Messages Subscription] Error:', errorDetails);
          } else {
            console.error('[Messages Subscription] Unknown error:', error);
          }
          if (isMounted.current) {
            setupPolling();
          }
        }
      );

      // Store subscription for cleanup
      messageSubscriptionRef.current = unsubscribe;
    }

    // Cleanup function
    return () => {
      console.log('[Messages Subscription] Cleaning up subscription');
      isMounted.current = false;

      // Clean up subscription
      if (messageSubscriptionRef.current) {
        messageSubscriptionRef.current();
        messageSubscriptionRef.current = null;
      }

      // Reset state
      setMessages([]);
      setLoading(true);
      setError(null);

      logLifecycle('UNMOUNT');
    };
  }, [offerId]); // Only re-run if offerId changes

  // Separate polling setup function for fallback
  const setupPolling = () => {
    let lastKnownTimestamp = messages[messages.length - 1]?.timestamp?.seconds * 1000 +
      Math.floor((messages[messages.length - 1]?.timestamp?.nanoseconds || 0) / 1e6) || 0;

    const pollInterval = setInterval(async () => {
      try {
        if (messages.length === 0) return;

        const result = await fetchMessagesForOffer(offerId);
        if (!result) return;

        const typedResult = result as MessageResult;

        if (typedResult.hasNewMessages && typedResult.newMessages) {
          console.log(`[setupPolling] Found new messages in poll since ${new Date(lastKnownTimestamp)}`);
          setMessages(prevMessages => {
            const messageMap = new Map<string, Message>();

            // Add existing messages to map
            prevMessages.forEach((msg: Message) => {
              if (msg.id) {
                messageMap.set(msg.id, msg);
              }
            });

            // Add new messages to map, overwriting if same ID
            typedResult.newMessages!.forEach((msg: Message) => {
              if (msg.id) {
                messageMap.set(msg.id, msg);
              }
            });

            // Convert map back to array and sort
            const allMessages = Array.from(messageMap.values());
            const sortedMessages = allMessages.sort((a: Message, b: Message) => {
              const timeA = (a.timestamp?.seconds || 0) * 1000 + Math.floor((a.timestamp?.nanoseconds || 0) / 1e6);
              const timeB = (b.timestamp?.seconds || 0) * 1000 + Math.floor((b.timestamp?.nanoseconds || 0) / 1e6);
              return timeB - timeA; // Newest first for inverted FlatList
            });

            const lastMessage = sortedMessages[0]; // First message is newest due to sort
            if (lastMessage?.timestamp) {
              lastKnownTimestamp = lastMessage.timestamp.seconds * 1000 +
                Math.floor(lastMessage.timestamp.nanoseconds / 1e6);
            }

            return sortedMessages;
          });
        }
      } catch (error: unknown) {
        if (error instanceof Error) {
          const errorDetails = {
            message: error.message,
            stack: error.stack,
            ...(error as FirebaseError).code ? { code: (error as FirebaseError).code } : {}
          };
          console.error('[setupPolling] Error polling messages:', errorDetails);
        } else {
          console.error('[setupPolling] Unknown error polling messages:', safeStringify(error));
        }
      }
    }, 60000);

    return pollInterval;
  };

  const loadInitialMessages = async () => {
    try {
      console.log('[loadInitialMessages] Starting to load messages...');
      const result = await fetchMessagesForOffer(offerId);

      if (!result || !result.messages) {
        console.log('[loadInitialMessages] No messages found or invalid result:', result);
        setMessages([]);
        setHasMoreMessages(false);
        return;
      }

      console.log('[loadInitialMessages] Raw messages received:', {
        count: result.messages.length,
        messages: result.messages.map((msg: Partial<Message>) => ({
          id: msg.id,
          senderId: msg.senderId,
          timestamp: msg.timestamp,
          hasTimestamp: !!msg.timestamp
        }))
      });

      if (result.messages.length > 0) {
        // Enhanced validation to check message structure
        const validMessages = result.messages.filter(validateMessage);

        console.log('[loadInitialMessages] Validation results:', {
          totalMessages: result.messages.length,
          validMessages: validMessages.length,
          invalidMessages: result.messages.length - validMessages.length,
          validMessageIds: validMessages.map((msg: Message) => msg.id)
        });

        if (validMessages.length > 0) {
          const sortedMessages = [...validMessages].sort((a, b) => {
            const timeA = (a.timestamp?.seconds || 0) * 1000 + Math.floor((a.timestamp?.nanoseconds || 0) / 1e6);
            const timeB = (b.timestamp?.seconds || 0) * 1000 + Math.floor((b.timestamp?.nanoseconds || 0) / 1e6);
            return timeA - timeB;
          });

          console.log('[loadInitialMessages] Final sorted messages:', {
            count: sortedMessages.length,
            messageIds: sortedMessages.map(msg => msg.id),
            timestamps: sortedMessages.map(msg => new Date(msg.timestamp.seconds * 1000).toISOString())
          });

          setMessages(sortedMessages.reverse());
          setLastMessageDoc(result.lastDoc);
          setHasMoreMessages(!!result.hasMore);
        } else {
          console.log('[loadInitialMessages] No valid messages found after filtering');
          setMessages([]);
          setHasMoreMessages(false);
        }
      } else {
        console.log('[loadInitialMessages] No messages found in result');
        setMessages([]);
        setHasMoreMessages(false);
      }
    } catch (error: unknown) {
      if (error instanceof Error) {
        const errorDetails = {
          message: error.message,
          stack: error.stack,
          ...(error as FirebaseError).code ? { code: (error as FirebaseError).code } : {}
        };
        console.error('[loadInitialMessages] Error:', errorDetails);
      } else {
        console.error('[loadInitialMessages] Unknown error:', error);
      }
      setError('Failed to load messages');
      setMessages([]);
      setHasMoreMessages(false);
    }
  };

  const handleLoadMore = async () => {
    if (loadingMore || !hasMoreMessages || !lastMessageDoc) {
      console.log('Skipping load more:', {
        loadingMore,
        hasMoreMessages,
        hasLastDoc: !!lastMessageDoc,
        lastDocId: lastMessageDoc?.id
      });
      return;
    }

    try {
      setLoadingMore(true);
      console.log('Loading more messages from:', lastMessageDoc?.timestamp?.toDate());

      const result = await fetchMessagesForOffer(offerId);
      if (!result) return;

      const typedResult = result as MessageResult;

      if (!typedResult.messages.length) {
        console.log('No more messages to load');
        setHasMoreMessages(false);
        return;
      }

      setMessages(prevMessages => {
        const messageMap = new Map<string, Message>();
        [...prevMessages, ...typedResult.messages].forEach(msg => {
          if (msg.id && !messageMap.has(msg.id)) {
            messageMap.set(msg.id, msg);
          }
        });
        return Array.from(messageMap.values()).sort(compareMessages);
      });

      setLastMessageDoc(typedResult.lastDoc);
      setHasMoreMessages(typedResult.hasMore);
    } catch (error) {
      if (error instanceof Error) {
        const firebaseError = error as FirebaseError;
        console.error('Error loading more messages:', {
          code: firebaseError.code,
          message: firebaseError.message,
          stack: firebaseError.stack
        });
      } else {
        console.error('Unknown error loading more messages:', safeStringify(error));
      }
    } finally {
      setLoadingMore(false);
    }
  };

  const handleRefresh = useCallback(async () => {
    if (isAtBottom) return; // Don't refresh if at bottom

    setRefreshing(true);
    try {
      await loadInitialMessages();
    } finally {
      setRefreshing(false);
    }
  }, [isAtBottom]);

  const handleSendMessage = async () => {
    if (!newMessage.trim() || !offerId || !currentUser) {
      console.log('[handleSendMessage] Invalid input:', {
        hasMessage: !!newMessage.trim(),
        hasOfferId: !!offerId,
        hasUser: !!currentUser,
        messageLength: newMessage?.length,
        offerId,
        userId: currentUser?.uid
      });
      return;
    }

    try {
      console.log('[handleSendMessage] Starting message send:', {
        offerId,
        userId: currentUser.uid,
        messageLength: newMessage.length,
        timestamp: new Date().toISOString()
      });

      const messageData = {
        text: newMessage.trim(),
        senderId: currentUser.uid,
        timestamp: Timestamp.now(),
        read: false,
        id: `${Date.now()}_${currentUser.uid}`
      };

      console.log('[handleSendMessage] Prepared message data:', {
        ...messageData,
        timestampType: typeof messageData.timestamp,
        timestampDetails: {
          seconds: messageData.timestamp.seconds,
          nanoseconds: messageData.timestamp.nanoseconds
        }
      });

      await addMessageToDiscussion(offerId, messageData);
      console.log('[handleSendMessage] Message sent successfully:', {
        messageId: messageData.id,
        timestamp: messageData.timestamp
      });

      setNewMessage('');

      // Refresh messages after sending
      try {
        console.log('[handleSendMessage] Refreshing messages');
        await loadInitialMessages();
        console.log('[handleSendMessage] Messages refreshed successfully');
      } catch (refreshError) {
        console.error('[handleSendMessage] Error refreshing messages:', {
          error: refreshError instanceof Error ? {
            message: refreshError.message,
            stack: refreshError.stack
          } : refreshError,
          messageId: messageData.id
        });
      }
    } catch (error: unknown) {
      handleMessageError(error, 'sendMessage', {
        messageLength: newMessage.length,
        offerId,
        userId: currentUser.uid
      });
      setError('Failed to send message');
      Alert.alert('Error', 'Failed to send message. Please try again.');
    }
  };

  const handleWithdrawOffer = async () => {
    if (!offerId) {
      console.error('Cannot withdraw offer: Missing offerId');
      return;
    }

    try {
      Alert.alert(
        'Withdraw Offer',
        'Are you sure you want to withdraw this offer?',
        [
          {
            text: 'Cancel',
            style: 'cancel',
          },
          {
            text: 'Withdraw',
            style: 'destructive',
            onPress: async () => {
              console.log('Withdrawing offer:', { offerId });
              setLoading(true);
              try {
                await withdrawOfferFn(offerId);
                console.log('Offer withdrawn successfully');

                // Navigate to posting detail screen after successful withdrawal
                if (offerDetails?.postingId) {
                  navigation.navigate('PostingDetail', {
                    postingId: offerDetails.postingId,
                    userId: offerDetails.posting?.userId,
                    refresh: true
                  });
                } else {
                  console.error('Cannot navigate: Missing postingId');
                }
              } catch (error: unknown) {
                const errorMessage = error instanceof Error ? error.message : 'Unknown error withdrawing offer';
                console.error('Error in withdrawOffer:', errorMessage);
                Alert.alert('Error', 'Failed to withdraw offer. Please try again.');
              } finally {
                setLoading(false);
              }
            },
          },
        ],
      );
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error showing withdraw dialog';
      console.error('Error showing withdraw dialog:', errorMessage);
    }
  };

  useEffect(() => {
    console.log('=== OfferDetail Screen Mounted ===');
    console.log('Route params:', route.params);

    if (route.params) {
      const { postingOwnerId: routePostingOwnerId, offerOwnerId: routeOfferOwnerId } = route.params;
      console.log('Setting owner IDs from route params:', {
        postingOwnerId: routePostingOwnerId,
        offerOwnerId: routeOfferOwnerId
      });

      if (routePostingOwnerId) {
        setPostingOwnerId(routePostingOwnerId);
      }
      if (routeOfferOwnerId) {
        setOfferOwnerId(routeOfferOwnerId);
      }
    }
  }, [route.params]);

  // Update mount effect
  useEffect(() => {
    lifecycleRef.current.mountCount++;
    logLifecycle('MOUNT', { params: route.params });

    return () => {
      logLifecycle('UNMOUNT');
    };
  }, []);

  // Update main cleanup effect
  useEffect(() => {
    return () => {
      logLifecycle('CLEANUP_START');

      try {
        navigationRef.current.isMounted = false;
        cleanupRef.current.isCleaningUp = true;
        cleanupRef.current.isMounted = false;

        // Clean up subscriptions first
        if (messageSubscriptionRef.current) {
          logLifecycle('CLEANUP_SUBSCRIPTIONS');
          messageSubscriptionRef.current();
          messageSubscriptionRef.current = null;
        }

        // Then clean up timers
        if (refreshTimerRef.current) {
          logLifecycle('CLEANUP_TIMERS');
          clearTimeout(refreshTimerRef.current);
          refreshTimerRef.current = null;
        }

        // Finally clean up state
        logLifecycle('CLEANUP_STATE');
        setIsRefreshing(false);
        setLoading(false);
        setError(null);

        logLifecycle('CLEANUP_COMPLETE');
      } catch (error: unknown) {
        const errorDetails = error instanceof Error
          ? {
              message: error.message,
              stack: error.stack
            }
          : { message: String(error) };

        logLifecycle('CLEANUP_ERROR', { error: errorDetails });
        lifecycleRef.current.errors.push(errorDetails.message);
        console.error('Error during cleanup:', errorDetails);
      }
    };
  }, []);

  // Update refresh effect to use the hook's refreshData function
  useEffect(() => {
    if (route.params?.refresh && !isRefreshing && !cleanupRef.current) {
      console.log('[OfferDetail] Refresh requested:', {
        hasInitialOffer: !!route.params.initialOffer,
        hasInitialPosting: !!route.params.initialPosting,
        timestamp: new Date().toISOString()
      });

      if (route.params.initialOffer) {
        // Update local state with initial offer data
        const updatedOffer = route.params.initialOffer;
        console.log('[OfferDetail] Using initial offer data:', {
          offerId: updatedOffer.id,
          price: updatedOffer.price,
          description: updatedOffer.description,
          timestamp: new Date().toISOString()
        });

        // Update the offer details state directly
        setOfferDetails(prevDetails => {
          if (!prevDetails) return {
            ...updatedOffer,
            posting: route.params.initialPosting || undefined
          };

          return {
            ...prevDetails,
            ...updatedOffer,
            posting: route.params.initialPosting || prevDetails.posting
          };
        });
      } else {
        // If no initial offer data, use the refreshData function
        refreshData().catch(error => {
          console.error('[OfferDetail] Error refreshing data:', error);
        });
      }
    }
  }, [route.params?.refresh, route.params?.initialOffer, route.params?.initialPosting, refreshData]);

  useEffect(() => {
    if (!currentUser) {
      console.error('No authenticated user');
      navigation.goBack();
      return;
    }

    console.log('Authenticated user:', currentUser.uid);
  }, [currentUser]);

  useEffect(() => {
    const keyboardWillShow = Keyboard.addListener('keyboardWillShow', (e) => {
      setKeyboardHeight(e.endCoordinates.height);
    });

    const keyboardWillHide = Keyboard.addListener('keyboardWillHide', () => {
      setKeyboardHeight(0);
    });

    return () => {
      keyboardWillShow.remove();
      keyboardWillHide.remove();
    };
  }, []);

  useEffect(() => {
    if (messages.length > 0 && flatListRef.current && isInitialLoad) {
      console.log('Initial load, scrolling to end:', messages.length);
      // Wait for next frame to ensure content is rendered
      requestAnimationFrame(() => {
        setTimeout(() => {
          try {
            if (flatListRef.current) {
              flatListRef.current.scrollToOffset({
                offset: 0,
                animated: false
              });
              setIsInitialLoad(false);
            }
          } catch (error) {
            console.error('Error on initial scroll:', error);
          }
        }, 100);
      });
    }
  }, [messages.length, isInitialLoad]);

  // Add function to handle layout
  const handleLayout = useCallback(() => {
    console.log('FlatList layout complete');
    if (messages.length > 0 && flatListRef.current) {
      flatListRef.current?.scrollToEnd({ animated: false });
    }
  }, [messages.length]);

  // Add scroll position tracking
  const handleScroll = useCallback((event: any) => {
    const { contentOffset, contentSize, layoutMeasurement } = event.nativeEvent;

    // Check if we're at the bottom (within 20px threshold)
    const isBottom = contentOffset.y <= 20;
    setIsAtBottom(isBottom);

    /*console.log('Scroll position:', {
      isBottom,
      contentOffset: contentOffset.y,
      contentHeight: contentSize.height,
      scrollViewHeight: layoutMeasurement.height
    });*/
  }, []);

  // Update state setting functions to check mount status
  const safeSetState = useCallback((setter: Function) => {
    logLifecycle('SAFE_SET_STATE_CALLED', {
      isMounted: navigationRef.current.isMounted,
      isCleanup: cleanupRef.current
    });

    if (navigationRef.current.isMounted && !cleanupRef.current) {
      try {
        setter();
        logLifecycle('SAFE_SET_STATE_SUCCESS');
      } catch (error: unknown) {
        const errorDetails = error instanceof Error
          ? {
              message: error.message,
              stack: error.stack
            }
          : { message: String(error) };

        logLifecycle('SAFE_SET_STATE_ERROR', { error: errorDetails });
        lifecycleRef.current.errors.push(errorDetails.message);
        console.error('Error in safeSetState:', errorDetails);
      }
    } else {
      logLifecycle('SAFE_SET_STATE_SKIPPED', {
        reason: !navigationRef.current.isMounted ? 'not mounted' : 'cleanup in progress'
      });
    }
  }, []);

  // Debug logging moved after state declarations

  useEffect(() => {
    if (hookPostingOwnerId) {
      // Explicitly cast string to string | null type
      setPostingOwnerId(hookPostingOwnerId as string);
    }
    if (hookOfferOwnerId) {
      // Explicitly cast string to string | null type
      setOfferOwnerId(hookOfferOwnerId as string);
    }
  }, [hookPostingOwnerId, hookOfferOwnerId]);

  // Add memoization for child components
  const MemoizedPostingDetailsSection = React.memo(PostingDetailsSection);
  const MemoizedOfferDetailsSection = React.memo(OfferDetailsSection);

  // Add param validation
  useEffect(() => {
    if (!offerId) {
      console.error('[OfferDetail] Missing required offerId in route params:', {
        params: route.params,
        stack: new Error().stack
      });
      // Navigate back if no offerId
      navigation.goBack();
      return;
    }

    console.log('[OfferDetail] Initializing with params:', {
      offerId,
      hasInitialOffer: !!initialOffer,
      hasInitialPosting: !!initialPosting,
      timestamp: new Date().toISOString()
    });
  }, [offerId, navigation]);

  // This function is used for loading messages with retry logic
  // Keep it for future use
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const _handleLoadMessages = async (offerId: string): Promise<void> => {
    try {
      await withRetry(
        () => loadInitialMessages(),
        DEFAULT_RETRY_CONFIG,
        {
          component: 'OfferDetail',
          action: 'loadMessages',
          data: { offerId }
        }
      );
    } catch (error: unknown) {
      if (error instanceof RetryError) {
        handleMessageError(error, 'loadMessages', {
          offerId,
          attempts: error.attempts,
          message: error.message
        });
      } else {
        handleMessageError(error, 'loadMessages', { offerId });
      }
    }
  };

  const [userScore, setUserScore] = useState<number | undefined>();
  const [restrictionEndDate, setRestrictionEndDate] = useState<Date | null>(null);
  const [isPostingOwner, setIsPostingOwner] = useState(false);


  useEffect(() => {
    const fetchUserScore = async () => {
      // Use offerOwnerId from state if available, fallback to route params
      const ownerId = offerOwnerId || route.params?.offerOwnerId;
      if (!ownerId) return;

      try {
        const userScoreDoc = await getDoc(
          doc(db, 'users', ownerId)
        );

        if (userScoreDoc.exists()) {
          const userData = userScoreDoc.data();
          setUserScore(userData?.score);
          if (userData?.restrictionEndDate) {
            setRestrictionEndDate(userData.restrictionEndDate.toDate());
          } else {
            setRestrictionEndDate(null);
          }
        }
      } catch (error) {
        console.error('Error fetching user score:', error);
        handleError(error, {
          action: 'fetchUserScore',
          severity: 'medium',
          data: { offerOwnerId: ownerId }
        });
      }
    };

    fetchUserScore();
  }, [offerOwnerId, route.params?.offerOwnerId]);

  useEffect(() => {
    const postingOwnerId = offerDetails?.posting?.userId || route.params?.postingOwnerId;
    setIsPostingOwner(
      !!currentUser?.uid &&
      !!postingOwnerId &&
      currentUser.uid === postingOwnerId
    );
  }, [currentUser?.uid, offerDetails?.posting?.userId, route.params?.postingOwnerId]);

  useEffect(() => {
    if (offerDetails) {
      // Use initialPosting's userId if available, fallback to offerDetails
      const postingOwnerIdToSet = initialPosting?.userId || offerDetails.posting?.userId;
      if (postingOwnerIdToSet) {
        setPostingOwnerId(postingOwnerIdToSet);
      }
      if (offerDetails.userId) {
        setOfferOwnerId(offerDetails.userId);
      }
    }
  }, [offerDetails, initialPosting]);

  useEffect(() => {
    if (route.params) {
      const {
        postingOwnerId: routePostingOwnerId,
        offerOwnerId: routeOfferOwnerId,
        initialPosting,
        initialOffer
      } = route.params;

      // First try to get postingOwnerId from initialPosting
      if (initialPosting?.userId) {
        console.log('[OfferDetail] Setting postingOwnerId from initialPosting:', {
          postingOwnerId: initialPosting.userId
        });
        // Handle string safely with type assertion
        if (typeof initialPosting.userId === 'string') {
          setPostingOwnerId(initialPosting.userId);
        }
      }
      // Fallback to routePostingOwnerId if available
      else if (routePostingOwnerId) {
        console.log('[OfferDetail] Setting postingOwnerId from route params:', {
          postingOwnerId: routePostingOwnerId
        });
        // Handle string safely with type assertion
        if (typeof routePostingOwnerId === 'string') {
          setPostingOwnerId(routePostingOwnerId);
        }
      }

      // Similarly for offerOwnerId, first try initialOffer
      if (initialOffer?.userId) {
        console.log('[OfferDetail] Setting offerOwnerId from initialOffer:', {
          offerOwnerId: initialOffer.userId
        });
        // Handle string safely with type assertion
        if (typeof initialOffer.userId === 'string') {
          setOfferOwnerId(initialOffer.userId);
        }
      }
      // Fallback to routeOfferOwnerId if available
      else if (routeOfferOwnerId) {
        console.log('[OfferDetail] Setting offerOwnerId from route params:', {
          offerOwnerId: routeOfferOwnerId
        });
        // Handle string safely with type assertion
        if (typeof routeOfferOwnerId === 'string') {
          setOfferOwnerId(routeOfferOwnerId);
        }
      }
    }
  }, [route.params]);

  const handleMarkAbusive = async () => {
    if (!postingOwnerId || !currentUser?.uid || !offerId) {
      console.error('[OfferDetail] Cannot mark user as abusive: missing required data', {
        hasPostingOwnerId: !!postingOwnerId,
        hasCurrentUser: !!currentUser,
        hasOfferId: !!offerId,
        timestamp: new Date().toISOString()
      });
      return;
    }

    try {
      // Get the posting ID from route params if available
      const postingId = route.params?.initialPosting?.id || offerDetails?.posting?.id;

      if (!postingId) {
        throw new Error('Posting ID is required to mark user as abusive');
      }

      const abusiveUserDocId = `${postingOwnerId}_${currentUser.uid}_${Date.now()}`;
      const abusiveUserRef = doc(db, 'abusiveUsers', abusiveUserDocId);

      await setDoc(abusiveUserRef, {
        markedBy: currentUser.uid,
        markedAt: serverTimestamp(),
        postingId: postingId,
        offerId: offerId,
        targetUserId: postingOwnerId,
        reason: 'Marked as abusive by user'
      });

      // Refresh owner IDs after marking abusive
      if (offerDetails) {
        const updatedPostingOwnerId = offerDetails.posting?.userId;
        if (updatedPostingOwnerId) {
          setPostingOwnerId(updatedPostingOwnerId);
        }

        const updatedOfferOwnerId = offerDetails.userId;
        if (updatedOfferOwnerId) {
          setOfferOwnerId(updatedOfferOwnerId);
        }
      }
    } catch (error) {
      console.error('[OfferDetail] Error marking user as abusive:', error);
      throw error;
    }
  };

  // This function is used by the AbusiveUserButton component
  // Keep it for future use
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const _handleNavigateAfterAbusive = useCallback(() => {
    console.log('[PostingDetailsSection] Navigating after marking as abusive:', {
      postingId: offerDetails?.posting?.id,
      currentUserId: currentUser?.uid,
      timestamp: new Date().toISOString()
    });

    // Preserve the owner IDs when refreshing, handle type conflicts
    const params: any = {
      refresh: true
    };

    // Only include owner IDs if they're non-null
    if (postingOwnerId) {
      params.postingOwnerId = postingOwnerId;
    }

    if (offerOwnerId) {
      params.offerOwnerId = offerOwnerId;
    }

    navigation.setParams(params);
  }, [navigation, offerDetails, currentUser?.uid, postingOwnerId, offerOwnerId]);

  // Initialize refresh button states to false
  const [showPostingRefresh, setShowPostingRefresh] = useState<boolean>(false);
  const [showOfferRefresh, setShowOfferRefresh] = useState<boolean>(false);

  // Add debug logging for refresh button states
  useEffect(() => {
    console.log('OfferDetail refresh button states:', {
      showPostingRefresh,
      showOfferRefresh,
      showPostingRefreshExplicit: showPostingRefresh === true,
      showOfferRefreshExplicit: showOfferRefresh === true,
      timestamp: new Date().toISOString()
    });
  }, [showPostingRefresh, showOfferRefresh]);

  // Reset refresh states when component mounts
  useEffect(() => {
    // Reset refresh states on mount
    setShowPostingRefresh(false);
    setShowOfferRefresh(false);

    return () => {
      // Reset refresh states on unmount
      setShowPostingRefresh(false);
      setShowOfferRefresh(false);
    };
  }, []);

  // Add notification handler
  useEffect(() => {
    const postingId = offerDetails?.posting?.id;
    console.log('[OfferDetail][NOTIFICATION_LISTENER_SETUP]', {
      offerId,
      postingId,
      timestamp: new Date().toISOString()
    });

    // Add direct Firestore listener for notifications
    const setupNotificationListener = () => {
      if (!currentUser) return null;

      console.log('[OfferDetail][NOTIFICATION_DB_LISTENER_SETUP]', {
        userId: currentUser.uid,
        offerId,
        postingId,
        timestamp: new Date().toISOString()
      });

      const notificationsRef = collection(db, 'notifications');
      const q = query(
        notificationsRef,
        where('recipientId', '==', currentUser.uid),
        where('read', '==', false)
      );

      return onSnapshot(q, (snapshot) => {
        console.log('[OfferDetail][NOTIFICATION_DB_UPDATE]', {
          count: snapshot.size,
          timestamp: new Date().toISOString()
        });

        snapshot.docChanges().forEach(change => {
          if (change.type === 'added') {
            const notification = change.doc.data();
            console.log('[OfferDetail][NOTIFICATION_DB_ADDED]', {
              notification,
              timestamp: new Date().toISOString()
            });

            // Process notification
            if (notification.offerId === offerId || notification.postingId === postingId) {
              console.log('[OfferDetail][NOTIFICATION_DB_RELEVANT]', {
                type: notification.type,
                offerId: notification.offerId,
                postingId: notification.postingId,
                timestamp: new Date().toISOString()
              });

              // Handle different notification types
              switch (notification.type) {
                case 'FAVORITE_POSTING_UPDATE':
                  console.log('[OfferDetail][SHOWING_POSTING_REFRESH]', {
                    timestamp: new Date().toISOString()
                  });
                  setShowPostingRefresh(true);
                  break;
                case 'OFFER_STATUS_CHANGE':
                  console.log('[OfferDetail][SHOWING_OFFER_REFRESH]', {
                    timestamp: new Date().toISOString()
                  });
                  setShowOfferRefresh(true);
                  break;
                case 'SYSTEM_WITHDRAWAL':
                  console.log('[OfferDetail][SYSTEM_WITHDRAWAL_DETECTED]', {
                    notificationData: notification.data,
                    timestamp: new Date().toISOString()
                  });
                  // Force refresh to get updated offer status
                  refreshData();
                  break;
              }
            }
          }
        });
      });
    };

    const handleNotification = async (remoteMessage: any) => {
      console.log('[OfferDetail][NOTIFICATION_RECEIVED]', {
        notification: remoteMessage,
        data: remoteMessage.data,
        offerId,
        postingId,
        timestamp: new Date().toISOString()
      });

      // Check if notification is relevant to this offer - more flexible matching
      const notificationOfferId = remoteMessage.data?.offerId || remoteMessage.data?.data?.offerId;
      const notificationPostingId = remoteMessage.data?.postingId || remoteMessage.data?.data?.postingId;
      const notificationType = remoteMessage.data?.type || remoteMessage.data?.data?.type;

      console.log('[OfferDetail][NOTIFICATION_DATA_EXTRACTED]', {
        notificationOfferId,
        notificationPostingId,
        notificationType,
        currentOfferId: offerId,
        currentPostingId: postingId,
        timestamp: new Date().toISOString()
      });

      if (notificationOfferId === offerId || notificationPostingId === postingId) {
        console.log('[OfferDetail][NOTIFICATION_PROCESSED]', {
          type: notificationType,
          timestamp: new Date().toISOString()
        });

        // Handle different notification types
        switch (notificationType) {
          case 'FAVORITE_POSTING_UPDATE':
            console.log('[OfferDetail][SHOWING_POSTING_REFRESH]', {
              timestamp: new Date().toISOString()
            });
            setShowPostingRefresh(true);
            break;
          case 'OFFER_STATUS_CHANGE':
            console.log('[OfferDetail][SHOWING_OFFER_REFRESH]', {
              timestamp: new Date().toISOString()
            });
            setShowOfferRefresh(true);
            break;
          case 'SYSTEM_WITHDRAWAL':
            console.log('[OfferDetail][SYSTEM_WITHDRAWAL_PUSH_DETECTED]', {
              notificationData: remoteMessage.data,
              timestamp: new Date().toISOString()
            });
            // Force refresh to get updated offer status
            refreshData();
            break;
        }
      }
    };

    // Initialize both foreground and background listeners
    const unsubscribeForeground = messaging().onMessage(handleNotification);
    const unsubscribeBackground = messaging().onNotificationOpenedApp(handleNotification);
    const unsubscribeFirestore = setupNotificationListener();

    return () => {
      console.log('[OfferDetail][NOTIFICATION_LISTENER_CLEANUP]', {
        offerId,
        postingId,
        timestamp: new Date().toISOString()
      });
      unsubscribeForeground();
      unsubscribeBackground();
      if (unsubscribeFirestore) unsubscribeFirestore();
    };
  }, [offerId, offerDetails?.posting?.id, currentUser]);

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      style={styles.container}
      keyboardVerticalOffset={keyboardVerticalOffset}
    >
      <View style={styles.container}>
        <StatusBanner status={offerStatus || 'active'} />

        <View style={[
          styles.headersContainer,
          offerStatus === 'withdrawn' && styles.headersContainerWithBanner
        ]}>
          <MemoizedPostingDetailsSection
            postingId={offerDetails?.postingId || ''}
            postingOwnerId={postingOwnerId}
            postingTitle={postingTitle}
            postingDescription={postingDescription}
            isDetailsExpanded={isDetailsExpanded}
            setIsDetailsExpanded={setIsDetailsExpanded}
            navigation={navigation}
            isLoading={loading}
            postingStatus={postingStatus}
            currentUserId={currentUser?.uid}
            onMarkAbusive={handleMarkAbusive}
            refetchPostingDetails={refreshData}
            showRefresh={showPostingRefresh === true}
            onRefreshComplete={() => setShowPostingRefresh(false)}
          />
          <MemoizedOfferDetailsSection
            offerPrice={offerPrice}
            offerDescription={offerDescription}
            isOfferDetailsExpanded={isOfferDetailsExpanded}
            setIsOfferDetailsExpanded={setIsOfferDetailsExpanded}
            currentUserId={currentUser?.uid}
            offerOwnerId={offerOwnerId}
            offerStatus={offerStatus || 'active'}
            offerId={offerId}
            navigation={navigation}
            onWithdraw={handleWithdrawOffer}
            isLoading={loading}
            userScore={userScore}
            isPostingOwner={isPostingOwner}
            onMarkAbusive={handleMarkAbusive}
            restrictionEndDate={restrictionEndDate}
            showRefresh={showOfferRefresh === true}
            onRefreshComplete={() => setShowOfferRefresh(false)}
            initialOffer={initialOffer}
            initialPosting={initialPosting}
            key={`offer-details-${offerId}`} // Add stable key to prevent remounting
          />
        </View>

        {messages.length === 0 ? (
          // Empty state view
          <View style={[
            styles.emptyMessageContainer,
            offerStatus === 'withdrawn' && styles.emptyMessageContainerWithBanner
          ]}>
            <Text style={styles.emptyMessageText}>
              {offerStatus === 'withdrawn' ?
                'No messages available for this withdrawn offer' :
                'No messages yet'}
            </Text>
          </View>
        ) : (
          // Messages FlatList
          <FlatList
            ref={flatListRef}
            data={messages}
            inverted={true}
            keyExtractor={item => item.id || `${item.senderId}_${item.timestamp?.toDate ? item.timestamp.toDate().getTime() : (item.timestamp.seconds * 1000 + Math.floor(item.timestamp.nanoseconds / 1e6))}`}
            renderItem={({ item }) => (
              <MessageItem
                item={item}
                currentUserId={currentUser?.uid || ''}
                offerOwnerId={offerOwnerId || ''}
              />
            )}
            style={[
              styles.messageList,
              {
                marginTop: (isDetailsExpanded || isOfferDetailsExpanded) ? 'auto' : 100
              },
              offerStatus === 'withdrawn' && styles.messageListWithBanner
            ]}
            contentContainerStyle={[
              styles.messageListContent,
              {
                paddingTop: isAuthorizedUser ? 55 : 10,
                paddingBottom: 16,
                flexGrow: 1,

              }
            ]}
            onEndReached={handleLoadMore}
            onEndReachedThreshold={0.5}
            onScroll={handleScroll}
            scrollEventThrottle={16}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={handleRefresh}
                enabled={!isAtBottom}
              />
            }
            onLayout={handleLayout}
            maintainVisibleContentPosition={{
              minIndexForVisible: 0,
              autoscrollToTopThreshold: 10
            }}
            removeClippedSubviews={false}
          />
        )}

        <MessageInput
          newMessage={newMessage}
          setNewMessage={setNewMessage}
          handleSendMessage={handleSendMessage}
          isAuthorized={isAuthorizedUser}
          offerStatus={offerStatus}
        />

        {error && (
          <View testID="error-container">
            <Text testID="error-message">Error: {error}</Text>
          </View>
        )}
      </View>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  headersContainer: {
    backgroundColor: '#fff',
    zIndex: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
  },
  headersContainerWithBanner: {
    top: 36, // Height of the status banner
  },
  messageList: {
    flex: 1,
  },
  messageListWithBanner: {
    marginTop: 136, // Original 100 + banner height
  },
  messageListContent: {
    flexGrow: 1,
  },
  emptyMessageContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    marginTop: 100,
  },
  emptyMessageContainerWithBanner: {
    marginTop: 136, // Original 100 + banner height
  },
  emptyMessageText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
});

export default OfferDetail;
