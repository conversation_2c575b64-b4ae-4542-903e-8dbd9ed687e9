// PostDeleteConfirmation.tsx

import React from 'react';
import { View, Text, Button, StyleSheet } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { deleteDoc, doc } from 'firebase/firestore';
import { db } from '../firebase';

function PostDeleteConfirmation({ route }): React.JSX.Element {
  const { itemId, itemName, itemDescription } = route.params;
  const navigation = useNavigation();

  const handleConfirmDelete = async () => {
    try {
      await deleteDoc(doc(db, 'postings', itemId)); // Updated from 'listings' to 'postings'
      console.log('Posting deleted successfully');
      navigation.navigate('MyPostings');
    } catch (error) {
      console.error('Error deleting posting:', error);
    }
  };

  const handleCancel = () => {
    navigation.goBack();
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Delete Posting</Text>
      <Text style={styles.message}>
        Are you sure you want to delete the posting titled "{itemName}"?
      </Text>
      <Text style={styles.description}>{itemDescription}</Text>
      <View style={styles.buttonContainer}>
        <Button title="Cancel" onPress={handleCancel} />
        <Button title="Confirm Delete" onPress={handleConfirmDelete} color="red" />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
    backgroundColor: 'white',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
  },
  message: {
    fontSize: 18,
    marginBottom: 16,
    textAlign: 'center',
  },
  description: {
    fontSize: 16,
    color: 'gray',
    marginBottom: 20,
    textAlign: 'center',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '80%',
    marginTop: 20,
  },
});

export default PostDeleteConfirmation;