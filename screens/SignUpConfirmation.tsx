// SignUpConfirmation.tsx

import React, { useState } from 'react';
import { View, Text, Button, StyleSheet, Alert } from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import FormInput from '../components/FormInput'; // Import the FormInput component

function SignUpConfirmation(): React.JSX.Element {
  const [confirmationCode, setConfirmationCode] = useState('');
  const navigation = useNavigation();
  const route = useRoute();
  const correctCode = route.params?.confirmationCode; // Get the correct code from route params

  const handleConfirm = () => {
    if (confirmationCode === correctCode) {
      console.log('Confirmation successful');
      navigation.navigate('Home'); // Navigate to the Home screen upon successful confirmation
    } else {
      console.error('Invalid confirmation code');
      Alert.alert('Error', 'Invalid confirmation code. Please try again.');
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Enter Confirmation Code</Text>
      <FormInput
        label=""
        value={confirmationCode}
        onChangeText={setConfirmationCode}
        placeholder="Enter confirmation code"
        keyboardType="numeric"
      />
      <Button title="Confirm" onPress={handleConfirm} />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
    backgroundColor: 'white',
  },
  title: {
    fontSize: 24,
    marginBottom: 16,
  },
});

export default SignUpConfirmation;