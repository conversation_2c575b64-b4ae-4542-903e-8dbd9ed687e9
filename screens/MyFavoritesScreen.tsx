// MyFavoritesScreen.tsx
import React from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { auth } from '../firebase';
import useFavoritesData from '../hooks/useFavoritesData';

interface FavoriteItem {
  id: string;
  title: string;
  description: string;
  userId: string;
  latitude: number;
  longitude: number;
}

interface FavoritesData {
  favorites: FavoriteItem[];
  loading: boolean;
  error: Error | string | null;
}

function MyFavoritesScreen(): React.JSX.Element {
  const navigation = useNavigation();
  const currentUser = auth.currentUser;
  const { favorites, loading, error }: FavoritesData = useFavoritesData(currentUser?.uid);

  const handleItemPress = (item: FavoriteItem) => {
    navigation.navigate('PostingDetail', {
      postingId: item.id,
      itemName: item.title,
      itemDescription: item.description,
      itemLocation: { latitude: item.latitude, longitude: item.longitude },
      userId: item.userId,
    });
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer} testID="loading-container">
        <Text>Loading...</Text>
      </View>
    );
  }

  if (error) {
    const errorMessage = typeof error === 'string' 
      ? error 
      : (error as Error).message || 'Unknown error';
    return (
      <View style={styles.errorContainer} testID="error-container">
        <Text>Error: {errorMessage}</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Text style={styles.header}>My Favorites</Text>
      <FlatList
        testID="favorites-list"
        data={favorites}
        keyExtractor={(item: FavoriteItem) => item.id}
        renderItem={({ item }: { item: FavoriteItem }) => (
          <TouchableOpacity 
            testID="favorite-item"
            onPress={() => handleItemPress(item)}
          >
            <View style={styles.listItem}>
              <Text style={styles.listItemTitle}>{item.title}</Text>
              <Text style={styles.listItemDescription}>{item.description}</Text>
            </View>
          </TouchableOpacity>
        )}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 20,
    backgroundColor: 'white',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    fontSize: 24,
    fontWeight: 'bold',
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  listItem: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#ccc',
  },
  listItemTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  listItemDescription: {
    fontSize: 14,
    color: '#555',
    marginTop: 4,
  },
});

export default MyFavoritesScreen;