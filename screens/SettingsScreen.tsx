// SettingsScreen.tsx

import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootStackParamList } from '../types/navigation';

type SettingsScreenNavigationProp = StackNavigationProp<RootStackParamList>;

function SettingsScreen(): React.JSX.Element {
  const navigation = useNavigation<SettingsScreenNavigationProp>();

  const handleAccountSettings = () => {
    console.log('Account Settings pressed');
    try {
      Alert.alert('Account Settings', 'Account settings functionality coming soon!');
    } catch (error) {
      console.error('Alert display error:', error);
    }
  };

  const handleNotificationSettings = () => {
    console.log('Navigating to NotificationSettings');
    try {
      navigation.navigate('NotificationSettings');
    } catch (error) {
      console.error('Navigation error:', error);
    }
  };

  const handlePrivacySettings = () => {
    console.log('Privacy Settings pressed');
    try {
      Alert.alert('Privacy Settings', 'Privacy settings functionality coming soon!');
    } catch (error) {
      console.error('Alert display error:', error);
    }
  };

  const handleLocationSettings = () => {
    console.log('Navigating to LocationSettings');
    try {
      navigation.navigate('LocationSettings');
    } catch (error) {
      console.error('Navigation error:', error);
    }
  };

  return (
    <View style={styles.container} testID="settings-container">
      <TouchableOpacity onPress={handleAccountSettings} style={styles.listItem} testID="settings-list-item">
        <Text style={styles.listItemText} testID="settings-list-item-text">Account Settings</Text>
      </TouchableOpacity>
      <TouchableOpacity onPress={handleNotificationSettings} style={styles.listItem} testID="settings-list-item">
        <Text style={styles.listItemText} testID="settings-list-item-text">Notification Settings</Text>
      </TouchableOpacity>
      <TouchableOpacity onPress={handlePrivacySettings} style={styles.listItem} testID="settings-list-item">
        <Text style={styles.listItemText} testID="settings-list-item-text">Privacy Settings</Text>
      </TouchableOpacity>
      <TouchableOpacity onPress={handleLocationSettings} style={styles.listItem} testID="settings-list-item">
        <Text style={styles.listItemText} testID="settings-list-item-text">Location Settings</Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 20,
    backgroundColor: 'white',
  },
  listItem: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#ccc',
  },
  listItemText: {
    fontSize: 16,
  },
});

export default SettingsScreen;