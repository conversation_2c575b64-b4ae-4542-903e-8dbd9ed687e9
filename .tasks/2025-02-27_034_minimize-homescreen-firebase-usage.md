# HomeScreen Firebase Usage Minimization

## Codes:
1. /Users/<USER>/Desktop/satbana-admin/src/monitoring/FirebaseUsageMonitor.ts
2. /Users/<USER>/Desktop/satbana-admin/src/monitoring/analyzeHomeScreenUsage.ts


## Summary:

Before we start, let me summarize what we've learned from the analysis to ensure a focused implementation:

**Key Areas to Address in HomeScreen.tsx:**
1. Region-based caching (highest impact - 91.56% query overlap)
2. Query debouncing optimization
3. Real-time subscription management
4. Search operation optimization

**I recommend we:**
1. Create a new task/ticket for tracking these changes
2. Start with implementing the caching layer as it has the highest impact
3. Work incrementally to avoid disrupting the current functionality

**Current Baseline Metrics:**
/Users/<USER>/Desktop/satbana/documentation/monitoring/homescreen_baseline_metrics_2024_02_27.md

## Current Firebase Interactions
1. Real-time Subscription (useFirebaseSubscriptions)
2. Search Operations (fetchPostingsBySearch)
3. Favorites Fetching (getUserFavorites)

## User Experience Requirements (from documentation)
1. Initial map view with default zoom level
2. Automatic updates only after user stops interaction (1s buffer)
3. Search This Area functionality
4. Maintain postings until new fetch required
5. Clear separation between pan/zoom updates and search behavior

## Optimization Opportunities

### 1. Replace Real-time Subscription with Controlled Fetching
**Current Issue:**
- Constant real-time connection maintained unnecessarily
- Multiple listeners active simultaneously

**Optimization Intent:**
- Replace continuous listener with controlled fetch operations
- Fetch only when necessary (significant map movement or search)
- Maintain local state for current postings
- Implement proper cleanup of listeners

### 2. Optimize Search Operations
**Current Issue:**
- Every map pan potentially triggers new search
- No caching mechanism for recent searches
- Redundant fetches for similar regions

**Optimization Intent:**
- Implement region-based fetch determination
- Cache recent search results
- Add proper debouncing for map movements
- Prevent unnecessary fetches for minor map movements

### 3. Optimize Favorites Management
**Current Issue:**
- Separate fetch operation for favorites
- Potential race conditions with posting data
- Unnecessary real-time updates

**Optimization Intent:**
- Combine favorites with posting data in single fetch
- Cache favorites locally
- Update favorites status without full refetch

### 4. Implement Data Caching
**Current Issue:**
- No local caching strategy
- Repeated fetches for same regions
- Full refetch on minor updates

**Optimization Intent:**
- Implement local storage for recently viewed regions
- Cache posting data with TTL (Time To Live)
- Implement partial updates for cached data

### 5. Batch Operations
**Current Issue:**
- Individual fetches for related data
- Multiple small queries instead of batched operations

**Optimization Intent:**
- Combine related queries into batch operations
- Implement proper pagination
- Optimize query patterns for Firestore billing

## Implementation Priority
1. Replace real-time subscription (Highest impact)
2. Optimize search operations
3. Implement data caching
4. Optimize favorites management
5. Implement batch operations

## Technical Considerations
- Maintain smooth UX during optimization
- Implement proper error handling
- Consider offline capabilities
- Monitor Firebase usage metrics before/after

## Next Steps
1. Create separate task for each optimization
2. Implement metrics collection for current usage
3. Create test cases for optimized functionality
4. Plan gradual rollout to prevent disruption

## Success Metrics
- Reduced Firebase read operations
- Maintained or improved UX
- Reduced latency in posting updates
- Improved offline capability
# HomeScreen Firebase Usage Minimization

## Current Firebase Interactions
1. Real-time Subscription (useFirebaseSubscriptions)
2. Search Operations (fetchPostingsBySearch)
3. Favorites Fetching (getUserFavorites)

## User Experience Requirements (from documentation)
1. Initial map view with default zoom level
2. Automatic updates only after user stops interaction (1s buffer)
3. Search This Area functionality
4. Maintain postings until new fetch required
5. Clear separation between pan/zoom updates and search behavior

## Optimization Opportunities

### 1. Replace Real-time Subscription with Controlled Fetching
**Current Issue:**
- Constant real-time connection maintained unnecessarily
- Multiple listeners active simultaneously

**Optimization Intent:**
- Replace continuous listener with controlled fetch operations
- Fetch only when necessary (significant map movement or search)
- Maintain local state for current postings
- Implement proper cleanup of listeners

### 2. Optimize Search Operations
**Current Issue:**
- Every map pan potentially triggers new search
- No caching mechanism for recent searches
- Redundant fetches for similar regions

**Optimization Intent:**
- Implement region-based fetch determination
- Cache recent search results
- Add proper debouncing for map movements
- Prevent unnecessary fetches for minor map movements

### 3. Optimize Favorites Management
**Current Issue:**
- Separate fetch operation for favorites
- Potential race conditions with posting data
- Unnecessary real-time updates

**Optimization Intent:**
- Combine favorites with posting data in single fetch
- Cache favorites locally
- Update favorites status without full refetch

### 4. Implement Data Caching
**Current Issue:**
- No local caching strategy
- Repeated fetches for same regions
- Full refetch on minor updates

**Optimization Intent:**
- Implement local storage for recently viewed regions
- Cache posting data with TTL (Time To Live)
- Implement partial updates for cached data

### 5. Batch Operations
**Current Issue:**
- Individual fetches for related data
- Multiple small queries instead of batched operations

**Optimization Intent:**
- Combine related queries into batch operations
- Implement proper pagination
- Optimize query patterns for Firestore billing

## Implementation Priority
1. Replace real-time subscription (Highest impact)
2. Optimize search operations
3. Implement data caching
4. Optimize favorites management
5. Implement batch operations

## Technical Considerations
- Maintain smooth UX during optimization
- Implement proper error handling
- Consider offline capabilities
- Monitor Firebase usage metrics before/after

## Next Steps
1. Create separate task for each optimization
2. Implement metrics collection for current usage
3. Create test cases for optimized functionality
4. Plan gradual rollout to prevent disruption

## Success Metrics
- Reduced Firebase read operations
- Maintained or improved UX
- Reduced latency in posting updates
- Improved offline capability


# Execution Log

### 2024-02-27: Optimized Map-List Synchronization and Query Efficiency

#### Changes Implemented:

1. **Query Optimization in FirebaseService**
   - Modified `fetchPostingsBySearch` to consistently return 10 results per visible area
   - Implemented strict bounds-based querying using map viewport coordinates
   - Added detailed logging for better debugging and monitoring
   ```javascript
   const queryConditions = [
     where('postingStatus', '!=', 'Deleted'),
     where('latitude', '>=', mapBounds.south),
     where('latitude', '<=', mapBounds.north),
     where('longitude', '>=', mapBounds.west),
     where('longitude', '<=', mapBounds.east),
     orderBy('latitude'),
     orderBy('longitude'),
     limit(numberOfResults)
   ];
   ```

2. **Controlled Postings Hook Refinement**
   - Simplified fetching logic in `useControlledPostings`
   - Removed unnecessary pagination code
   - Added region-based fetching with proper bounds calculation
   - Improved error handling and loading states

3. **HomeScreen Synchronization**
   - Implemented `visiblePostings` memoization to ensure map-list sync
   - Updated FlatList to use synchronized visible postings
   - Improved map interaction responsiveness

#### Performance Improvements:
- Achieved consistent display of 10 results per visible map area
- Eliminated map-list desynchronization issues
- Reduced Firebase query complexity
- Improved user experience with faster updates during map navigation

#### Technical Debt Addressed:
- Removed redundant pagination logic
- Simplified region change handling
- Added comprehensive logging for debugging
- Improved code maintainability

#### Pending Items:
- User tier implementation (postponed pending user feedback)
- Performance monitoring implementation (cancelled to avoid complexity)
- Long-term caching strategy (postponed)

#### Next Steps:
1. Monitor Firebase usage metrics with new implementation
2. Gather user feedback on the improved responsiveness
3. Consider implementing performance monitoring
4. Document any edge cases discovered during testing
