# Auto-withdraw offers when posting is deleted

## Purpose
When a posting is deleted, all associated offers should be automatically withdrawn to maintain data consistency and improve user experience.

## Current State
- [x] Basic implementation of auto-withdrawal in `deletePosting` function
- [x] System message added to discussion thread about withdrawal
- [x] Fixed error handling and validation for message timestamps
- [x] Implemented timestamp validation utility
- [x] Updated legacy message records to use consistent Firestore Timestamp format
- [x] Added comprehensive logging for message validation and error handling

## Requirements
- [x] Automatically withdraw all offers when a posting is deleted
- [x] Add a system message to the discussion thread
- [x] Notify offer owners about the withdrawal
- [x] Maintain data consistency
- [x] Handle errors gracefully with proper rollback
- [x] Add comprehensive logging
- [x] Ensure consistent timestamp format across all messages
- [x] Validate timestamps for all new messages

## Implementation Steps
1. [x] Add `SYSTEM_WITHDRAWAL` to notification types
2. [x] Implement auto-withdrawal in `deletePosting`
3. [x] Add system message to discussion thread
4. [x] Create notification for offer owners
5. [x] Add error handling and rollback mechanism
6. [x] Add logging for debugging and monitoring
7. [x] Create timestamp validation utility
8. [x] Update legacy message records
9. [x] Integrate timestamp validation in OfferDetail screen

## Validation Criteria
- [x] All offers are withdrawn when posting is deleted
- [x] System message appears in discussion thread
- [x] Offer owners receive notifications
- [x] Data remains consistent after deletion
- [x] Errors are handled gracefully
- [x] All actions are properly logged
- [x] All messages use consistent timestamp format
- [x] New messages are properly validated

## Dependencies
- Firebase Firestore
- Notification system
- Error logging system
- Timestamp validation utility

## Risks
- Data inconsistency if process fails mid-way
- User experience impact if notifications fail
- Performance impact of validation checks

## Notes
- Added timestamp validation to ensure consistent format across all messages
- Updated legacy records to use Firestore Timestamp format
- Improved error handling and logging in OfferDetail screen
- System messages now use proper timestamp format

## History
- 2024-01-20: Initial implementation
- 2024-01-21: Added system message and notifications
- 2024-01-22: Improved error handling and logging
- 2024-01-23: Added timestamp validation
- 2024-01-24: Updated legacy records
- 2024-01-25: Integrated timestamp validation in OfferDetail screen 