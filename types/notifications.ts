export enum NotificationType {
  NEW_OFFER = 'NEW_OFFER',
  OFFER_STATUS_CHANGE = 'OFFER_STATUS_CHANGE',
  NEW_MESSAGE = 'NEW_MESSAGE',
  FAVORITE_POSTING_UPDATE = 'FAVORITE_POSTING_UPDATE',
  SYSTEM_WITHDRAWAL = 'SYSTEM_WITHDRAWAL'
}

export interface NotificationCategory {
  id: string;
  actions: {
    id: string;
    title: string;
    options: {
      foreground: boolean;
      destructive?: boolean;
      authenticationRequired?: boolean;
    };
  }[];
}

export const NOTIFICATION_CATEGORIES: { [key: string]: NotificationCategory } = {
  [NotificationType.NEW_OFFER]: {
    id: NotificationType.NEW_OFFER,
    actions: [
      {
        id: 'view',
        title: 'View',
        options: { foreground: true }
      },
      {
        id: 'decline',
        title: 'Decline',
        options: { foreground: true, destructive: true }
      }
    ]
  },
  [NotificationType.NEW_MESSAGE]: {
    id: NotificationType.NEW_MESSAGE,
    actions: [
      {
        id: 'reply',
        title: 'Reply',
        options: { foreground: true }
      }
    ]
  },
  [NotificationType.FAVORITE_POSTING_UPDATE]: {
    id: NotificationType.FAVORITE_POSTING_UPDATE,
    actions: [
      {
        id: 'view',
        title: 'View',
        options: { foreground: true }
      },
      {
        id: 'unfollow',
        title: 'Unfollow',
        options: { 
          foreground: true,
          destructive: true
        }
      }
    ]
  },
  [NotificationType.OFFER_STATUS_CHANGE]: {
    id: NotificationType.OFFER_STATUS_CHANGE,
    actions: [
      {
        id: 'view',
        title: 'View',
        options: { foreground: true }
      }
    ]
  },
  [NotificationType.SYSTEM_WITHDRAWAL]: {
    id: NotificationType.SYSTEM_WITHDRAWAL,
    actions: [
      {
        id: 'view',
        title: 'View',
        options: { foreground: true }
      }
    ]
  }
};

export interface NotificationSettings {
  [NotificationType.NEW_OFFER]: boolean;
  [NotificationType.OFFER_STATUS_CHANGE]: boolean;
  [NotificationType.NEW_MESSAGE]: boolean;
  [NotificationType.FAVORITE_POSTING_UPDATE]: boolean;
  [NotificationType.SYSTEM_WITHDRAWAL]: boolean;
}

export interface NotificationSettingsHookResult {
  settings: NotificationSettings;
  updateSetting: (type: NotificationType, value: boolean) => Promise<void>;
  loading: boolean;
  error: Error | null;
}

interface OfferUpdateNotificationData {
  currentPrice?: number;
  currentDescription?: string;
  currentStatus?: string;
  timestamp: number;
}

// Update the NotificationData type
interface NotificationData {
  // ... existing fields ...
  data?: OfferUpdateNotificationData;
} 