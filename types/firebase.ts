import { NotificationType } from './notifications';

export interface OfferData {
  postingId: string;
  userId: string;
  price: number;
  description: string;
  status: 'pending' | 'accepted' | 'rejected' | 'withdrawn';
  timestamp: Date;
  createdAt?: any; // Firebase Timestamp
}

export interface DiscussionData {
  offerId: string;
  postingOwnerId: string;
  offerOwnerId: string;
  messages: Message[];
  createdAt?: any; // Firebase Timestamp
}

export interface FirebaseTimestamp {
  seconds: number;
  nanoseconds: number;
  toDate(): Date;
}

export interface Message {
  id: string;
  text: string;
  senderId: string;
  timestamp: FirebaseTimestamp;
  read?: boolean;
  readAt?: FirebaseTimestamp;
}

export interface MessageResult {
  messages: Message[];
  lastDoc: any;
  hasMore: boolean;
  hasNewMessages?: boolean;
  newMessages?: Message[];
}

export interface NotificationData {
  id: string;
  type: NotificationType;
  recipientId: string;
  senderId: string;
  postingId?: string;
  offerId?: string;
  title: string;
  body: string;
  read: boolean;
  createdAt?: any; // Firebase Timestamp
  data?: {
    postingId?: string;
    currentPrice?: number;
    currentDescription?: string;
    currentStatus?: string;
    timestamp?: number;
  };
}

export interface FirebaseError extends Error {
  code?: string;
  message: string;
} 