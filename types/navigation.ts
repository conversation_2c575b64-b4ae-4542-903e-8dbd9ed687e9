import { Posting } from './posting';

export interface NavigationFlowMetrics {
  startTime: number;
  sourceScreen: string;
  state?: Record<string, any>;
  events?: Array<{
    name: string;
    timestamp: number;
    duration?: number;
  }>;
}

export type RootStackParamList = {
  LoginScreen: undefined;
  Home: undefined;
  SignUp: undefined;
  SignUpConfirmation: undefined;
  ForgotPassword: undefined;
  CreatePosting: undefined;
  EditPosting: { 
    postingId: string;
    itemName: string;
    itemDescription: string;
    itemLocation: any;
    flowMetrics?: NavigationFlowMetrics; // Optional to maintain backward compatibility
  };
  PostingDetail: { 
    postingId: string;
    itemLocation: any;
    userId: string;
    itemName: string;
  };
  Profile: undefined;
  MakeOffer: { 
    postingId: string;
    itemName: string;
    itemDescription: string;
    itemLocation: any;
    userId: string;
  };
  EditOffer: {
    offerId: string;
    postingId: string;
    ownerId: string;
  };
  OfferDetail: {
    offerId: string;
    postingId: string;
    postingOwnerId: string;
    offerOwnerId: string;
  };
  MyFavorites: undefined;
  MyPostings: undefined;
  MyOffers: undefined;
  Settings: undefined;
  NotificationSettings: undefined;
  Notifications: undefined;
  LocationSettings: undefined;
};

declare global {
  namespace ReactNavigation {
    interface RootParamList extends RootStackParamList {}
  }
} 
