// Add performance tracking to reducer
const postingDetailReducer = (state: PostingDetailState, action: PostingDetailAction): PostingDetailState => {
  const startTime = performance.now();
  
  // Log pre-update state
  console.log('[PostingDetailReducer] Pre-update:', {
    actionType: action.type,
    stateKeys: Object.keys(state),
    timestamp: new Date().toISOString()
  });

  const newState = (() => {
    switch (action.type) {
      case 'BATCH_UPDATE':
        return {
          ...state,
          ...action.payload,
          lastBatchUpdate: action.payload.batchId,
          metrics: {
            ...state.metrics,
            lastBatchTime: Date.now(),
            batchCount: (state.metrics?.batchCount || 0) + 1
          }
        };

      case 'UPDATE_OFFERS':
        return {
          ...state,
          userOffer: action.payload.userOffer,
          offersCount: action.payload.offersCount,
          lastUpdate: Date.now(),
          metrics: {
            ...state.metrics,
            lastOfferUpdate: Date.now()
          }
        };

      case 'POSTING_LOADED':
        return {
          ...state,
          currentUserId: action.payload.currentUserId,
          isOwner: action.payload.isOwner,
          postingDetailsId: action.payload.postingDetailsId,
          lastUpdate: Date.now(),
          metrics: {
            ...state.metrics,
            loadTime: Date.now()
          }
        };

      // ... other cases ...

      default:
        return state;
    }
  })();

  // Log performance metrics
  const duration = performance.now() - startTime;
  console.log('[PostingDetailReducer] Update completed:', {
    actionType: action.type,
    duration,
    changedKeys: Object.keys(newState).filter(key => state[key] !== newState[key]),
    timestamp: new Date().toISOString()
  });

  return newState;
};
