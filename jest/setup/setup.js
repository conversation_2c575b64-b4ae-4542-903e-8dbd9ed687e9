// Import Jest globals directly
const { expect, jest, test, describe, beforeEach, afterEach } = require('@jest/globals');

// Log initial state
console.log('[0] Jest Setup: Starting with globals', {
  hasExpect: typeof expect === 'function',
  hasJest: typeof jest === 'object',
  hasTest: typeof test === 'function'
});

// Initialize globals
global.expect = expect;
global.jest = jest;
global.test = test;
global.describe = describe;
global.beforeEach = beforeEach;
global.afterEach = afterEach;

// Import testing library
require('@testing-library/jest-native/extend-expect');

// Mock Firebase modules
console.log('[3] Jest Setup: Mocking Firebase');

// Mock @react-native-firebase/app
jest.mock('@react-native-firebase/app', () => ({
  __esModule: true,
  default: {
    app: jest.fn(() => ({
      currentUser: { uid: 'test-user-id' }
    })),
    apps: [],
    initializeApp: jest.fn(),
  }
}));

// Mock @react-native-firebase/messaging
jest.mock('@react-native-firebase/messaging', () => ({
  __esModule: true,
  default: {
    messaging: jest.fn(() => ({
      hasPermission: jest.fn(() => Promise.resolve(true)),
      requestPermission: jest.fn(() => Promise.resolve(true)),
      getToken: jest.fn(() => Promise.resolve('mock-token')),
      onMessage: jest.fn(),
      onNotificationOpenedApp: jest.fn(),
      getInitialNotification: jest.fn(() => Promise.resolve(null)),
    })),
  },
}));

// Mock firebase/auth
jest.mock('firebase/auth', () => ({
  getAuth: jest.fn(),
  initializeAuth: jest.fn(),
  getReactNativePersistence: jest.fn(),
  signInWithEmailAndPassword: jest.fn(),
  createUserWithEmailAndPassword: jest.fn(),
  signOut: jest.fn()
}));

// Mock firebase/firestore
jest.mock('firebase/firestore');

// Mock other React Native dependencies
console.log('[4] Jest Setup: Mocking React Native dependencies');

jest.mock('react-native/Libraries/EventEmitter/NativeEventEmitter');
jest.mock('react-native/Libraries/Animated/NativeAnimatedHelper');

jest.mock('react-native-reanimated', () => {
  const Reanimated = require('react-native-reanimated/mock');
  Reanimated.default.call = () => {};
  return Reanimated;
});

jest.mock('react-native-gesture-handler', () => {});

jest.mock('@react-native-async-storage/async-storage', () => ({
  setItem: jest.fn(),
  getItem: jest.fn(),
  removeItem: jest.fn(),
}));

jest.mock('@react-native-community/push-notification-ios', () => ({
  addEventListener: jest.fn(),
  requestPermissions: jest.fn(() => Promise.resolve()),
  getInitialNotification: jest.fn(() => Promise.resolve(null)),
}));

jest.mock('@react-navigation/native', () => ({
  useNavigation: () => ({
    navigate: jest.fn(),
    goBack: jest.fn(),
    setOptions: jest.fn(),
    addListener: jest.fn(),
    removeListener: jest.fn(),
  }),
  useRoute: () => ({
    params: {
      offerId: 'test-offer-id',
      initialOffer: null
    },
  }),
}));

// Log final verification
console.log('[Final] Jest Setup: Verifying globals', {
  globalExpect: typeof global.expect === 'function',
  globalJest: typeof global.jest === 'object',
  expectMatchers: Object.keys(expect.extend.mock?.calls?.[0]?.[0] || {})
});