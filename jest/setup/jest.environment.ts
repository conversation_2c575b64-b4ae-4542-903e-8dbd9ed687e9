import NodeEnvironment from 'jest-environment-node';
import { TestEnvironment } from '@jest/environment';
import { JestEnvironmentConfig, EnvironmentContext } from '@jest/environment';

class CustomEnvironment extends NodeEnvironment {
  constructor(config: JestEnvironmentConfig, context: EnvironmentContext) {
    super(config, context);
    this.global.expect = this.expect;
  }

  async setup() {
    await super.setup();
    
    // Set up globals
    Object.assign(this.global, {
      fetch: jest.fn(),
      __DEV__: true,
      window: {}
    });

    console.log('Environment Setup:', {
      hasExpect: typeof this.global.expect !== 'undefined',
      hasJest: typeof this.global.jest !== 'undefined',
      expectKeys: Object.keys(this.global.expect || {})
    });
  }
}

module.exports = CustomEnvironment; 