import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { act } from '@testing-library/react-native';
import { Alert } from 'react-native';
import ForgotPassword from '@/screens/ForgotPassword';
import { sendPasswordResetEmail } from 'firebase/auth';

// Mock navigation
const mockNavigate = jest.fn();
jest.mock('@react-navigation/native', () => ({
  useNavigation: () => ({
    navigate: mockNavigate,
  }),
}));

// Mock Firebase auth
jest.mock('firebase/auth', () => ({
  sendPasswordResetEmail: jest.fn(),
}));

// Mock Alert
jest.spyOn(Alert, 'alert');

describe('ForgotPassword Screen', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders correctly', () => {
    const { getByText, getByPlaceholderText } = render(<ForgotPassword />);
    
    expect(getByText('Reset Your Password')).toBeTruthy();
    expect(getByPlaceholderText('Enter your email address')).toBeTruthy();
    expect(getByText('Reset Password')).toBeTruthy();
  });

  it('handles empty email validation', async () => {
    const { getByText } = render(<ForgotPassword />);
    
    const resetButton = getByText('Reset Password');
    
    await act(async () => {
      fireEvent.press(resetButton);
      await waitFor(() => {
        expect(Alert.alert).toHaveBeenCalledWith(
          'Validation Error',
          'Please enter a valid email address.'
        );
      });
    });
  });

  it('handles successful password reset', async () => {
    (sendPasswordResetEmail as jest.Mock).mockResolvedValueOnce(undefined);
    
    const { getByText, getByPlaceholderText } = render(<ForgotPassword />);
    
    const emailInput = getByPlaceholderText('Enter your email address');
    const resetButton = getByText('Reset Password');

    fireEvent.changeText(emailInput, '<EMAIL>');
    
    await act(async () => {
      fireEvent.press(resetButton);
      await waitFor(() => {
        expect(sendPasswordResetEmail).toHaveBeenCalledWith(expect.anything(), '<EMAIL>');
        expect(Alert.alert).toHaveBeenCalledWith(
          'Success',
          'A password reset link has been sent to your email.'
        );
        expect(mockNavigate).toHaveBeenCalledWith('LoginScreen');
      });
    });
  });

  it('handles password reset error', async () => {
    const mockError = new Error('Network error');
    (sendPasswordResetEmail as jest.Mock).mockRejectedValueOnce(mockError);
    
    const { getByText, getByPlaceholderText } = render(<ForgotPassword />);
    
    const emailInput = getByPlaceholderText('Enter your email address');
    const resetButton = getByText('Reset Password');

    fireEvent.changeText(emailInput, '<EMAIL>');
    
    await act(async () => {
      fireEvent.press(resetButton);
      await waitFor(() => {
        expect(Alert.alert).toHaveBeenCalledWith(
          'Error',
          'Failed to send password reset email. Please try again.'
        );
      });
    });
  });
}); 