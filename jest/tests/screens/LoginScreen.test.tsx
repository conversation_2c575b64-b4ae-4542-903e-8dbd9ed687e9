import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { act } from '@testing-library/react-native';
import { Alert } from 'react-native';
import LoginScreen from '@/screens/LoginScreen';

// Mock navigation
const mockNavigate = jest.fn();
jest.mock('@react-navigation/native', () => ({
  useNavigation: () => ({
    navigate: mockNavigate,
  }),
}));

// Mock sign-in hook
const mockHandleSignIn = jest.fn();
jest.mock('@/hooks/useSignIn', () => ({
  useSignIn: () => ({
    handleSignIn: mockHandleSignIn,
    loading: false,
  }),
}));

// Mock Alert
jest.spyOn(Alert, 'alert');

describe('LoginScreen Screen', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders correctly', () => {
    const { getByText, getByPlaceholderText } = render(<LoginScreen />);
    
    expect(getByText('Welcome to satbana')).toBeTruthy();
    expect(getByPlaceholderText('Email Address')).toBeTruthy();
    expect(getByPlaceholderText('Password')).toBeTruthy();
    expect(getByText('Sign In')).toBeTruthy();
    expect(getByText('Forgot Password?')).toBeTruthy();
    expect(getByText('Sign Up')).toBeTruthy();
  });

  it('handles sign in flow correctly', async () => {
    const { getByPlaceholderText, getByText } = render(<LoginScreen />);
    
    const emailInput = getByPlaceholderText('Email Address');
    const passwordInput = getByPlaceholderText('Password');
    const signInButton = getByText('Sign In');

    fireEvent.changeText(emailInput, '<EMAIL>');
    fireEvent.changeText(passwordInput, 'password123');
    
    await act(async () => {
      fireEvent.press(signInButton);
      await waitFor(() => {
        expect(mockHandleSignIn).toHaveBeenCalledWith('<EMAIL>', 'password123');
        expect(mockNavigate).toHaveBeenCalledWith('Home');
      });
    });
  });

  it('handles sign in error correctly', async () => {
    mockHandleSignIn.mockRejectedValueOnce(new Error('Invalid credentials'));
    
    const { getByPlaceholderText, getByText } = render(<LoginScreen />);
    
    const emailInput = getByPlaceholderText('Email Address');
    const passwordInput = getByPlaceholderText('Password');
    const signInButton = getByText('Sign In');

    fireEvent.changeText(emailInput, '<EMAIL>');
    fireEvent.changeText(passwordInput, 'wrongpassword');
    
    await act(async () => {
      fireEvent.press(signInButton);
      await waitFor(() => {
        expect(Alert.alert).toHaveBeenCalledWith(
          'Sign In Failed',
          'Invalid credentials'
        );
      });
    });
  });

  it('navigates to forgot password screen', () => {
    const { getByText } = render(<LoginScreen />);
    
    const forgotPasswordButton = getByText('Forgot Password?');
    fireEvent.press(forgotPasswordButton);
    
    expect(mockNavigate).toHaveBeenCalledWith('ForgotPassword');
  });

  it('navigates to sign up screen', () => {
    const { getByText } = render(<LoginScreen />);
    
    const signUpButton = getByText('Sign Up');
    fireEvent.press(signUpButton);
    
    expect(mockNavigate).toHaveBeenCalledWith('SignUp');
  });
}); 