import React from 'react';
import { Alert } from 'react-native';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import CreatePostingScreen from '../../../screens/CreatePostingScreen';
import { useNavigation } from '@react-navigation/native';
import { useAddPosting } from '../../../hooks/useAddPosting';
import { useFormValidation } from '../../../hooks/useFormValidation';
import { useAuthUser } from '../../../hooks/useAuthUser';

// Mock the required hooks and components
jest.mock('@react-navigation/native', () => ({
  useNavigation: jest.fn(),
}));

jest.mock('../../../hooks/useAddPosting', () => ({
  useAddPosting: jest.fn(),
}));

jest.mock('../../../hooks/useFormValidation', () => ({
  useFormValidation: jest.fn(),
}));

jest.mock('../../../hooks/useAuthUser', () => ({
  useAuthUser: jest.fn(),
}));

jest.mock('react-native-maps', () => {
  const { View } = require('react-native');
  const MockMapView = (props: any) => {
    return <View testID="mock-map-view" {...props} />;
  };
  const MockMarker = (props: any) => {
    return <View testID="mock-marker" {...props} />;
  };
  return {
    __esModule: true,
    default: MockMapView,
    Marker: MockMarker,
  };
});

// Mock Alert
jest.spyOn(Alert, 'alert');

describe('CreatePostingScreen', () => {
  // Mock navigation
  const mockNavigate = jest.fn();
  
  // Mock hooks
  const mockAddPosting = jest.fn();
  const mockValidate = jest.fn();
  const mockCurrentUser = { uid: 'test-user-id' };
  
  // Mock console.error
  const originalConsoleError = console.error;

  beforeEach(() => {
    jest.clearAllMocks();
    console.error = jest.fn();
    
    // Setup navigation mock
    (useNavigation as jest.Mock).mockReturnValue({
      navigate: mockNavigate,
    });

    // Setup useAddPosting mock
    (useAddPosting as jest.Mock).mockReturnValue({
      addPosting: mockAddPosting,
      loading: false,
    });

    // Setup useFormValidation mock
    (useFormValidation as jest.Mock).mockReturnValue({
      validate: mockValidate,
      validationErrors: [],
    });

    // Setup useAuthUser mock
    (useAuthUser as jest.Mock).mockReturnValue(mockCurrentUser);
  });

  afterEach(() => {
    console.error = originalConsoleError;
  });

  it('renders correctly', () => {
    const { getByTestId, getByPlaceholderText } = render(<CreatePostingScreen />);
    
    // Check if form inputs are rendered
    expect(getByPlaceholderText('Enter item name')).toBeTruthy();
    expect(getByPlaceholderText('Enter item description')).toBeTruthy();
    
    // Check if map is rendered
    expect(getByTestId('mock-map-view')).toBeTruthy();
  });

  it('handles form input changes correctly', () => {
    const { getByPlaceholderText } = render(<CreatePostingScreen />);
    
    const itemNameInput = getByPlaceholderText('Enter item name');
    const descriptionInput = getByPlaceholderText('Enter item description');
    
    fireEvent.changeText(itemNameInput, 'Test Item');
    fireEvent.changeText(descriptionInput, 'Test Description');
    
    expect(itemNameInput.props.value).toBe('Test Item');
    expect(descriptionInput.props.value).toBe('Test Description');
  });

  it('handles map press correctly', () => {
    const { getByTestId } = render(<CreatePostingScreen />);
    const mapView = getByTestId('mock-map-view');
    
    const mockCoordinate = {
      latitude: 40.7128,
      longitude: -74.0060,
    };
    
    fireEvent(mapView, 'press', {
      nativeEvent: {
        coordinate: mockCoordinate,
      },
    });
    
    const marker = getByTestId('mock-marker');
    expect(marker.props.coordinate).toEqual(mockCoordinate);
  });

  it('validates form before submission', async () => {
    mockValidate.mockReturnValue(false);
    (useFormValidation as jest.Mock).mockReturnValue({
      validate: mockValidate,
      validationErrors: ['Item name is required', 'Description is required'],
    });

    const { getByText } = render(<CreatePostingScreen />);
    const submitButton = getByText('Submit Posting');
    
    fireEvent.press(submitButton);
    
    expect(mockValidate).toHaveBeenCalled();
    expect(Alert.alert).toHaveBeenCalledWith(
      'Validation Error',
      'Item name is required\nDescription is required'
    );
    expect(mockAddPosting).not.toHaveBeenCalled();
  });

  it('submits form successfully', async () => {
    mockValidate.mockReturnValue(true);
    mockAddPosting.mockResolvedValue(undefined);

    const { getByText, getByPlaceholderText } = render(<CreatePostingScreen />);
    
    // Fill in form data
    fireEvent.changeText(getByPlaceholderText('Enter item name'), 'Test Item');
    fireEvent.changeText(getByPlaceholderText('Enter item description'), 'Test Description');
    
    // Submit form
    const submitButton = getByText('Submit Posting');
    fireEvent.press(submitButton);
    
    await waitFor(() => {
      expect(mockAddPosting).toHaveBeenCalledWith({
        title: 'Test Item',
        description: 'Test Description',
        latitude: expect.any(Number),
        longitude: expect.any(Number),
        userId: 'test-user-id',
        postingStatus: 'Active',
      });
      
      expect(Alert.alert).toHaveBeenCalledWith(
        'Success',
        'Your posting has been successfully created.',
        [{ text: 'OK', onPress: expect.any(Function) }]
      );
    });
  });

  it('handles submission error', async () => {
    mockValidate.mockReturnValue(true);
    mockAddPosting.mockRejectedValue(new Error('Failed to add posting'));

    const { getByText } = render(<CreatePostingScreen />);
    const submitButton = getByText('Submit Posting');
    
    fireEvent.press(submitButton);
    
    await waitFor(() => {
      expect(Alert.alert).toHaveBeenCalledWith(
        'Error',
        'There was an error adding the posting. Please try again.'
      );
    });
  });

  it('shows loading state during submission', () => {
    (useAddPosting as jest.Mock).mockReturnValue({
      addPosting: mockAddPosting,
      loading: true,
    });

    const { getByText } = render(<CreatePostingScreen />);
    expect(getByText('Submitting...')).toBeTruthy();
  });

  it('requires user authentication', () => {
    (useAuthUser as jest.Mock).mockReturnValue(null);

    const { getByText } = render(<CreatePostingScreen />);
    const submitButton = getByText('Submit Posting');
    
    fireEvent.press(submitButton);
    
    expect(mockAddPosting).not.toHaveBeenCalled();
    expect(console.error).toHaveBeenCalledWith('No user is logged in');
  });
}); 