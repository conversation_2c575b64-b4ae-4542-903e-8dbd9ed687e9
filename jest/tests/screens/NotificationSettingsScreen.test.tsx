import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { act } from '@testing-library/react-native';
import { Alert } from 'react-native';
import NotificationSettingsScreen from '@/screens/NotificationSettingsScreen';
import { NotificationType } from '@/types/notifications';
import * as useNotificationSettingsModule from '@/hooks/useNotificationSettings';

// Mock the useNotificationSettings hook
const mockUpdateSetting = jest.fn();
const mockSettings = {
  [NotificationType.NEW_OFFER]: true,
  [NotificationType.OFFER_STATUS_CHANGE]: false,
  [NotificationType.NEW_MESSAGE]: true,
  [NotificationType.FAVORITE_POSTING_UPDATE]: false,
};

jest.mock('@/hooks/useNotificationSettings', () => ({
  useNotificationSettings: jest.fn(),
}));

// Mock Alert
jest.spyOn(Alert, 'alert');

// Mock console methods
const originalConsole = { ...console };
beforeAll(() => {
  console.log = jest.fn();
  console.error = jest.fn();
  console.warn = jest.fn();
  console.info = jest.fn();
  console.debug = jest.fn();
});

afterAll(() => {
  console.log = originalConsole.log;
  console.error = originalConsole.error;
  console.warn = originalConsole.warn;
  console.info = originalConsole.info;
  console.debug = originalConsole.debug;
});

describe('NotificationSettingsScreen', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (useNotificationSettingsModule.useNotificationSettings as jest.Mock).mockReturnValue({
      settings: mockSettings,
      updateSetting: mockUpdateSetting,
      loading: false,
      error: null,
    });
  });

  describe('Rendering Tests', () => {
    it('renders all notification options correctly', () => {
      const { getByText } = render(<NotificationSettingsScreen />);
      
      // Verify all notification options are rendered
      expect(getByText('New Offers')).toBeTruthy();
      expect(getByText('Offer Updates')).toBeTruthy();
      expect(getByText('Messages')).toBeTruthy();
      expect(getByText('Posting Updates')).toBeTruthy();
      
      // Verify descriptions are rendered
      expect(getByText('Receive notifications when someone makes an offer on your posting')).toBeTruthy();
      expect(getByText('Receive notifications when someone updates their offer price or description')).toBeTruthy();
      expect(getByText('Receive notifications when you get new messages in offer discussions')).toBeTruthy();
      expect(getByText('Receive notifications when postings in your favorites list are updated')).toBeTruthy();
    });

    it('renders switches with correct initial states', () => {
      const { getAllByTestId } = render(<NotificationSettingsScreen />);
      const switches = getAllByTestId('notification-switch');
      
      expect(switches[0].props.value).toBe(mockSettings[NotificationType.NEW_OFFER]);
      expect(switches[1].props.value).toBe(mockSettings[NotificationType.OFFER_STATUS_CHANGE]);
      expect(switches[2].props.value).toBe(mockSettings[NotificationType.NEW_MESSAGE]);
      expect(switches[3].props.value).toBe(mockSettings[NotificationType.FAVORITE_POSTING_UPDATE]);
    });

    it('displays loading state correctly', () => {
      (useNotificationSettingsModule.useNotificationSettings as jest.Mock).mockReturnValue({
        settings: {},
        updateSetting: mockUpdateSetting,
        loading: true,
        error: null,
      });

      const { getByTestId } = render(<NotificationSettingsScreen />);
      expect(getByTestId('loading-indicator')).toBeTruthy();
    });
  });

  describe('Interaction Tests', () => {
    it('handles switch toggle correctly', async () => {
      const { getAllByTestId } = render(<NotificationSettingsScreen />);
      const switches = getAllByTestId('notification-switch');
      
      await act(async () => {
        fireEvent(switches[0], 'valueChange', !mockSettings[NotificationType.NEW_OFFER]);
      });

      expect(mockUpdateSetting).toHaveBeenCalledWith(
        NotificationType.NEW_OFFER,
        !mockSettings[NotificationType.NEW_OFFER]
      );
    });

    it('handles update errors correctly', async () => {
      const error = new Error('Failed to update setting');
      mockUpdateSetting.mockImplementation(() => Promise.reject(error));
      
      const { getAllByTestId } = render(<NotificationSettingsScreen />);
      const switches = getAllByTestId('notification-switch');
      
      await act(async () => {
        try {
          await fireEvent(switches[0], 'valueChange', !mockSettings[NotificationType.NEW_OFFER]);
        } catch (e) {
          // Ignore the error as it's expected
        }
      });

      expect(mockUpdateSetting).toHaveBeenCalledWith(
        NotificationType.NEW_OFFER,
        !mockSettings[NotificationType.NEW_OFFER]
      );
      expect(console.error).toHaveBeenCalledWith(
        'Error updating notification setting:',
        error
      );
      expect(Alert.alert).toHaveBeenCalledWith(
        'Error',
        'Failed to update notification setting'
      );
    });
  });

  describe('Error Handling', () => {
    it('handles hook error state correctly', () => {
      const errorMessage = 'Failed to load settings';
      (useNotificationSettingsModule.useNotificationSettings as jest.Mock).mockReturnValue({
        settings: {},
        updateSetting: mockUpdateSetting,
        loading: false,
        error: new Error(errorMessage),
      });

      const { getByText } = render(<NotificationSettingsScreen />);
      expect(getByText(`Error: ${errorMessage}`)).toBeTruthy();
    });
  });

  describe('Accessibility', () => {
    it('has accessible labels for notification options', () => {
      const { getAllByTestId } = render(<NotificationSettingsScreen />);
      const options = getAllByTestId('notification-option');
      
      expect(options[0].props.accessibilityLabel).toBe('Toggle New Offers notifications');
      expect(options[1].props.accessibilityLabel).toBe('Toggle Offer Updates notifications');
      expect(options[2].props.accessibilityLabel).toBe('Toggle Messages notifications');
      expect(options[3].props.accessibilityLabel).toBe('Toggle Posting Updates notifications');
    });
  });
}); 