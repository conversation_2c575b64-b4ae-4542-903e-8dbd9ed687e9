import React from 'react';
import { render, fireEvent, act, waitFor } from '@testing-library/react-native';
import NotificationsScreen from '../../../screens/NotificationsScreen';
import { subscribeToNotifications, markNotificationAsRead } from '../../../services/notificationService';
import { NotificationType } from '../../../types/notifications';

// Mock navigation
jest.mock('@react-navigation/native', () => ({
  useNavigation: () => mockNavigation,
}));

// Mock services
jest.mock('../../../services/notificationService', () => ({
  subscribeToNotifications: jest.fn(),
  markNotificationAsRead: jest.fn().mockResolvedValue(undefined),
}));

// Mock Ionicons
jest.mock('react-native-vector-icons/Ionicons', () => 'Icon');

const mockNavigation = {
  navigate: jest.fn(),
};

const mockUnsubscribe = jest.fn();
const mockError = new Error('Cleanup failed');

const mockNotifications = [
  {
    id: 'notification1',
    type: NotificationType.NEW_OFFER,
    offerId: 'offer1',
    postingId: 'posting1',
    recipientId: 'recipient1',
    senderId: 'sender1',
    title: 'New Offer',
    body: 'You have a new offer',
    read: false,
    createdAt: new Date(),
  },
  {
    id: 'notification2',
    type: NotificationType.FAVORITE_POSTING_UPDATE,
    postingId: 'posting3',
    senderId: 'sender3',
    title: 'Posting Updated',
    body: 'A posting you favorited was updated',
    read: false,
    createdAt: new Date(),
  },
  {
    id: 'notification3',
    type: NotificationType.NEW_LOWER_OFFER,
    data: {
      postingId: 'posting5',
    },
    recipientId: 'recipient5',
    title: 'Lower Offer',
    body: 'Someone made a lower offer',
    read: false,
    createdAt: new Date(),
  },
];

describe('NotificationsScreen', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockNavigation.navigate.mockClear();
    (markNotificationAsRead as jest.Mock).mockClear();
  });

  it('renders loading state initially', async () => {
    const { getByTestId } = render(<NotificationsScreen />);
    expect(getByTestId('loading-indicator')).toBeTruthy();
  });

  it('renders notifications list after loading', async () => {
    (subscribeToNotifications as jest.Mock).mockImplementation((callback) => {
      callback(mockNotifications);
      return mockUnsubscribe;
    });

    const { getByText } = render(<NotificationsScreen />);
    await waitFor(() => {
      expect(getByText('New Offer')).toBeTruthy();
    });
  });

  it('renders empty state when no notifications', async () => {
    (subscribeToNotifications as jest.Mock).mockImplementation((callback) => {
      callback([]);
      return mockUnsubscribe;
    });

    const { getByText } = render(<NotificationsScreen />);
    await waitFor(() => {
      expect(getByText('No notifications yet')).toBeTruthy();
    });
  });

  it('navigates to offer detail for NEW_OFFER type', async () => {
    (subscribeToNotifications as jest.Mock).mockImplementation((callback) => {
      callback(mockNotifications);
      return mockUnsubscribe;
    });

    const { getByText } = render(<NotificationsScreen />);
    
    await waitFor(() => {
      expect(getByText('New Offer')).toBeTruthy();
    });

    await act(async () => {
      fireEvent.press(getByText('New Offer'));
    });

    await waitFor(() => {
      expect(markNotificationAsRead).toHaveBeenCalledWith(mockNotifications[0].id);
      expect(mockNavigation.navigate).toHaveBeenCalledWith('OfferDetail', {
        offerId: mockNotifications[0].offerId,
        postingId: mockNotifications[0].postingId,
        postingOwnerId: mockNotifications[0].recipientId,
        offerOwnerId: mockNotifications[0].senderId,
      });
    });
  });

  it('navigates to posting detail for FAVORITE_POSTING_UPDATE type', async () => {
    (subscribeToNotifications as jest.Mock).mockImplementation((callback) => {
      callback(mockNotifications);
      return mockUnsubscribe;
    });

    const { getByText } = render(<NotificationsScreen />);
    
    await waitFor(() => {
      expect(getByText('Posting Updated')).toBeTruthy();
    });

    await act(async () => {
      fireEvent.press(getByText('Posting Updated'));
    });

    await waitFor(() => {
      expect(markNotificationAsRead).toHaveBeenCalledWith(mockNotifications[1].id);
      expect(mockNavigation.navigate).toHaveBeenCalledWith('PostingDetail', {
        postingId: mockNotifications[1].postingId,
        userId: mockNotifications[1].senderId,
        itemLocation: null,
        itemName: '',
        itemDescription: '',
      });
    });
  });

  it('navigates to posting detail for NEW_LOWER_OFFER type', async () => {
    (subscribeToNotifications as jest.Mock).mockImplementation((callback) => {
      callback(mockNotifications);
      return mockUnsubscribe;
    });

    const { getByText } = render(<NotificationsScreen />);
    
    await waitFor(() => {
      expect(getByText('Lower Offer')).toBeTruthy();
    });

    await act(async () => {
      fireEvent.press(getByText('Lower Offer'));
    });

    await waitFor(() => {
      expect(markNotificationAsRead).toHaveBeenCalledWith(mockNotifications[2].id);
      expect(mockNavigation.navigate).toHaveBeenCalledWith('PostingDetail', {
        postingId: mockNotifications[2].data?.postingId,
        userId: mockNotifications[2].recipientId,
        itemLocation: null,
        itemName: '',
        itemDescription: '',
      });
    });
  });

  it('handles error when marking notification as read fails', async () => {
    const mockError = new Error('Failed to mark as read');
    (markNotificationAsRead as jest.Mock).mockRejectedValue(mockError);
    (subscribeToNotifications as jest.Mock).mockImplementation((callback) => {
      callback(mockNotifications);
      return mockUnsubscribe;
    });

    const { getByText } = render(<NotificationsScreen />);
    
    await waitFor(() => {
      expect(getByText('New Offer')).toBeTruthy();
    });

    await act(async () => {
      fireEvent.press(getByText('New Offer'));
    });

    await waitFor(() => {
      expect(markNotificationAsRead).toHaveBeenCalledWith(mockNotifications[0].id);
    });
  });

  it('handles error in notifications subscription', async () => {
    const mockError = new Error('Subscription failed');
    (subscribeToNotifications as jest.Mock).mockRejectedValue(mockError);

    const { getByText } = render(<NotificationsScreen />);
    await waitFor(() => {
      expect(getByText('No notifications yet')).toBeTruthy();
    });
  });

  it('cleans up subscription on unmount', async () => {
    (subscribeToNotifications as jest.Mock).mockImplementation((callback) => {
      callback(mockNotifications);
      return mockUnsubscribe;
    });

    const { unmount } = render(<NotificationsScreen />);
    
    await waitFor(() => {
      expect(mockUnsubscribe).toBeDefined();
    });

    unmount();

    await waitFor(() => {
      expect(mockUnsubscribe).toHaveBeenCalled();
    }, { timeout: 2000 });
  });

  it('handles error in subscription cleanup', async () => {
    const mockConsoleError = jest.spyOn(console, 'error').mockImplementation();
    mockUnsubscribe.mockImplementation(() => {
      throw mockError;
    });

    (subscribeToNotifications as jest.Mock).mockImplementation((callback) => {
      callback(mockNotifications);
      return mockUnsubscribe;
    });

    const { unmount } = render(<NotificationsScreen />);
    
    await waitFor(() => {
      expect(mockUnsubscribe).toBeDefined();
    });

    unmount();

    await waitFor(() => {
      expect(mockConsoleError).toHaveBeenCalledWith('Error cleaning up notifications subscription:', mockError);
    }, { timeout: 2000 });
    
    mockConsoleError.mockRestore();
  });
}); 