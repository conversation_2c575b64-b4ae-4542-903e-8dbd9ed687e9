import React, { useState } from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { render, fireEvent, act, waitFor } from '@testing-library/react-native';

// Mock services
const mockFirebaseService = {
  getOfferById: jest.fn(),
  updateOffer: jest.fn(),
  updateTimestamp: jest.fn(),
  enforceMessageRestrictions: jest.fn(),
  getMessages: jest.fn(),
  deleteMessage: jest.fn(),
  updateMessage: jest.fn(),
};

jest.mock('../../../services/firebaseService', () => mockFirebaseService);

// Mock component
const MockOfferDetail = () => {
  const [status, setStatus] = useState('active');
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [messages, setMessages] = useState<any[]>([]);

  const testOffer = {
    id: 'test-offer-id',
    status: status,
    price: 100,
    description: 'Test offer',
    ownerId: 'test-user-id',
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const handleUpdateStatus = async (newStatus: string) => {
    setIsLoading(true);
    try {
      await mockFirebaseService.updateOffer(testOffer.id, { status: newStatus });
      await mockFirebaseService.updateTimestamp(testOffer.id);
      if (newStatus === 'withdrawn') {
        await mockFirebaseService.enforceMessageRestrictions(testOffer.id);
      }
      setStatus(newStatus);
    } catch (err) {
      setError('Failed to update status');
      console.error('Status update error:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleLoadMessages = async () => {
    try {
      const fetchedMessages = await mockFirebaseService.getMessages(testOffer.id);
      setMessages(fetchedMessages);
    } catch (err) {
      setError('Failed to load messages');
      console.error('Message load error:', err);
    }
  };

  const handleDeleteMessage = async (messageId: string) => {
    try {
      await mockFirebaseService.deleteMessage(messageId);
      setMessages(prev => prev.filter(msg => msg.id !== messageId));
    } catch (err) {
      setError('Failed to delete message');
      console.error('Message delete error:', err);
    }
  };

  return (
    <View>
      <TouchableOpacity
        testID="update-status-button"
        onPress={() => handleUpdateStatus('withdrawn')}
      >
        <Text>Update Status</Text>
      </TouchableOpacity>
      <TouchableOpacity
        testID="load-messages-button"
        onPress={handleLoadMessages}
      >
        <Text>Load Messages</Text>
      </TouchableOpacity>
      <TouchableOpacity
        testID="delete-message-button"
        onPress={() => handleDeleteMessage('test-message-id')}
      >
        <Text>Delete Message</Text>
      </TouchableOpacity>
      {error && <Text testID="error-message">{error}</Text>}
      {isLoading && <Text testID="loading-indicator">Loading...</Text>}
      <Text testID="offer-status">{status}</Text>
      <View testID="messages-container">
        {messages.map(msg => (
          <Text key={msg.id} testID={`message-${msg.id}`}>
            {msg.content}
          </Text>
        ))}
      </View>
    </View>
  );
};

jest.mock('../../../screens/OfferDetail', () => MockOfferDetail);

describe('OfferDetail Database Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Status Updates', () => {
    it('updates offer status in database', async () => {
      const { getByTestId } = render(<MockOfferDetail />);
      const updateButton = getByTestId('update-status-button');
      
      await act(async () => {
        fireEvent.press(updateButton);
      });

      expect(mockFirebaseService.updateOffer).toHaveBeenCalledWith(
        'test-offer-id',
        { status: 'withdrawn' }
      );
      expect(mockFirebaseService.updateTimestamp).toHaveBeenCalledWith('test-offer-id');
    });

    it('maintains data consistency with timestamps', async () => {
      let updateOfferCallOrder = -1;
      let updateTimestampCallOrder = -1;
      let currentCall = 0;

      mockFirebaseService.updateOffer.mockImplementation(() => {
        updateOfferCallOrder = currentCall++;
        return Promise.resolve();
      });

      mockFirebaseService.updateTimestamp.mockImplementation(() => {
        updateTimestampCallOrder = currentCall++;
        return Promise.resolve();
      });

      const { getByTestId } = render(<MockOfferDetail />);
      const updateButton = getByTestId('update-status-button');
      
      await act(async () => {
        fireEvent.press(updateButton);
      });

      expect(updateOfferCallOrder).toBeLessThan(updateTimestampCallOrder);
    });
  });

  describe('Message Restrictions', () => {
    it('enforces message restrictions when offer is withdrawn', async () => {
      const { getByTestId } = render(<MockOfferDetail />);
      const updateButton = getByTestId('update-status-button');
      
      await act(async () => {
        fireEvent.press(updateButton);
      });

      expect(mockFirebaseService.enforceMessageRestrictions).toHaveBeenCalledWith('test-offer-id');
    });

    it('handles message deletion correctly', async () => {
      mockFirebaseService.getMessages.mockResolvedValueOnce([
        { id: 'test-message-id', content: 'Test message' }
      ]);

      const { getByTestId } = render(<MockOfferDetail />);
      
      // First load messages
      await act(async () => {
        fireEvent.press(getByTestId('load-messages-button'));
      });

      // Then delete a message
      await act(async () => {
        fireEvent.press(getByTestId('delete-message-button'));
      });

      expect(mockFirebaseService.deleteMessage).toHaveBeenCalledWith('test-message-id');
    });

    it('maintains message list consistency after deletion', async () => {
      mockFirebaseService.getMessages.mockResolvedValueOnce([
        { id: 'test-message-id', content: 'Test message' }
      ]);

      const { getByTestId, queryByTestId } = render(<MockOfferDetail />);
      
      // Load messages
      await act(async () => {
        fireEvent.press(getByTestId('load-messages-button'));
      });

      // Delete message
      await act(async () => {
        fireEvent.press(getByTestId('delete-message-button'));
      });

      // Verify message is removed from UI
      expect(queryByTestId('message-test-message-id')).toBeNull();
    });
  });

  describe('Error Handling', () => {
    it('handles database update errors gracefully', async () => {
      mockFirebaseService.updateOffer.mockRejectedValueOnce(
        new Error('Database update failed')
      );
      
      const { getByTestId } = render(<MockOfferDetail />);
      const updateButton = getByTestId('update-status-button');
      
      await act(async () => {
        fireEvent.press(updateButton);
      });

      await waitFor(() => {
        expect(getByTestId('error-message')).toBeTruthy();
      });
    });
  });
}); 