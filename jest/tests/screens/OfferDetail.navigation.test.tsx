import React from 'react';
import { render, fireEvent, act, waitFor } from '@testing-library/react-native';
import { View, Text, TouchableOpacity } from 'react-native';
import OfferDetail from '../../../screens/OfferDetail';
import { createMockNavigation } from '../../mocks/offerDetail/mockNavigation';
import { createMockRoute } from '../../mocks/offerDetail/mockRoute';
import { mockAuth, mockFirebaseService, mockKeyboardSubscription, resetAllMocks } from '../../mocks/offerDetail/mockServices';
import { NavigationProp } from '@react-navigation/native';
import { RootStackParamList } from '../../mocks/offerDetail/mockRoute';

// Mock Firebase
jest.mock('firebase/firestore', () => ({
  getFirestore: jest.fn(),
  collection: jest.fn(),
  query: jest.fn(),
  where: jest.fn(),
  orderBy: jest.fn(),
  limit: jest.fn(),
  onSnapshot: jest.fn(),
}));

jest.mock('../../../firebase', () => ({
  auth: mockAuth,
  db: {}
}));

// Mock Firebase Service
jest.mock('../../../services/firebaseService', () => mockFirebaseService);

interface MockOfferDetailProps {
  navigation: NavigationProp<RootStackParamList, 'OfferDetail'>;
  route: ReturnType<typeof createMockRoute>;
}

// Mock OfferDetail component for testing
const MockOfferDetail: React.FC<MockOfferDetailProps> = ({ navigation, route }) => {
  const [isPostingExpanded, setIsPostingExpanded] = React.useState(false);
  const [messages, setMessages] = React.useState([]);

  // Simulate message subscription and initial fetch
  React.useEffect(() => {
    const unsubscribe = mockFirebaseService.subscribeToMessages(route.params.offerId);
    mockFirebaseService.getMessages(route.params.offerId);
    return () => {
      if (typeof unsubscribe === 'function') {
        unsubscribe();
      }
    };
  }, [route.params.offerId]);

  // Handle refresh flag
  React.useEffect(() => {
    if (route.params.refresh) {
      mockFirebaseService.getMessages(route.params.offerId);
    }
  }, [route.params.refresh, route.params.offerId]);

  // Simulate navigation focus/blur handlers
  React.useEffect(() => {
    const unsubscribeFocus = navigation.addListener('focus', () => {
      mockFirebaseService.getMessages(route.params.offerId);
    });

    const unsubscribeBlur = navigation.addListener('blur', () => {
      setMessages([]);
    });

    return () => {
      if (typeof unsubscribeFocus === 'function') {
        unsubscribeFocus();
      }
      if (typeof unsubscribeBlur === 'function') {
        unsubscribeBlur();
      }
    };
  }, [navigation, route.params.offerId]);

  return (
    <View testID="offer-details-container">
      <TouchableOpacity 
        testID="posting-details-header" 
        onPress={() => {
          if (!isPostingExpanded) {
            navigation.navigate('PostingDetail', {
              postingId: route.params.initialPosting.id,
              userId: route.params.initialPosting.ownerId
            });
          }
          setIsPostingExpanded(!isPostingExpanded);
        }}
      >
        <Text>Posting Details Header</Text>
      </TouchableOpacity>

      <TouchableOpacity 
        testID="edit-offer-button"
        onPress={() => {
          navigation.navigate('EditOffer', {
            offerId: route.params.initialOffer.id,
            initialOffer: route.params.initialOffer
          });
        }}
      >
        <Text>Edit Offer</Text>
      </TouchableOpacity>
    </View>
  );
};

jest.mock('../../../screens/OfferDetail', () => MockOfferDetail);

describe('OfferDetail Navigation Tests', () => {
  beforeEach(() => {
    resetAllMocks();
    // Mock navigation listener returns
    (createMockNavigation().addListener as jest.Mock).mockReturnValue(jest.fn());
  });

  describe('Navigation Flows', () => {
    it('navigates to PostingDetail when clicking posting details', async () => {
      const mockNavigation = createMockNavigation();
      const mockRoute = createMockRoute();

      const { getByTestId } = render(
        <MockOfferDetail navigation={mockNavigation} route={mockRoute} />
      );

      await act(async () => {
        const postingDetailsHeader = getByTestId('posting-details-header');
        await fireEvent.press(postingDetailsHeader);
      });

      expect(mockNavigation.navigate).toHaveBeenCalledWith('PostingDetail', {
        postingId: mockRoute.params.initialPosting.id,
        userId: mockRoute.params.initialPosting.ownerId
      });
    });

    it('navigates to EditOffer when clicking edit offer button', async () => {
      const mockNavigation = createMockNavigation();
      const mockRoute = createMockRoute();

      const { getByTestId } = render(
        <MockOfferDetail navigation={mockNavigation} route={mockRoute} />
      );

      await act(async () => {
        const editOfferButton = getByTestId('edit-offer-button');
        await fireEvent.press(editOfferButton);
      });

      expect(mockNavigation.navigate).toHaveBeenCalledWith('EditOffer', {
        offerId: mockRoute.params.initialOffer.id,
        initialOffer: mockRoute.params.initialOffer
      });
    });
  });

  describe('State Management', () => {
    it('refreshes messages on navigation focus', async () => {
      const mockNavigation = createMockNavigation();
      const mockRoute = createMockRoute();

      render(<MockOfferDetail navigation={mockNavigation} route={mockRoute} />);

      // Get focus listener callback
      const focusCallback = (mockNavigation.addListener as jest.Mock).mock.calls
        .find(call => call[0] === 'focus')[1];

      // Simulate focus event
      await act(async () => {
        focusCallback();
      });

      // Should be called twice: once on mount, once on focus
      expect(mockFirebaseService.getMessages).toHaveBeenCalledTimes(2);
      expect(mockFirebaseService.getMessages).toHaveBeenCalledWith(
        mockRoute.params.offerId
      );
    });

    it('clears messages on navigation blur', async () => {
      const mockNavigation = createMockNavigation();
      const mockRoute = createMockRoute();

      const { rerender } = render(
        <MockOfferDetail navigation={mockNavigation} route={mockRoute} />
      );

      // Get blur listener callback
      const blurCallback = (mockNavigation.addListener as jest.Mock).mock.calls
        .find(call => call[0] === 'blur')[1];

      // Simulate blur event
      await act(async () => {
        blurCallback();
      });

      // Rerender to check state update
      rerender(
        <MockOfferDetail navigation={mockNavigation} route={mockRoute} />
      );

      // Should be called once on initial mount
      expect(mockFirebaseService.getMessages).toHaveBeenCalledTimes(1);
    });

    it('maintains state during navigation return with refresh flag', async () => {
      const mockNavigation = createMockNavigation();
      const mockRoute = createMockRoute();

      const { rerender } = render(
        <MockOfferDetail navigation={mockNavigation} route={mockRoute} />
      );

      // Simulate return with refresh flag
      const updatedRoute = createMockRoute({ refresh: true });
      
      await act(async () => {
        rerender(
          <MockOfferDetail navigation={mockNavigation} route={updatedRoute} />
        );
      });

      // Should be called twice: once on initial mount, once on refresh
      expect(mockFirebaseService.getMessages).toHaveBeenCalledTimes(2);
    });
  });

  describe('Cleanup Handlers', () => {
    it('cleans up message subscription on unmount', async () => {
      const mockUnsubscribe = jest.fn();
      mockFirebaseService.subscribeToMessages.mockReturnValue(mockUnsubscribe);

      const { unmount } = render(
        <MockOfferDetail 
          navigation={createMockNavigation()} 
          route={createMockRoute()} 
        />
      );

      unmount();
      expect(mockUnsubscribe).toHaveBeenCalled();
    });

    it('cleans up navigation listeners on unmount', async () => {
      const mockNavigation = createMockNavigation();
      const mockUnsubscribeFocus = jest.fn();
      const mockUnsubscribeBlur = jest.fn();

      (mockNavigation.addListener as jest.Mock)
        .mockReturnValueOnce(mockUnsubscribeFocus)  // focus
        .mockReturnValueOnce(mockUnsubscribeBlur);  // blur

      const { unmount } = render(
        <MockOfferDetail navigation={mockNavigation} route={createMockRoute()} />
      );

      unmount();
      expect(mockUnsubscribeFocus).toHaveBeenCalled();
      expect(mockUnsubscribeBlur).toHaveBeenCalled();
    });
  });

  describe('Navigation State Tracking', () => {
    it('handles navigation state tracking for PostingDetail', async () => {
      const mockNavigation = createMockNavigation();
      const mockRoute = createMockRoute();

      const { getByTestId } = render(
        <MockOfferDetail navigation={mockNavigation} route={mockRoute} />
      );

      await act(async () => {
        await fireEvent.press(getByTestId('posting-details-header'));
      });

      expect(mockNavigation.navigate).toHaveBeenCalledWith(
        'PostingDetail',
        expect.any(Object)
      );
    });

    it('handles navigation state tracking for EditOffer', async () => {
      const mockNavigation = createMockNavigation();
      const mockRoute = createMockRoute();

      const { getByTestId } = render(
        <MockOfferDetail navigation={mockNavigation} route={mockRoute} />
      );

      await act(async () => {
        await fireEvent.press(getByTestId('edit-offer-button'));
      });

      expect(mockNavigation.navigate).toHaveBeenCalledWith(
        'EditOffer',
        expect.any(Object)
      );
    });
  });
}); 