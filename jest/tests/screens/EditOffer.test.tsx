import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { act } from '@testing-library/react-native';
import { Alert, AlertButton } from 'react-native';
import { updateDoc, doc } from 'firebase/firestore';
import EditOffer from '@/screens/EditOffer';

// Mock dependencies
const mockNavigate = jest.fn();
const mockGoBack = jest.fn();
const mockAddListener = jest.fn(() => jest.fn());
const mockSetOptions = jest.fn();

jest.mock('@react-navigation/native', () => ({
  useRoute: () => ({
    params: {
      offerId: 'test-offer-id'
    }
  }),
  useNavigation: () => ({
    navigate: mockNavigate,
    goBack: mockGoBack,
    addListener: mockAddListener,
    setOptions: mockSetOptions
  })
}));

// Mock offer details with state management
let mockError: string | null = null;
let mockOfferDetails: {
  price: string;
  description: string;
  postingId: string;
  postingOwnerId: string;
} | null = {
  price: '100',
  description: 'Test description',
  postingId: 'test-posting-id',
  postingOwnerId: 'test-owner-id'
};

let mockLoading = false;

jest.mock('@/hooks/useOfferDetails', () => ({
  __esModule: true,
  default: () => ({
    offerDetails: mockOfferDetails,
    loading: mockLoading,
    error: mockError,
    cleanup: jest.fn()
  })
}));

jest.mock('@/hooks/useAuthUser', () => ({
  useAuthUser: () => ({
    uid: 'test-user-id'
  })
}));

const mockWithdrawOffer = jest.fn();
jest.mock('@/hooks/useWithdrawOffer', () => ({
  __esModule: true,
  default: () => ({
    withdrawOffer: mockWithdrawOffer
  })
}));

// Mock Alert
const mockAlert = jest.spyOn(Alert, 'alert').mockImplementation((title: string, message?: string, buttons?: AlertButton[]) => {
  return buttons;
}) as jest.MockedFunction<typeof Alert.alert>;

describe('EditOffer Screen', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockOfferDetails = {
      price: '100',
      description: 'Test description',
      postingId: 'test-posting-id',
      postingOwnerId: 'test-posting-owner-id'
    };
    mockLoading = false;
    mockError = null;
  });

  it('renders correctly with initial state', async () => {
    const { getByTestId } = render(<EditOffer />);

    await waitFor(() => {
      expect(getByTestId('edit-offer-price-input')).toBeTruthy();
      expect(getByTestId('edit-offer-description-input')).toBeTruthy();
      expect(getByTestId('edit-offer-update-button')).toBeTruthy();
      expect(getByTestId('edit-offer-withdraw-button')).toBeTruthy();
      expect(getByTestId('edit-offer-back-button')).toBeTruthy();
    });
  });

  it('shows loading state when loading is true', async () => {
    mockLoading = true;
    const { getByTestId } = render(<EditOffer />);

    await waitFor(() => {
      expect(getByTestId('edit-offer-loading-indicator')).toBeTruthy();
    });
  });

  describe('Form Validation', () => {
    it('validates empty price field', async () => {
      const { getByTestId } = render(<EditOffer />);
      const priceInput = getByTestId('edit-offer-price-input');
      const updateButton = getByTestId('edit-offer-update-button');

      await act(async () => {
        fireEvent.changeText(priceInput, '');
      });

      await act(async () => {
        fireEvent.press(updateButton);
      });

      expect(mockAlert).toHaveBeenCalledWith(
        'Error',
        'Please enter a valid price'
      );
    });

    it('validates non-numeric price input', async () => {
      const { getByTestId } = render(<EditOffer />);
      const priceInput = getByTestId('edit-offer-price-input');

      // Set initial value
      await act(async () => {
        fireEvent.changeText(priceInput, '100');
      });

      // Try to set non-numeric value
      await act(async () => {
        fireEvent.changeText(priceInput, 'abc');
      });

      // Should keep original value
      expect(priceInput.props.value).toBe('100');
    });

    it('allows valid numeric price input', async () => {
      const { getByTestId } = render(<EditOffer />);
      const priceInput = getByTestId('edit-offer-price-input');

      await act(async () => {
        fireEvent.changeText(priceInput, '123');
      });

      expect(priceInput.props.value).toBe('123');
    });
  });

  describe('Description Validation', () => {
    it('allows empty description', async () => {
      const { getByTestId } = render(<EditOffer />);
      const descriptionInput = getByTestId('edit-offer-description-input');

      await act(async () => {
        fireEvent.changeText(descriptionInput, '');
      });

      expect(descriptionInput.props.value).toBe('');
    });

    it('handles special characters in description', async () => {
      const { getByTestId } = render(<EditOffer />);
      const descriptionInput = getByTestId('edit-offer-description-input');
      const specialCharsText = '!@#$%^&*()_+ Special chars test';

      await act(async () => {
        fireEvent.changeText(descriptionInput, specialCharsText);
      });

      expect(descriptionInput.props.value).toBe(specialCharsText);
    });
  });

  describe('Offer Withdrawal', () => {
    it('shows withdrawal confirmation dialog', async () => {
      const { getByTestId } = render(<EditOffer />);
      const withdrawButton = getByTestId('edit-offer-withdraw-button');

      await act(async () => {
        fireEvent.press(withdrawButton);
      });

      expect(mockAlert).toHaveBeenCalledWith(
        'Withdraw Offer',
        'Are you sure you want to withdraw this offer?',
        expect.arrayContaining([
          expect.objectContaining({ text: 'Cancel', style: 'cancel' }),
          expect.objectContaining({ text: 'Withdraw', style: 'destructive' })
        ])
      );
    });

    it('handles successful withdrawal', async () => {
      mockWithdrawOffer.mockResolvedValueOnce(undefined);
      const { getByTestId } = render(<EditOffer />);
      const withdrawButton = getByTestId('edit-offer-withdraw-button');

      await act(async () => {
        fireEvent.press(withdrawButton);
      });

      // Find and press the Withdraw button in the alert
      const alertButtons = mockAlert.mock.calls[0][2] as AlertButton[];
      const withdrawAction = alertButtons.find(button => button.text === 'Withdraw');
      await act(async () => {
        await withdrawAction?.onPress?.();
      });

      expect(mockWithdrawOffer).toHaveBeenCalledWith('test-offer-id');
      expect(mockAlert).toHaveBeenLastCalledWith(
        'Success',
        'Offer withdrawn successfully',
        expect.arrayContaining([
          expect.objectContaining({ text: 'OK' })
        ])
      );
    });

    it('handles withdrawal failure', async () => {
      mockWithdrawOffer.mockRejectedValueOnce(new Error('Withdrawal failed'));
      const { getByTestId } = render(<EditOffer />);
      const withdrawButton = getByTestId('edit-offer-withdraw-button');

      await act(async () => {
        fireEvent.press(withdrawButton);
      });

      // Find and press the Withdraw button in the alert
      const alertButtons = mockAlert.mock.calls[0][2] as AlertButton[];
      const withdrawAction = alertButtons.find(button => button.text === 'Withdraw');
      await act(async () => {
        await withdrawAction?.onPress?.();
      });

      expect(mockWithdrawOffer).toHaveBeenCalledWith('test-offer-id');
      expect(mockAlert).toHaveBeenLastCalledWith(
        'Error',
        'Failed to withdraw offer'
      );
    });
  });

  describe('Navigation Handling', () => {
    it('handles back button press', async () => {
      const { getByTestId } = render(<EditOffer />);
      const backButton = getByTestId('edit-offer-back-button');

      await act(async () => {
        fireEvent.press(backButton);
      });

      expect(mockGoBack).toHaveBeenCalled();
    });

    it('navigates after successful update', async () => {
      const { getByTestId } = render(<EditOffer />);
      const priceInput = getByTestId('edit-offer-price-input');
      const updateButton = getByTestId('edit-offer-update-button');

      // Set valid price
      await act(async () => {
        fireEvent.changeText(priceInput, '100');
      });

      // Mock successful Firebase update
      jest.spyOn(require('firebase/firestore'), 'updateDoc').mockResolvedValueOnce(undefined);

      // Press update button
      await act(async () => {
        fireEvent.press(updateButton);
      });

      // Verify alert was shown with correct buttons
      expect(mockAlert).toHaveBeenCalledWith(
        'Success',
        'Offer updated successfully',
        expect.arrayContaining([
          expect.objectContaining({
            text: 'OK',
            onPress: expect.any(Function)
          })
        ])
      );

      // Press OK button
      const alertButtons = mockAlert.mock.calls[0][2] as AlertButton[];
      if (!alertButtons) {
        throw new Error('Alert buttons not found');
      }
      const okButton = alertButtons.find((button: AlertButton) => button.text === 'OK');
      if (!okButton || typeof okButton.onPress !== 'function') {
        throw new Error('OK button or onPress handler not found');
      }
      const onPressHandler = okButton.onPress as () => void;
      await act(async () => {
        onPressHandler();
      });

      // Verify navigation
      expect(mockNavigate).toHaveBeenCalledWith('OfferDetail', {
        offerId: 'test-offer-id',
        refresh: true
      });
    });
  });

  describe('Error States', () => {
    it('renders error state when offerError exists', async () => {
      mockError = 'Failed to load offer details';
      const { getByText } = render(<EditOffer />);

      await waitFor(() => {
        expect(getByText(`Error: ${mockError}`)).toBeTruthy();
      });
    });

    it('handles error in offer details processing', async () => {
      mockOfferDetails = null;
      const { getByTestId } = render(<EditOffer />);
      const priceInput = getByTestId('edit-offer-price-input');
      const descriptionInput = getByTestId('edit-offer-description-input');

      await waitFor(() => {
        expect(priceInput.props.value).toBe('');
        expect(descriptionInput.props.value).toBe('');
      });
    });

    it('handles error in update offer logic', async () => {
      // Mock the initial state
      mockOfferDetails = {
        price: '100',
        description: 'Test description',
        postingId: 'test-posting-id',
        postingOwnerId: 'test-owner-id'
      };

      const { getByTestId } = render(<EditOffer />);
      const updateButton = getByTestId('edit-offer-update-button');
      const priceInput = getByTestId('edit-offer-price-input');

      // Wait for initial render and state update
      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, 0));
      });

      // Clear the price field
      await act(async () => {
        fireEvent.changeText(priceInput, '');
      });

      // Trigger update
      await act(async () => {
        fireEvent.press(updateButton);
      });

      expect(mockAlert).toHaveBeenCalledWith('Error', 'Please enter a valid price');
    });

    it('logs lifecycle events correctly', async () => {
      const consoleSpy = jest.spyOn(console, 'log');
      
      const { unmount } = render(<EditOffer />);

      // Wait for mount log
      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, 0));
      });

      // Verify mount log
      expect(consoleSpy).toHaveBeenCalledWith('[EditOffer][MOUNT]', expect.objectContaining({
        mountCount: expect.any(Number),
        cleanupStarted: false,
        navigationStarted: false,
        lastAction: 'MOUNT',
        timestamp: expect.any(String)
      }));

      // Unmount component
      unmount();

      // Wait for cleanup log
      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, 0));
      });

      // Verify cleanup log
      expect(consoleSpy).toHaveBeenCalledWith('[EditOffer][CLEANUP]', expect.objectContaining({
        mountCount: expect.any(Number),
        cleanupStarted: true,
        navigationStarted: false,
        lastAction: 'CLEANUP',
        timestamp: expect.any(String)
      }));

      consoleSpy.mockRestore();
    });

    it('handles malformed offer details', async () => {
      mockOfferDetails = { 
        price: undefined as unknown as string,
        description: null as unknown as string,
        postingId: 'test-posting-id',
        postingOwnerId: 'test-owner-id'
      };
      
      const { getByTestId } = render(<EditOffer />);
      const priceInput = getByTestId('edit-offer-price-input');
      const descriptionInput = getByTestId('edit-offer-description-input');

      await waitFor(() => {
        expect(priceInput.props.value).toBe('');
        expect(descriptionInput.props.value).toBe('');
      });
    });

    it('handles update error from Firebase', async () => {
      const { getByTestId } = render(<EditOffer />);
      const updateButton = getByTestId('edit-offer-update-button');
      const priceInput = getByTestId('edit-offer-price-input');

      // Set valid price
      await act(async () => {
        fireEvent.changeText(priceInput, '100');
      });

      // Mock Firebase update to fail
      jest.spyOn(require('firebase/firestore'), 'updateDoc')
        .mockRejectedValueOnce(new Error('Firebase update failed'));

      // Trigger update
      await act(async () => {
        fireEvent.press(updateButton);
      });

      expect(mockAlert).toHaveBeenCalledWith('Error', 'Failed to update offer');
    });
  });

  describe('Navigation State', () => {
    beforeEach(() => {
      jest.resetModules();
      mockGoBack.mockClear();
    });

    it('handles navigation after withdrawal', async () => {
      const mockAlert = jest.spyOn(Alert, 'alert');
      const { getByTestId } = render(<EditOffer />);
      const withdrawButton = getByTestId('edit-offer-withdraw-button');

      // Press the withdraw button
      await act(async () => {
        fireEvent.press(withdrawButton);
      });

      // Get the alert buttons and press withdraw
      expect(mockAlert).toHaveBeenCalledWith(
        'Withdraw Offer',
        'Are you sure you want to withdraw this offer?',
        expect.any(Array)
      );

      const withdrawButtons = mockAlert.mock.calls[0][2];
      if (!withdrawButtons || !withdrawButtons[1] || !withdrawButtons[1].onPress) {
        throw new Error('Withdraw button not found in alert');
      }
      const withdrawAction = withdrawButtons[1].onPress as () => void;

      // Press the withdraw button
      await act(async () => {
        withdrawAction();
      });

      // Get the success alert buttons and press OK
      expect(mockAlert).toHaveBeenCalledWith(
        'Success',
        'Offer withdrawn successfully',
        expect.any(Array)
      );

      const successButtons = mockAlert.mock.calls[1][2];
      if (!successButtons || !successButtons[0] || !successButtons[0].onPress) {
        throw new Error('OK button not found in success alert');
      }
      const okAction = successButtons[0].onPress as () => void;

      // Press the OK button
      await act(async () => {
        okAction();
      });

      // Verify navigation occurred
      expect(mockNavigate).toHaveBeenCalledWith('OfferDetail', {
        offerId: 'test-offer-id',
        refresh: true
      });

      mockAlert.mockRestore();
    });
  });
});