/**
 * ProfileScreen.test.tsx
 * 
 * Test suite for the Profile screen component.
 * Tests cover rendering, user interactions, navigation, styling, and error handling.
 */

import React from 'react';
import { render, fireEvent, act } from '@testing-library/react-native';
import { Alert } from 'react-native';
import ProfileScreen from '../../../screens/ProfileScreen';
import { useProfileActions } from '../../../hooks/useProfileActions.js';
import { CommonActions } from '@react-navigation/native';
import { signOut } from '../../../services/authService';
import { clearNotificationListeners } from '../../../services/notificationService';

interface ProfileOption {
  id: string;
  title: string;
  navigateTo: string | null;
}

// Mock navigation module
const mockNavigate = jest.fn();
const mockDispatch = jest.fn();
const mockReset = jest.fn(() => ({ type: 'RESET' }));

jest.mock('@react-navigation/native', () => ({
  useNavigation: () => ({
    navigate: mockNavigate,
    dispatch: mockDispatch,
  }),
  CommonActions: {
    reset: mockReset,
  },
}));

// Mock services
jest.mock('../../../services/authService', () => ({
  signOut: jest.fn(),
}));

jest.mock('../../../services/notificationService', () => ({
  clearNotificationListeners: jest.fn(),
}));

// Mock useProfileActions hook
const mockOptions: ProfileOption[] = [
  { id: '1', title: 'Create Posting', navigateTo: 'CreatePosting' },
  { id: '2', title: 'My Postings', navigateTo: 'MyPostings' },
  { id: '3', title: 'My Offers', navigateTo: 'MyOffers' },
  { id: '4', title: 'My Favorites', navigateTo: 'MyFavorites' },
  { id: '5', title: 'Settings', navigateTo: 'Settings' },
  { id: '6', title: 'Sign Out', navigateTo: null },
];

const mockHandleOptionPress = jest.fn();

jest.mock('../../../hooks/useProfileActions', () => ({
  useProfileActions: () => ({
    options: mockOptions,
    handleOptionPress: mockHandleOptionPress,
  }),
}));

describe('ProfileScreen', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering Tests', () => {
    it('renders all profile options correctly', () => {
      const { getAllByTestId } = render(<ProfileScreen />);
      const listItems = getAllByTestId('profile-list-item');
      expect(listItems).toHaveLength(mockOptions.length);
    });

    it('renders options with correct titles', () => {
      const { getAllByTestId } = render(<ProfileScreen />);
      const listItemTexts = getAllByTestId('profile-list-item-text');
      listItemTexts.forEach((text, index) => {
        expect(text.props.children).toBe(mockOptions[index].title);
      });
    });
  });

  describe('Navigation Tests', () => {
    beforeEach(() => {
      // Set up mock implementation for handleOptionPress
      mockHandleOptionPress.mockImplementation(async (option: ProfileOption) => {
        if (option.navigateTo) {
          console.log(`Navigating to ${option.navigateTo}`);
          mockNavigate(option.navigateTo);
        } else if (option.title === 'Sign Out') {
          console.log('=== Starting Sign Out Flow ===');
          try {
            await clearNotificationListeners();
            await signOut();
            const resetAction = mockReset();
            mockDispatch(resetAction);
          } catch (error) {
            console.error('Error in sign out flow:', error);
          }
        }
      });
    });

    it('navigates to the correct screen when pressing a navigation option', async () => {
      const { getAllByTestId } = render(<ProfileScreen />);
      const listItems = getAllByTestId('profile-list-item');

      // Test navigation for Create Posting
      await act(async () => {
        fireEvent.press(listItems[0]);
      });
      expect(mockNavigate).toHaveBeenCalledWith('CreatePosting');

      // Test navigation for My Postings
      await act(async () => {
        fireEvent.press(listItems[1]);
      });
      expect(mockNavigate).toHaveBeenCalledWith('MyPostings');
    });

    it('handles sign out navigation correctly', async () => {
      const { getAllByTestId } = render(<ProfileScreen />);
      const listItems = getAllByTestId('profile-list-item');
      const signOutButton = listItems[listItems.length - 1];

      await act(async () => {
        fireEvent.press(signOutButton);
      });

      expect(clearNotificationListeners).toHaveBeenCalled();
      expect(signOut).toHaveBeenCalled();
      expect(mockDispatch).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'RESET'
        })
      );
    });

    it('logs navigation actions for debugging', async () => {
      const consoleSpy = jest.spyOn(console, 'log');
      const { getAllByTestId } = render(<ProfileScreen />);
      const listItems = getAllByTestId('profile-list-item');

      await act(async () => {
        fireEvent.press(listItems[0]);
      });

      expect(consoleSpy).toHaveBeenCalledWith('Navigating to CreatePosting');
      consoleSpy.mockRestore();
    });
  });

  describe('Error Handling Tests', () => {
    it('handles sign out error when notification cleanup fails', async () => {
      const mockError = new Error('Failed to clean up notifications');
      (clearNotificationListeners as jest.Mock).mockRejectedValueOnce(mockError);
      const consoleSpy = jest.spyOn(console, 'error');

      const { getAllByTestId } = render(<ProfileScreen />);
      const listItems = getAllByTestId('profile-list-item');
      const signOutButton = listItems[listItems.length - 1];

      await act(async () => {
        fireEvent.press(signOutButton);
      });

      expect(consoleSpy).toHaveBeenCalledWith('Error in sign out flow:', mockError);
      consoleSpy.mockRestore();
    });

    it('handles sign out error when auth service fails', async () => {
      const mockError = new Error('Sign out failed');
      (signOut as jest.Mock).mockRejectedValueOnce(mockError);
      const consoleSpy = jest.spyOn(console, 'error');

      const { getAllByTestId } = render(<ProfileScreen />);
      const listItems = getAllByTestId('profile-list-item');
      const signOutButton = listItems[listItems.length - 1];

      await act(async () => {
        fireEvent.press(signOutButton);
      });

      expect(consoleSpy).toHaveBeenCalledWith('Error in sign out flow:', mockError);
      consoleSpy.mockRestore();
    });

    it('handles navigation error gracefully', async () => {
      // Set up mock implementation to throw error
      mockHandleOptionPress.mockImplementationOnce(async (option: ProfileOption) => {
        if (option.navigateTo) {
          console.error('Navigation error:', new Error('Navigation failed'));
        }
      });

      const consoleSpy = jest.spyOn(console, 'error');
      const { getAllByTestId } = render(<ProfileScreen />);
      const listItems = getAllByTestId('profile-list-item');

      await act(async () => {
        await fireEvent.press(listItems[0]);
      });

      expect(consoleSpy).toHaveBeenCalledWith(
        'Navigation error:',
        expect.any(Error)
      );
      consoleSpy.mockRestore();
    });
  });

  describe('Styling Tests', () => {
    it('applies correct container styles', () => {
      const { getByTestId } = render(<ProfileScreen />);
      const container = getByTestId('profile-container');
      expect(container.props.style).toMatchObject({
        flex: 1,
        paddingTop: 20,
        backgroundColor: 'white',
      });
    });

    it('applies correct text styles', () => {
      const { getAllByTestId } = render(<ProfileScreen />);
      const listItemTexts = getAllByTestId('profile-list-item-text');
      listItemTexts.forEach(text => {
        expect(text.props.style).toMatchObject({
          fontSize: 16,
        });
      });
    });

    it('applies correct list item styles', () => {
      const { getAllByTestId } = render(<ProfileScreen />);
      const listItems = getAllByTestId('profile-list-item');
      listItems.forEach(item => {
        const innerView = item.findAllByType('View')[1];
        expect(innerView.props.style).toMatchObject({
          padding: 16,
          borderBottomWidth: 1,
          borderBottomColor: '#ccc',
        });
      });
    });
  });
}); 