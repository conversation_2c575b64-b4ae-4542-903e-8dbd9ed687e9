import React, { useState } from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { render, fireEvent, act, waitFor } from '@testing-library/react-native';

// Mock services
const mockFirebaseService = {
  getOfferById: jest.fn(),
  getMessages: jest.fn(),
  updateBadgeCount: jest.fn(),
};

const mockNotificationService = {
  sendPushNotification: jest.fn(),
  updateBadgeCount: jest.fn(),
  scheduleLocalNotification: jest.fn(),
  removeScheduledNotification: jest.fn(),
};

jest.mock('../../../services/firebaseService', () => mockFirebaseService);
jest.mock('../../../services/notificationService', () => mockNotificationService);

// Mock component
const MockOfferDetail = () => {
  const [notificationSent, setNotificationSent] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const testOffer = {
    id: 'test-offer-id',
    postingId: 'test-posting-id',
    postingOwnerId: 'posting-owner-id',
    ownerId: 'test-user-id',
    status: 'active',
    price: 100,
    description: 'Test offer',
  };

  const handleSendNotification = async () => {
    try {
      await mockNotificationService.sendPushNotification(testOffer.postingOwnerId, {
        title: 'New Message',
        body: 'You have a new message',
      });
      await mockNotificationService.updateBadgeCount(testOffer.postingOwnerId, 1);
      await mockNotificationService.scheduleLocalNotification({
        title: 'Message Sent',
        body: 'Your message has been sent successfully',
      });
      setNotificationSent(true);
    } catch (err) {
      setError('Failed to send notification');
      console.error('Notification error:', err);
    }
  };

  const handleClearNotification = async () => {
    try {
      await mockNotificationService.removeScheduledNotification('test-notification-id');
      await mockNotificationService.updateBadgeCount(testOffer.postingOwnerId, 0);
    } catch (err) {
      setError('Failed to clear notification');
      console.error('Clear notification error:', err);
    }
  };

  return (
    <View>
      <TouchableOpacity
        testID="send-notification-button"
        onPress={handleSendNotification}
      >
        <Text>Send Notification</Text>
      </TouchableOpacity>
      <TouchableOpacity
        testID="clear-notification-button"
        onPress={handleClearNotification}
      >
        <Text>Clear Notification</Text>
      </TouchableOpacity>
      {error && <Text testID="error-message">{error}</Text>}
      {notificationSent && <Text testID="notification-status">Notification sent</Text>}
    </View>
  );
};

jest.mock('../../../screens/OfferDetail', () => MockOfferDetail);

describe('OfferDetail Notification Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Push Notifications', () => {
    it('sends push notification to posting owner', async () => {
      const { getByTestId } = render(<MockOfferDetail />);
      const notificationButton = getByTestId('send-notification-button');
      
      await act(async () => {
        fireEvent.press(notificationButton);
      });

      expect(mockNotificationService.sendPushNotification).toHaveBeenCalledWith(
        'posting-owner-id',
        {
          title: 'New Message',
          body: 'You have a new message',
        }
      );
    });

    it('updates badge count after sending notification', async () => {
      const { getByTestId } = render(<MockOfferDetail />);
      const notificationButton = getByTestId('send-notification-button');
      
      await act(async () => {
        fireEvent.press(notificationButton);
      });

      expect(mockNotificationService.updateBadgeCount).toHaveBeenCalledWith(
        'posting-owner-id',
        1
      );
    });
  });

  describe('Local Notifications', () => {
    it('schedules local notification after successful message send', async () => {
      const { getByTestId } = render(<MockOfferDetail />);
      const notificationButton = getByTestId('send-notification-button');
      
      await act(async () => {
        fireEvent.press(notificationButton);
      });

      expect(mockNotificationService.scheduleLocalNotification).toHaveBeenCalledWith({
        title: 'Message Sent',
        body: 'Your message has been sent successfully',
      });
    });

    it('clears scheduled notifications when requested', async () => {
      const { getByTestId } = render(<MockOfferDetail />);
      const clearButton = getByTestId('clear-notification-button');
      
      await act(async () => {
        fireEvent.press(clearButton);
      });

      expect(mockNotificationService.removeScheduledNotification).toHaveBeenCalledWith(
        'test-notification-id'
      );
      expect(mockNotificationService.updateBadgeCount).toHaveBeenCalledWith(
        'posting-owner-id',
        0
      );
    });
  });

  describe('Error Handling', () => {
    it('handles push notification failure gracefully', async () => {
      mockNotificationService.sendPushNotification.mockRejectedValueOnce(
        new Error('Failed to send push notification')
      );
      
      const { getByTestId } = render(<MockOfferDetail />);
      const notificationButton = getByTestId('send-notification-button');
      
      await act(async () => {
        fireEvent.press(notificationButton);
      });

      await waitFor(() => {
        expect(getByTestId('error-message')).toBeTruthy();
      });
    });

    it('handles badge count update failure gracefully', async () => {
      mockNotificationService.updateBadgeCount.mockRejectedValueOnce(
        new Error('Failed to update badge count')
      );
      
      const { getByTestId } = render(<MockOfferDetail />);
      const notificationButton = getByTestId('send-notification-button');
      
      await act(async () => {
        fireEvent.press(notificationButton);
      });

      await waitFor(() => {
        expect(getByTestId('error-message')).toBeTruthy();
      });
    });
  });
}); 