import React from 'react';
import { render, fireEvent, waitFor, act } from '@testing-library/react-native';
import { Alert } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import MakeOffer from '../../../screens/MakeOffer';
import { auth } from '../../../firebase';
import { getPostingOwner, addOfferAndDiscussion } from '../../../services/firebaseService';

// Mock the required modules
jest.mock('@react-navigation/native', () => ({
  useNavigation: jest.fn(),
}));

jest.mock('../../../firebase', () => ({
  auth: {
    currentUser: {
      uid: 'test-user-id',
    },
  },
}));

jest.mock('../../../services/firebaseService', () => ({
  getPostingOwner: jest.fn(),
  addOfferAndDiscussion: jest.fn(),
  addToFavorites: jest.fn(),
}));

// Mock Alert.alert
jest.spyOn(Alert, 'alert');

describe('MakeOffer Screen', () => {
  const mockNavigation = {
    navigate: jest.fn(),
  };

  const mockRoute = {
    params: {
      postingId: 'test-posting-id',
      itemName: 'Test Item',
      itemDescription: 'Test Description',
      itemLocation: { latitude: 0, longitude: 0 }
    }
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useNavigation as jest.Mock).mockReturnValue(mockNavigation);
    (getPostingOwner as jest.Mock).mockResolvedValue('testOwnerId');
  });

  it('renders correctly', () => {
    const { getByText, getByPlaceholderText } = render(
      <MakeOffer route={mockRoute} />
    );

    expect(getByText('Make an Offer')).toBeTruthy();
    expect(getByPlaceholderText('Enter your offer price')).toBeTruthy();
    expect(getByPlaceholderText('Enter a description for your offer')).toBeTruthy();
  });

  it('validates price input to allow only numbers', () => {
    const { getByPlaceholderText } = render(
      <MakeOffer route={mockRoute} />
    );

    const priceInput = getByPlaceholderText('Enter your offer price');
    
    // Test valid input
    fireEvent.changeText(priceInput, '123');
    expect(priceInput.props.value).toBe('123');

    // Test invalid input (letters)
    fireEvent.changeText(priceInput, 'abc');
    expect(priceInput.props.value).toBe('123');
  });

  it('shows error alert when submitting without price', async () => {
    const { getByText } = render(
      <MakeOffer route={mockRoute} />
    );

    fireEvent.press(getByText('Submit Offer'));

    await waitFor(() => {
      expect(Alert.alert).toHaveBeenCalledWith(
        'Validation Error',
        'Please enter a valid offer price greater than zero.'
      );
    });
  });

  it('shows error alert when submitting without description', async () => {
    const { getByText, getByPlaceholderText } = render(
      <MakeOffer route={mockRoute} />
    );

    const priceInput = getByPlaceholderText('Enter your offer price');
    fireEvent.changeText(priceInput, '100');

    fireEvent.press(getByText('Submit Offer'));

    await waitFor(() => {
      expect(Alert.alert).toHaveBeenCalledWith(
        'Validation Error',
        'Please enter a description for your offer.'
      );
    });
  });

  it('successfully submits offer and navigates back', async () => {
    const mockRoute = {
      params: {
        postingId: 'test-posting-id',
        itemName: 'Test Item',
        itemDescription: 'Test Description',
        itemLocation: { latitude: 0, longitude: 0 },
        userId: 'test-owner-id'
      }
    };

    const { getByText, getByPlaceholderText } = render(<MakeOffer route={mockRoute} />);

    fireEvent.changeText(getByPlaceholderText('Enter your offer price'), '100');
    fireEvent.changeText(getByPlaceholderText('Enter a description for your offer'), 'Test offer description');
    fireEvent.press(getByText('Submit Offer'));

    await waitFor(() => {
      expect(addOfferAndDiscussion).toHaveBeenCalledWith(
        expect.objectContaining({
          postingId: 'test-posting-id',
          userId: 'test-user-id',
          price: 100,
          description: 'Test offer description',
          status: 'pending'
        }),
        'test-owner-id'
      );

      expect(mockNavigation.navigate).toHaveBeenCalledWith('PostingDetail', {
        postingId: 'test-posting-id',
        itemName: 'Test Item',
        itemDescription: 'Test Description',
        itemLocation: { latitude: 0, longitude: 0 },
        userId: 'test-owner-id'
      });
    });
  });

  it('shows error alert when offer submission fails', async () => {
    const { getByText, getByPlaceholderText } = render(
      <MakeOffer route={mockRoute} />
    );

    const priceInput = getByPlaceholderText('Enter your offer price');
    const descriptionInput = getByPlaceholderText('Enter a description for your offer');

    fireEvent.changeText(priceInput, '100');
    fireEvent.changeText(descriptionInput, 'Test offer description');

    (addOfferAndDiscussion as jest.Mock).mockRejectedValueOnce(new Error('Test error'));

    fireEvent.press(getByText('Submit Offer'));

    await waitFor(() => {
      expect(Alert.alert).toHaveBeenCalledWith(
        'Error',
        'There was an error submitting your offer. Please try again.'
      );
    });
  });

  it('handles missing postingId gracefully', async () => {
    const mockRouteWithoutPostingId = {
      params: {
        itemName: 'Test Item',
        itemDescription: 'Test Description',
        itemLocation: { latitude: 0, longitude: 0 },
        userId: 'testOwnerId',
      },
    };

    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

    render(<MakeOffer route={mockRouteWithoutPostingId} />);

    await waitFor(() => {
      expect(consoleSpy).toHaveBeenCalledWith(
        'Error: postingId is undefined. Ensure that the postingId is being passed correctly from the previous screen.'
      );
    });

    consoleSpy.mockRestore();
  });

  it('handles error when fetching posting owner fails', async () => {
    const error = new Error('Failed to fetch owner');
    const consoleSpy = jest.spyOn(console, 'error');
    (getPostingOwner as jest.Mock).mockRejectedValueOnce(error);

    render(<MakeOffer route={mockRoute} />);

    await waitFor(() => {
      expect(consoleSpy).toHaveBeenCalledWith(
        'Error fetching posting owner:',
        error
      );
    });

    consoleSpy.mockRestore();
  });
}); 