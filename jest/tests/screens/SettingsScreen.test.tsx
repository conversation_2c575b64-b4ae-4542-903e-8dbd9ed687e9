/**
 * SettingsScreen.test.tsx
 * 
 * Test suite for the Settings screen component.
 * Tests cover rendering, navigation, alert displays, and user interactions.
 */

import React from 'react';
import { render, fireEvent, act } from '@testing-library/react-native';
import { Alert } from 'react-native';
import SettingsScreen from '../../../screens/SettingsScreen';

// Mock navigation
const mockNavigate = jest.fn();
jest.mock('@react-navigation/native', () => ({
  useNavigation: () => ({
    navigate: mockNavigate,
  }),
}));

// Mock Alert
jest.spyOn(Alert, 'alert').mockImplementation(jest.fn());

describe('SettingsScreen', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering Tests', () => {
    it('renders all settings options correctly', () => {
      const { getAllByTestId } = render(<SettingsScreen />);
      const listItems = getAllByTestId('settings-list-item');
      expect(listItems).toHaveLength(4); // Account, Notification, Privacy, Location
    });

    it('renders options with correct titles', () => {
      const { getByText } = render(<SettingsScreen />);
      expect(getByText('Account Settings')).toBeTruthy();
      expect(getByText('Notification Settings')).toBeTruthy();
      expect(getByText('Privacy Settings')).toBeTruthy();
      expect(getByText('Location Settings')).toBeTruthy();
    });
  });

  describe('Navigation Tests', () => {
    it('navigates to NotificationSettings when pressing notification option', async () => {
      console.log('Testing navigation to NotificationSettings');
      const { getByText } = render(<SettingsScreen />);
      
      await act(async () => {
        fireEvent.press(getByText('Notification Settings'));
      });

      expect(mockNavigate).toHaveBeenCalledWith('NotificationSettings');
    });

    it('navigates to LocationSettings when pressing location option', async () => {
      console.log('Testing navigation to LocationSettings');
      const { getByText } = render(<SettingsScreen />);
      
      await act(async () => {
        fireEvent.press(getByText('Location Settings'));
      });

      expect(mockNavigate).toHaveBeenCalledWith('LocationSettings');
    });
  });

  describe('Alert Tests', () => {
    it('shows alert for Account Settings', async () => {
      console.log('Testing Account Settings alert');
      const { getByText } = render(<SettingsScreen />);
      
      await act(async () => {
        fireEvent.press(getByText('Account Settings'));
      });

      expect(Alert.alert).toHaveBeenCalledWith(
        'Account Settings',
        'Account settings functionality coming soon!'
      );
    });

    it('shows alert for Privacy Settings', async () => {
      console.log('Testing Privacy Settings alert');
      const { getByText } = render(<SettingsScreen />);
      
      await act(async () => {
        fireEvent.press(getByText('Privacy Settings'));
      });

      expect(Alert.alert).toHaveBeenCalledWith(
        'Privacy Settings',
        'Privacy settings functionality coming soon!'
      );
    });
  });

  describe('Styling Tests', () => {
    it('applies correct container styles', () => {
      const { getByTestId } = render(<SettingsScreen />);
      const container = getByTestId('settings-container');
      expect(container.props.style).toMatchObject({
        flex: 1,
        paddingTop: 20,
        backgroundColor: 'white',
      });
    });

    it('applies correct list item styles', () => {
      const { getAllByTestId } = render(<SettingsScreen />);
      const listItems = getAllByTestId('settings-list-item');
      listItems.forEach(item => {
        expect(item.props.style).toMatchObject({
          padding: 16,
          borderBottomWidth: 1,
          borderBottomColor: '#ccc',
        });
      });
    });

    it('applies correct text styles', () => {
      const { getAllByTestId } = render(<SettingsScreen />);
      const listItemTexts = getAllByTestId('settings-list-item-text');
      listItemTexts.forEach(text => {
        expect(text.props.style).toMatchObject({
          fontSize: 16,
        });
      });
    });
  });

  describe('Error Handling Tests', () => {
    it('handles navigation errors gracefully', async () => {
      mockNavigate.mockImplementationOnce(() => {
        throw new Error('Navigation failed');
      });
      const consoleSpy = jest.spyOn(console, 'error');
      
      const { getByText } = render(<SettingsScreen />);
      
      await act(async () => {
        fireEvent.press(getByText('Notification Settings'));
      });

      expect(consoleSpy).toHaveBeenCalledWith(
        'Navigation error:',
        expect.any(Error)
      );
      consoleSpy.mockRestore();
    });

    it('handles alert display errors gracefully', async () => {
      (Alert.alert as jest.Mock).mockImplementationOnce(() => {
        throw new Error('Alert display failed');
      });
      const consoleSpy = jest.spyOn(console, 'error');
      
      const { getByText } = render(<SettingsScreen />);
      
      await act(async () => {
        fireEvent.press(getByText('Account Settings'));
      });

      expect(consoleSpy).toHaveBeenCalledWith(
        'Alert display error:',
        expect.any(Error)
      );
      consoleSpy.mockRestore();
    });
  });
}); 