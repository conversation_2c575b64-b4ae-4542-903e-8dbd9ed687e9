import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import MyFavoritesScreen from '../../../screens/MyFavoritesScreen';
import { mockFavorites, mockEmptyFavorites, mockError } from '../../mocks/mockFavorites';
import * as useFavoritesDataModule from '../../../hooks/useFavoritesData';

// Mock the navigation
const mockNavigate = jest.fn();
jest.mock('@react-navigation/native', () => ({
  useNavigation: () => ({
    navigate: mockNavigate,
  }),
}));

// Mock the Firebase auth
jest.mock('../../../firebase', () => ({
  auth: {
    currentUser: {
      uid: 'testUserId',
    },
  },
}));

// Mock the useFavoritesData hook
jest.mock('../../../hooks/useFavoritesData');
const mockUseFavoritesData = useFavoritesDataModule.default as jest.Mock;

describe('MyFavoritesScreen', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  describe('Loading State', () => {
    beforeEach(() => {
      mockUseFavoritesData.mockReturnValue({
        favorites: [],
        loading: true,
        error: null,
      });
    });

    it('should display loading indicator when data is being fetched', () => {
      const { getByText } = render(<MyFavoritesScreen />);
      expect(getByText('Loading...')).toBeTruthy();
    });

    it('should not display favorites list while loading', () => {
      const { queryByTestId } = render(<MyFavoritesScreen />);
      expect(queryByTestId('favorites-list')).toBeNull();
    });
  });

  describe('Error State', () => {
    beforeEach(() => {
      mockUseFavoritesData.mockReturnValue({
        favorites: [],
        loading: false,
        error: mockError,
      });
    });

    it('should display error message when fetching fails', () => {
      const { getByText } = render(<MyFavoritesScreen />);
      expect(getByText(`Error: ${mockError.message}`)).toBeTruthy();
    });
  });

  describe('Favorites List Rendering', () => {
    beforeEach(() => {
      mockUseFavoritesData.mockReturnValue({
        favorites: mockFavorites,
        loading: false,
        error: null,
      });
    });

    it('should render favorites list when data is available', () => {
      const { getByText, getAllByTestId } = render(<MyFavoritesScreen />);
      expect(getByText('My Favorites')).toBeTruthy();
      expect(getAllByTestId('favorite-item')).toHaveLength(mockFavorites.length);
    });

    it('should display favorite item details correctly', () => {
      const { getByText } = render(<MyFavoritesScreen />);
      mockFavorites.forEach(favorite => {
        expect(getByText(favorite.title)).toBeTruthy();
        expect(getByText(favorite.description)).toBeTruthy();
      });
    });

    it('should navigate to posting detail when favorite item is pressed', () => {
      const { getAllByTestId } = render(<MyFavoritesScreen />);
      const firstFavoriteItem = getAllByTestId('favorite-item')[0];
      
      fireEvent.press(firstFavoriteItem);

      expect(mockNavigate).toHaveBeenCalledWith('PostingDetail', {
        postingId: mockFavorites[0].id,
        itemName: mockFavorites[0].title,
        itemDescription: mockFavorites[0].description,
        itemLocation: {
          latitude: mockFavorites[0].latitude,
          longitude: mockFavorites[0].longitude,
        },
        userId: mockFavorites[0].userId,
      });
    });
  });

  describe('Empty State', () => {
    beforeEach(() => {
      mockUseFavoritesData.mockReturnValue({
        favorites: mockEmptyFavorites,
        loading: false,
        error: null,
      });
    });

    it('should display empty state message when no favorites exist', () => {
      const { getByText, queryByTestId } = render(<MyFavoritesScreen />);
      expect(getByText('My Favorites')).toBeTruthy();
      expect(queryByTestId('favorites-list')).toBeTruthy();
      expect(queryByTestId('favorite-item')).toBeNull();
    });
  });
}); 