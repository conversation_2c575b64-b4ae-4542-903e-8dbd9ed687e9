import React, { useState } from 'react';
import { View, Text, TouchableOpacity, Alert } from 'react-native';
import { render, fireEvent, act, waitFor } from '@testing-library/react-native';
import { NavigationProp } from '@react-navigation/native';
import { RootStackParamList } from '../../../types/navigation';

// Mock services
const mockFirebaseService = {
  getOfferById: jest.fn(),
  updateOffer: jest.fn(),
  updateTimestamp: jest.fn(),
  enforceMessageRestrictions: jest.fn(),
};

const mockNotificationService = {
  sendPushNotification: jest.fn(),
};

jest.mock('../../../services/firebaseService', () => mockFirebaseService);
jest.mock('../../../services/notificationService', () => mockNotificationService);

// Mock Alert
jest.spyOn(Alert, 'alert').mockImplementation((title, message, buttons) => {
  const confirmButton = buttons?.find(button => button.text === 'Confirm');
  const cancelButton = buttons?.find(button => button.text === 'Cancel');
  
  if (confirmButton?.onPress) {
    confirmButton.onPress();
  } else if (cancelButton?.onPress) {
    cancelButton.onPress();
  }
});

// Mock component
const MockOfferDetail = () => {
  const [isWithdrawn, setIsWithdrawn] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const testOffer = {
    id: 'test-offer-id',
    price: 100,
    description: 'Test offer',
    status: 'active',
    ownerId: 'test-user-id',
  };

  const handleWithdrawOffer = async () => {
    Alert.alert(
      'Confirm Withdrawal',
      'Are you sure you want to withdraw this offer?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Confirm',
          onPress: async () => {
            setIsLoading(true);
            try {
              await mockFirebaseService.updateOffer(testOffer.id, { status: 'withdrawn' });
              await mockFirebaseService.updateTimestamp(testOffer.id);
              await mockFirebaseService.enforceMessageRestrictions(testOffer.id);
              await mockNotificationService.sendPushNotification('posting-owner-id', {
                title: 'Offer Withdrawn',
                body: 'A user has withdrawn their offer on your posting',
              });
              setIsWithdrawn(true);
            } catch (err) {
              console.error('Failed to withdraw offer:', err);
              setError('Failed to withdraw offer');
            } finally {
              setIsLoading(false);
            }
          },
        },
      ]
    );
  };

  const shouldShowWithdrawButton = !isWithdrawn && testOffer.status === 'active' && testOffer.ownerId === 'test-user-id';

  return (
    <View>
      {shouldShowWithdrawButton && (
        <TouchableOpacity
          testID="withdraw-offer-button"
          onPress={handleWithdrawOffer}
          disabled={isLoading}
        >
          <Text>Withdraw Offer</Text>
        </TouchableOpacity>
      )}
      {error && <Text testID="error-message">{error}</Text>}
      {isLoading && <Text testID="loading-indicator">Loading...</Text>}
      <Text testID="offer-status">{isWithdrawn ? 'withdrawn' : testOffer.status}</Text>
    </View>
  );
};

jest.mock('../../../screens/OfferDetail', () => MockOfferDetail);

describe('OfferDetail Withdrawal Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Withdrawal Button Visibility', () => {
    it('shows withdrawal button for active offers owned by current user', async () => {
      const { queryByTestId } = render(<MockOfferDetail />);
      expect(queryByTestId('withdraw-offer-button')).toBeTruthy();
    });

    it('hides withdrawal button for withdrawn offers', async () => {
      const { queryByTestId } = render(<MockOfferDetail />);
      await act(async () => {
        const withdrawButton = queryByTestId('withdraw-offer-button');
        if (withdrawButton) {
          fireEvent.press(withdrawButton);
        }
      });
      await waitFor(() => {
        expect(queryByTestId('withdraw-offer-button')).toBeNull();
      });
    });

    it('hides withdrawal button for other user offers', async () => {
      const { queryByTestId } = render(<MockOfferDetail />);
      await act(async () => {
        const withdrawButton = queryByTestId('withdraw-offer-button');
        if (withdrawButton) {
          fireEvent.press(withdrawButton);
        }
      });
      await waitFor(() => {
        expect(queryByTestId('withdraw-offer-button')).toBeNull();
      });
    });
  });

  describe('Withdrawal Confirmation Flow', () => {
    it('shows confirmation dialog when withdraw button is pressed', async () => {
      const { getByTestId } = render(<MockOfferDetail />);
      const withdrawButton = getByTestId('withdraw-offer-button');
      
      await act(async () => {
        fireEvent.press(withdrawButton);
      });

      expect(Alert.alert).toHaveBeenCalledWith(
        'Confirm Withdrawal',
        'Are you sure you want to withdraw this offer?',
        expect.any(Array)
      );
    });

    it('cancels withdrawal when user dismisses confirmation', async () => {
      jest.spyOn(Alert, 'alert').mockImplementationOnce((title, message, buttons) => {
        const cancelButton = buttons?.find(button => button.text === 'Cancel');
        if (cancelButton?.onPress) {
          cancelButton.onPress();
        }
      });

      const { getByTestId } = render(<MockOfferDetail />);
      const withdrawButton = getByTestId('withdraw-offer-button');
      
      await act(async () => {
        fireEvent.press(withdrawButton);
      });

      expect(mockFirebaseService.updateOffer).not.toHaveBeenCalled();
      expect(mockFirebaseService.updateTimestamp).not.toHaveBeenCalled();
    });
  });

  describe('Withdrawal Process', () => {
    it('updates offer status and timestamp when withdrawal is confirmed', async () => {
      const { getByTestId } = render(<MockOfferDetail />);
      const withdrawButton = getByTestId('withdraw-offer-button');
      
      await act(async () => {
        fireEvent.press(withdrawButton);
      });

      expect(mockFirebaseService.updateOffer).toHaveBeenCalledWith(
        'test-offer-id',
        { status: 'withdrawn' }
      );
      expect(mockFirebaseService.updateTimestamp).toHaveBeenCalledWith('test-offer-id');
    });

    it('enforces message restrictions after withdrawal', async () => {
      const { getByTestId } = render(<MockOfferDetail />);
      const withdrawButton = getByTestId('withdraw-offer-button');
      
      await act(async () => {
        fireEvent.press(withdrawButton);
      });

      expect(mockFirebaseService.enforceMessageRestrictions).toHaveBeenCalledWith('test-offer-id');
    });

    it('sends notification to posting owner after withdrawal', async () => {
      const { getByTestId } = render(<MockOfferDetail />);
      const withdrawButton = getByTestId('withdraw-offer-button');
      
      await act(async () => {
        fireEvent.press(withdrawButton);
      });

      expect(mockNotificationService.sendPushNotification).toHaveBeenCalledWith(
        'posting-owner-id',
        {
          title: 'Offer Withdrawn',
          body: 'A user has withdrawn their offer on your posting',
        }
      );
    });

    it('handles notification failure gracefully', async () => {
      mockNotificationService.sendPushNotification.mockRejectedValueOnce(new Error('Failed to send notification'));
      
      const { getByTestId } = render(<MockOfferDetail />);
      const withdrawButton = getByTestId('withdraw-offer-button');
      
      await act(async () => {
        fireEvent.press(withdrawButton);
      });

      expect(mockFirebaseService.updateOffer).toHaveBeenCalled();
      expect(mockFirebaseService.updateTimestamp).toHaveBeenCalled();
    });
  });

  describe('Error Handling', () => {
    it('shows error message when withdrawal fails', async () => {
      mockFirebaseService.updateOffer.mockRejectedValueOnce(new Error('Update failed'));
      
      const { getByTestId } = render(<MockOfferDetail />);
      const withdrawButton = getByTestId('withdraw-offer-button');
      
      await act(async () => {
        fireEvent.press(withdrawButton);
      });

      await waitFor(() => {
        expect(getByTestId('error-message')).toBeTruthy();
      });
    });

    it('resets UI state when withdrawal fails', async () => {
      mockFirebaseService.updateOffer.mockRejectedValueOnce(new Error('Update failed'));
      
      const { getByTestId, queryByTestId } = render(<MockOfferDetail />);
      const withdrawButton = getByTestId('withdraw-offer-button');
      
      await act(async () => {
        fireEvent.press(withdrawButton);
      });

      await waitFor(() => {
        expect(queryByTestId('loading-indicator')).toBeNull();
      });
    });
  });
}); 