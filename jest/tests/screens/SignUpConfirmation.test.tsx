import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { act } from '@testing-library/react-native';
import { Alert } from 'react-native';
import SignUpConfirmation from '@/screens/SignUpConfirmation';

// Mock navigation
const mockNavigate = jest.fn();
jest.mock('@react-navigation/native', () => ({
  useNavigation: () => ({
    navigate: mockNavigate,
  }),
  useRoute: () => ({
    params: {
      confirmationCode: '123456',
    },
  }),
}));

// Mock Alert
jest.spyOn(Alert, 'alert');

describe('SignUpConfirmation Screen', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders correctly', () => {
    const { getByText, getByPlaceholderText } = render(<SignUpConfirmation />);
    
    expect(getByText('Enter Confirmation Code')).toBeTruthy();
    expect(getByPlaceholderText('Enter confirmation code')).toBeTruthy();
    expect(getByText('Confirm')).toBeTruthy();
  });

  it('handles successful confirmation', async () => {
    const { getByPlaceholderText, getByText } = render(<SignUpConfirmation />);
    
    const codeInput = getByPlaceholderText('Enter confirmation code');
    const confirmButton = getByText('Confirm');

    fireEvent.changeText(codeInput, '123456');
    
    await act(async () => {
      fireEvent.press(confirmButton);
      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('Home');
      });
    });
  });

  it('handles invalid confirmation code', async () => {
    const { getByPlaceholderText, getByText } = render(<SignUpConfirmation />);
    
    const codeInput = getByPlaceholderText('Enter confirmation code');
    const confirmButton = getByText('Confirm');

    fireEvent.changeText(codeInput, '654321');
    
    await act(async () => {
      fireEvent.press(confirmButton);
      await waitFor(() => {
        expect(Alert.alert).toHaveBeenCalledWith(
          'Error',
          'Invalid confirmation code. Please try again.'
        );
        expect(mockNavigate).not.toHaveBeenCalled();
      });
    });
  });

  it('handles empty confirmation code', async () => {
    const { getByPlaceholderText, getByText } = render(<SignUpConfirmation />);
    
    const codeInput = getByPlaceholderText('Enter confirmation code');
    const confirmButton = getByText('Confirm');

    fireEvent.changeText(codeInput, '');
    
    await act(async () => {
      fireEvent.press(confirmButton);
      await waitFor(() => {
        expect(Alert.alert).toHaveBeenCalledWith(
          'Error',
          'Invalid confirmation code. Please try again.'
        );
        expect(mockNavigate).not.toHaveBeenCalled();
      });
    });
  });
}); 