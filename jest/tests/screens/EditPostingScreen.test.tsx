import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { Alert } from 'react-native';
import EditPostingScreen from '../../../screens/EditPostingScreen';
import { useNavigation } from '@react-navigation/native';
import usePostingDetails from '../../../hooks/usePostingDetails';
import { updatePostingDetails } from '../../../services/firebaseService';
import { mockUpdatePostingDetails } from '../../mocks/firebase/postingService';

// Mock dependencies
jest.mock('@react-navigation/native', () => ({
  useNavigation: jest.fn(),
}));

jest.mock('../../../hooks/usePostingDetails', () => ({
  __esModule: true,
  default: jest.fn(),
}));

jest.mock('../../../services/firebaseService', () => ({
  updatePostingDetails: jest.fn(),
}));

jest.mock('react-native-maps', () => {
  const { View } = require('react-native');
  const MockMapView = (props: any) => {
    return <View testID="mock-map-view" {...props} />;
  };
  const MockMarker = (props: any) => {
    return <View testID="mock-marker" {...props} />;
  };
  return {
    __esModule: true,
    default: MockMapView,
    Marker: MockMarker,
  };
});

// Mock Alert
jest.spyOn(Alert, 'alert');

// Mock console.error
const originalConsoleError = console.error;
beforeAll(() => {
  console.error = jest.fn();
});

afterAll(() => {
  console.error = originalConsoleError;
});

describe('EditPostingScreen', () => {
  const mockNavigation = {
    navigate: jest.fn(),
  };

  const mockRoute = {
    params: {
      postingId: 'test-posting-id',
    },
  };

  const mockPostingDetails = {
    title: 'Test Item',
    description: 'Test Description',
    latitude: 37.7749,
    longitude: -122.4194,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useNavigation as jest.Mock).mockReturnValue(mockNavigation);
    (usePostingDetails as jest.Mock).mockReturnValue({
      postingDetails: mockPostingDetails,
      loading: false,
      error: null,
    });
    (updatePostingDetails as jest.Mock).mockImplementation(mockUpdatePostingDetails);
  });

  it('renders loading state correctly', () => {
    (usePostingDetails as jest.Mock).mockReturnValue({
      postingDetails: null,
      loading: true,
      error: null,
    });

    const { getByTestId } = render(<EditPostingScreen route={mockRoute} />);
    expect(getByTestId('loading-indicator')).toBeTruthy();
  });

  it('renders error state correctly', () => {
    const errorMessage = 'Failed to load posting details';
    (usePostingDetails as jest.Mock).mockReturnValue({
      postingDetails: null,
      loading: false,
      error: errorMessage,
    });

    const { getByText } = render(<EditPostingScreen route={mockRoute} />);
    expect(getByText(`Error: ${errorMessage}`)).toBeTruthy();
  });

  it('renders posting details correctly', () => {
    const { getByTestId, getByDisplayValue } = render(
      <EditPostingScreen route={mockRoute} />
    );

    expect(getByDisplayValue(mockPostingDetails.title)).toBeTruthy();
    expect(getByDisplayValue(mockPostingDetails.description)).toBeTruthy();
    expect(getByTestId('mock-map-view')).toBeTruthy();
    expect(getByTestId('mock-marker')).toBeTruthy();
  });

  it('validates empty title', async () => {
    const { getByDisplayValue, getByText } = render(
      <EditPostingScreen route={mockRoute} />
    );

    const titleInput = getByDisplayValue(mockPostingDetails.title);
    fireEvent.changeText(titleInput, '');

    const saveButton = getByText('Save Changes');
    fireEvent.press(saveButton);

    expect(Alert.alert).toHaveBeenCalledWith(
      'Validation Error',
      'Title cannot be empty.'
    );
    expect(updatePostingDetails).not.toHaveBeenCalled();
  });

  it('validates empty description', async () => {
    const { getByDisplayValue, getByText } = render(
      <EditPostingScreen route={mockRoute} />
    );

    const descriptionInput = getByDisplayValue(mockPostingDetails.description);
    fireEvent.changeText(descriptionInput, '');

    const saveButton = getByText('Save Changes');
    fireEvent.press(saveButton);

    expect(Alert.alert).toHaveBeenCalledWith(
      'Validation Error',
      'Description cannot be empty.'
    );
    expect(updatePostingDetails).not.toHaveBeenCalled();
  });

  it('handles successful update', async () => {
    const { getByText } = render(<EditPostingScreen route={mockRoute} />);

    const saveButton = getByText('Save Changes');
    fireEvent.press(saveButton);

    await waitFor(() => {
      expect(updatePostingDetails).toHaveBeenCalledWith('test-posting-id', {
        title: mockPostingDetails.title,
        description: mockPostingDetails.description,
        latitude: mockPostingDetails.latitude,
        longitude: mockPostingDetails.longitude,
      });
    });

    expect(Alert.alert).toHaveBeenCalledWith(
      'Success',
      'Posting updated successfully!',
      [{ text: 'OK', onPress: expect.any(Function) }]
    );
  });

  it('handles update failure', async () => {
    const errorMessage = 'Failed to update posting';
    (updatePostingDetails as jest.Mock).mockRejectedValueOnce(new Error(errorMessage));

    const { getByText } = render(<EditPostingScreen route={mockRoute} />);

    const saveButton = getByText('Save Changes');
    fireEvent.press(saveButton);

    await waitFor(() => {
      expect(updatePostingDetails).toHaveBeenCalled();
      expect(console.error).toHaveBeenCalledWith('Error updating posting:', expect.any(Error));
    });
  });

  it('handles map location update', () => {
    const { getByTestId } = render(<EditPostingScreen route={mockRoute} />);

    const mapView = getByTestId('mock-map-view');
    const newLocation = {
      nativeEvent: {
        coordinate: {
          latitude: 37.7833,
          longitude: -122.4167,
          latitudeDelta: 0.05,
          longitudeDelta: 0.05,
        },
      },
    };

    fireEvent(mapView, 'press', newLocation);

    const marker = getByTestId('mock-marker');
    expect(marker.props.coordinate).toEqual(newLocation.nativeEvent.coordinate);
  });
}); 