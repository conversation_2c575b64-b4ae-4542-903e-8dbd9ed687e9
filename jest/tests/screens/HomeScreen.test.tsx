import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { act } from '@testing-library/react-native';
import { Alert, Keyboard, Dimensions, Platform, View, TextInput, NativeScrollEvent, NativeSyntheticEvent } from 'react-native';
import HomeScreen from '@/screens/HomeScreen';
import { Posting } from '@/types/posting';
import { mockPostings } from '../../mocks/mockPostings';

// Constants for testing
const TEST_REGION = {
  latitude: 37.78825,
  longitude: -122.4324,
  latitudeDelta: 0.0922,
  longitudeDelta: 0.0421,
};

const TEST_SEARCH_TERM = 'test search';

const TEST_SCROLL_EVENT: NativeSyntheticEvent<NativeScrollEvent> = {
  nativeEvent: {
    contentOffset: { x: 0, y: 100 },
    contentSize: { height: 1000, width: 390 },
    layoutMeasurement: { height: 800, width: 390 },
    velocity: { y: 2 },
  },
} as NativeSyntheticEvent<NativeScrollEvent>;

// Mock setup
const mockNavigate = jest.fn();
const mockHandleLoadMore = jest.fn();
const mockSetPostings = jest.fn();
const mockFetchPostingsBySearch = jest.fn().mockResolvedValue({ postings: [] });

// Mock console methods
const originalConsole = { ...console };
beforeAll(() => {
  console.log = jest.fn();
  console.error = jest.fn();
  console.warn = jest.fn();
  console.info = jest.fn();
  console.debug = jest.fn();
});

afterAll(() => {
  console.log = originalConsole.log;
  console.error = originalConsole.error;
  console.warn = originalConsole.warn;
  console.info = originalConsole.info;
  console.debug = originalConsole.debug;
});

// Navigation mock
jest.mock('@react-navigation/native', () => ({
  useNavigation: () => ({
    navigate: mockNavigate,
  }),
  useRoute: () => ({
    params: {},
  }),
}));

// Firebase subscriptions mock
jest.mock('@/hooks/useFirebaseSubscriptions', () => ({
  useFirebaseSubscriptions: () => ({
    postings: mockPostings,
    handleLoadMore: mockHandleLoadMore,
    setPostings: mockSetPostings,
  }),
}));

// Firebase service mock
jest.mock('@/services/firebaseService', () => ({
  fetchPostingsBySearch: (...args: unknown[]) => mockFetchPostingsBySearch(...args),
}));

// Component mocks
jest.mock('react-native-maps', () => {
  const { View } = require('react-native');
  const MockMapView = (props: Record<string, unknown>) => {
    return <View testID="mock-map-view" {...props} />;
  };
  MockMapView.Marker = (props: Record<string, unknown>) => <View testID="mock-marker" {...props} />;
  return {
    __esModule: true,
    default: MockMapView,
  };
});

jest.mock('react-native-safe-area-context', () => ({
  useSafeAreaInsets: () => ({
    top: 47,
    right: 0,
    bottom: 34,
    left: 0,
  }),
}));

jest.mock('@/components/DraggableListContainer', () => {
  const { View } = require('react-native');
  return {
    DraggableListContainer: ({ children }: { children: React.ReactNode }) => (
      <View testID="draggable-list-container">{children}</View>
    ),
  };
});

jest.mock('@/components/ListItem', () => {
  const { View } = require('react-native');
  return {
    __esModule: true,
    default: ({ posting }: { posting: Posting }) => <View testID={`list-item-${posting.id}`} />,
  };
});

jest.mock('@/components/MapMarker', () => {
  const { TouchableOpacity } = require('react-native');
  return {
    __esModule: true,
    default: ({ identifier, onCalloutPress }: { identifier: string; onCalloutPress: () => void }) => (
      <TouchableOpacity testID={`map-marker-${identifier}`} onPress={onCalloutPress} />
    ),
  };
});

jest.mock('@/components/SearchInput', () => {
  const { View, TextInput, TouchableOpacity, Text } = require('react-native');
  return {
    __esModule: true,
    default: ({ onChangeText, value }: { onChangeText: (text: string) => void; value: string }) => (
      <View>
        <TextInput
          testID="search-input"
          onChangeText={onChangeText}
          value={value}
          placeholder="Search..."
          onSubmitEditing={() => onChangeText(value)}
        />
      </View>
    ),
  };
});

jest.mock('@/components/NotificationBell', () => {
  const { View } = require('react-native');
  return {
    __esModule: true,
    default: () => <View testID="notification-bell" />,
  };
});

// Mock dimensions
jest.spyOn(Dimensions, 'get').mockReturnValue({
  width: 390,
  height: 844,
  scale: 3,
  fontScale: 1,
});

// Mock Platform
Platform.OS = 'ios';

// Remove the react-native mock
jest.mock('react-native/Libraries/Lists/FlatList', () => {
  const { View, TouchableOpacity } = require('react-native');
  return function MockFlatList({ data, renderItem, onEndReached }: any) {
    return (
      <View testID="flat-list">
        {data.map((item: any, index: number) => (
          <View key={index}>
            {renderItem({ item, index })}
          </View>
        ))}
        <TouchableOpacity
          testID="end-reached-trigger"
          onPress={() => onEndReached?.({ distanceFromEnd: 0 })}
        />
      </View>
    );
  };
});

// Mock lodash debounce to execute immediately
jest.mock('lodash', () => ({
  ...jest.requireActual('lodash'),
  debounce: (fn: Function) => {
    const debounced = fn as Function & { cancel: () => void };
    debounced.cancel = () => {};
    return debounced;
  },
}));

describe('HomeScreen', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockFetchPostingsBySearch.mockResolvedValue({ postings: [] });
  });

  // Helper function to render HomeScreen with common assertions
  const renderHomeScreen = () => {
    const renderResult = render(<HomeScreen />);
    const { getByTestId } = renderResult;

    // Common elements that should always be present
    expect(getByTestId('mock-map-view')).toBeTruthy();
    expect(getByTestId('search-input')).toBeTruthy();
    expect(getByTestId('notification-bell')).toBeTruthy();

    return renderResult;
  };

  it('renders correctly with initial state', () => {
    renderHomeScreen();
  });

  it('handles initial search on mount', async () => {
    renderHomeScreen();
    
    await waitFor(() => {
      expect(mockFetchPostingsBySearch).toHaveBeenCalledWith(
        '',
        null,
        expect.objectContaining({
          east: expect.any(Number),
          north: expect.any(Number),
          south: expect.any(Number),
          west: expect.any(Number),
        }),
        10,
        false
      );
    });
  });

  it('updates search term when input changes', async () => {
    const { getByTestId } = renderHomeScreen();
    const searchInput = getByTestId('search-input');

    await act(async () => {
      fireEvent.changeText(searchInput, TEST_SEARCH_TERM);
    });

    expect(searchInput.props.value).toBe(TEST_SEARCH_TERM);
  });

  it('loads more postings when scrolling', async () => {
    const { getByTestId } = renderHomeScreen();
    const endReachedTrigger = getByTestId('end-reached-trigger');

    await act(async () => {
      fireEvent.press(endReachedTrigger);
    });

    expect(mockHandleLoadMore).toHaveBeenCalled();
  });

  it('navigates to posting details on marker callout press', async () => {
    const { getByTestId } = renderHomeScreen();
    const mockPosting = mockPostings[0];
    const marker = getByTestId(`map-marker-${mockPosting.id}`);

    await act(async () => {
      fireEvent.press(marker);
    });

    expect(mockNavigate).toHaveBeenCalledWith('PostingDetail', { 
      postingId: mockPosting.id,
      userId: mockPosting.userId
    });
  });

  // Clean up mocks after all tests
  afterAll(() => {
    jest.restoreAllMocks();
  });
}); 