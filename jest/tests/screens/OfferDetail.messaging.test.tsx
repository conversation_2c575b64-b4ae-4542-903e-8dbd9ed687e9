import React from 'react';
import { render, fireEvent, act, waitFor } from '@testing-library/react-native';
import { View, Text, TouchableOpacity, TextInput } from 'react-native';
import OfferDetail from '../../../screens/OfferDetail';
import { createMockNavigation } from '../../mocks/offerDetail/mockNavigation';
import { createMockRoute } from '../../mocks/offerDetail/mockRoute';
import { mockAuth, mockFirebaseService, resetAllMocks } from '../../mocks/offerDetail/mockServices';
import { NavigationProp } from '@react-navigation/native';
import { RootStackParamList } from '../../mocks/offerDetail/mockRoute';

// Mock Firebase
jest.mock('firebase/firestore', () => ({
  getFirestore: jest.fn(),
  collection: jest.fn(),
  query: jest.fn(),
  where: jest.fn(),
  orderBy: jest.fn(),
  limit: jest.fn(),
  onSnapshot: jest.fn(),
}));

jest.mock('../../../firebase', () => ({
  auth: mockAuth,
  db: {}
}));

// Mock Firebase Service
jest.mock('../../../services/firebaseService', () => mockFirebaseService);

interface MockOfferDetailProps {
  navigation: NavigationProp<RootStackParamList, 'OfferDetail'>;
  route: ReturnType<typeof createMockRoute>;
}

interface Message {
  id: string;
  text: string;
  senderId: string;
}

// Mock OfferDetail component for testing
const MockOfferDetail: React.FC<MockOfferDetailProps> = ({ navigation, route }) => {
  const [newMessage, setNewMessage] = React.useState('');
  const [error, setError] = React.useState<string | null>(null);
  const [messages, setMessages] = React.useState<Message[]>([]);

  // Simulate message subscription
  React.useEffect(() => {
    let isMounted = true;
    const unsubscribe = mockFirebaseService.subscribeToMessages(route.params.offerId);
    
    const loadMessages = async () => {
      try {
        const msgs = await mockFirebaseService.getMessages(route.params.offerId);
        if (isMounted) {
          setMessages(msgs || []);
        }
      } catch (error) {
        if (isMounted) {
          setError('Failed to load messages');
        }
      }
    };

    loadMessages();

    return () => {
      isMounted = false;
      if (typeof unsubscribe === 'function') {
        unsubscribe();
      }
    };
  }, [route.params.offerId]);

  const handleSendMessage = async () => {
    if (!newMessage.trim()) {
      setError('Message cannot be empty');
      return;
    }

    try {
      await mockFirebaseService.addMessageToDiscussion({
        offerId: route.params.offerId,
        message: newMessage,
      });
      setNewMessage('');
      setError(null);
      const updatedMessages = await mockFirebaseService.getMessages(route.params.offerId);
      setMessages(updatedMessages || []);
    } catch (err) {
      setError('Failed to send message');
    }
  };

  return (
    <View testID="offer-details-container">
      {route.params.initialOffer.status !== 'withdrawn' ? (
        <View testID="message-input-section">
          <TextInput
            testID="message-input"
            value={newMessage}
            onChangeText={setNewMessage}
            placeholder="Type a message..."
          />
          <TouchableOpacity
            testID="send-message-button"
            onPress={handleSendMessage}
          >
            <Text>Send</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <View testID="withdrawn-message">
          <Text>Messages cannot be sent in withdrawn offers</Text>
        </View>
      )}

      {error && (
        <View testID="error-container">
          <Text testID="error-text">{error}</Text>
        </View>
      )}

      <View testID="message-list">
        {messages.map(message => (
          <View key={message.id} testID={`message-${message.id}`}>
            <Text>{message.text}</Text>
          </View>
        ))}
      </View>
    </View>
  );
};

jest.mock('../../../screens/OfferDetail', () => MockOfferDetail);

describe('OfferDetail Messaging Tests', () => {
  let cleanup: () => void;

  beforeEach(() => {
    resetAllMocks();
    jest.useFakeTimers();
  });

  afterEach(() => {
    if (cleanup) {
      cleanup();
    }
    jest.clearAllTimers();
    jest.useRealTimers();
  });

  describe('Message Input Handling', () => {
    it('renders message input section for active offers', async () => {
      const { getByTestId, unmount } = render(
        <MockOfferDetail 
          navigation={createMockNavigation()} 
          route={createMockRoute()} 
        />
      );
      cleanup = unmount;

      await waitFor(() => {
        expect(getByTestId('message-input-section')).toBeTruthy();
      });
    });

    it('hides message input for withdrawn offers', async () => {
      const { queryByTestId, getByTestId, unmount } = render(
        <MockOfferDetail 
          navigation={createMockNavigation()} 
          route={createMockRoute({
            initialOffer: {
              id: 'test-offer-id',
              price: '100',
              description: 'Test Description',
              status: 'withdrawn',
              ownerId: 'test-user-id'
            }
          })} 
        />
      );
      cleanup = unmount;

      await waitFor(() => {
        expect(queryByTestId('message-input-section')).toBeNull();
        expect(getByTestId('withdrawn-message')).toBeTruthy();
      });
    });

    it('updates input value on change', async () => {
      const { getByTestId, unmount } = render(
        <MockOfferDetail 
          navigation={createMockNavigation()} 
          route={createMockRoute()} 
        />
      );
      cleanup = unmount;

      const input = getByTestId('message-input');
      await act(async () => {
        fireEvent.changeText(input, 'Test message');
      });

      expect(input.props.value).toBe('Test message');
    });
  });

  describe('Message Sending', () => {
    it('handles empty message input', async () => {
      const { getByTestId, findByTestId, unmount } = render(
        <MockOfferDetail 
          navigation={createMockNavigation()} 
          route={createMockRoute()} 
        />
      );
      cleanup = unmount;

      await act(async () => {
        fireEvent.press(getByTestId('send-message-button'));
      });

      const errorText = await findByTestId('error-text');
      expect(errorText).toHaveTextContent('Message cannot be empty');
      expect(mockFirebaseService.addMessageToDiscussion).not.toHaveBeenCalled();
    });

    it('successfully sends a message', async () => {
      const mockMessages = [{ id: '1', text: 'Test message', senderId: 'test-user-id' }];
      mockFirebaseService.getMessages.mockImplementation(() => Promise.resolve(mockMessages));

      const { getByTestId, unmount } = render(
        <MockOfferDetail 
          navigation={createMockNavigation()} 
          route={createMockRoute()} 
        />
      );
      cleanup = unmount;

      await act(async () => {
        fireEvent.changeText(getByTestId('message-input'), 'Test message');
      });

      await act(async () => {
        fireEvent.press(getByTestId('send-message-button'));
      });

      expect(mockFirebaseService.addMessageToDiscussion).toHaveBeenCalledWith({
        offerId: 'test-offer-id',
        message: 'Test message'
      });
    });

    it('handles message sending error', async () => {
      mockFirebaseService.addMessageToDiscussion.mockRejectedValueOnce(new Error('Failed to send'));

      const { getByTestId, findByTestId, unmount } = render(
        <MockOfferDetail 
          navigation={createMockNavigation()} 
          route={createMockRoute()} 
        />
      );
      cleanup = unmount;

      await act(async () => {
        fireEvent.changeText(getByTestId('message-input'), 'Test message');
      });

      await act(async () => {
        fireEvent.press(getByTestId('send-message-button'));
      });

      const errorText = await findByTestId('error-text');
      expect(errorText).toHaveTextContent('Failed to send message');
    });
  });

  describe('Message List', () => {
    it('displays messages from Firebase', async () => {
      const mockMessages = [
        { id: '1', text: 'Message 1', senderId: 'user1' },
        { id: '2', text: 'Message 2', senderId: 'user2' }
      ];
      mockFirebaseService.getMessages.mockImplementation(() => Promise.resolve(mockMessages));

      const { getByTestId, unmount } = render(
        <MockOfferDetail 
          navigation={createMockNavigation()} 
          route={createMockRoute()} 
        />
      );
      cleanup = unmount;

      await waitFor(() => {
        mockMessages.forEach(msg => {
          expect(getByTestId(`message-${msg.id}`)).toBeTruthy();
        });
      });
    });
  });
}); 