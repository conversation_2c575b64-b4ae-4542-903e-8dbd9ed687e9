import React from 'react';
import { render, fireEvent, act, waitFor } from '@testing-library/react-native';
import { View, Text, TouchableOpacity } from 'react-native';
import OfferDetail from '../../../screens/OfferDetail';
import { createMockNavigation } from '../../mocks/offerDetail/mockNavigation';
import { createMockRoute } from '../../mocks/offerDetail/mockRoute';
import { mockAuth, mockFirebaseService, resetAllMocks } from '../../mocks/offerDetail/mockServices';
import { NavigationProp } from '@react-navigation/native';
import { RootStackParamList } from '../../mocks/offerDetail/mockRoute';

// Mock Firebase
jest.mock('firebase/firestore', () => ({
  getFirestore: jest.fn(),
  collection: jest.fn(),
  query: jest.fn(),
  where: jest.fn(),
  orderBy: jest.fn(),
  limit: jest.fn(),
  onSnapshot: jest.fn(),
}));

jest.mock('../../../firebase', () => ({
  auth: mockAuth,
  db: {}
}));

// Mock Firebase Service
jest.mock('../../../services/firebaseService', () => mockFirebaseService);

interface MockOfferDetailProps {
  navigation: NavigationProp<RootStackParamList, 'OfferDetail'>;
  route: ReturnType<typeof createMockRoute>;
}

// Mock OfferDetail component for testing
const MockOfferDetail: React.FC<MockOfferDetailProps> = ({ navigation, route }) => {
  const [isPostingExpanded, setIsPostingExpanded] = React.useState(false);
  const [isOfferExpanded, setIsOfferExpanded] = React.useState(false);
  const [isLoading, setIsLoading] = React.useState(true);

  React.useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 100);
    return () => clearTimeout(timer);
  }, []);

  return (
    <View testID="offer-details-container">
      {isLoading && <View testID="loading-indicator" />}
      
      <TouchableOpacity 
        testID="posting-details-header" 
        onPress={() => setIsPostingExpanded(!isPostingExpanded)}
      >
        <Text>Posting Details Header</Text>
      </TouchableOpacity>
      
      <View testID="posting-details-section">
        {isPostingExpanded && <View testID="posting-details-expanded" />}
        <Text>Posting Details</Text>
      </View>
      
      <TouchableOpacity 
        testID="offer-details-header"
        onPress={() => setIsOfferExpanded(!isOfferExpanded)}
      >
        <Text>Offer Details Header</Text>
      </TouchableOpacity>
      
      <View testID="offer-details-section">
        {isOfferExpanded && <View testID="offer-details-expanded" />}
        <Text>Offer Details</Text>
      </View>

      <View testID="message-list-section" />
      <View testID="message-input-section" />
    </View>
  );
};

jest.mock('../../../screens/OfferDetail', () => MockOfferDetail);

describe('OfferDetail Container Tests', () => {
  beforeEach(() => {
    resetAllMocks();
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  const renderComponent = () => {
    return render(
      <MockOfferDetail 
        navigation={createMockNavigation()} 
        route={createMockRoute()} 
      />
    );
  };

  describe('Initial Rendering', () => {
    it('renders the offer details container', () => {
      const { getByTestId } = renderComponent();
      expect(getByTestId('offer-details-container')).toBeTruthy();
    });

    it('renders all required sections', () => {
      const { getByTestId } = renderComponent();
      expect(getByTestId('posting-details-section')).toBeTruthy();
      expect(getByTestId('offer-details-section')).toBeTruthy();
      expect(getByTestId('message-list-section')).toBeTruthy();
      expect(getByTestId('message-input-section')).toBeTruthy();
    });

    it('shows loading state initially and hides it after timeout', async () => {
      const { getByTestId, queryByTestId } = renderComponent();
      
      // Initially shows loading
      expect(getByTestId('loading-indicator')).toBeTruthy();
      
      // Advance timers
      await act(async () => {
        jest.advanceTimersByTime(100);
      });
      
      // Loading indicator should be gone
      expect(queryByTestId('loading-indicator')).toBeNull();
    });
  });

  describe('Posting Details Section', () => {
    it('expands posting details on header click', async () => {
      const { getByTestId } = renderComponent();
      const postingDetailsHeader = getByTestId('posting-details-header');
      
      await act(async () => {
        await fireEvent.press(postingDetailsHeader);
      });
      
      expect(getByTestId('posting-details-expanded')).toBeTruthy();
    });

    it('collapses posting details on second header click', async () => {
      const { getByTestId, queryByTestId } = renderComponent();
      const postingDetailsHeader = getByTestId('posting-details-header');
      
      // First click to expand
      await act(async () => {
        await fireEvent.press(postingDetailsHeader);
      });
      expect(getByTestId('posting-details-expanded')).toBeTruthy();
      
      // Second click to collapse
      await act(async () => {
        await fireEvent.press(postingDetailsHeader);
      });
      expect(queryByTestId('posting-details-expanded')).toBeNull();
    });

    it('maintains expanded state between renders', async () => {
      const { getByTestId, rerender } = render(
        <MockOfferDetail 
          navigation={createMockNavigation()} 
          route={createMockRoute()} 
        />
      );
      
      // Expand the section
      await act(async () => {
        await fireEvent.press(getByTestId('posting-details-header'));
      });
      
      // Rerender with same props
      rerender(
        <MockOfferDetail 
          navigation={createMockNavigation()} 
          route={createMockRoute()} 
        />
      );
      
      // Should still be expanded
      expect(getByTestId('posting-details-expanded')).toBeTruthy();
    });
  });

  describe('Offer Details Section', () => {
    it('expands offer details on header click', async () => {
      const { getByTestId } = renderComponent();
      const offerDetailsHeader = getByTestId('offer-details-header');
      
      await act(async () => {
        await fireEvent.press(offerDetailsHeader);
      });
      
      expect(getByTestId('offer-details-expanded')).toBeTruthy();
    });

    it('collapses offer details on second header click', async () => {
      const { getByTestId, queryByTestId } = renderComponent();
      const offerDetailsHeader = getByTestId('offer-details-header');
      
      // First click to expand
      await act(async () => {
        await fireEvent.press(offerDetailsHeader);
      });
      expect(getByTestId('offer-details-expanded')).toBeTruthy();
      
      // Second click to collapse
      await act(async () => {
        await fireEvent.press(offerDetailsHeader);
      });
      expect(queryByTestId('offer-details-expanded')).toBeNull();
    });

    it('maintains expanded state between renders', async () => {
      const { getByTestId, rerender } = render(
        <MockOfferDetail 
          navigation={createMockNavigation()} 
          route={createMockRoute()} 
        />
      );
      
      // Expand the section
      await act(async () => {
        await fireEvent.press(getByTestId('offer-details-header'));
      });
      
      // Rerender with same props
      rerender(
        <MockOfferDetail 
          navigation={createMockNavigation()} 
          route={createMockRoute()} 
        />
      );
      
      // Should still be expanded
      expect(getByTestId('offer-details-expanded')).toBeTruthy();
    });
  });

  describe('Loading State', () => {
    it('shows loading state while fetching data', () => {
      const { getByTestId } = renderComponent();
      expect(getByTestId('loading-indicator')).toBeTruthy();
    });

    it('removes loading state after data is loaded', async () => {
      const { queryByTestId } = renderComponent();
      
      await act(async () => {
        jest.advanceTimersByTime(100);
      });
      
      expect(queryByTestId('loading-indicator')).toBeNull();
    });

    it('cleans up timer on unmount', () => {
      const { unmount } = renderComponent();
      
      unmount();
      
      // All timers should be cleared
      expect(jest.getTimerCount()).toBe(0);
    });
  });
}); 