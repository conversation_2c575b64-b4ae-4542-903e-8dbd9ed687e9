/**
 * MyPostingsScreen.test.tsx
 * 
 * Test suite for the My Postings screen component.
 * Tests cover rendering, data fetching, navigation, and empty state handling.
 */

import React from 'react';
import { render, fireEvent, act } from '@testing-library/react-native';
import MyPostingsScreen from '../../../screens/MyPostingsScreen';
import { useFetchUserPostings } from '../../../hooks/useFetchUserPostings';

// Mock navigation
const mockNavigate = jest.fn();
jest.mock('@react-navigation/native', () => ({
  useNavigation: () => ({
    navigate: mockNavigate,
  }),
}));

// Mock custom hook
jest.mock('../../../hooks/useFetchUserPostings');

// Mock data
const mockPostings = [
  {
    id: '1',
    title: 'Test Posting 1',
    description: 'Description 1',
    latitude: 40.7128,
    longitude: -74.0060,
    userId: 'user123',
  },
  {
    id: '2',
    title: 'Test Posting 2',
    description: 'Description 2',
    latitude: 41.8781,
    longitude: -87.6298,
    userId: 'user123',
  },
];

describe('MyPostingsScreen', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (useFetchUserPostings as jest.Mock).mockReturnValue([]);
  });

  describe('Rendering Tests', () => {
    it('renders the header correctly', () => {
      const { getByText } = render(<MyPostingsScreen />);
      expect(getByText('My Postings')).toBeTruthy();
    });

    it('renders empty state message when no postings exist', () => {
      const { getByText } = render(<MyPostingsScreen />);
      expect(getByText('You have no postings yet.')).toBeTruthy();
    });

    it('renders list of postings when data exists', () => {
      (useFetchUserPostings as jest.Mock).mockReturnValue(mockPostings);
      const { getByText, getAllByTestId } = render(<MyPostingsScreen />);
      
      const listItems = getAllByTestId('posting-list-item');
      expect(listItems).toHaveLength(mockPostings.length);
      expect(getByText('Test Posting 1')).toBeTruthy();
      expect(getByText('Description 1')).toBeTruthy();
    });

    it('handles missing title and description gracefully', () => {
      const incompletePosting = [{
        id: '3',
        latitude: 40.7128,
        longitude: -74.0060,
        userId: 'user123',
      }];
      (useFetchUserPostings as jest.Mock).mockReturnValue(incompletePosting);
      
      const { getByText } = render(<MyPostingsScreen />);
      expect(getByText('Untitled')).toBeTruthy();
      expect(getByText('No description available')).toBeTruthy();
    });
  });

  describe('Navigation Tests', () => {
    it('navigates to PostingDetail when pressing a posting', async () => {
      (useFetchUserPostings as jest.Mock).mockReturnValue(mockPostings);
      const { getAllByTestId } = render(<MyPostingsScreen />);
      
      const listItems = getAllByTestId('posting-list-item');
      await act(async () => {
        fireEvent.press(listItems[0]);
      });

      expect(mockNavigate).toHaveBeenCalledWith('PostingDetail', {
        postingId: mockPostings[0].id,
        itemName: mockPostings[0].title,
        itemDescription: mockPostings[0].description,
        itemLocation: {
          latitude: mockPostings[0].latitude,
          longitude: mockPostings[0].longitude,
        },
        userId: mockPostings[0].userId,
      });
    });

    it('navigates with default values for missing data', async () => {
      const incompletePosting = [{
        id: '3',
        latitude: 40.7128,
        longitude: -74.0060,
        userId: 'user123',
      }];
      (useFetchUserPostings as jest.Mock).mockReturnValue(incompletePosting);
      
      const { getAllByTestId } = render(<MyPostingsScreen />);
      const listItems = getAllByTestId('posting-list-item');
      
      await act(async () => {
        fireEvent.press(listItems[0]);
      });

      expect(mockNavigate).toHaveBeenCalledWith('PostingDetail', {
        postingId: incompletePosting[0].id,
        itemName: 'Untitled',
        itemDescription: 'No description available',
        itemLocation: {
          latitude: incompletePosting[0].latitude,
          longitude: incompletePosting[0].longitude,
        },
        userId: incompletePosting[0].userId,
      });
    });
  });

  describe('Data Fetching Tests', () => {
    it('calls useFetchUserPostings hook on render', () => {
      render(<MyPostingsScreen />);
      expect(useFetchUserPostings).toHaveBeenCalled();
    });

    it('updates UI when postings data changes', () => {
      const { rerender, queryByText } = render(<MyPostingsScreen />);
      expect(queryByText('Test Posting 1')).toBeFalsy();

      (useFetchUserPostings as jest.Mock).mockReturnValue(mockPostings);
      rerender(<MyPostingsScreen />);
      expect(queryByText('Test Posting 1')).toBeTruthy();
    });
  });

  describe('Accessibility Tests', () => {
    it('has accessible labels for posting items', () => {
      (useFetchUserPostings as jest.Mock).mockReturnValue(mockPostings);
      const { getAllByTestId } = render(<MyPostingsScreen />);
      
      const listItems = getAllByTestId('posting-list-item');
      expect(listItems[0].props.accessibilityLabel).toBe('Open details for Test Posting 1');
    });
  });
}); 