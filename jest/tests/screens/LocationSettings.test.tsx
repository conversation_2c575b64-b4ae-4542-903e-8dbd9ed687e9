import React from 'react';
import { render, fireEvent, act, waitFor } from '@testing-library/react-native';
import { Alert } from 'react-native';
import LocationSettings from '../../../screens/LocationSettings';
import { saveUserLocation } from '../../../services/firebaseService';

// Mock navigation
jest.mock('@react-navigation/native', () => ({
  useNavigation: () => mockNavigation,
}));

// Mock services
jest.mock('../../../services/firebaseService', () => ({
  saveUserLocation: jest.fn(),
}));

// Mock react-native-maps
jest.mock('react-native-maps', () => {
  const { View } = require('react-native');
  const MockMapView = (props: any) => {
    return <View testID="mock-map-view" {...props} />;
  };
  const MockMarker = (props: any) => {
    return <View testID="mock-map-marker" {...props} />;
  };
  return {
    __esModule: true,
    default: MockMapView,
    Marker: MockMarker,
  };
});

// Mock Alert
jest.spyOn(Alert, 'alert');

const mockNavigation = {
  navigate: jest.fn(),
};

const mockLocation = {
  latitude: 37.7749,
  longitude: -122.4194,
};

describe('LocationSettings', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockNavigation.navigate.mockClear();
    (saveUserLocation as jest.Mock).mockClear();
  });

  it('renders correctly with initial state', () => {
    const { getByText, getByTestId, queryByTestId } = render(<LocationSettings />);
    
    // Check header text
    expect(getByText('Mark your preferred location on the map')).toBeTruthy();
    
    // Check map is rendered
    expect(getByTestId('mock-map-view')).toBeTruthy();
    
    // Check no marker is rendered initially
    expect(queryByTestId('mock-map-marker')).toBeNull();
    
    // Check buttons are rendered
    expect(getByText('Confirm Location')).toBeTruthy();
    expect(getByText('Cancel')).toBeTruthy();
  });

  it('shows marker when location is selected', async () => {
    const { getByTestId } = render(<LocationSettings />);
    
    const mapView = getByTestId('mock-map-view');
    
    await act(async () => {
      // Simulate map press event
      fireEvent(mapView, 'press', {
        nativeEvent: {
          coordinate: mockLocation,
        },
      });
    });

    // Check marker is rendered after location selection
    expect(getByTestId('mock-map-marker')).toBeTruthy();
  });

  it('shows alert when trying to confirm without selecting location', async () => {
    const { getByText } = render(<LocationSettings />);
    
    await act(async () => {
      fireEvent.press(getByText('Confirm Location'));
    });

    expect(Alert.alert).toHaveBeenCalledWith(
      'No Location Selected',
      'Please select a location on the map.'
    );
  });

  it('saves location and navigates on successful confirmation', async () => {
    (saveUserLocation as jest.Mock).mockResolvedValueOnce(undefined);
    
    const { getByTestId, getByText } = render(<LocationSettings />);
    
    // Select location
    await act(async () => {
      fireEvent(getByTestId('mock-map-view'), 'press', {
        nativeEvent: {
          coordinate: mockLocation,
        },
      });
    });

    // Confirm location
    await act(async () => {
      fireEvent.press(getByText('Confirm Location'));
    });

    await waitFor(() => {
      expect(saveUserLocation).toHaveBeenCalledWith(mockLocation);
      expect(Alert.alert).toHaveBeenCalledWith(
        'Location Saved',
        'Your location has been updated.'
      );
      expect(mockNavigation.navigate).toHaveBeenCalledWith('Home', {
        userLocation: mockLocation,
      });
    });
  });

  it('shows error alert when saving location fails', async () => {
    const mockError = new Error('Failed to save location');
    (saveUserLocation as jest.Mock).mockRejectedValueOnce(mockError);
    
    const { getByTestId, getByText } = render(<LocationSettings />);
    
    // Select location
    await act(async () => {
      fireEvent(getByTestId('mock-map-view'), 'press', {
        nativeEvent: {
          coordinate: mockLocation,
        },
      });
    });

    // Confirm location
    await act(async () => {
      fireEvent.press(getByText('Confirm Location'));
    });

    await waitFor(() => {
      expect(saveUserLocation).toHaveBeenCalledWith(mockLocation);
      expect(Alert.alert).toHaveBeenCalledWith(
        'Error',
        'Failed to save location. Please try again.'
      );
    });
  });

  it('navigates back to Home on cancel', async () => {
    const { getByText } = render(<LocationSettings />);
    
    await act(async () => {
      fireEvent.press(getByText('Cancel'));
    });

    expect(mockNavigation.navigate).toHaveBeenCalledWith('Home');
  });
}); 