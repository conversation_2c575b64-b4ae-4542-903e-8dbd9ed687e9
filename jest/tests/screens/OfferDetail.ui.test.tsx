import React from 'react';
import { render, fireEvent, act, waitFor } from '@testing-library/react-native';
import { View, Text, TouchableOpacity } from 'react-native';
import OfferDetail from '../../../screens/OfferDetail';
import { createMockNavigation } from '../../mocks/offerDetail/mockNavigation';
import { createMockRoute } from '../../mocks/offerDetail/mockRoute';
import { mockAuth, mockFirebaseService, resetAllMocks } from '../../mocks/offerDetail/mockServices';
import { NavigationProp } from '@react-navigation/native';
import { RootStackParamList } from '../../mocks/offerDetail/mockRoute';

jest.mock('firebase/firestore', () => ({
  getFirestore: jest.fn(),
  collection: jest.fn(),
  query: jest.fn(),
  where: jest.fn(),
  orderBy: jest.fn(),
  limit: jest.fn(),
  onSnapshot: jest.fn(),
}));

jest.mock('../../../firebase', () => ({
  auth: mockAuth,
  db: {}
}));

jest.mock('../../../services/firebaseService', () => mockFirebaseService);

interface MockOfferDetailProps {
  navigation: NavigationProp<RootStackParamList, 'OfferDetail'>;
  route: ReturnType<typeof createMockRoute>;
}

const MockOfferDetail: React.FC<MockOfferDetailProps> = ({ navigation, route }) => {
  const [expandedSections, setExpandedSections] = React.useState<string[]>([]);
  const isCurrentUserOffer = route.params.initialOffer.ownerId === mockAuth.currentUser.uid;

  const toggleSection = (sectionId: string) => {
    setExpandedSections(prev => 
      prev.includes(sectionId) 
        ? prev.filter(id => id !== sectionId)
        : [...prev, sectionId]
    );
  };

  return (
    <View testID="offer-details-container">
      {isCurrentUserOffer && (
        <View testID="owner-actions">
          <TouchableOpacity testID="edit-offer-button">
            <Text>Edit Offer</Text>
          </TouchableOpacity>
        </View>
      )}

      <TouchableOpacity 
        testID="toggle-details-button"
        onPress={() => toggleSection('details')}
      >
        <Text>Toggle Details</Text>
      </TouchableOpacity>

      {expandedSections.includes('details') && (
        <View testID="details-section">
          <Text>{route.params.initialOffer.description}</Text>
        </View>
      )}
    </View>
  );
};

jest.mock('../../../screens/OfferDetail', () => MockOfferDetail);

describe('OfferDetail UI Tests', () => {
  beforeEach(() => {
    resetAllMocks();
  });

  describe('User Role Based UI', () => {
    it('shows owner actions for current user offer', async () => {
      const { findByTestId } = render(
        <MockOfferDetail 
          navigation={createMockNavigation()} 
          route={createMockRoute({
            initialOffer: {
              id: 'test-offer-id',
              price: '100',
              description: 'Test Description',
              ownerId: mockAuth.currentUser.uid,
              status: 'active'
            }
          })} 
        />
      );

      await waitFor(() => {
        expect(findByTestId('owner-actions')).toBeTruthy();
      });
    });

    it('hides owner actions for other user offers', async () => {
      const { queryByTestId } = render(
        <MockOfferDetail 
          navigation={createMockNavigation()} 
          route={createMockRoute({
            initialOffer: {
              id: 'test-offer-id',
              price: '100',
              description: 'Test Description',
              ownerId: 'other-user-id',
              status: 'active'
            }
          })} 
        />
      );

      await waitFor(() => {
        expect(queryByTestId('owner-actions')).toBeNull();
      });
    });
  });

  describe('Expandable Sections', () => {
    it('toggles details section visibility', async () => {
      const { getByTestId, queryByTestId } = render(
        <MockOfferDetail 
          navigation={createMockNavigation()} 
          route={createMockRoute()} 
        />
      );

      await waitFor(() => {
        expect(queryByTestId('details-section')).toBeNull();
      });

      await act(async () => {
        fireEvent.press(getByTestId('toggle-details-button'));
      });

      expect(getByTestId('details-section')).toBeTruthy();

      await act(async () => {
        fireEvent.press(getByTestId('toggle-details-button'));
      });

      expect(queryByTestId('details-section')).toBeNull();
    });
  });
}); 