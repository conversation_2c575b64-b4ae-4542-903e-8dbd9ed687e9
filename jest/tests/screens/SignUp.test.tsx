import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { act } from '@testing-library/react-native';
import SignUp from '@/screens/SignUp';
import { createUserWithEmailAndPassword } from 'firebase/auth';
import { setDoc, doc } from 'firebase/firestore';

// Mock Firebase Auth
jest.mock('firebase/auth', () => ({
  createUserWithEmailAndPassword: jest.fn(),
}));

// Mock Firebase Firestore
jest.mock('firebase/firestore', () => ({
  setDoc: jest.fn(),
  doc: jest.fn(),
}));

// Mock navigation
const mockNavigate = jest.fn();
jest.mock('@react-navigation/native', () => ({
  useNavigation: () => ({
    navigate: mockNavigate,
  }),
}));

describe('SignUp Screen', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders correctly', () => {
    const { getByText, getByPlaceholderText } = render(<SignUp />);
    
    expect(getByText('Create Your Account')).toBeTruthy();
    expect(getByPlaceholderText('Enter your email')).toBeTruthy();
    expect(getByPlaceholderText('Enter your password')).toBeTruthy();
    expect(getByPlaceholderText('Confirm your password')).toBeTruthy();
    expect(getByText('Sign Up')).toBeTruthy();
  });

  it('handles successful sign up flow', async () => {
    const mockUser = { uid: 'test-uid' };
    (createUserWithEmailAndPassword as jest.Mock).mockResolvedValueOnce({ user: mockUser });
    (setDoc as jest.Mock).mockResolvedValueOnce(undefined);
    (doc as jest.Mock).mockReturnValue('mocked-doc-ref');

    const { getByPlaceholderText, getByText } = render(<SignUp />);
    
    const emailInput = getByPlaceholderText('Enter your email');
    const passwordInput = getByPlaceholderText('Enter your password');
    const confirmPasswordInput = getByPlaceholderText('Confirm your password');
    const signUpButton = getByText('Sign Up');

    fireEvent.changeText(emailInput, '<EMAIL>');
    fireEvent.changeText(passwordInput, 'password123');
    fireEvent.changeText(confirmPasswordInput, 'password123');
    
    await act(async () => {
      fireEvent.press(signUpButton);
      await waitFor(() => {
        expect(createUserWithEmailAndPassword).toHaveBeenCalledWith(
          expect.anything(),
          '<EMAIL>',
          'password123'
        );
        expect(doc).toHaveBeenCalledWith(expect.anything(), 'users', mockUser.uid);
        expect(setDoc).toHaveBeenCalledWith(
          'mocked-doc-ref',
          { favorites: [], score: 100 }
        );
        expect(mockNavigate).toHaveBeenCalledWith('SignUpConfirmation', expect.any(Object));
      });
    });
  });

  it('prevents sign up when passwords do not match', async () => {
    const { getByPlaceholderText, getByText } = render(<SignUp />);
    
    const emailInput = getByPlaceholderText('Enter your email');
    const passwordInput = getByPlaceholderText('Enter your password');
    const confirmPasswordInput = getByPlaceholderText('Confirm your password');
    const signUpButton = getByText('Sign Up');

    fireEvent.changeText(emailInput, '<EMAIL>');
    fireEvent.changeText(passwordInput, 'password123');
    fireEvent.changeText(confirmPasswordInput, 'differentpassword');
    
    await act(async () => {
      fireEvent.press(signUpButton);
      await waitFor(() => {
        expect(createUserWithEmailAndPassword).not.toHaveBeenCalled();
        expect(setDoc).not.toHaveBeenCalled();
        expect(mockNavigate).not.toHaveBeenCalled();
      });
    });
  });

  it('handles sign up error correctly', async () => {
    const mockError = new Error('Email already in use');
    (createUserWithEmailAndPassword as jest.Mock).mockRejectedValueOnce(mockError);

    const { getByPlaceholderText, getByText } = render(<SignUp />);
    
    const emailInput = getByPlaceholderText('Enter your email');
    const passwordInput = getByPlaceholderText('Enter your password');
    const confirmPasswordInput = getByPlaceholderText('Confirm your password');
    const signUpButton = getByText('Sign Up');

    fireEvent.changeText(emailInput, '<EMAIL>');
    fireEvent.changeText(passwordInput, 'password123');
    fireEvent.changeText(confirmPasswordInput, 'password123');
    
    await act(async () => {
      fireEvent.press(signUpButton);
      await waitFor(() => {
        expect(createUserWithEmailAndPassword).toHaveBeenCalled();
        expect(setDoc).not.toHaveBeenCalled();
        expect(mockNavigate).not.toHaveBeenCalled();
      });
    });
  });
}); 