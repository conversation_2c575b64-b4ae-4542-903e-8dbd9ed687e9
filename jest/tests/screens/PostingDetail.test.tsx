import React from 'react';
import { render, fireEvent, waitFor, act } from '@testing-library/react-native';
import { Alert } from 'react-native';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import PostingDetail from '../../../screens/PostingDetail';
import { auth } from '../../../firebase';
import { deletePosting } from '../../../services/firebaseService';
import usePostingDetails from '../../../hooks/usePostingDetails';
import useOffers from '../../../hooks/useOffers';
import useFavorites from '../../../hooks/useFavorites';

// Mock console methods
const originalError = console.error;
beforeAll(() => {
  console.error = jest.fn();
});

afterAll(() => {
  console.error = originalError;
});

// Mock Alert
const mockAlert = jest.fn();
jest.mock('react-native/Libraries/Alert/Alert', () => ({
  alert: mockAlert,
}));

// Mock the navigation hook
jest.mock('@react-navigation/native', () => ({
  useNavigation: jest.fn(),
  useFocusEffect: jest.fn((callback) => callback()),
}));

// Mock the firebase auth
jest.mock('../../../firebase', () => ({
  auth: {
    currentUser: null,
  },
}));

// Mock the firebase service
jest.mock('../../../services/firebaseService', () => ({
  deletePosting: jest.fn(),
}));

// Mock the custom hooks
jest.mock('../../../hooks/usePostingDetails', () => jest.fn());
jest.mock('../../../hooks/useOffers', () => jest.fn());
jest.mock('../../../hooks/useFavorites', () => jest.fn());

// Mock icons
jest.mock('react-native-vector-icons/Ionicons', () => 'Ionicons');
jest.mock('react-native-vector-icons/AntDesign', () => 'AntDesign');

// Mock the VirtualizedList component
jest.mock('react-native/Libraries/Lists/VirtualizedList', () => {
  const React = require('react');
  const VirtualizedListMock = ({ children }: { children: React.ReactNode }) => <>{children}</>;
  VirtualizedListMock.getItem = (data: any[], index: number) => data[index];
  VirtualizedListMock.getItemCount = (data: any[]) => data.length;
  return VirtualizedListMock;
});

describe('PostingDetail', () => {
  // Mock data
  const mockPostingId = 'test-posting-id';
  const mockUserId = 'test-user-id';
  const mockItemLocation = {
    latitude: 40.7128,
    longitude: -74.0060,
  };

  const mockPostingDetails = {
    id: mockPostingId,
    title: 'Test Item',
    description: 'Test Description',
    userId: mockUserId,
    latitude: mockItemLocation.latitude,
    longitude: mockItemLocation.longitude,
    postingStatus: 'Active',
  };

  const mockOffers = [
    {
      id: 'offer-1',
      userId: 'user-1',
      price: 100,
      description: 'Offer 1 description',
    },
    {
      id: 'offer-2',
      userId: 'user-2',
      price: 90,
      description: 'Offer 2 description',
    },
  ];

  // Mock navigation
  const mockNavigate = jest.fn();

  // Default props
  const defaultProps = {
    route: {
      params: {
        postingId: mockPostingId,
        itemLocation: mockItemLocation,
        userId: mockUserId,
      },
    },
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useNavigation as jest.Mock).mockReturnValue({ navigate: mockNavigate });
    (useFocusEffect as jest.Mock).mockImplementation((callback) => callback());
    
    // Reset auth.currentUser
    (auth as any).currentUser = null;

    // Mock hook default returns
    (usePostingDetails as jest.Mock).mockReturnValue({
      postingDetails: mockPostingDetails,
      loading: false,
      error: null,
      refetchPostingDetails: jest.fn(),
    });

    (useOffers as jest.Mock).mockReturnValue({
      offers: mockOffers,
    });

    (useFavorites as jest.Mock).mockReturnValue({
      isFavorite: false,
      addToFavorites: jest.fn(),
      removeFromFavorites: jest.fn(),
      refetchFavorites: jest.fn(),
    });

    // Reset Alert mock
    (Alert.alert as jest.Mock).mockReset();
  });

  describe('Rendering Tests', () => {
    it('renders loading state correctly', () => {
      (usePostingDetails as jest.Mock).mockReturnValue({
        postingDetails: null,
        loading: true,
        error: null,
        refetchPostingDetails: jest.fn(),
      });

      const { getByTestId } = render(<PostingDetail {...defaultProps} />);
      expect(getByTestId('loading-container')).toBeTruthy();
    });

    it('renders error state correctly', () => {
      const errorMessage = 'Failed to load posting details';
      (usePostingDetails as jest.Mock).mockReturnValue({
        postingDetails: null,
        loading: false,
        error: errorMessage,
        refetchPostingDetails: jest.fn(),
      });

      const { getByTestId, getByText } = render(<PostingDetail {...defaultProps} />);
      expect(getByTestId('error-container')).toBeTruthy();
      expect(getByText(`Error: ${errorMessage}`)).toBeTruthy();
    });

    it('renders posting details correctly', () => {
      const { getByText } = render(<PostingDetail {...defaultProps} />);
      expect(getByText(mockPostingDetails.title)).toBeTruthy();
      expect(getByText(mockPostingDetails.description)).toBeTruthy();
    });

    it('renders owner actions when user is owner', () => {
      (auth as any).currentUser = { uid: mockUserId };
      
      // Force a re-render to update isOwner state
      const { getByTestId, rerender } = render(<PostingDetail {...defaultProps} />);
      rerender(<PostingDetail {...defaultProps} />);
      
      expect(getByTestId('edit-posting-button')).toBeTruthy();
      expect(getByTestId('delete-posting-button')).toBeTruthy();
    });

    it('renders viewer actions when user is not owner', () => {
      // Set up non-owner user
      jest.resetModules();
      const mockedAuth = require('../../../firebase').auth;
      mockedAuth.currentUser = { uid: 'different-user-id' };

      (usePostingDetails as jest.Mock).mockReturnValue({
        loading: false,
        error: null,
        postingDetails: {
          ...mockPostingDetails,
          userId: 'owner-user-id', // Different from currentUser.uid
        },
        refetchPostingDetails: jest.fn()
      });

      (useOffers as jest.Mock).mockReturnValue({
        loading: false,
        error: null,
        offers: [],
        refetchOffers: jest.fn()
      });

      const { getByTestId, rerender } = render(<PostingDetail {...defaultProps} />);
      
      // Force a re-render to update isOwner state
      rerender(<PostingDetail {...defaultProps} />);

      expect(getByTestId('make-offer-button')).toBeTruthy();
      expect(getByTestId('favorite-button')).toBeTruthy();
      expect(getByTestId('location-button')).toBeTruthy();
    });

    it('renders deleted posting notice when posting is deleted', () => {
      (usePostingDetails as jest.Mock).mockReturnValue({
        postingDetails: { ...mockPostingDetails, postingStatus: 'Deleted' },
        loading: false,
        error: null,
        refetchPostingDetails: jest.fn(),
      });

      const { getByText } = render(<PostingDetail {...defaultProps} />);
      expect(getByText('This posting was deleted by the owner.')).toBeTruthy();
    });
  });

  describe('User Interaction Tests', () => {
    it('expands and collapses description on press', () => {
      const longDescription = 'A'.repeat(200);
      (usePostingDetails as jest.Mock).mockReturnValue({
        postingDetails: { ...mockPostingDetails, description: longDescription },
        loading: false,
        error: null,
        refetchPostingDetails: jest.fn(),
      });

      const { getByText } = render(<PostingDetail {...defaultProps} />);
      
      // Initially collapsed
      expect(getByText('Show More')).toBeTruthy();
      
      // Expand
      fireEvent.press(getByText('Show More'));
      expect(getByText('Show Less')).toBeTruthy();
      
      // Collapse
      fireEvent.press(getByText('Show Less'));
      expect(getByText('Show More')).toBeTruthy();
    });

    it('handles delete posting confirmation', async () => {
      (auth as any).currentUser = { uid: mockUserId };
      
      // Force a re-render to update isOwner state
      const { getByTestId, rerender } = render(<PostingDetail {...defaultProps} />);
      rerender(<PostingDetail {...defaultProps} />);

      await act(async () => {
        fireEvent.press(getByTestId('delete-posting-button'));
      });

      expect(Alert.alert).toHaveBeenCalledWith(
        'Confirm Delete',
        'Are you sure you want to delete this posting? All active offers will be withdrawn.',
        expect.any(Array),
        { cancelable: true }
      );

      // Simulate confirming deletion
      const alertButtons = (Alert.alert as jest.Mock).mock.calls[0][2];
      const deleteButton = alertButtons.find((button: any) => button.text === 'Delete');
      
      await act(async () => {
        await deleteButton.onPress();
      });

      expect(deletePosting).toHaveBeenCalledWith(mockPostingId);
      
      // Simulate success alert callback
      const successAlertButtons = (Alert.alert as jest.Mock).mock.calls[1][2];
      const okButton = successAlertButtons[0];
      await act(async () => {
        await okButton.onPress();
      });
      
      expect(mockNavigate).toHaveBeenCalledWith('MyPostings');
    });
  });

  describe('Navigation Tests', () => {
    it('navigates to make offer screen when make offer button is pressed', () => {
      // Set up non-owner user
      jest.resetModules();
      const mockedAuth = require('../../../firebase').auth;
      mockedAuth.currentUser = { uid: 'different-user-id' };

      (usePostingDetails as jest.Mock).mockReturnValue({
        loading: false,
        error: null,
        postingDetails: {
          ...mockPostingDetails,
          userId: 'owner-user-id', // Different from currentUser.uid
        },
        refetchPostingDetails: jest.fn()
      });

      (useOffers as jest.Mock).mockReturnValue({
        loading: false,
        error: null,
        offers: [],
        refetchOffers: jest.fn()
      });

      const { getByTestId, rerender } = render(<PostingDetail {...defaultProps} />);
      
      // Force a re-render to update isOwner state
      rerender(<PostingDetail {...defaultProps} />);

      const makeOfferButton = getByTestId('make-offer-button');
      fireEvent.press(makeOfferButton);
      
      expect(mockNavigate).toHaveBeenCalledWith('MakeOffer', {
        postingId: mockPostingDetails.id,
        itemName: mockPostingDetails.title,
        itemDescription: mockPostingDetails.description,
        itemLocation: {
          latitude: mockPostingDetails.latitude,
          longitude: mockPostingDetails.longitude
        },
        userId: mockPostingDetails.userId
      });
    });

    it('navigates to EditPosting screen correctly', () => {
      (auth as any).currentUser = { uid: mockUserId };
      
      // Force a re-render to update isOwner state
      const { getByTestId, rerender } = render(<PostingDetail {...defaultProps} />);
      rerender(<PostingDetail {...defaultProps} />);

      fireEvent.press(getByTestId('edit-posting-button'));

      expect(mockNavigate).toHaveBeenCalledWith('EditPosting', {
        postingId: mockPostingId,
        itemName: mockPostingDetails.title,
        itemDescription: mockPostingDetails.description,
        itemLocation: mockItemLocation,
      });
    });

    it('navigates to Home screen with map focus', () => {
      const { getByTestId } = render(<PostingDetail {...defaultProps} />);

      fireEvent.press(getByTestId('location-button'));

      expect(mockNavigate).toHaveBeenCalledWith('Home', {
        focusLocation: {
          latitude: mockPostingDetails.latitude,
          longitude: mockPostingDetails.longitude,
          latitudeDelta: 0.0922,
          longitudeDelta: 0.0421,
        },
        selectedPosting: mockPostingDetails,
        showCallout: true,
      });
    });
  });

  describe('Error Handling Tests', () => {
    it('handles delete posting error', async () => {
      (auth as any).currentUser = { uid: mockUserId };
      const error = new Error('Failed to delete posting');
      (deletePosting as jest.Mock).mockRejectedValue(error);

      // Force a re-render to update isOwner state
      const { getByTestId, rerender } = render(<PostingDetail {...defaultProps} />);
      rerender(<PostingDetail {...defaultProps} />);

      await act(async () => {
        fireEvent.press(getByTestId('delete-posting-button'));
      });

      // Simulate confirming deletion
      const alertButtons = (Alert.alert as jest.Mock).mock.calls[0][2];
      const deleteButton = alertButtons.find((button: any) => button.text === 'Delete');
      
      await act(async () => {
        await deleteButton.onPress();
      });

      expect(console.error).toHaveBeenCalledWith('Error deleting posting:', error);
      expect(Alert.alert).toHaveBeenCalledWith('Error', 'Failed to delete posting');
    });
  });
}); 