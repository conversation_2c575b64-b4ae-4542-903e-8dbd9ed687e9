import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { Alert } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { deleteDoc } from 'firebase/firestore';
import PostDeleteConfirmation from '../../../screens/PostDeleteConfirmation';

// Mock the required modules
jest.mock('@react-navigation/native', () => ({
  useNavigation: jest.fn(),
}));

jest.mock('firebase/firestore', () => ({
  deleteDoc: jest.fn(),
  doc: jest.fn(),
}));

jest.mock('../../../firebase', () => ({
  db: {},
}));

describe('PostDeleteConfirmation', () => {
  const mockNavigation = {
    navigate: jest.fn(),
    goBack: jest.fn(),
  };

  const mockRoute = {
    params: {
      itemId: 'test-item-id',
      itemName: 'Test Item',
      itemDescription: 'Test Description',
    },
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useNavigation as jest.Mock).mockReturnValue(mockNavigation);
  });

  it('renders correctly with item details', () => {
    const { getByText } = render(
      <PostDeleteConfirmation route={mockRoute} />
    );

    expect(getByText('Delete Posting')).toBeTruthy();
    expect(getByText('Are you sure you want to delete the posting titled "Test Item"?')).toBeTruthy();
    expect(getByText('Test Description')).toBeTruthy();
    expect(getByText('Cancel')).toBeTruthy();
    expect(getByText('Confirm Delete')).toBeTruthy();
  });

  it('navigates back when Cancel is pressed', () => {
    const { getByText } = render(
      <PostDeleteConfirmation route={mockRoute} />
    );

    fireEvent.press(getByText('Cancel'));
    expect(mockNavigation.goBack).toHaveBeenCalled();
  });

  it('deletes posting and navigates to MyPostings on successful deletion', async () => {
    (deleteDoc as jest.Mock).mockResolvedValueOnce(undefined);

    const { getByText } = render(
      <PostDeleteConfirmation route={mockRoute} />
    );

    fireEvent.press(getByText('Confirm Delete'));

    await waitFor(() => {
      expect(deleteDoc).toHaveBeenCalled();
      expect(mockNavigation.navigate).toHaveBeenCalledWith('MyPostings');
    });
  });

  it('handles deletion error correctly', async () => {
    const mockError = new Error('Failed to delete');
    (deleteDoc as jest.Mock).mockRejectedValueOnce(mockError);
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

    const { getByText } = render(
      <PostDeleteConfirmation route={mockRoute} />
    );

    fireEvent.press(getByText('Confirm Delete'));

    await waitFor(() => {
      expect(deleteDoc).toHaveBeenCalled();
      expect(consoleSpy).toHaveBeenCalledWith('Error deleting posting:', mockError);
    });

    consoleSpy.mockRestore();
  });

  it('logs deletion flow correctly', async () => {
    const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {});
    (deleteDoc as jest.Mock).mockResolvedValueOnce(undefined);

    const { getByText } = render(
      <PostDeleteConfirmation route={mockRoute} />
    );

    fireEvent.press(getByText('Confirm Delete'));

    await waitFor(() => {
      expect(consoleSpy).toHaveBeenCalledWith('Posting deleted successfully');
    });

    consoleSpy.mockRestore();
  });
}); 