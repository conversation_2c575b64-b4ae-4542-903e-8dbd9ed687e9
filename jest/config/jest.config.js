module.exports = {
  preset: 'react-native',
  testEnvironment: 'node',
  rootDir: '../../',
  setupFilesAfterEnv: [
    '@testing-library/jest-native/extend-expect',
    '<rootDir>/jest/setup/setup.js',
    '<rootDir>/jest/setup/jest.env.js',
    '<rootDir>/jest/setup/jest.environment.ts'
  ],
  transform: {
    '^.+\\.(js|jsx|ts|tsx)$': ['babel-jest', { configFile: './jest/config/babel.config.test.js' }],
  },
  transformIgnorePatterns: [
    'node_modules/(?!(react-native|@react-native|@react-navigation|firebase|@firebase|@testing-library|@react-native-firebase|@expo|expo|expo-font|react-native-vector-icons|@expo/vector-icons)/)',
  ],
  moduleNameMapper: {
    '^@/screens/(.*)$': '<rootDir>/screens/$1',
    '^@/(.*)$': '<rootDir>/$1',
    '\\.svg': '<rootDir>/jest/mocks/svgMock.js',
    '^@expo/vector-icons$': '<rootDir>/jest/mocks/expoVectorIconsMock.js',
    '^expo-font$': '<rootDir>/jest/mocks/expoFontMock.js',
    '^firebase$': '<rootDir>/jest/mocks/firebaseMock.ts',
    '^../firebase$': '<rootDir>/jest/mocks/firebaseMock.ts',
    '^./firebase$': '<rootDir>/jest/mocks/firebaseMock.ts',
    '^firebase/firestore$': '<rootDir>/jest/mocks/firebase/firestore.ts',
    '^../../hooks/useOfferDetails$': '<rootDir>/jest/mocks/useOfferDetailsMock.ts',
    '^./services/notificationService$': '<rootDir>/jest/mocks/notificationServiceMock.ts',
  },
  verbose: true,
  testTimeout: 30000,
  globals: {
    __DEV__: true
  },
  testPathIgnorePatterns: [
    '/node_modules/',
    '/babel.config.test.js'
  ],
  // Coverage configuration
  collectCoverage: true,
  coverageDirectory: '<rootDir>/jest/coverage',
  coverageReporters: ['text', 'lcov', 'clover', 'html'],
  collectCoverageFrom: [
    'screens/**/*.{js,jsx,ts,tsx}',
    'hooks/**/*.{js,jsx,ts,tsx}',
    'services/**/*.{js,jsx,ts,tsx}',
    'utils/**/*.{js,jsx,ts,tsx}',
    '!**/*.d.ts',
    '!**/node_modules/**',
    '!**/vendor/**'
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    }
  }
};