import '@testing-library/jest-dom';
import '@testing-library/jest-native/extend-expect';

declare global {
  namespace jest {
    interface Matchers<R> {
      toBeDisabled(): R;
      toContainElement(element: any): R;
      toBeEmpty(): R;
      toHaveProp(prop: string, value?: any): R;
      toHaveTextContent(text: string): R;
      toBeEnabled(): R;
      toHaveStyle(style: object): R;
    }
  }
} 