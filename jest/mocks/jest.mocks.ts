// Import types
import { FirebaseApp } from 'firebase/app';
import { Auth, User } from 'firebase/auth';
import { Firestore, DocumentData, QuerySnapshot } from 'firebase/firestore';
import { NavigationProp, RouteProp } from '@react-navigation/native';

// Import AsyncStorage mock
import mockAsyncStorage from '@react-native-async-storage/async-storage/jest/async-storage-mock';

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () => mockAsyncStorage);

// Mock Firebase App
jest.mock('firebase/app', () => ({
  initializeApp: jest.fn(() => ({} as FirebaseApp)),
  getApp: jest.fn(() => ({} as FirebaseApp)),
  registerVersion: jest.fn()
}));

// Mock Firebase Auth
jest.mock('firebase/auth', () => ({
  getAuth: jest.fn(() => ({
    currentUser: { uid: 'test-user-id' } as User,
    onAuthStateChanged: jest.fn()
  } as Auth)),
  signInWithEmailAndPassword: jest.fn(),
  createUserWithEmailAndPassword: jest.fn(),
  signOut: jest.fn()
}));

// Mock Firebase modules
jest.mock('./firebase', () => require('./__mocks__/firebaseMock'));

// Mock Firestore
jest.mock('firebase/firestore', () => ({
  getFirestore: jest.fn(() => ({} as Firestore)),
  collection: jest.fn(),
  doc: jest.fn(),
  getDoc: jest.fn(() => Promise.resolve({
    exists: () => true,
    data: () => ({
      title: 'Test Offer',
      description: 'Test Description',
      price: 100
    } as DocumentData)
  })),
  getDocs: jest.fn(),
  query: jest.fn(),
  where: jest.fn(),
  orderBy: jest.fn(),
  limit: jest.fn(),
  onSnapshot: jest.fn((query, callback) => {
    callback({
      docs: [],
      empty: true
    } as QuerySnapshot<DocumentData>);
    return jest.fn(); // Unsubscribe function
  })
}));

// Mock React Navigation
jest.mock('@react-navigation/native', () => ({
  useNavigation: () => ({
    navigate: jest.fn(),
    goBack: jest.fn(),
    setOptions: jest.fn(),
    addListener: jest.fn(() => jest.fn())
  } as NavigationProp<any>),
  useRoute: () => ({
    params: {
      offerId: 'test-offer-id',
      postingId: 'test-posting-id',
      postingOwnerId: 'test-posting-owner-id',
      offerOwnerId: 'test-offer-owner-id'
    }
  } as RouteProp<any, any>)
}));

// Mock notification service
jest.mock('./services/notificationService', () => ({
  ...require('./__mocks__/notificationServiceMock'),
  clearNotificationListeners: jest.fn(),
  initializeNotifications: jest.fn(() => Promise.resolve({ 
    success: true, 
    platform: 'ios' 
  })),
  requestNotificationPermissions: jest.fn(() => Promise.resolve(true)),
  setupNotificationHandlers: jest.fn(),
  clearBadgeCount: jest.fn()
}));

// Add console logging for debugging
console.log('Mocks Setup:', {
  asyncStorageMocked: typeof mockAsyncStorage !== 'undefined',
  firebaseMocked: typeof jest.mock('firebase/app') !== 'undefined',
  navigationMocked: typeof jest.mock('@react-navigation/native') !== 'undefined'
}); 