import { jest } from '@jest/globals';

// Mock document snapshot
const mockDocumentSnapshot = {
  exists: () => true,
  data: () => ({
    messages: [],
    title: 'Test Offer',
    description: 'Test Description',
    price: 100,
    userId: 'test-user-id'
  }),
  id: 'test-doc-id'
};

// Mock query snapshot
const mockQuerySnapshot = {
  empty: false,
  docs: [mockDocumentSnapshot],
  forEach: jest.fn(callback => {
    callback(mockDocumentSnapshot);
  })
};

// Mock onSnapshot as both function and property
function mockOnSnapshot(query, callback) {
  if (callback) {
    callback(mockQuerySnapshot);
  }
  return jest.fn(); // unsubscribe function
}

// Add properties to make it work as a method too
Object.assign(mockOnSnapshot, {
  mockImplementation: jest.fn().mockReturnValue(mockOnSnapshot)
});

// Mock query result with onSnapshot method
const mockQueryWithSnapshot = {
  onSnapshot: mockOnSnapshot
};

const mockCollection = jest.fn(() => ({
  where: jest.fn().mockReturnThis(),
  orderBy: jest.fn().mockReturnThis(),
  limit: jest.fn().mockReturnThis(),
  startAfter: jest.fn().mockReturnThis(),
}));

const mockQuery = jest.fn(() => mockQueryWithSnapshot);
const mockWhere = jest.fn().mockReturnThis();
const mockOrderBy = jest.fn().mockReturnThis();
const mockLimit = jest.fn().mockReturnThis();
const mockStartAfter = jest.fn().mockReturnThis();

const mockGetDoc = jest.fn().mockResolvedValue(mockDocumentSnapshot);
const mockGetDocs = jest.fn().mockResolvedValue(mockQuerySnapshot);
const mockDoc = jest.fn();
const mockAddDoc = jest.fn().mockResolvedValue({ id: 'new-doc-id' });
const mockUpdateDoc = jest.fn().mockResolvedValue(true);
const mockDeleteDoc = jest.fn().mockResolvedValue(true);
const mockServerTimestamp = jest.fn(() => new Date().toISOString());

// Export all mocks
export {
  mockCollection as collection,
  mockQuery as query,
  mockWhere as where,
  mockOrderBy as orderBy,
  mockLimit as limit,
  mockStartAfter as startAfter,
  mockOnSnapshot as onSnapshot,
  mockGetDoc as getDoc,
  mockGetDocs as getDocs,
  mockDoc as doc,
  mockAddDoc as addDoc,
  mockUpdateDoc as updateDoc,
  mockDeleteDoc as deleteDoc,
  mockServerTimestamp as serverTimestamp,
};

// Export default for compatibility
export default {
  collection: mockCollection,
  query: mockQuery,
  where: mockWhere,
  orderBy: mockOrderBy,
  limit: mockLimit,
  startAfter: mockStartAfter,
  onSnapshot: mockOnSnapshot,
  getDoc: mockGetDoc,
  getDocs: mockGetDocs,
  doc: mockDoc,
  addDoc: mockAddDoc,
  updateDoc: mockUpdateDoc,
  deleteDoc: mockDeleteDoc,
  serverTimestamp: mockServerTimestamp,
}; 