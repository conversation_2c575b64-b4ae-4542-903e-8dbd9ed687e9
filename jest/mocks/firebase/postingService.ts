import { jest } from '@jest/globals';

interface PostingUpdates {
  title?: string;
  description?: string;
  latitude?: number;
  longitude?: number;
}

interface PostingDetails {
  id: string;
  title: string;
  description: string;
  latitude: number;
  longitude: number;
  userId: string;
  createdAt: string;
  updatedAt: string;
}

export const mockUpdatePostingDetails = jest.fn(
  async (postingId: string, updates: PostingUpdates) => {
    // Mock successful update
    return Promise.resolve();
  }
);

export const mockGetPostingDetails = jest.fn(
  async (postingId: string) => {
    return Promise.resolve({
      id: postingId,
      title: 'Test Item',
      description: 'Test Description',
      latitude: 37.7749,
      longitude: -122.4194,
      userId: 'test-user-id',
      createdAt: new Date('2025-01-01').toISOString(),
      updatedAt: new Date('2025-01-01').toISOString()
    });
  }
);

export default {
  updatePostingDetails: mockUpdatePostingDetails,
  getPostingDetails: mockGetPostingDetails
}; 