import { jest } from '@jest/globals';
import type { DocumentSnapshot, QuerySnapshot, Query } from 'firebase/firestore';

// Add Timestamp mock
const mockTimestamp = {
  toDate: () => new Date('2025-01-01'),
  seconds: 1735689600,
  nanoseconds: 0
};

// Mock document snapshot
const mockDocumentSnapshot = {
  exists: () => true,
  data: () => ({
    messages: [{
      id: 'test-message-id',
      text: 'Test message',
      userId: 'test-user-id',
      createdAt: mockTimestamp,
      updatedAt: mockTimestamp,
      timestamp: mockTimestamp
    }],
    title: 'Test Offer',
    description: 'Test Description',
    price: 100,
    userId: 'test-user-id',
    status: 'active',
    createdAt: mockTimestamp,
    updatedAt: mockTimestamp
  }),
  id: 'test-doc-id',
  ref: {
    path: 'offers/test-doc-id'
  }
} as DocumentSnapshot;

// Mock query snapshot
const mockQuerySnapshot = {
  empty: false,
  docs: [mockDocumentSnapshot],
  forEach: jest.fn((callback: (doc: DocumentSnapshot) => void) => {
    callback(mockDocumentSnapshot);
  }),
  docChanges: jest.fn(() => [{
    type: 'added',
    doc: mockDocumentSnapshot
  }])
} as QuerySnapshot;

// Mock collection and other basic functions
const mockCollection = jest.fn(() => ({
  where: jest.fn().mockReturnThis(),
  orderBy: jest.fn().mockReturnThis(),
  limit: jest.fn().mockReturnThis(),
  startAfter: jest.fn().mockReturnThis(),
}));

const mockQuery = jest.fn().mockReturnThis();
const mockWhere = jest.fn().mockReturnThis();
const mockOrderBy = jest.fn().mockReturnThis();
const mockLimit = jest.fn().mockReturnThis();
const mockStartAfter = jest.fn().mockReturnThis();

// Create a proper function that can be both called directly and as a method
const mockOnSnapshot = jest.fn((queryOrRef, callback) => {
  if (typeof callback === 'function') {
    // Called as onSnapshot(query, callback)
    callback(mockQuerySnapshot);
  } else if (typeof queryOrRef === 'function') {
    // Called as onSnapshot(callback)
    queryOrRef(mockQuerySnapshot);
  }
  return () => {}; // Unsubscribe function
});

// Make it work as a method too
mockOnSnapshot.bind = jest.fn().mockReturnValue(mockOnSnapshot);
mockOnSnapshot.call = jest.fn().mockReturnValue(mockOnSnapshot);
mockOnSnapshot.apply = jest.fn().mockReturnValue(mockOnSnapshot);

const mockGetDoc = jest.fn().mockResolvedValue(mockDocumentSnapshot);
const mockGetDocs = jest.fn().mockResolvedValue(mockQuerySnapshot);
const mockDoc = jest.fn();
const mockAddDoc = jest.fn().mockResolvedValue({ id: 'new-doc-id' });
const mockUpdateDoc = jest.fn().mockResolvedValue(true);
const mockDeleteDoc = jest.fn().mockResolvedValue(true);
const mockServerTimestamp = jest.fn(() => new Date().toISOString());

// Add writeBatch mock
const mockWriteBatch = jest.fn(() => ({
  set: jest.fn().mockReturnThis(),
  update: jest.fn().mockReturnThis(),
  delete: jest.fn().mockReturnThis(),
  commit: jest.fn().mockResolvedValue(true)
}));

// Export named exports
export {
  mockCollection as collection,
  mockQuery as query,
  mockWhere as where,
  mockOrderBy as orderBy,
  mockLimit as limit,
  mockStartAfter as startAfter,
  mockOnSnapshot as onSnapshot,
  mockGetDoc as getDoc,
  mockGetDocs as getDocs,
  mockDoc as doc,
  mockAddDoc as addDoc,
  mockUpdateDoc as updateDoc,
  mockDeleteDoc as deleteDoc,
  mockServerTimestamp as serverTimestamp,
  mockWriteBatch as writeBatch,
};

// Export default with all functions
export default {
  collection: mockCollection,
  query: mockQuery,
  where: mockWhere,
  orderBy: mockOrderBy,
  limit: mockLimit,
  startAfter: mockStartAfter,
  onSnapshot: mockOnSnapshot,
  getDoc: mockGetDoc,
  getDocs: mockGetDocs,
  doc: mockDoc,
  addDoc: mockAddDoc,
  updateDoc: mockUpdateDoc,
  deleteDoc: mockDeleteDoc,
  serverTimestamp: mockServerTimestamp,
  writeBatch: mockWriteBatch,
};

// Export Timestamp constructor
export const Timestamp = jest.fn(() => mockTimestamp); 