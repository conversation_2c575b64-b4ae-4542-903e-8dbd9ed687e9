import { initializeApp } from 'firebase/app';
import { getFirestore } from 'firebase/firestore';
import { getAuth } from 'firebase/auth';
import { 
  collection, 
  query, 
  where, 
  onSnapshot,
  orderBy,
  limit,
  startAfter,
  getDocs,
  getDoc,
  addDoc,
  updateDoc,
  deleteDoc,
  serverTimestamp
} from 'firebase/firestore';

const mockDb = jest.fn();
const mockAuth = {
  currentUser: { uid: 'test-user-id' },
  onAuthStateChanged: jest.fn((callback) => {
    // Immediately call the callback with a mock user
    callback({ uid: 'test-user-id' });
    // Return mock unsubscribe function
    return jest.fn();
  }),
  signInWithEmailAndPassword: jest.fn(),
  signOut: jest.fn(),
};
const mockApp = jest.fn();

// Mock document snapshot
const mockDocumentSnapshot = {
  exists: () => true,
  data: () => ({
    messages: [],
    title: 'Test Offer',
    description: 'Test Description',
    price: 100,
    userId: 'test-user-id'
  }),
  id: 'test-doc-id'
};

// Mock query snapshot
const mockQuerySnapshot = {
  empty: false,
  docs: [mockDocumentSnapshot],
  forEach: jest.fn(callback => {
    callback(mockDocumentSnapshot);
  })
};

// Mock Firestore functions (internal use only)
const mockOnSnapshot = jest.fn((query, callback) => {
  callback(mockQuerySnapshot);
  return jest.fn(); // unsubscribe function
});

// Mock other Firestore functions
const mockFirestore = {
  collection: jest.fn(() => ({
    where: jest.fn().mockReturnThis(),
    orderBy: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    startAfter: jest.fn().mockReturnThis(),
    onSnapshot: mockOnSnapshot,
  })),
  doc: jest.fn(() => ({
    get: jest.fn().mockResolvedValue(mockDocumentSnapshot),
    set: jest.fn().mockResolvedValue(true),
    update: jest.fn().mockResolvedValue(true),
    delete: jest.fn().mockResolvedValue(true),
  })),
  getDoc: jest.fn().mockResolvedValue(mockDocumentSnapshot),
  getDocs: jest.fn().mockResolvedValue(mockQuerySnapshot),
  addDoc: jest.fn().mockResolvedValue({ id: 'new-doc-id' }),
  updateDoc: jest.fn().mockResolvedValue(true),
  deleteDoc: jest.fn().mockResolvedValue(true),
  serverTimestamp: jest.fn(() => new Date().toISOString()),
  query: jest.fn().mockReturnThis(),
  where: jest.fn().mockReturnThis(),
  orderBy: jest.fn().mockReturnThis(),
  limit: jest.fn().mockReturnThis(),
  startAfter: jest.fn().mockReturnThis(),
};

export const db = mockFirestore;

export const auth = {
  currentUser: { uid: 'test-user-id' },
  onAuthStateChanged: jest.fn((callback) => {
    // Immediately call the callback with a mock user
    callback({ uid: 'test-user-id' });
    // Return mock unsubscribe function
    return jest.fn();
  }),
  signInWithEmailAndPassword: jest.fn(),
  signOut: jest.fn(),
};

export const app = mockApp;

// Add any other exports that your firebase.ts has
export const getFirestoreInstance = jest.fn(() => mockDb);
export const getAuthInstance = jest.fn(() => mockAuth);

export default {
  db: mockDb,
  auth: mockAuth,
  app: mockApp,
  getFirestoreInstance,
  getAuthInstance
}; 