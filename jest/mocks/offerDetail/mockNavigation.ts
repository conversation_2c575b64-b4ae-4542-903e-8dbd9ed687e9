import { NavigationProp } from '@react-navigation/native';
import { RootStackParamList } from './mockRoute';

export const createMockNavigation = (overrides?: Partial<NavigationProp<RootStackParamList, 'OfferDetail'>>): NavigationProp<RootStackParamList, 'OfferDetail'> => ({
  navigate: jest.fn(),
  goBack: jest.fn(),
  addListener: jest.fn(),
  removeListener: jest.fn(),
  dispatch: jest.fn(),
  reset: jest.fn(),
  isFocused: jest.fn(),
  canGoBack: jest.fn(),
  getId: jest.fn(),
  getParent: jest.fn(),
  getState: jest.fn(),
  setOptions: jest.fn(),
  setParams: jest.fn(),
  ...overrides
}); 