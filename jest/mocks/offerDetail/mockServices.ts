interface Message {
  id: string;
  text: string;
  senderId: string;
}

// Mock Firebase Auth
export const mockAuth = {
  currentUser: {
    uid: 'test-user-id',
    email: '<EMAIL>'
  }
};

// Mock Firebase Service
export const mockFirebaseService = {
  getMessages: jest.fn().mockImplementation(() => Promise.resolve([])),
  addMessageToDiscussion: jest.fn().mockImplementation(() => Promise.resolve()),
  subscribeToMessages: jest.fn().mockImplementation(() => {
    // Return a cleanup function that can be called during unmount
    return jest.fn();
  }),
  getOfferById: jest.fn().mockImplementation(() => Promise.resolve({
    id: 'test-offer-id',
    price: '100',
    description: 'Test Description',
    status: 'active',
    ownerId: 'test-user-id'
  })),
};

// Mock Notification Service
export const mockNotificationService = {
  sendPushNotification: jest.fn(),
  updateBadgeCount: jest.fn(),
  clearNotification: jest.fn()
};

// Mock Keyboard Subscription
export const mockKeyboardSubscription = {
  remove: jest.fn()
};

// Reset all mocks helper
export const resetAllMocks = () => {
  // Clear all mock implementations
  jest.clearAllMocks();
  
  // Reset default implementations
  mockFirebaseService.getMessages.mockImplementation(() => Promise.resolve([]));
  mockFirebaseService.addMessageToDiscussion.mockImplementation(() => Promise.resolve());
  mockFirebaseService.subscribeToMessages.mockImplementation(() => jest.fn());
  mockFirebaseService.getOfferById.mockImplementation(() => Promise.resolve({
    id: 'test-offer-id',
    price: '100',
    description: 'Test Description',
    status: 'active',
    ownerId: 'test-user-id'
  }));
}; 