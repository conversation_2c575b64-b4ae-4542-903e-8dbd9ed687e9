import { RouteProp } from '@react-navigation/native';

export type RootStackParamList = {
  OfferDetail: {
    offerId: string;
    initialOffer: {
      id: string;
      price: string;
      description: string;
      status: string;
      ownerId: string;
    };
    initialPosting: {
      id: string;
      title: string;
      description: string;
      ownerId: string;
    };
    postingOwnerId?: string;
    refresh?: boolean;
  };
  PostingDetail: {
    postingId: string;
    userId: string;
  };
  EditOffer: {
    offerId: string;
    initialOffer: {
      id: string;
      price: string;
      description: string;
      status: string;
      ownerId: string;
    };
  };
};

export const createMockRoute = (overrides?: Partial<RootStackParamList['OfferDetail']>): RouteProp<RootStackParamList, 'OfferDetail'> => ({
  key: 'test-route-key',
  name: 'OfferDetail',
  params: {
    offerId: 'test-offer-id',
    initialOffer: {
      id: 'test-offer-id',
      price: '100',
      description: 'Test Description',
      status: 'active',
      ownerId: 'test-user-id'
    },
    initialPosting: {
      id: 'test-posting-id',
      title: 'Test Posting',
      description: 'Test Posting Description',
      ownerId: 'posting-owner-id'
    },
    ...overrides
  }
}); 