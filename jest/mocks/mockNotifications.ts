import { NotificationType } from '../../types/notifications';
import { NotificationData } from '../../types/firebase';

export const mockNotifications: NotificationData[] = [
  {
    type: NotificationType.NEW_OFFER,
    title: 'New Offer Received',
    body: 'You have received a new offer for your posting',
    createdAt: new Date('2025-01-14T08:00:00Z'),
    read: false,
    offerId: 'offer1',
    postingId: 'posting1',
    senderId: 'sender1',
    recipientId: 'recipient1',
  },
  {
    type: NotificationType.NEW_MESSAGE,
    title: 'New Message',
    body: 'You have a new message regarding your offer',
    createdAt: new Date('2025-01-14T07:00:00Z'),
    read: true,
    offerId: 'offer2',
    postingId: 'posting2',
    senderId: 'sender2',
    recipientId: 'recipient2',
  },
  {
    type: NotificationType.FAVORITE_POSTING_UPDATE,
    title: 'Favorite Posting Updated',
    body: 'A posting you favorited has been updated',
    createdAt: new Date('2025-01-14T06:00:00Z'),
    read: false,
    postingId: 'posting3',
    senderId: 'sender3',
    recipientId: 'recipient3',
  },
  {
    type: NotificationType.OFFER_STATUS_CHANGE,
    title: 'Offer Status Changed',
    body: 'The status of your offer has changed',
    createdAt: new Date('2025-01-14T05:00:00Z'),
    read: false,
    offerId: 'offer4',
    postingId: 'posting4',
    senderId: 'sender4',
    recipientId: 'recipient4',
  },
  {
    type: NotificationType.NEW_LOWER_OFFER,
    title: 'New Lower Offer Available',
    body: 'A lower offer is now available',
    createdAt: new Date('2025-01-14T04:00:00Z'),
    read: true,
    postingId: 'posting5',
    senderId: 'sender5',
    recipientId: 'recipient5',
  },
]; 