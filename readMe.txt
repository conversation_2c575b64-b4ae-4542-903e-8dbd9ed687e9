Development Notes:

1- Firebase Integration

		Integrated Firebase as the backend service for authentication and database needs. Configured both Firestore and Firebase Auth.

		Added necessary GoogleService-Info.plist and updated AppDelegate.mm file for Firebase setup.

2- Firestore Security Rules

		Implemented Firestore rules to ensure secure data access:

		Only authenticated users can read/write their own data in collections such as users, offers, listings, and discussions.

		Messaging is restricted to Posting Owner and Offer Owner for discussion documents while allowing third users to read messages but preventing write access.

3- Hermes Engine

		Resolved Hermes dependency issues by manually excluding redundant podspec references in Podfile.

4- Core Functionalities Implemented

		Postings and Offers: Created a user-friendly interface to create postings, view offers, and withdraw offers. Controlled access to actions like deleting postings only by the Posting Owner.

		Favorites Management: Added functionalities to add and remove items from favorites, ensuring that deleted postings are properly managed across user favorites.

		Messaging System: Set up a discussion feature allowing Posting Owner and Offer Owner to exchange messages, with third persons limited to read-only access.

5- Testing Setup

		Created three test users to test the functionality of postings, offers, and discussions.

		All functionalities (e.g., Create Posting, Make Offer, Send Message) were successfully tested for different users, confirming proper working behavior.

6- Sign Out Implementation

		Fixed issue with Firestore listeners continuing after sign-out, leading to permission errors. Ensured proper unsubscriptions of snapshot listeners during sign-out.

7- UI/UX Improvements

		Ensured navigation consistency across screens, such as remaining on the Item Detail screen after removing from favorites.

		Restricted access to certain UI components (like sending messages) based on user roles (i.e., Posting Owner, Offer Owner, or third person).

8- Firestore Queries

		Modified Firestore queries to only fetch active postings in the Home Screen and prevent displaying deleted postings in list and map views.

		Updated Firestore rules and documents to correctly reflect postingStatus on creation and modification.

9- Error Handling & Debugging

		Resolved multiple console errors and warnings, such as the "Missing or insufficient permissions" error when attempting to send messages.

		Used detailed logs to help identify issues related to Firebase rules, permissions, and redundant API calls.

10- Optimizations

		Removed redundant Firestore fetches on navigation events to improve performance.

		Implemented proper conditional checks to ensure only necessary snapshot listeners are active, reducing server load and enhancing user experience.

Next Steps

Extensive Testing: Perform end-to-end testing, including edge cases for different user types.

Code Refactoring: Focus on polishing the codebase, improving readability, and reducing redundancy.

Profile Page Enhancements: Add more user settings options and enhance profile management functionalities.

Notifications & Alerts: Implement a notification system to alert users of new offers or messages.
