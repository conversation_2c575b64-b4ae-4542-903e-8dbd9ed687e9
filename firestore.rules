rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper functions
    function isSignedIn() {
      return request.auth != null;
    }
    
    function isPostingOwner(postingId) {
      return isSignedIn() && 
             exists(/databases/$(database)/documents/postings/$(postingId)) &&
             get(/databases/$(database)/documents/postings/$(postingId)).data.userId == request.auth.uid;
    }
    
    function isOfferOwner(offerId) {
      return isSignedIn() && 
             exists(/databases/$(database)/documents/offers/$(offerId)) &&
             get(/databases/$(database)/documents/offers/$(offerId)).data.userId == request.auth.uid;
    }
    
    function canAccessDiscussion(offerId) {
      let offer = get(/databases/$(database)/documents/offers/$(offerId));
      let posting = get(/databases/$(database)/documents/postings/$(offer.data.postingId));
      return isSignedIn() && (
        request.auth.uid == offer.data.userId || 
        request.auth.uid == posting.data.userId
      );
    }

    // Keep existing collection rules
    match /userLocations/{locationId} {
      allow read, write: if isSignedIn();
    }

    match /offers/{offerId} {
      allow read, write: if isSignedIn();
    }

    match /postings/{postingId} {
      allow read, write: if isSignedIn();
    }

    match /users/{userId} {
      allow read, write: if isSignedIn() && request.auth.uid == userId;
      
      // Notification settings subcollection
      match /notificationSettings/{settingId} {
        allow read, write: if isSignedIn() && request.auth.uid == userId;
      }
    }

    // Discussion rules
    match /discussions/{discussionId} {
      allow read: if isSignedIn() && canAccessDiscussion(resource.data.offerId);
      allow create, update: if isSignedIn() && canAccessDiscussion(request.resource.data.offerId);
      
      // Rule for updating message read status
      allow update: if isSignedIn() && 
        (request.auth.uid == resource.data.postingOwnerId || 
         request.auth.uid == resource.data.offerOwnerId) &&
        request.resource.data.diff(resource.data).affectedKeys().hasOnly(['messages']) &&
        request.resource.data.messages.size() == resource.data.messages.size();
      
      // Additional rule for real-time updates
      allow get, list: if isSignedIn() && (
        request.auth.uid == resource.data.postingOwnerId || 
        request.auth.uid == resource.data.offerOwnerId
      );
      
      // Allow any signed in user to read discussions
      allow read: if isSignedIn();
      // Only allow posting owner or offer owner to write to discussions
      allow write: if isSignedIn() && (
        isOfferOwner(resource.data.offerId) ||
        isPostingOwner(get(/databases/$(database)/documents/offers/$(resource.data.offerId)).data.postingId)
      );
    }

    // Update the notifications collection rules
    match /notifications/{notificationId} {
      // Allow system-generated notifications
      allow create: if isSignedIn() && 
        request.resource.data.type in [
          "FAVORITE_POSTING_UPDATE", 
          "OFFER_STATUS_CHANGE", 
          "NEW_MESSAGE",
          "NEW_OFFER",
          "SYSTEM_WITHDRAWAL"
        ] &&
        request.resource.data.recipientId != null &&
        (request.resource.data.senderId == request.auth.uid || 
         request.resource.data.senderId == null); // Allow null sender for system notifications

      // Users can only read their own notifications
      allow read: if isSignedIn() && 
        resource.data.recipientId == request.auth.uid;

      // Users can only mark their own notifications as read
      allow update: if isSignedIn() && 
        resource.data.recipientId == request.auth.uid &&
        request.resource.data.diff(resource.data).affectedKeys().hasOnly(['read']);
    }

    // Add user token rules
    match /users/{userId} {
      allow read: if isSignedIn();
      allow write: if isSignedIn() && (
        request.auth.uid == userId ||
        request.resource.data.diff(resource.data).affectedKeys().hasOnly(['expoPushToken'])
      );
    }
    
    // Device tokens
    match /deviceTokens/{tokenId} {
      allow read, write: if isSignedIn() && request.auth.uid == tokenId;
    }

    // Messages subcollection rules
    match /offers/{offerId}/messages/{messageId} {
      // Allow any signed in user to read messages
      allow read: if isSignedIn();
      // Only allow posting owner or offer owner to write messages
      allow write: if isSignedIn() && (
        isOfferOwner(offerId) ||
        isPostingOwner(get(/databases/$(database)/documents/offers/$(offerId)).data.postingId)
      );
    }

    // Add abusiveUsers collection rules
    match /abusiveUsers/{abusiveUserId} {
      allow create: if isSignedIn() && 
        request.resource.data.markedBy == request.auth.uid &&
        request.resource.data.markedAt != null &&
        request.resource.data.postingId != null &&
        request.resource.data.offerId != null &&
        request.resource.data.targetUserId != null &&
        (isPostingOwner(request.resource.data.postingId) || 
         isOfferOwner(request.resource.data.offerId) ||
         isSignedIn());
      
      allow read: if isSignedIn() && (
        resource.data.markedBy == request.auth.uid ||
        resource.data.targetUserId == request.auth.uid
      );
    }
  }
}