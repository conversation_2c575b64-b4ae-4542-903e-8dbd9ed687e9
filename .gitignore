# Learn more https://docs.github.com/en/get-started/getting-started-with-git/ignoring-files

# dependencies
node_modules/


# Expo
.expo/
dist/
web-build/

# Native
*.orig.*
*.jks
*.p8
*.p12
*.key
*.mobileprovision

# Metro
.metro-health-check*

# debug
npm-debug.*
yarn-debug.*
yarn-error.*

# macOS
.DS_Store
*.pem

# local env files
.env*.local

# typescript
*.tsbuildinfo

serviceAccountKey.json

# APNs Keys
ios/Keys/*.p8

# Firebase config files
ios/GoogleService-Info.plist
android/app/google-services.json

# Test coverage
jest/coverage/

#temporary notepad for copy pasting logs
documentation/tempNotepad.md


# Added from the code block
ios/Pods/FirebaseFirestore/Firestore/Source/API/FIRAggregateQuery.mm

mcp.json
mcp-server-simulator-ios-idb/
