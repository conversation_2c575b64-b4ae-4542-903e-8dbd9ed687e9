const { getDefaultConfig } = require('expo/metro-config');

const config = getDefaultConfig(__dirname, {
  isCSSEnabled: true,
});

config.resolver.assetExts.push('db');
config.resolver.sourceExts = process.env.RN_SRC_EXT
  ? [...process.env.RN_SRC_EXT.split(',').concat(config.resolver.sourceExts), 'mjs']
  : [...config.resolver.sourceExts, 'mjs'];

config.resolver.extraNodeModules = {
  ...config.resolver.extraNodeModules,
  // Add polyfills for Node.js core modules
  stream: require.resolve('stream-browserify'),
  crypto: require.resolve('crypto-browserify'),
};

module.exports = config;