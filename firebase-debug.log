[debug] [2024-12-18T09:14:03.391Z] ----------------------------------------------------------------------
[debug] [2024-12-18T09:14:03.391Z] Command:       /Users/<USER>/.nvm/versions/node/v18.20.4/bin/node /Users/<USER>/.nvm/versions/node/v18.20.4/bin/firebase init
[debug] [2024-12-18T09:14:03.392Z] CLI Version:   13.29.1
[debug] [2024-12-18T09:14:03.392Z] Platform:      darwin
[debug] [2024-12-18T09:14:03.392Z] Node Version:  v18.20.4
[debug] [2024-12-18T09:14:03.397Z] Time:          Wed Dec 18 2024 12:14:03 GMT+0300 (GMT+03:00)
[debug] [2024-12-18T09:14:03.397Z] ----------------------------------------------------------------------
[debug] 
[debug] [2024-12-18T09:14:03.400Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2024-12-18T09:14:03.400Z] > authorizing via signed-in user (<EMAIL>)
[info] 
     ######## #### ########  ######## ########     ###     ######  ########
     ##        ##  ##     ## ##       ##     ##  ##   ##  ##       ##
     ######    ##  ########  ######   ########  #########  ######  ######
     ##        ##  ##    ##  ##       ##     ## ##     ##       ## ##
     ##       #### ##     ## ######## ########  ##     ##  ######  ########

You're about to initialize a Firebase project in this directory:

  /Users/<USER>/Desktop/satbana

