this is the list of tests that should be perform the end to end tests for my mobile application, your task is to create a markdown file as a checklist so we don''t forget when performing tests.

Positive Case:
Negative Case:
Corner Case:

1. Launch the app

2. Login Screen

    - Forgot Password: 
        Positive Case: user clicks the Forgot Password? link and is taken to the Forgot Password screen.
        Negative Case: n/a
        Corner Case: n/a

    - Sign In: 
        Positive Case: user enters valid credentials and clicks Sign In and is taken to the Home screen.
        Negative Case: user enters wrong email or password and clicks Sign Up and sees the Sign in failed alert .
        Corner Case: n/a
    

    - Sign Up:    
        Positive Case: user clicks the Sign Up button and is taken to the Sign Up screen.
        Negative Case: n/a
        Corner Case: n/a

3. Forgot Password Screen: 

    - Reset Password: 
        Positive Case: user enters their email address and clicks Reset Password and receives the password reset email.
        Negative Case: n/a
        Corner Case: n/a

4. Sign Up Screen: 

    - Sign Up:

        Positive Case: user enters valid email, password, and confirm password and clicks Sign Up and is taken to the Sign Up Confirmation screen.
        Negative Case: n/a
        Corner Case: n/a 

    - Password Match: 

        Positive Case: user enters matching passwords and clicks Sign Up and is taken to the Sign Up Confirmation screen.
        Negative Case: user enters non-matching passwords and clicks Sign Up and sees the Passwords do not match alert.
        Corner Case: n/a

5. Sign Up Confirmation Screen: 

    Positive Case: user enters the valid confirmation code and is taken to the Home screen.
    Negative Case: user enters an invalid confirmation code and sees the Invalid confirmation code alert.
    Corner Case: n/a


6. Home Screen:

    - Panning and Search:
        
        Positive Case: the user is centered on the map with default zoom level. Postings within the displayed map area will be retrieved automatically only once and maintained on the map and list view, with respect to numberOfSearchResults parameter.real-time.
        Negative Case: n/a
        Corner Case: n/a


        Positive Case: When the user pans or zooms in/out of the map before performing a search action, the postings on the map and list will be updated automatically only for once, () for the updated map area once the panning/zooming stops, () with respect to numberOfSearchResults parameter ().
        Negative Case: n/a
        Corner Case: n/a

        Positive Case: When the user types text (e.g test) in search field regardless of zoom level, "Search This Area" button will appear and no queries will be sent to the db automatically.
        Negative Case: n/a
        Corner Case: n/a

        Positive Case: When the user clicks on "Search This Area" button, results will be retrieved only for once for the displayed map area with respect to numberOfSearchResults parameter(), and "Search This Area" button will disappear.()
        Negative Case: n/a
        Corner Case: n/a

        Positive Case: When the user deletes one letter from the search term (e.g tes), "Search This Area" button will appear (), no queries will be sent to the db to retrieve results. ()
        Negative Case: n/a
        Corner Case: n/a

        Positive Case: When the user clicks on "Search This Area" button results will be retrieved only for once for the displayed map area with respect to numberOfSearchResults parameter, and "Search This Area" button will disappear.  ()
        Negative Case: n/a
        Corner Case: n/a
        
        Positive Case: When the user adds one letter to the search term (e.g test1), "Search This Area" button will appear. ()
        Negative Case: n/a
        Corner Case: n/a

        Positive Case: When the user clicks on "Search This Area" button results will be retrieved only for once for the displayed map area with respect to numberOfSearchResults parameter (), and "Search This Area" button will disappear.  ()
        Negative Case: n/a
        Corner Case: n/a

        Positive Case: When the user deletes the search term completely by clicking the clear text button in the search field, results are retrieved only once for blank search with respect to numberOfSearchResults parameter (), and "Search This Area" button will disappear.  ()
        Negative Case: n/a
        Corner Case: n/a

        Positive Case: When the user pans the map while having a search term in the search field,  "Search This Area" button will appear. ()
        Negative Case: n/a
        Corner Case: n/a

        Positive Case: When the user pans the map while having a search term in the search field, no automatic query will be sent to the db. ()
        Negative Case: n/a
        Corner Case: n/a

        Positive Case: When the user pans the map while having a search term in the search field, results for previous search will be maintained in list view. ()
        Negative Case: n/a
        Corner Case: n/a

        Positive Case: When the user clicks on "Search This Area" button results will be retrieved only for once for the displayed map area with respect to numberOfSearchResults parameter (), and "Search This Area" button will disappear.  ()

    - Displaying Postings: 

        Displaying Postings via Map Markers:
            Positive Case: When the user clicks on a map marker, a callout bubble appears, displaying the posting''s title and a truncated part of the posting description and a favorite icon depending on whether the posting is favorited or not. ()
            Negative Case: n/a
            Corner Case: n/a

        Displaying Postings via List Container: 
            Postings are displayed in the list container with relevant details in coordination with the displayed map markers, including the title and description. Description text is displayed in a truncated format which only displays two lines of text.


        When the user taps on the callout bubble, the application navigates to the Posting Detail Screen, displaying detailed information about the selected posting.




7. Profile Screen:

    - Create Posting: 
        Positive Case: user clicks the Create Posting button and is taken to the Create Posting screen.
        Negative Case: n/a
        Corner Case: n/a

    -   My Postings: 
        Positive Case: user clicks the My Postings button and is taken to the My Postings screen.
        Negative Case: n/a
        Corner Case: n/a
    
    -  My Offers: 
        Positive Case: user clicks the My Offers button and is taken to the My Offers screen.
        Negative Case: n/a
        Corner Case: n/a

    -  My Favorites: 
        Positive Case: user clicks the My Favorites button and is taken to the My Favorites screen.
        Negative Case: n/a
        Corner Case: n/a

    -  Settings: 
        Positive Case: user clicks the Settings button and is taken to the Settings screen.
        Negative Case: n/a
        Corner Case: n/a

    -  Sign Out: 
        Positive Case: user clicks the Sign Out button and is taken to the Login screen.
        Negative Case: n/a
        Corner Case: n/a
            

    
8. Create Posting Screen: 

    Positive Case: user enters valid title, description, and location and clicks Submit Posting and is taken to the Home screen.
    Negative Case: user leaves any field empty and clicks Submit Posting and sees the Please fill in all fields alert 
    ***<<<<<<<<<<<<<<<***<<<<<<<<<<<<<<<***<<<<<<<<<<<<<<<***<<<<<<<<<<<<<<<***<<<<<<<<<<<<<<<***<<<<<<<<<<<<<<<***<<<<<<<<<<<<<<<
        >>>>>>>>>>>>>*** Currently only Validation Error is displayed - UPDATE REQUIRED ***<<<<<<<<<<<<<<<
        ***<<<<<<<<<<<<<<<***<<<<<<<<<<<<<<<***<<<<<<<<<<<<<<<***<<<<<<<<<<<<<<<***<<<<<<<<<<<<<<<
    Corner Case: n/a

9. Edit Posting Screen: 
    Positive Case: user edits the title, description, and location and clicks Save Changes and is taken to the Posting Detail screen.
    Negative Case: user leaves title/description field empty and clicks Save Changes and sees the Validation Error: Title/Description cannot be empty alert
    Corner Case: n/a



10. Posting Detail Screen: 

    - Viewing Posting Detail screen as Posting Owner:
        * At the top of the screen user sees the Posting Title, Edit Posting Button, Delete Posting Button, and Navigate to Posting on the Map Button.
        * Below the posting title, user sees the Posting Description.
        * Below the Posting Desctiption, user sees the Offers header.
        * Under the Offers header, user sees the Active and Deleted tabs.
        * Under the Active tab, user sees the list of active offers ordered by price in ascending order.
        * Under the Deleted tab, user sees the list of deleted offers ordered by price in ascending order.

        - Edit Posting:
            Positive Case: user clicks the edit posting button and is taken to the Edit Posting screen.
            Negative Case: n/a
            Corner Case: n/a

        - Delete Posting:
            Positive Case: 
                * User clicks the delete posting button and Confirm Delete alert is displayed. 
                * User clicks Delete and is taken to the Home screen.   
            Negative Case: 
            Corner Case: n/a
        
        - Navigate to Posting on the Map
            Positive Case: user clicks the navigate to posting on the map button and is taken to the Home screen with the posting displayed at the center on the map.
            Negative Case: n/a
            Corner Case: n/a
        
    - Viewing Posting Detail screen as Third Party User:
        * At the top of the screen user sees the Posting Title, Make Offer Button, Favorite Button, and Navigate to Posting on the Map Button.
        * Below the posting title, user sees the Posting Description.
        * Below the Posting Desctiption, user sees the Offers header.
        * Under the Offers header, user sees the Active and Deleted tabs.
        * Under the Active tab, user sees the list of active offers ordered by price in ascending order.
        * Under the Deleted tab, user sees the list of deleted offers ordered by price in ascending order.

        - Make Offer:
            Positive Case: user clicks the make offer button and is taken to the Make Offer screen.
            Negative Case: n/a
            Corner Case: n/a
        
        - Add to Favorites:
            Positive Case: 
                * user clicks the add to favorites button and the posting is added to their favorites.
                * favorite icon activated for the user in posting detail screen
            Negative Case: n/a
            Corner Case: n/a
        
        - Remove from Favorites:
            Positive Case: 
                * user clicks the remove from favorites button and the posting is removed from their favorites.
                * favorite icon deactivated for the user in posting detail screen
            Negative Case: n/a
            Corner Case: n/a

        - Navigate to Posting on the Map
            Positive Case: user clicks the navigate to posting on the map button and is taken to the Home screen with the posting displayed at the center on the map.
            Negative Case: n/a
            Corner Case: n/a

    - Viewing Posting Detail screen as Offer Owner:
        * At the top of the screen user sees the Posting Title, Edit Offer Button, Withdraw Offer Button, and Navigate to Posting on the Map Button.
        * Below the posting title, user sees the Posting Description.
        * Below the Posting Desctiption, user sees the Offers header.
        * Under the Offers header, user sees the Active and Deleted tabs.
        * Under the Active tab, user sees the list of active offers ordered by price in ascending order.
        * Under the Deleted tab, user sees the list of deleted offers ordered by price in ascending order.

        - Edit Offer:
            Positive Case: user clicks the edit offer button and is taken to the Edit Offer screen.
            Negative Case: n/a
            Corner Case: n/a

        - Remove from Favorites:
            Positive Case: 
                * user clicks the remove from favorites button and the posting is removed from their favorites.
                * favorite icon deactivated for the user in posting detail screen
            Negative Case: n/a
            Corner Case: n/a
        
        - Add to Favorites:
            Positive Case: 
                * user clicks the add to favorites button and the posting is added to their favorites.
                * favorite icon activated for the user in posting detail screen
            Negative Case: n/a
            Corner Case: n/a

        - Navigate to Posting on the Map
            Positive Case: user clicks the navigate to posting on the map button and is taken to the Home screen with the posting displayed at the center on the map.
            Negative Case: n/a
            Corner Case: n/a

        

11. Make Offer Screen:

    - Positive Case: 
        * user enters valid price and description and clicks Submit Offer and is taken to the Posting Detail screen.
        * posting is added to user''s favorites and favorite icon activated in posting detail screen
        * offer displayed immediately in the posting detail screen
        * offer displayed immediately in the My Offers screen
        * offer displayed immediately in the My Favorites screen if the posting is favorited
        
    - Negative Case: user leaves any field empty and clicks Submit Offer and sees the Please fill in all fields alert
    - Corner Case: user attempts to enter text in the price field and not allowed to enter text.



12. Offer Detail Screen: 

    - Viewing Offer Detail screen as Posting Owner for an Active Offer:

        - Posting Details Section: 
            * displays the Posting Details header along with a right-facing chevron to navigate to posting detail screen of the posting, also an expand/collapse down/up chevron button to expand/collapse the posting details.

                - Expanding /Collapsing Posting Details:
                    - Positive Case: 
                        * user clicks the expand/collapse button and the posting details is expanded/collapsed.
                        * when the posting details is expanded, the posting title and description and status is displayed.
                        * "Mark as Abusive" button is not displayed at the bottom of the posting details section to Posting owner.
                    - Negative Case: n/a
                    - Corner Case: n/a
            

        - Offer Details Section:
            * displays the Offer Details header along with an expand/collapse down/up chevron button to expand/collapse the offer details.
            * displays the User Score of the Offer Owner on the left of the chevron button.
            * Edit Offer and Withdraw Offer buttons are not displayed to the Posting Owner 

                - Expanding /Collapsing Offer Details:
                    - Positive Case: 
                        * user clicks the expand/collapse button and the offer details is expanded/collapsed.
                        * when the offer details is expanded, the offer price and description and status is displayed.
                        * when the offer details is expanded the "Mark as Abusive" button is displayed at the bottom of the offer details section.
                    - Negative Case: n/a
                    - Corner Case: n/a


                - Marking Offer Owner as Abusive:
                    - Positive Case: 
                        * user clicks the Mark as Abusive button and Mark User as Abusive alert is displayed along with text: "Are you sure you want to mark this user as abusive? This action will restrict their account from submitting offers to your postings for 30 days."
                        * user clicks the Cancel button and alert is dismissed.
                        * user clicks the Confirm button and Feature in Development alert is displayed along with text: "This feature is currently under development and will be available soon."
                        * user is taken back to the Offer Detail screen.
                    - Negative Case: n/a
                    - Corner Case: n/a

        - Messaging Section:
            
            - Viewing Discussion Messages:
                - Positive Case: 
                    * displays the messaging between the posting owner and offer owner.
                    * displays "No messages yet" text at the center of the screen if there were no messages sent for the offer.
                    * displays the timestamp of when the message was sent.
                    * displays the read receipt of when the message was read.
                    * posting owner messages are displayed on the right side of the screen
                    * offer owner messages are displayed on the left side of the screen
                - Negative Case: n/a
                - Corner Case: n/a

            - Sending a Message:

                - Positive Case: 
                    * user types a message in the text input field and clicks the Send Message button and the message is sent to the offer owner.
                    * user sees the message they sent in the messaging section.
                - Negative Case:
                    * Send button is disabled if the text input field is empty.
                - Corner Case: n/a


    - Viewing Offer Detail screen as Posting Owner for a withdrawn Offer:

        - Posting Details Section: 
            * displays the "This offer has been withdrawn" banner at the top of the screen
            * displays the Posting Details header along with a right-facing chevron to navigate to posting detail screen of the posting, also an expand/collapse down/up chevron button to expand/collapse the posting details.
                        
                - Expanding /Collapsing Posting Details:
                    - Positive Case: 
                        * user clicks the expand/collapse button and the posting details is expanded/collapsed.
                        * when the posting details is expanded, the posting title and description and status is displayed.
                        * "Mark as Abusive" button is not displayed at the bottom of the posting details section to Posting owner.
                    - Negative Case: n/a
                    - Corner Case: n/av
            

        - Offer Details Section:
            * displays the Offer Details header along with an expand/collapse down/up chevron button to expand/collapse the offer details.
            * displays the User Score of the Offer Owner on the left of the chevron button.

                - Expanding /Collapsing Offer Details:
                    - Positive Case: 
                        * user clicks the expand/collapse button and the offer details is expanded/collapsed.
                        * when the offer details is expanded, the offer price and description and status is displayed.
                        * when the offer details is expanded the "Mark as Abusive" button is not displayed at the bottom of the offer details section.
                    - Negative Case: n/a
                    - Corner Case: n/a


                - Marking Offer Owner as Abusive:
                    - Positive Case: 
                        * Markin Offer Owner as Abusive is not allowed for a withdrawn offer, Mark as Abusive button is not displayed.
                    - Negative Case: n/a
                    - Corner Case: n/a

        - Messaging Section:
            
            - Viewing Discussion Messages:
                - Positive Case: 
                    * displays the messaging between the posting owner and offer owner.
                    * displays "No messages available for this withdrawn offer" text at the center of the screen if there were no messages sent for the offer.
                    * displays the timestamp of when the message was sent.
                    * displays the read receipt of when the message was read.
                    * posting owner messages are displayed on the right side of the screen
                    * offer owner messages are displayed on the left side of the screen
                - Negative Case: n/a
                - Corner Case: n/a

            - Sending a Message:

                - Positive Case: 
                    * sending messages are disabled for withdrawn offers.
                    * user sees the "This offer has been withdrawn. Messages are disabled" message instead of text input field and send message button.
                - Negative Case: n/a
                - Corner Case: n/a
            

    

    
    - Viewing Offer Detail screen as Offer Owner for an Active Offer:

                
        - Posting Details Section: 
            * displays the Posting Details header along with a right-facing chevron to navigate to posting detail screen of the posting, also an expand/collapse down/up chevron button to expand/collapse the posting details.

                - Expanding /Collapsing Posting Details:
                    - Positive Case: 
                        * user clicks the expand/collapse button and the posting details is expanded/collapsed.
                        * when the posting details is expanded, the posting title and description and status is displayed.
                        * "Mark as Abusive" button is displayed at the bottom of the posting details section to Offer owner.
                    - Negative Case: n/a
                    - Corner Case: n/a

                - Marking Posting Owner as Abusive:
                    - Positive Case: 
                        * user clicks the Mark as Abusive button and Mark User as Abusive alert is displayed along with text: "Are you sure you want to mark this user as abusive? This action will restrict their account from submitting offers to your postings for 30 days."
                        * user clicks the Cancel button and alert is dismissed.
                        * user clicks the Confirm button and Feature in Development alert is displayed along with text: "This feature is currently under development and will be available soon."
                        * user is taken back to the Offer Detail screen.
                    - Negative Case: n/a
                    - Corner Case: n/a
            

        - Offer Details Section:
            * displays the Offer Details header along with an expand/collapse down/up chevron button to expand/collapse the offer details.
            * displays the User Score of the Offer Owner on the left of the chevron button.
            * displays the Edit Offer and Withdraw Offer buttons on the left of the User Score.
            * When the Edit Offer button is clicked, the user is taken to the Edit Offer screen.
            * When the Withdraw button is clicked, a confirmation alert is displayed. Upon confirming, the offer is withdrawn and the user is taken to the Posting Detail screen, user''s Favorite status of this Posting is remained.

                - Expanding /Collapsing Offer Details:
                    - Positive Case: 
                        * user clicks the expand/collapse button and the offer details is expanded/collapsed.
                        * when the offer details is expanded, the offer price and description and status is displayed.
                        * when the offer details is expanded the "Mark as Abusive" button is not displayed at the bottom of the offer details section to the Offer Owner.
                    - Negative Case: n/a
                    - Corner Case: n/a
                

        - Messaging Section:
            
            - Viewing Discussion Messages:
                - Positive Case: 
                    * displays the messaging between the posting owner and offer owner.
                    * displays "No messages yet" text at the center of the screen if there were no messages sent for the offer.
                    * displays the timestamp of when the message was sent.
                    * displays the read receipt of when the message was read.
                    * posting owner messages are displayed on the right side of the screen
                    * offer owner messages are displayed on the left side of the screen
                - Negative Case: n/a
                - Corner Case: n/a

            - Sending a Message:

                - Positive Case: 
                    * user types a message in the text input field and clicks the Send Message button and the message is sent to the offer owner.
                    * user sees the message they sent in the messaging section.
                - Negative Case:
                    * Send button is disabled if the text input field is empty.
                - Corner Case: n/a


    - Viewing Offer Detail screen as Offer Owner for a withdrawn Offer:

        - Posting Details Section: 
            * displays the "This offer has been withdrawn" banner at the top of the screen
            * displays the Posting Details header along with a right-facing chevron to navigate to posting detail screen of the posting, also an expand/collapse down/up chevron button to expand/collapse the posting details.
                        
                - Expanding /Collapsing Posting Details:
                    - Positive Case: 
                        * user clicks the expand/collapse button and the posting details is expanded/collapsed.
                        * when the posting details is expanded, the posting title and description and status is displayed.
                        * "Mark as Abusive" button is displayed at the bottom of the posting details section to Offer Owner.
                    - Negative Case: n/a
                    - Corner Case: n/av
            

        - Offer Details Section:
            * displays the Offer Details header along with an expand/collapse down/up chevron button to expand/collapse the offer details.
            * displays the User Score of the Offer Owner on the left of the chevron button.
            * Edit Offer and Withdraw Offer buttons are not displayed on the left of the User Score.
           
                - Expanding /Collapsing Offer Details:
                    - Positive Case: 
                        * user clicks the expand/collapse button and the offer details is expanded/collapsed.
                        * when the offer details is expanded, the offer price and description and status is displayed.
                        * when the offer details is expanded the "Mark as Abusive" button is not displayed at the bottom of the offer details section to the Offer Owner.
                    - Negative Case: n/a
                    - Corner Case: n/a

        - Messaging Section:
    
            - Viewing Discussion Messages:
                - Positive Case: 
                    * displays the messaging between the posting owner and offer owner.
                    * displays "No messages available for this withdrawn offer" text at the center of the screen if there were no messages sent for the offer.
                    * displays the timestamp of when the message was sent.
                    * displays the read receipt of when the message was read.
                    * posting owner messages are displayed on the right side of the screen
                    * offer owner messages are displayed on the left side of the screen
                - Negative Case: n/a
                - Corner Case: n/a

            - Sending a Message:

                - Positive Case: 
                    * sending messages are disabled for withdrawn offers.
                    * user sees the "This offer has been withdrawn. Messages are disabled" message instead of text input field and send message button.
                - Negative Case: n/a
                - Corner Case: n/a



    
 


    



