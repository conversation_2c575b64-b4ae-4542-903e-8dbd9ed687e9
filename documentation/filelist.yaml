omeryazici@Omers-MBP:~/Desktop/satbana|firebase-optimization/minimize-postingdetail-reads⚡ ⇒  tree -I "node_modules|ios|vendor"
.
├── App.tsx
├── CertificateSigningRequest.certSigningRequest
├── GoogleService-Info.plist
├── app.json
├── assets
│   ├── adaptive-icon.png
│   ├── favicon.png
│   ├── icon.png
│   └── splash.png
├── babel.config.js
├── components
│   ├── AbusiveUserButton.tsx
│   ├── DraggableListContainer.tsx
│   ├── ErrorBoundary.tsx
│   ├── FormInput.tsx
│   ├── ListItem.tsx
│   ├── MapMarker.tsx
│   ├── NotificationBell.tsx
│   ├── RestrictionMessage.tsx
│   ├── SearchInput.tsx
│   ├── UserScore.tsx
│   ├── offer-detail
│   │   ├── MessageInput.tsx
│   │   ├── MessageItem.tsx
│   │   ├── OfferDetailsSection.tsx
│   │   ├── PostingDetailsSection.tsx
│   │   ├── RefreshButton.tsx
│   │   ├── ScrollableContent.tsx
│   │   └── StatusBanner.tsx
│   └── styles.js
├── coverage
│   ├── clover.xml
│   ├── coverage-final.json
│   ├── lcov-report
│   │   ├── ProfileScreen.tsx.html
│   │   ├── base.css
│   │   ├── block-navigation.js
│   │   ├── components
│   │   │   ├── FormInput.tsx.html
│   │   │   ├── index.html
│   │   │   └── offer-detail
│   │   │       ├── MessageInput.tsx.html
│   │   │       ├── MessageItem.tsx.html
│   │   │       ├── OfferDetailsSection.tsx.html
│   │   │       ├── PostingDetailsSection.tsx.html
│   │   │       ├── ScrollableContent.tsx.html
│   │   │       ├── StatusBanner.tsx.html
│   │   │       └── index.html
│   │   ├── favicon.png
│   │   ├── hooks
│   │   │   ├── index.html
│   │   │   ├── useAuthUser.js.html
│   │   │   ├── useFetchUserPostings.js.html
│   │   │   ├── useFormValidation.js.html
│   │   │   ├── useOfferDetails.js.html
│   │   │   └── useWithdrawOffer.js.html
│   │   ├── index.html
│   │   ├── jest
│   │   │   └── mocks
│   │   │       ├── expoVectorIconsMock.js.html
│   │   │       ├── firebase
│   │   │       │   ├── firestore.ts.html
│   │   │       │   └── index.html
│   │   │       ├── firebaseMock.ts.html
│   │   │       └── index.html
│   │   ├── prettify.css
│   │   ├── prettify.js
│   │   ├── screens
│   │   │   ├── EditOffer.tsx.html
│   │   │   ├── LoginScreen.tsx.html
│   │   │   ├── MyPostingsScreen.tsx.html
│   │   │   ├── OfferDetail.tsx.html
│   │   │   ├── SignUp.tsx.html
│   │   │   ├── SignUpConfirmation.tsx.html
│   │   │   └── index.html
│   │   ├── services
│   │   │   ├── cacheManager.ts.html
│   │   │   ├── firebaseService.js.html
│   │   │   ├── index.html
│   │   │   └── notificationService.ts.html
│   │   ├── sort-arrow-sprite.png
│   │   ├── sorter.js
│   │   ├── types
│   │   │   ├── index.html
│   │   │   └── notifications.ts.html
│   │   └── utils
│   │       ├── cacheManager.ts.html
│   │       ├── dateUtils.ts.html
│   │       ├── firebaseCleanup.ts.html
│   │       └── index.html
│   └── lcov.info
├── development.cer
├── documentation
│   ├── # satbana-Change-Log.txt
│   ├── EXPO_TO_RN_MIGRATION.md
│   ├── FileStructure.md
│   ├── ProjectContext.md
│   ├── TEST_editoffer_message.txt
│   ├── TEST_editoffer_message2.txt
│   ├── assumption-behavior-analysis.md
│   ├── database_structure_analysis.md
│   ├── filelist.yaml
│   ├── grepsatbana.txt
│   ├── makeoffer_listenerresolution.txt
│   ├── monitoring
│   │   ├── firebase_usage_analysis_coverage.md
│   │   └── homescreen_baseline_metrics_2024_02_27.md
│   ├── offer-detail-screen-test-005.md
│   ├── personas.txt
│   ├── protocol.md
│   ├── protocol_woYOLO.md
│   ├── test-cases.txt
│   ├── test_logs
│   │   ├── updateoffer_01.txt
│   │   ├── updateoffer_02.txt
│   │   ├── updateoffer_03.txt
│   │   ├── updateoffer_04.txt
│   │   ├── updateoffer_05.txt
│   │   ├── updateoffer_06.txt
│   │   ├── updateoffer_07.txt
│   │   ├── updateoffer_08.txt
│   │   └── updateoffer_09.txt
│   ├── to-do
│   │   ├── FIREBASE_LISTENER_MIGRATION_notStarted.md
│   │   └── to-do.txt
│   └── userjourneys
│       ├── addremovefavorites.txt
│       ├── appfirstlaunch.txt
│       ├── createposting.txt
│       ├── deleteposting.txt
│       ├── discussionmessage.txt
│       ├── editoffer.txt
│       ├── editofferfrommyoffers.txt
│       ├── editposting.txt
│       ├── forgotpassword.txt
│       ├── listviewhomescreen.txt
│       ├── locationsettings.txt
│       ├── makeoffer.txt
│       ├── mapnavigationhomescreen.txt
│       ├── markuserabusive.txt
│       ├── mypostings.txt
│       ├── navigationtopostingdetailviamapmarker.txt
│       ├── notificationsettings.txt
│       ├── offerdetailscreen.txt
│       ├── postingdetailscreen.txt
│       ├── searchinhomescreen.txt
│       ├── signin.txt
│       ├── signout.txt
│       ├── signup.txt
│       └── withdrawoffer.txt
├── eas.json
├── firebase-debug.log
├── firebase.ts
├── firebaseConfig.ts
├── firestore.rules
├── functions
│   ├── firebase.json
│   ├── functions
│   │   ├── index.js
│   │   ├── package-lock.json
│   │   └── package.json
│   └── index.js
├── hooks
│   ├── useAddPosting.js
│   ├── useAuthUser.js
│   ├── useControlledPostings.js
│   ├── useFavorites.js
│   ├── useFavoritesData.js
│   ├── useFetchUserPostings.js
│   ├── useFirebaseSubscriptions.js
│   ├── useFormValidation.js
│   ├── useGestureHandler.js
│   ├── useLocationInitialization.js
│   ├── useNotificationSettings.ts
│   ├── useNotificationSetup.ts
│   ├── useNotifications.ts
│   ├── useOfferDetails.js
│   ├── useOfferSubmission.js
│   ├── useOffers.js
│   ├── usePostingDetails.js
│   ├── useProfileActions.js
│   ├── useSignIn.js
│   ├── useUserOffers.js
│   └── useWithdrawOffer.js
├── index.js
├── jest
│   ├── config
│   │   ├── babel.config.test.js
│   │   ├── jest.config.js
│   │   └── jest.d.ts
│   ├── mocks
│   │   ├── expoFontMock.js
│   │   ├── expoVectorIconsMock.js
│   │   ├── firebase
│   │   │   ├── firestore.ts
│   │   │   ├── firestoreMock.ts
│   │   │   └── postingService.ts
│   │   ├── firebaseMock.ts
│   │   ├── jest.mocks.ts
│   │   ├── mockFavorites.ts
│   │   ├── mockNotifications.ts
│   │   ├── mockPostings.ts
│   │   ├── notificationServiceMock.ts
│   │   ├── offerDetail
│   │   │   ├── mockNavigation.ts
│   │   │   ├── mockRoute.ts
│   │   │   └── mockServices.ts
│   │   ├── svgMock.js
│   │   └── useOfferDetailsMock.ts
│   ├── setup
│   │   ├── jest.env.js
│   │   ├── jest.environment.ts
│   │   ├── setup.js
│   │   └── testSetup.ts
│   └── tests
│       └── screens
│           ├── CreatePostingScreen.test.tsx
│           ├── EditOffer.test.tsx
│           ├── EditPostingScreen.test.tsx
│           ├── ForgotPassword.test.tsx
│           ├── HomeScreen.test.tsx
│           ├── LocationSettings.test.tsx
│           ├── LoginScreen.test.tsx
│           ├── MakeOffer.test.tsx
│           ├── MyFavoritesScreen.test.tsx
│           ├── MyOffersScreen.test.tsx
│           ├── MyPostingsScreen.test.tsx
│           ├── NotificationSettingsScreen.test.tsx
│           ├── NotificationsScreen.test.tsx
│           ├── OfferDetail.container.test.tsx
│           ├── OfferDetail.database.test.tsx
│           ├── OfferDetail.messaging.test.tsx
│           ├── OfferDetail.navigation.test.tsx
│           ├── OfferDetail.notifications.test.tsx
│           ├── OfferDetail.ui.test.tsx
│           ├── OfferDetail.withdrawal.test.tsx
│           ├── PostDeleteConfirmation.test.tsx
│           ├── PostingDetail.test.tsx
│           ├── ProfileScreen.test.tsx
│           ├── SettingsScreen.test.tsx
│           ├── SignUp.test.tsx
│           └── SignUpConfirmation.test.tsx
├── metro.config.js
├── navigation
│   ├── AppNavigator.tsx
│   └── index.tsx
├── package-lock.json
├── package.json
├── patches
│   └── react-native-reanimated
│       └── fix-version.patch
├── plugins
│   └── withPodfile.js
├── react-native.config.js
├── readMe.txt
├── reducers
│   └── postingDetailReducer.ts
├── satbana_temp_icon_1024.png
├── screens
│   ├── CreatePostingScreen.tsx
│   ├── EditOffer.tsx
│   ├── EditPostingScreen.tsx
│   ├── ForgotPassword.tsx
│   ├── HomeScreen.tsx
│   ├── LocationSettings.tsx
│   ├── LoginScreen.tsx
│   ├── MakeOffer.tsx
│   ├── MyFavoritesScreen.tsx
│   ├── MyOffersScreen.tsx
│   ├── MyPostingsScreen.tsx
│   ├── NotificationSettingsScreen.tsx
│   ├── NotificationsScreen.tsx
│   ├── OfferDetail.tsx
│   ├── PostDeleteConfirmation.tsx
│   ├── PostingDetail.tsx
│   ├── ProfileScreen.tsx
│   ├── SettingsScreen.tsx
│   ├── SignUp.tsx
│   └── SignUpConfirmation.tsx
├── services
│   ├── authService.ts
│   ├── cacheManager.ts
│   ├── fcmService.ts
│   ├── firebaseService.js
│   ├── locationService.js
│   ├── notificationHandlers.ts
│   ├── notificationQueueService.ts
│   └── notificationService.ts
├── test-outputs
├── tsconfig.json
├── types
│   ├── components.ts
│   ├── env.d.ts
│   ├── fcm.ts
│   ├── firebase.ts
│   ├── navigation.ts
│   ├── notifications.ts
│   ├── offer.ts
│   ├── posting.ts
│   └── subscription.ts
└── utils
    ├── cacheManager.ts
    ├── dateUtils.ts
    ├── debugTest.ts
    ├── errorLogger.ts
    ├── errorRecovery.ts
    ├── fcmHelper.ts
    ├── firebaseCleanup.ts
    ├── listenerRegistry.ts
    ├── notificationActionHandler.ts
    ├── notificationCategories.ts
    ├── notificationConfig.ts
    ├── notificationHelper.ts
    ├── notificationManager.ts
    ├── notificationTester.ts
    ├── pushNotifications.ts
    ├── retryUtils.ts
    └── timestampValidation.ts

42 directories, 277 files