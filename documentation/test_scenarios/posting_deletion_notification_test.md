# Posting Deletion Notification Test Scenario

## Overview
This test scenario is designed to verify that when a posting is deleted, all offer owners receive a notification about the deletion. The test will help identify potential issues with notification delivery during the posting deletion process.

## Prerequisites
- Two iOS simulators running the app
- User accounts:
  - Simulator 1: Posting owner account (e.g., <EMAIL>)
  - Simulator 2: Offer owner account (any other test account)
- A posting created by the posting owner
- An offer submitted on that posting by the offer owner

## Test Setup

### Step 1: Prepare Simulators
1. Launch two iOS simulators (e.g., iPhone SE and iPhone 14)
2. Install and run the app on both simulators

### Step 2: Create Test Data
1. **On Simulator 1 (Posting Owner)**:
   - Log in with the posting owner account
   - Create a new posting with a distinctive title (e.g., "Test Deletion Notification")
   - Note the posting ID (visible in the URL or logs)

2. **On Simulator 2 (Offer Owner)**:
   - Log in with the offer owner account
   - <PERSON>rowse to find the posting created in the previous step
   - Submit an offer on the posting
   - Note the offer ID (visible in logs)

## Test Execution

### Step 3: Delete the Posting
1. **On Simulator 1 (Posting Owner)**:
   - Navigate to the posting detail screen for the test posting
   - Click the trash bin icon to delete the posting
   - Confirm the deletion when prompted
   - Observe the logs during the deletion process

### Step 4: Check for Notification
1. **On Simulator 2 (Offer Owner)**:
   - Wait for a notification to appear
   - Check the notification center
   - Check the notification bell in the app
   - Navigate to the Notifications screen in the app
   - Observe the logs related to notification processing

## Expected Results
- The posting should be successfully deleted
- The offer owner should receive a notification about the posting deletion
- The notification should be visible in the notification center and in the app
- The notification should have the correct type (SYSTEM_WITHDRAWAL)
- Clicking the notification should navigate to the appropriate screen

## Log Collection
Collect logs from both simulators during the test:
1. Console logs from Simulator 1 during posting deletion
2. Console logs from Simulator 2 when receiving and processing the notification

## Troubleshooting Points
If the notification is not received, check the following in the logs:

1. **Notification Creation**:
   - Look for `[deletePosting] Creating notification for offer owner` logs
   - Verify notification document was created (`[deletePosting] Notification document created`)
   - Check notification results summary (`[deletePosting] Notification results`)

2. **Notification Delivery**:
   - Look for `[notificationService][SEND_START]` logs
   - Check if there are any `[notificationService][USER_NOT_FOUND]` or `[notificationService][NO_PUSH_TOKEN]` warnings
   - Verify push notification was sent (`[notificationService][PUSH_SENT]`)

3. **Notification Handling**:
   - Look for `[NotificationActionHandler] Handling notification` logs
   - Check for `[NotificationsScreen] Processing SYSTEM_WITHDRAWAL notification` logs
   - Verify navigation occurred correctly

4. **Race Conditions**:
   - Check the timing between batch commit and notification sending
   - Look for any cleanup operations that might interfere with notification processing

## Notes
- The enhanced logging added to the codebase will help identify where in the process any issues occur
- Pay special attention to the Promise.allSettled results to see if notifications are being created but failing to deliver
- Check for any race conditions between cleanup operations and notification processing
