omerya<PERSON><PERSON>@Omers-MBP satbana % git grep -i "satBana"
.env:FIREBASE_AUTH_DOMAIN=satbana-2052f.firebaseapp.com
.env:FIREBASE_PROJECT_ID=satbana-2052f
.env:FIREBASE_STORAGE_BUCKET=satbana-2052f.firebasestorage.app
.tasks/2025-01-22_026_fix-ios-build.md:- Unable to open base configuration file for Pods-satbana.debug.xcconfig
GoogleService-Info.plist:       <string>com.company.satbana</string>
GoogleService-Info.plist:       <string>satbana-2052f</string>
GoogleService-Info.plist:       <string>satbana-2052f.firebasestorage.app</string>
app.json:    "name": "satbana",
app.json:    "displayName": "satbana",
app.json:    "slug": "satbana",
app.json:      "bundleIdentifier": "com.company.satbana",
app.json:      "package": "com.company.satbana"
components/ListItem.tsx:// /Users/<USER>/Desktop/satbana/components/ListItem.tsx
coverage/clover.xml:      <file name="useFetchUserPostings.js" path="/Users/<USER>/Desktop/satbana/hooks/useFetchUserPostings.js">
coverage/clover.xml:      <file name="firebaseMock.ts" path="/Users/<USER>/Desktop/satbana/jest/mocks/firebaseMock.ts">
coverage/clover.xml:      <file name="firestore.ts" path="/Users/<USER>/Desktop/satbana/jest/mocks/firebase/firestore.ts">
coverage/clover.xml:      <file name="MyPostingsScreen.tsx" path="/Users/<USER>/Desktop/satbana/screens/MyPostingsScreen.tsx">
coverage/clover.xml:      <file name="firebaseService.js" path="/Users/<USER>/Desktop/satbana/services/firebaseService.js">
coverage/clover.xml:      <file name="notificationService.ts" path="/Users/<USER>/Desktop/satbana/services/notificationService.ts">
coverage/clover.xml:      <file name="notifications.ts" path="/Users/<USER>/Desktop/satbana/types/notifications.ts">
coverage/clover.xml:      <file name="cacheManager.ts" path="/Users/<USER>/Desktop/satbana/utils/cacheManager.ts">
coverage/clover.xml:      <file name="firebaseCleanup.ts" path="/Users/<USER>/Desktop/satbana/utils/firebaseCleanup.ts">
coverage/coverage-final.json:{"/Users/<USER>/Desktop/satbana/hooks/useFetchUserPostings.js": {"path":"/Users/<USER>/Desktop/satbana/hooks/useFetchUserPostings.js","statementMap":{"0":{"start":{"line":7,"column":36},"end":{"line":31,"column":1}},"1":{"start":{"line":8,"column":34},"end":{"line":8,"column":46}},"2":{"start":{"line":9,"column":22},"end":{"line":9,"column":38}},"3":{"start":{"line":11,"column":2},"end":{"line":28,"column":20}},"4":{"start":{"line":12,"column":4},"end":{"line":15,"column":5}},"5":{"start":{"line":13,"column":6},"end":{"line":13,"column":44}},"6":{"start":{"line":14,"column":6},"end":{"line":14,"column":13}},"7":{"start":{"line":18,"column":24},"end":{"line":20,"column":6}},"8":{"start":{"line":19,"column":6},"end":{"line":19,"column":32}},"9":{"start":{"line":23,"column":4},"end":{"line":27,"column":6}},"10":{"start":{"line":24,"column":6},"end":{"line":26,"column":7}},"11":{"start":{"line":25,"column":8},"end":{"line":25,"column":22}},"12":{"start":{"line":30,"column":2},"end":{"line":30,"column":18}}},"fnMap":{"0":{"name":"(anonymous_0)","decl":{"start":{"line":7,"column":36},"end":{"line":7,"column":37}},"loc":{"start":{"line":7,"column":42},"end":{"line":31,"column":1}},"line":7},"1":{"name":"(anonymous_1)","decl":{"start":{"line":11,"column":12},"end":{"line":11,"column":13}},"loc":{"start":{"line":11,"column":18},"end":{"line":28,"column":3}},"line":11},"2":{"name":"(anonymous_2)","decl":{"start":{"line":18,"column":59},"end":{"line":18,"column":60}},"loc":{"start":{"line":18,"column":77},"end":{"line":20,"column":5}},"line":18},"3":{"name":"(anonymous_3)","decl":{"start":{"line":23,"column":11},"end":{"line":23,"column":12}},"loc":{"start":{"line":23,"column":17},"end":{"line":27,"column":5}},"line":23}},"branchMap":{"0":{"loc":{"start":{"line":12,"column":4},"end":{"line":15,"column":5}},"type":"if","locations":[{"start":{"line":12,"column":4},"end":{"line":15,"column":5}},{"start":{},"end":{}}],"line":12},"1":{"loc":{"start":{"line":24,"column":6},"end":{"line":26,"column":7}},"type":"if","locations":[{"start":{"line":24,"column":6},"end":{"line":26,"column":7}},{"start":{},"end":{}}],"line":24}},"s":{"0":1,"1":0,"2":0,"3":0,"4":0,"5":0,"6":0,"7":0,"8":0,"9":0,"10":0,"11":0,"12":0},"f":{"0":0,"1":0,"2":0,"3":0},"b":{"0":[0,0],"1":[0,0]},"_coverageSchema":"1a1c01bbd47fc00a2c39e90264f33305004495a9","hash":"b479bbdab13af1a3f0a747777167990037e600a2"}
coverage/coverage-final.json:,"/Users/<USER>/Desktop/satbana/jest/mocks/firebaseMock.ts": {"path":"/Users/<USER>/Desktop/satbana/jest/mocks/firebaseMock.ts","statementMap":{"0":{"start":{"line":20,"column":15},"end":{"line":20,"column":24}},"1":{"start":{"line":21,"column":17},"end":{"line":31,"column":1}},"2":{"start":{"line":25,"column":4},"end":{"line":25,"column":38}},"3":{"start":{"line":27,"column":4},"end":{"line":27,"column":21}},"4":{"start":{"line":32,"column":16},"end":{"line":32,"column":25}},"5":{"start":{"line":35,"column":29},"end":{"line":45,"column":1}},"6":{"start":{"line":36,"column":16},"end":{"line":36,"column":20}},"7":{"start":{"line":37,"column":15},"end":{"line":43,"column":3}},"8":{"start":{"line":48,"column":26},"end":{"line":54,"column":1}},"9":{"start":{"line":52,"column":4},"end":{"line":52,"column":35}},"10":{"start":{"line":57,"column":23},"end":{"line":60,"column":2}},"11":{"start":{"line":58,"column":2},"end":{"line":58,"column":30}},"12":{"start":{"line":59,"column":2},"end":{"line":59,"column":19}},"13":{"start":{"line":63,"column":22},"end":{"line":88,"column":1}},"14":{"start":{"line":64,"column":29},"end":{"line":70,"column":3}},"15":{"start":{"line":71,"column":22},"end":{"line":76,"column":3}},"16":{"start":{"line":82,"column":33},"end":{"line":82,"column":57}},"17":{"start":{"line":90,"column":18},"end":{"line":90,"column":31}},"18":{"start":{"line":92,"column":20},"end":{"line":102,"column":1}},"19":{"start":{"line":96,"column":4},"end":{"line":96,"column":38}},"20":{"start":{"line":98,"column":4},"end":{"line":98,"column":21}},"21":{"start":{"line":104,"column":19},"end":{"line":104,"column":26}},"22":{"start":{"line":107,"column":36},"end":{"line":107,"column":57}},"23":{"start":{"line":107,"column":50},"end":{"line":107,"column":56}},"24":{"start":{"line":108,"column":31},"end":{"line":108,"column":54}},"25":{"start":{"line":108,"column":45},"end":{"line":108,"column":53}}},"fnMap":{"0":{"name":"(anonymous_0)","decl":{"start":{"line":23,"column":30},"end":{"line":23,"column":31}},"loc":{"start":{"line":23,"column":44},"end":{"line":28,"column":3}},"line":23},"1":{"name":"(anonymous_1)","decl":{"start":{"line":36,"column":10},"end":{"line":36,"column":11}},"loc":{"start":{"line":36,"column":16},"end":{"line":36,"column":20}},"line":36},"2":{"name":"(anonymous_2)","decl":{"start":{"line":37,"column":8},"end":{"line":37,"column":9}},"loc":{"start":{"line":37,"column":15},"end":{"line":43,"column":3}},"line":37},"3":{"name":"(anonymous_3)","decl":{"start":{"line":51,"column":19},"end":{"line":51,"column":20}},"loc":{"start":{"line":51,"column":31},"end":{"line":53,"column":3}},"line":51},"4":{"name":"(anonymous_4)","decl":{"start":{"line":57,"column":31},"end":{"line":57,"column":32}},"loc":{"start":{"line":57,"column":52},"end":{"line":60,"column":1}},"line":57},"5":{"name":"(anonymous_5)","decl":{"start":{"line":64,"column":22},"end":{"line":64,"column":23}},"loc":{"start":{"line":64,"column":29},"end":{"line":70,"column":3}},"line":64},"6":{"name":"(anonymous_6)","decl":{"start":{"line":71,"column":15},"end":{"line":71,"column":16}},"loc":{"start":{"line":71,"column":22},"end":{"line":76,"column":3}},"line":71},"7":{"name":"(anonymous_7)","decl":{"start":{"line":82,"column":27},"end":{"line":82,"column":28}},"loc":{"start":{"line":82,"column":33},"end":{"line":82,"column":57}},"line":82},"8":{"name":"(anonymous_8)","decl":{"start":{"line":94,"column":30},"end":{"line":94,"column":31}},"loc":{"start":{"line":94,"column":44},"end":{"line":99,"column":3}},"line":94},"9":{"name":"(anonymous_9)","decl":{"start":{"line":107,"column":44},"end":{"line":107,"column":45}},"loc":{"start":{"line":107,"column":50},"end":{"line":107,"column":56}},"line":107},"10":{"name":"(anonymous_10)","decl":{"start":{"line":108,"column":39},"end":{"line":108,"column":40}},"loc":{"start":{"line":108,"column":45},"end":{"line":108,"column":53}},"line":108}},"branchMap":{},"s":{"0":1,"1":1,"2":0,"3":0,"4":1,"5":1,"6":0,"7":0,"8":1,"9":0,"10":1,"11":0,"12":0,"13":1,"14":0,"15":0,"16":0,"17":1,"18":1,"19":0,"20":0,"21":1,"22":1,"23":0,"24":1,"25":0},"f":{"0":0,"1":0,"2":0,"3":0,"4":0,"5":0,"6":0,"7":0,"8":0,"9":0,"10":0},"b":{},"_coverageSchema":"1a1c01bbd47fc00a2c39e90264f33305004495a9","hash":"5d0872920343d7930fb0730f2335a4f559391966"}
coverage/coverage-final.json:,"/Users/<USER>/Desktop/satbana/jest/mocks/firebase/firestore.ts": {"path":"/Users/<USER>/Desktop/satbana/jest/mocks/firebase/firestore.ts","statementMap":{"0":{"start":{"line":5,"column":22},"end":{"line":9,"column":1}},"1":{"start":{"line":6,"column":16},"end":{"line":6,"column":38}},"2":{"start":{"line":12,"column":29},"end":{"line":35,"column":21}},"3":{"start":{"line":13,"column":16},"end":{"line":13,"column":20}},"4":{"start":{"line":14,"column":15},"end":{"line":30,"column":3}},"5":{"start":{"line":38,"column":26},"end":{"line":48,"column":18}},"6":{"start":{"line":42,"column":4},"end":{"line":42,"column":35}},"7":{"start":{"line":44,"column":28},"end":{"line":47,"column":4}},"8":{"start":{"line":51,"column":23},"end":{"line":56,"column":3}},"9":{"start":{"line":51,"column":38},"end":{"line":56,"column":1}},"10":{"start":{"line":58,"column":18},"end":{"line":58,"column":44}},"11":{"start":{"line":59,"column":18},"end":{"line":59,"column":44}},"12":{"start":{"line":60,"column":20},"end":{"line":60,"column":46}},"13":{"start":{"line":61,"column":18},"end":{"line":61,"column":44}},"14":{"start":{"line":62,"column":23},"end":{"line":62,"column":49}},"15":{"start":{"line":65,"column":23},"end":{"line":74,"column":2}},"16":{"start":{"line":66,"column":2},"end":{"line":72,"column":3}},"17":{"start":{"line":68,"column":4},"end":{"line":68,"column":32}},"18":{"start":{"line":69,"column":9},"end":{"line":72,"column":3}},"19":{"start":{"line":71,"column":4},"end":{"line":71,"column":34}},"20":{"start":{"line":73,"column":2},"end":{"line":73,"column":18}},"21":{"start":{"line":77,"column":0},"end":{"line":77,"column":64}},"22":{"start":{"line":78,"column":0},"end":{"line":78,"column":64}},"23":{"start":{"line":79,"column":0},"end":{"line":79,"column":65}},"24":{"start":{"line":81,"column":19},"end":{"line":81,"column":68}},"25":{"start":{"line":82,"column":20},"end":{"line":82,"column":66}},"26":{"start":{"line":83,"column":16},"end":{"line":83,"column":25}},"27":{"start":{"line":84,"column":19},"end":{"line":84,"column":68}},"28":{"start":{"line":85,"column":22},"end":{"line":85,"column":55}},"29":{"start":{"line":86,"column":22},"end":{"line":86,"column":55}},"30":{"start":{"line":87,"column":28},"end":{"line":87,"column":67}},"31":{"start":{"line":87,"column":42},"end":{"line":87,"column":66}},"32":{"start":{"line":90,"column":23},"end":{"line":95,"column":3}},"33":{"start":{"line":90,"column":38},"end":{"line":95,"column":1}},"34":{"start":{"line":136,"column":25},"end":{"line":136,"column":53}},"35":{"start":{"line":136,"column":39},"end":{"line":136,"column":52}}},"fnMap":{"0":{"name":"(anonymous_0)","decl":{"start":{"line":6,"column":10},"end":{"line":6,"column":11}},"loc":{"start":{"line":6,"column":16},"end":{"line":6,"column":38}},"line":6},"1":{"name":"(anonymous_1)","decl":{"start":{"line":13,"column":10},"end":{"line":13,"column":11}},"loc":{"start":{"line":13,"column":16},"end":{"line":13,"column":20}},"line":13},"2":{"name":"(anonymous_2)","decl":{"start":{"line":14,"column":8},"end":{"line":14,"column":9}},"loc":{"start":{"line":14,"column":15},"end":{"line":30,"column":3}},"line":14},"3":{"name":"(anonymous_3)","decl":{"start":{"line":41,"column":19},"end":{"line":41,"column":20}},"loc":{"start":{"line":41,"column":66},"end":{"line":43,"column":3}},"line":41},"4":{"name":"(anonymous_4)","decl":{"start":{"line":44,"column":22},"end":{"line":44,"column":23}},"loc":{"start":{"line":44,"column":28},"end":{"line":47,"column":4}},"line":44},"5":{"name":"(anonymous_5)","decl":{"start":{"line":51,"column":31},"end":{"line":51,"column":32}},"loc":{"start":{"line":51,"column":38},"end":{"line":56,"column":1}},"line":51},"6":{"name":"(anonymous_6)","decl":{"start":{"line":65,"column":31},"end":{"line":65,"column":32}},"loc":{"start":{"line":65,"column":57},"end":{"line":74,"column":1}},"line":65},"7":{"name":"(anonymous_7)","decl":{"start":{"line":73,"column":9},"end":{"line":73,"column":10}},"loc":{"start":{"line":73,"column":15},"end":{"line":73,"column":17}},"line":73},"8":{"name":"(anonymous_8)","decl":{"start":{"line":87,"column":36},"end":{"line":87,"column":37}},"loc":{"start":{"line":87,"column":42},"end":{"line":87,"column":66}},"line":87},"9":{"name":"(anonymous_9)","decl":{"start":{"line":90,"column":31},"end":{"line":90,"column":32}},"loc":{"start":{"line":90,"column":38},"end":{"line":95,"column":1}},"line":90},"10":{"name":"(anonymous_10)","decl":{"start":{"line":136,"column":33},"end":{"line":136,"column":34}},"loc":{"start":{"line":136,"column":39},"end":{"line":136,"column":52}},"line":136}},"branchMap":{"0":{"loc":{"start":{"line":66,"column":2},"end":{"line":72,"column":3}},"type":"if","locations":[{"start":{"line":66,"column":2},"end":{"line":72,"column":3}},{"start":{"line":69,"column":9},"end":{"line":72,"column":3}}],"line":66},"1":{"loc":{"start":{"line":69,"column":9},"end":{"line":72,"column":3}},"type":"if","locations":[{"start":{"line":69,"column":9},"end":{"line":72,"column":3}},{"start":{},"end":{}}],"line":69}},"s":{"0":1,"1":0,"2":1,"3":0,"4":0,"5":1,"6":0,"7":0,"8":1,"9":0,"10":1,"11":1,"12":1,"13":1,"14":1,"15":1,"16":0,"17":0,"18":0,"19":0,"20":0,"21":1,"22":1,"23":1,"24":1,"25":1,"26":1,"27":1,"28":1,"29":1,"30":1,"31":0,"32":1,"33":0,"34":1,"35":0},"f":{"0":0,"1":0,"2":0,"3":0,"4":0,"5":0,"6":0,"7":0,"8":0,"9":0,"10":0},"b":{"0":[0,0],"1":[0,0]},"_coverageSchema":"1a1c01bbd47fc00a2c39e90264f33305004495a9","hash":"77b8e35d2a730642f790648de588feaef0983f5c"}
coverage/coverage-final.json:,"/Users/<USER>/Desktop/satbana/screens/MyPostingsScreen.tsx": {"path":"/Users/<USER>/Desktop/satbana/screens/MyPostingsScreen.tsx","statementMap":{"0":{"start":{"line":18,"column":21},"end":{"line":18,"column":36}},"1":{"start":{"line":19,"column":21},"end":{"line":19,"column":43}},"2":{"start":{"line":21,"column":26},"end":{"line":29,"column":3}},"3":{"start":{"line":22,"column":4},"end":{"line":28,"column":7}},"4":{"start":{"line":31,"column":2},"end":{"line":57,"column":4}},"5":{"start":{"line":37,"column":34},"end":{"line":37,"column":41}},"6":{"start":{"line":39,"column":12},"end":{"line":50,"column":31}},"7":{"start":{"line":40,"column":29},"end":{"line":40,"column":50}},"8":{"start":{"line":60,"column":15},"end":{"line":92,"column":2}}},"fnMap":{"0":{"name":"MyPostingsScreen","decl":{"start":{"line":17,"column":9},"end":{"line":17,"column":25}},"loc":{"start":{"line":17,"column":47},"end":{"line":58,"column":1}},"line":17},"1":{"name":"(anonymous_1)","decl":{"start":{"line":21,"column":26},"end":{"line":21,"column":27}},"loc":{"start":{"line":21,"column":45},"end":{"line":29,"column":3}},"line":21},"2":{"name":"(anonymous_2)","decl":{"start":{"line":37,"column":24},"end":{"line":37,"column":25}},"loc":{"start":{"line":37,"column":34},"end":{"line":37,"column":41}},"line":37},"3":{"name":"(anonymous_3)","decl":{"start":{"line":38,"column":22},"end":{"line":38,"column":23}},"loc":{"start":{"line":39,"column":12},"end":{"line":50,"column":31}},"line":39},"4":{"name":"(anonymous_4)","decl":{"start":{"line":40,"column":23},"end":{"line":40,"column":24}},"loc":{"start":{"line":40,"column":29},"end":{"line":40,"column":50}},"line":40}},"branchMap":{"0":{"loc":{"start":{"line":24,"column":16},"end":{"line":24,"column":40}},"type":"binary-expr","locations":[{"start":{"line":24,"column":16},"end":{"line":24,"column":26}},{"start":{"line":24,"column":30},"end":{"line":24,"column":40}}],"line":24},"1":{"loc":{"start":{"line":25,"column":23},"end":{"line":25,"column":69}},"type":"binary-expr","locations":[{"start":{"line":25,"column":23},"end":{"line":25,"column":39}},{"start":{"line":25,"column":43},"end":{"line":25,"column":69}}],"line":25},"2":{"loc":{"start":{"line":34,"column":7},"end":{"line":55,"column":7}},"type":"cond-expr","locations":[{"start":{"line":35,"column":8},"end":{"line":52,"column":10}},{"start":{"line":54,"column":8},"end":{"line":54,"column":76}}],"line":34},"3":{"loc":{"start":{"line":41,"column":54},"end":{"line":41,"column":78}},"type":"binary-expr","locations":[{"start":{"line":41,"column":54},"end":{"line":41,"column":64}},{"start":{"line":41,"column":68},"end":{"line":41,"column":78}}],"line":41},"4":{"loc":{"start":{"line":45,"column":52},"end":{"line":45,"column":76}},"type":"binary-expr","locations":[{"start":{"line":45,"column":52},"end":{"line":45,"column":62}},{"start":{"line":45,"column":66},"end":{"line":45,"column":76}}],"line":45},"5":{"loc":{"start":{"line":47,"column":19},"end":{"line":47,"column":65}},"type":"binary-expr","locations":[{"start":{"line":47,"column":19},"end":{"line":47,"column":35}},{"start":{"line":47,"column":39},"end":{"line":47,"column":65}}],"line":47}},"s":{"0":10,"1":10,"2":10,"3":2,"4":10,"5":10,"6":10,"7":2,"8":1},"f":{"0":10,"1":2,"2":10,"3":10,"4":2},"b":{"0":[2,1],"1":[2,1],"2":[6,4],"3":[10,2],"4":[10,2],"5":[10,2]},"_coverageSchema":"1a1c01bbd47fc00a2c39e90264f33305004495a9","hash":"0c88cb6f7729be368dffc7a5706410410203358b"}
coverage/coverage-final.json:,"/Users/<USER>/Desktop/satbana/services/firebaseService.js": {"path":"/Users/<USER>/Desktop/satbana/services/firebaseService.js","statementMap":{"0":{"start":{"line":73,"column":32},"end":{"line":98,"column":1}},"1":{"start":{"line":74,"column":2},"end":{"line":97,"column":3}},"2":{"start":{"line":75,"column":24},"end":{"line":75,"column":50}},"3":{"start":{"line":76,"column":14},"end":{"line":80,"column":5}},"4":{"start":{"line":82,"column":26},"end":{"line":82,"column":42}},"5":{"start":{"line":83,"column":21},"end":{"line":88,"column":7}},"6":{"start":{"line":83,"column":52},"end":{"line":88,"column":5}},"7":{"start":{"line":90,"column":4},"end":{"line":93,"column":6}},"8":{"start":{"line":91,"column":6},"end":{"line":92,"column":31}},"9":{"start":{"line":95,"column":4},"end":{"line":95,"column":57}},"10":{"start":{"line":96,"column":4},"end":{"line":96,"column":14}},"11":{"start":{"line":102,"column":31},"end":{"line":133,"column":1}},"12":{"start":{"line":103,"column":20},"end":{"line":103,"column":44}},"13":{"start":{"line":104,"column":12},"end":{"line":104,"column":59}},"14":{"start":{"line":106,"column":2},"end":{"line":132,"column":5}},"15":{"start":{"line":107,"column":23},"end":{"line":109,"column":6}},"16":{"start":{"line":108,"column":31},"end":{"line":108,"column":51}},"17":{"start":{"line":111,"column":25},"end":{"line":113,"column":10}},"18":{"start":{"line":115,"column":23},"end":{"line":129,"column":6}},"19":{"start":{"line":116,"column":24},"end":{"line":116,"column":63}},"20":{"start":{"line":117,"column":26},"end":{"line":121,"column":7}},"21":{"start":{"line":123,"column":6},"end":{"line":128,"column":8}},"22":{"start":{"line":131,"column":4},"end":{"line":131,"column":25}},"23":{"start":{"line":136,"column":20},"end":{"line":136,"column":21}},"24":{"start":{"line":137,"column":20},"end":{"line":137,"column":24}},"25":{"start":{"line":139,"column":27},"end":{"line":192,"column":1}},"26":{"start":{"line":140,"column":19},"end":{"line":140,"column":20}},"27":{"start":{"line":142,"column":28},"end":{"line":184,"column":3}},"28":{"start":{"line":143,"column":4},"end":{"line":183,"column":5}},"29":{"start":{"line":144,"column":6},"end":{"line":144,"column":68}},"30":{"start":{"line":146,"column":22},"end":{"line":146,"column":24}},"31":{"start":{"line":147,"column":24},"end":{"line":147,"column":26}},"32":{"start":{"line":149,"column":6},"end":{"line":152,"column":7}},"33":{"start":{"line":149,"column":19},"end":{"line":149,"column":20}},"34":{"start":{"line":150,"column":22},"end":{"line":150,"column":56}},"35":{"start":{"line":151,"column":8},"end":{"line":151,"column":28}},"36":{"start":{"line":154,"column":6},"end":{"line":154,"column":54}},"37":{"start":{"line":156,"column":22},"end":{"line":166,"column":7}},"38":{"start":{"line":158,"column":10},"end":{"line":158,"column":71}},"39":{"start":{"line":159,"column":20},"end":{"line":162,"column":11}},"40":{"start":{"line":163,"column":27},"end":{"line":163,"column":43}},"41":{"start":{"line":164,"column":10},"end":{"line":164,"column":31}},"42":{"start":{"line":168,"column":27},"end":{"line":171,"column":12}},"43":{"start":{"line":169,"column":8},"end":{"line":169,"column":33}},"44":{"start":{"line":170,"column":8},"end":{"line":170,"column":19}},"45":{"start":{"line":173,"column":6},"end":{"line":173,"column":94}},"46":{"start":{"line":174,"column":6},"end":{"line":174,"column":26}},"47":{"start":{"line":176,"column":6},"end":{"line":181,"column":7}},"48":{"start":{"line":177,"column":8},"end":{"line":177,"column":21}},"49":{"start":{"line":178,"column":8},"end":{"line":178,"column":93}},"50":{"start":{"line":179,"column":8},"end":{"line":179,"column":84}},"51":{"start":{"line":179,"column":37},"end":{"line":179,"column":82}},"52":{"start":{"line":180,"column":8},"end":{"line":180,"column":35}},"53":{"start":{"line":182,"column":6},"end":{"line":182,"column":18}},"54":{"start":{"line":186,"column":2},"end":{"line":191,"column":3}},"55":{"start":{"line":187,"column":4},"end":{"line":187,"column":37}},"56":{"start":{"line":189,"column":4},"end":{"line":189,"column":75}},"57":{"start":{"line":190,"column":4},"end":{"line":190,"column":14}},"58":{"start":{"line":195,"column":33},"end":{"line":199,"column":1}},"59":{"start":{"line":196,"column":21},"end":{"line":196,"column":51}},"60":{"start":{"line":197,"column":26},"end":{"line":197,"column":50}},"61":{"start":{"line":198,"column":2},"end":{"line":198,"column":66}},"62":{"start":{"line":202,"column":31},"end":{"line":209,"column":1}},"63":{"start":{"line":203,"column":21},"end":{"line":203,"column":51}},"64":{"start":{"line":204,"column":26},"end":{"line":204,"column":50}},"65":{"start":{"line":205,"column":2},"end":{"line":207,"column":3}},"66":{"start":{"line":206,"column":4},"end":{"line":206,"column":49}},"67":{"start":{"line":208,"column":2},"end":{"line":208,"column":14}},"68":{"start":{"line":212,"column":31},"end":{"line":214,"column":1}},"69":{"start":{"line":213,"column":2},"end":{"line":213,"column":85}},"70":{"start":{"line":217,"column":37},"end":{"line":270,"column":1}},"71":{"start":{"line":218,"column":2},"end":{"line":269,"column":3}},"72":{"start":{"line":219,"column":24},"end":{"line":219,"column":40}},"73":{"start":{"line":220,"column":4},"end":{"line":222,"column":5}},"74":{"start":{"line":221,"column":6},"end":{"line":221,"column":48}},"75":{"start":{"line":224,"column":29},"end":{"line":227,"column":5}},"76":{"start":{"line":229,"column":31},"end":{"line":229,"column":62}},"77":{"start":{"line":231,"column":4},"end":{"line":257,"column":5}},"78":{"start":{"line":232,"column":28},"end":{"line":232,"column":54}},"79":{"start":{"line":233,"column":29},"end":{"line":233,"column":49}},"80":{"start":{"line":235,"column":6},"end":{"line":256,"column":7}},"81":{"start":{"line":237,"column":8},"end":{"line":248,"column":9}},"82":{"start":{"line":238,"column":30},"end":{"line":240,"column":11}},"83":{"start":{"line":239,"column":12},"end":{"line":239,"column":60}},"84":{"start":{"line":242,"column":10},"end":{"line":247,"column":12}},"85":{"start":{"line":251,"column":8},"end":{"line":255,"column":10}},"86":{"start":{"line":259,"column":4},"end":{"line":265,"column":6}},"87":{"start":{"line":267,"column":4},"end":{"line":267,"column":53}},"88":{"start":{"line":268,"column":4},"end":{"line":268,"column":16}},"89":{"start":{"line":273,"column":38},"end":{"line":273,"column":59}},"90":{"start":{"line":276,"column":37},"end":{"line":330,"column":1}},"91":{"start":{"line":277,"column":2},"end":{"line":329,"column":3}},"92":{"start":{"line":278,"column":4},"end":{"line":278,"column":57}},"93":{"start":{"line":279,"column":4},"end":{"line":279,"column":71}},"94":{"start":{"line":280,"column":4},"end":{"line":280,"column":56}},"95":{"start":{"line":283,"column":21},"end":{"line":287,"column":6}},"96":{"start":{"line":288,"column":4},"end":{"line":288,"column":55}},"97":{"start":{"line":291,"column":27},"end":{"line":297,"column":5}},"98":{"start":{"line":299,"column":26},"end":{"line":299,"column":85}},"99":{"start":{"line":300,"column":4},"end":{"line":300,"column":65}},"100":{"start":{"line":303,"column":29},"end":{"line":311,"column":5}},"101":{"start":{"line":313,"column":4},"end":{"line":313,"column":60}},"102":{"start":{"line":314,"column":4},"end":{"line":314,"column":53}},"103":{"start":{"line":317,"column":4},"end":{"line":323,"column":5}},"104":{"start":{"line":318,"column":22},"end":{"line":318,"column":60}},"105":{"start":{"line":319,"column":6},"end":{"line":321,"column":9}},"106":{"start":{"line":322,"column":6},"end":{"line":322,"column":44}},"107":{"start":{"line":325,"column":4},"end":{"line":325,"column":23}},"108":{"start":{"line":327,"column":4},"end":{"line":327,"column":73}},"109":{"start":{"line":328,"column":4},"end":{"line":328,"column":16}},"110":{"start":{"line":333,"column":38},"end":{"line":406,"column":1}},"111":{"start":{"line":334,"column":2},"end":{"line":405,"column":3}},"112":{"start":{"line":335,"column":4},"end":{"line":335,"column":56}},"113":{"start":{"line":336,"column":4},"end":{"line":336,"column":37}},"114":{"start":{"line":337,"column":4},"end":{"line":337,"column":46}},"115":{"start":{"line":339,"column":29},"end":{"line":342,"column":5}},"116":{"start":{"line":344,"column":31},"end":{"line":344,"column":62}},"117":{"start":{"line":347,"column":4},"end":{"line":380,"column":5}},"118":{"start":{"line":349,"column":6},"end":{"line":349,"column":47}},"119":{"start":{"line":350,"column":28},"end":{"line":350,"column":54}},"120":{"start":{"line":351,"column":6},"end":{"line":351,"column":38}},"121":{"start":{"line":352,"column":28},"end":{"line":352,"column":64}},"122":{"start":{"line":355,"column":35},"end":{"line":359,"column":7}},"123":{"start":{"line":361,"column":6},"end":{"line":364,"column":9}},"124":{"start":{"line":367,"column":6},"end":{"line":367,"column":45}},"125":{"start":{"line":368,"column":28},"end":{"line":376,"column":7}},"126":{"start":{"line":378,"column":28},"end":{"line":378,"column":86}},"127":{"start":{"line":379,"column":6},"end":{"line":379,"column":38}},"128":{"start":{"line":383,"column":21},"end":{"line":383,"column":61}},"129":{"start":{"line":384,"column":4},"end":{"line":399,"column":5}},"130":{"start":{"line":385,"column":24},"end":{"line":385,"column":39}},"131":{"start":{"line":386,"column":25},"end":{"line":386,"column":79}},"132":{"start":{"line":387,"column":26},"end":{"line":389,"column":26}},"133":{"start":{"line":391,"column":6},"end":{"line":398,"column":9}},"134":{"start":{"line":401,"column":4},"end":{"line":401,"column":24}},"135":{"start":{"line":403,"column":4},"end":{"line":403,"column":50}},"136":{"start":{"line":404,"column":4},"end":{"line":404,"column":16}},"137":{"start":{"line":409,"column":33},"end":{"line":414,"column":3}},"138":{"start":{"line":410,"column":21},"end":{"line":410,"column":47}},"139":{"start":{"line":411,"column":4},"end":{"line":413,"column":7}},"140":{"start":{"line":417,"column":28},"end":{"line":444,"column":1}},"141":{"start":{"line":418,"column":16},"end":{"line":418,"column":48}},"142":{"start":{"line":419,"column":22},"end":{"line":419,"column":31}},"143":{"start":{"line":422,"column":2},"end":{"line":432,"column":5}},"144":{"start":{"line":424,"column":4},"end":{"line":424,"column":26}},"145":{"start":{"line":427,"column":4},"end":{"line":431,"column":5}},"146":{"start":{"line":427,"column":17},"end":{"line":427,"column":18}},"147":{"start":{"line":428,"column":6},"end":{"line":430,"column":7}},"148":{"start":{"line":428,"column":19},"end":{"line":428,"column":24}},"149":{"start":{"line":429,"column":8},"end":{"line":429,"column":42}},"150":{"start":{"line":435,"column":2},"end":{"line":438,"column":3}},"151":{"start":{"line":435,"column":15},"end":{"line":435,"column":16}},"152":{"start":{"line":436,"column":19},"end":{"line":436,"column":48}},"153":{"start":{"line":437,"column":4},"end":{"line":437,"column":28}},"154":{"start":{"line":441,"column":2},"end":{"line":441,"column":39}},"155":{"start":{"line":443,"column":2},"end":{"line":443,"column":33}},"156":{"start":{"line":447,"column":26},"end":{"line":467,"column":1}},"157":{"start":{"line":448,"column":2},"end":{"line":466,"column":3}},"158":{"start":{"line":449,"column":24},"end":{"line":449,"column":50}},"159":{"start":{"line":452,"column":32},"end":{"line":458,"column":5}},"160":{"start":{"line":460,"column":19},"end":{"line":460,"column":65}},"161":{"start":{"line":461,"column":4},"end":{"line":461,"column":53}},"162":{"start":{"line":462,"column":4},"end":{"line":462,"column":21}},"163":{"start":{"line":464,"column":4},"end":{"line":464,"column":50}},"164":{"start":{"line":465,"column":4},"end":{"line":465,"column":16}},"165":{"start":{"line":470,"column":33},"end":{"line":476,"column":3}},"166":{"start":{"line":471,"column":14},"end":{"line":471,"column":119}},"167":{"start":{"line":472,"column":4},"end":{"line":475,"column":7}},"168":{"start":{"line":473,"column":27},"end":{"line":473,"column":86}},"169":{"start":{"line":473,"column":55},"end":{"line":473,"column":84}},"170":{"start":{"line":474,"column":6},"end":{"line":474,"column":29}},"171":{"start":{"line":479,"column":37},"end":{"line":486,"column":1}},"172":{"start":{"line":480,"column":20},"end":{"line":480,"column":44}},"173":{"start":{"line":481,"column":12},"end":{"line":481,"column":101}},"174":{"start":{"line":482,"column":2},"end":{"line":485,"column":5}},"175":{"start":{"line":483,"column":19},"end":{"line":483,"column":78}},"176":{"start":{"line":483,"column":47},"end":{"line":483,"column":76}},"177":{"start":{"line":484,"column":4},"end":{"line":484,"column":21}},"178":{"start":{"line":489,"column":29},"end":{"line":576,"column":1}},"179":{"start":{"line":490,"column":2},"end":{"line":490,"column":59}},"180":{"start":{"line":491,"column":2},"end":{"line":491,"column":39}},"181":{"start":{"line":493,"column":16},"end":{"line":493,"column":30}},"182":{"start":{"line":495,"column":2},"end":{"line":575,"column":3}},"183":{"start":{"line":497,"column":4},"end":{"line":497,"column":57}},"184":{"start":{"line":498,"column":24},"end":{"line":502,"column":5}},"185":{"start":{"line":504,"column":27},"end":{"line":504,"column":53}},"186":{"start":{"line":505,"column":4},"end":{"line":505,"column":73}},"187":{"start":{"line":508,"column":23},"end":{"line":508,"column":53}},"188":{"start":{"line":509,"column":23},"end":{"line":509,"column":47}},"189":{"start":{"line":511,"column":4},"end":{"line":513,"column":5}},"190":{"start":{"line":512,"column":6},"end":{"line":512,"column":43}},"191":{"start":{"line":515,"column":24},"end":{"line":515,"column":41}},"192":{"start":{"line":517,"column":27},"end":{"line":517,"column":45}},"193":{"start":{"line":518,"column":25},"end":{"line":518,"column":64}},"194":{"start":{"line":521,"column":4},"end":{"line":521,"column":49}},"195":{"start":{"line":522,"column":4},"end":{"line":525,"column":7}},"196":{"start":{"line":528,"column":33},"end":{"line":528,"column":35}},"197":{"start":{"line":529,"column":4},"end":{"line":560,"column":7}},"198":{"start":{"line":530,"column":24},"end":{"line":530,"column":39}},"199":{"start":{"line":531,"column":23},"end":{"line":531,"column":53}},"200":{"start":{"line":533,"column":6},"end":{"line":533,"column":56}},"201":{"start":{"line":536,"column":6},"end":{"line":540,"column":9}},"202":{"start":{"line":543,"column":31},"end":{"line":557,"column":7}},"203":{"start":{"line":559,"column":6},"end":{"line":559,"column":76}},"204":{"start":{"line":563,"column":4},"end":{"line":563,"column":47}},"205":{"start":{"line":564,"column":4},"end":{"line":564,"column":25}},"206":{"start":{"line":567,"column":4},"end":{"line":567,"column":60}},"207":{"start":{"line":568,"column":4},"end":{"line":568,"column":44}},"208":{"start":{"line":570,"column":4},"end":{"line":570,"column":61}},"209":{"start":{"line":571,"column":4},"end":{"line":571,"column":16}},"210":{"start":{"line":573,"column":4},"end":{"line":573,"column":52}},"211":{"start":{"line":574,"column":4},"end":{"line":574,"column":16}},"212":{"start":{"line":580,"column":30},"end":{"line":591,"column":1}},"213":{"start":{"line":581,"column":2},"end":{"line":590,"column":3}},"214":{"start":{"line":582,"column":20},"end":{"line":582,"column":44}},"215":{"start":{"line":583,"column":4},"end":{"line":585,"column":7}},"216":{"start":{"line":586,"column":4},"end":{"line":586,"column":58}},"217":{"start":{"line":588,"column":4},"end":{"line":588,"column":55}},"218":{"start":{"line":589,"column":4},"end":{"line":589,"column":16}},"219":{"start":{"line":594,"column":35},"end":{"line":605,"column":1}},"220":{"start":{"line":595,"column":2},"end":{"line":604,"column":3}},"221":{"start":{"line":596,"column":20},"end":{"line":596,"column":44}},"222":{"start":{"line":597,"column":4},"end":{"line":599,"column":7}},"223":{"start":{"line":600,"column":4},"end":{"line":600,"column":62}},"224":{"start":{"line":602,"column":4},"end":{"line":602,"column":59}},"225":{"start":{"line":603,"column":4},"end":{"line":603,"column":16}},"226":{"start":{"line":608,"column":32},"end":{"line":620,"column":1}},"227":{"start":{"line":609,"column":2},"end":{"line":619,"column":3}},"228":{"start":{"line":610,"column":20},"end":{"line":610,"column":44}},"229":{"start":{"line":611,"column":20},"end":{"line":611,"column":41}},"230":{"start":{"line":612,"column":4},"end":{"line":614,"column":5}},"231":{"start":{"line":613,"column":6},"end":{"line":613,"column":44}},"232":{"start":{"line":615,"column":4},"end":{"line":615,"column":14}},"233":{"start":{"line":617,"column":4},"end":{"line":617,"column":59}},"234":{"start":{"line":618,"column":4},"end":{"line":618,"column":16}},"235":{"start":{"line":623,"column":36},"end":{"line":669,"column":1}},"236":{"start":{"line":624,"column":2},"end":{"line":668,"column":3}},"237":{"start":{"line":625,"column":19},"end":{"line":625,"column":49}},"238":{"start":{"line":628,"column":28},"end":{"line":628,"column":48}},"239":{"start":{"line":629,"column":25},"end":{"line":629,"column":47}},"240":{"start":{"line":632,"column":4},"end":{"line":635,"column":7}},"241":{"start":{"line":638,"column":21},"end":{"line":638,"column":44}},"242":{"start":{"line":639,"column":14},"end":{"line":639,"column":78}},"243":{"start":{"line":640,"column":26},"end":{"line":640,"column":42}},"244":{"start":{"line":643,"column":33},"end":{"line":661,"column":6}},"245":{"start":{"line":644,"column":22},"end":{"line":644,"column":24}},"246":{"start":{"line":645,"column":6},"end":{"line":645,"column":74}},"247":{"start":{"line":645,"column":52},"end":{"line":645,"column":74}},"248":{"start":{"line":646,"column":6},"end":{"line":646,"column":92}},"249":{"start":{"line":646,"column":64},"end":{"line":646,"column":92}},"250":{"start":{"line":647,"column":6},"end":{"line":647,"column":74}},"251":{"start":{"line":647,"column":51},"end":{"line":647,"column":74}},"252":{"start":{"line":649,"column":6},"end":{"line":660,"column":7}},"253":{"start":{"line":650,"column":33},"end":{"line":657,"column":9}},"254":{"start":{"line":659,"column":8},"end":{"line":659,"column":57}},"255":{"start":{"line":663,"column":4},"end":{"line":663,"column":44}},"256":{"start":{"line":664,"column":4},"end":{"line":664,"column":60}},"257":{"start":{"line":666,"column":4},"end":{"line":666,"column":52}},"258":{"start":{"line":667,"column":4},"end":{"line":667,"column":16}},"259":{"start":{"line":672,"column":34},"end":{"line":679,"column":1}},"260":{"start":{"line":673,"column":2},"end":{"line":675,"column":3}},"261":{"start":{"line":674,"column":4},"end":{"line":674,"column":48}},"262":{"start":{"line":677,"column":21},"end":{"line":677,"column":42}},"263":{"start":{"line":678,"column":2},"end":{"line":678,"column":62}},"264":{"start":{"line":682,"column":40},"end":{"line":689,"column":1}},"265":{"start":{"line":683,"column":18},"end":{"line":683,"column":42}},"266":{"start":{"line":684,"column":2},"end":{"line":688,"column":5}},"267":{"start":{"line":685,"column":4},"end":{"line":687,"column":5}},"268":{"start":{"line":686,"column":6},"end":{"line":686,"column":47}},"269":{"start":{"line":692,"column":32},"end":{"line":711,"column":1}},"270":{"start":{"line":693,"column":2},"end":{"line":710,"column":3}},"271":{"start":{"line":694,"column":17},"end":{"line":694,"column":33}},"272":{"start":{"line":695,"column":4},"end":{"line":697,"column":5}},"273":{"start":{"line":696,"column":6},"end":{"line":696,"column":44}},"274":{"start":{"line":699,"column":29},"end":{"line":699,"column":60}},"275":{"start":{"line":700,"column":4},"end":{"line":705,"column":7}},"276":{"start":{"line":706,"column":4},"end":{"line":706,"column":47}},"277":{"start":{"line":708,"column":4},"end":{"line":708,"column":51}},"278":{"start":{"line":709,"column":4},"end":{"line":709,"column":16}},"279":{"start":{"line":715,"column":33},"end":{"line":744,"column":1}},"280":{"start":{"line":716,"column":2},"end":{"line":743,"column":3}},"281":{"start":{"line":717,"column":17},"end":{"line":717,"column":33}},"282":{"start":{"line":718,"column":4},"end":{"line":720,"column":5}},"283":{"start":{"line":719,"column":6},"end":{"line":719,"column":44}},"284":{"start":{"line":722,"column":29},"end":{"line":722,"column":60}},"285":{"start":{"line":723,"column":14},"end":{"line":728,"column":5}},"286":{"start":{"line":730,"column":26},"end":{"line":730,"column":42}},"287":{"start":{"line":731,"column":4},"end":{"line":739,"column":5}},"288":{"start":{"line":732,"column":29},"end":{"line":732,"column":57}},"289":{"start":{"line":733,"column":6},"end":{"line":736,"column":8}},"290":{"start":{"line":738,"column":6},"end":{"line":738,"column":48}},"291":{"start":{"line":741,"column":4},"end":{"line":741,"column":53}},"292":{"start":{"line":742,"column":4},"end":{"line":742,"column":16}},"293":{"start":{"line":750,"column":32},"end":{"line":773,"column":1}},"294":{"start":{"line":757,"column":2},"end":{"line":772,"column":3}},"295":{"start":{"line":759,"column":19},"end":{"line":765,"column":5}},"296":{"start":{"line":768,"column":4},"end":{"line":768,"column":33}},"297":{"start":{"line":770,"column":4},"end":{"line":770,"column":64}},"298":{"start":{"line":771,"column":4},"end":{"line":771,"column":16}},"299":{"start":{"line":777,"column":37},"end":{"line":837,"column":1}},"300":{"start":{"line":784,"column":2},"end":{"line":836,"column":3}},"301":{"start":{"line":785,"column":24},"end":{"line":785,"column":50}},"302":{"start":{"line":788,"column":28},"end":{"line":796,"column":5}},"303":{"start":{"line":799,"column":4},"end":{"line":804,"column":5}},"304":{"start":{"line":800,"column":26},"end":{"line":800,"column":50}},"305":{"start":{"line":801,"column":6},"end":{"line":803,"column":8}},"306":{"start":{"line":806,"column":24},"end":{"line":810,"column":5}},"307":{"start":{"line":812,"column":4},"end":{"line":814,"column":5}},"308":{"start":{"line":813,"column":6},"end":{"line":813,"column":71}},"309":{"start":{"line":816,"column":26},"end":{"line":816,"column":54}},"310":{"start":{"line":817,"column":4},"end":{"line":817,"column":61}},"311":{"start":{"line":819,"column":24},"end":{"line":826,"column":7}},"312":{"start":{"line":819,"column":55},"end":{"line":826,"column":5}},"313":{"start":{"line":828,"column":4},"end":{"line":832,"column":6}},"314":{"start":{"line":834,"column":4},"end":{"line":834,"column":53}},"315":{"start":{"line":835,"column":4},"end":{"line":835,"column":43}},"316":{"start":{"line":840,"column":34},"end":{"line":879,"column":1}},"317":{"start":{"line":841,"column":2},"end":{"line":878,"column":3}},"318":{"start":{"line":843,"column":21},"end":{"line":843,"column":47}},"319":{"start":{"line":844,"column":21},"end":{"line":844,"column":43}},"320":{"start":{"line":846,"column":4},"end":{"line":848,"column":5}},"321":{"start":{"line":847,"column":6},"end":{"line":847,"column":41}},"322":{"start":{"line":850,"column":22},"end":{"line":850,"column":37}},"323":{"start":{"line":853,"column":4},"end":{"line":856,"column":5}},"324":{"start":{"line":854,"column":6},"end":{"line":854,"column":76}},"325":{"start":{"line":855,"column":6},"end":{"line":855,"column":13}},"326":{"start":{"line":858,"column":14},"end":{"line":858,"column":83}},"327":{"start":{"line":859,"column":26},"end":{"line":859,"column":42}},"328":{"start":{"line":861,"column":4},"end":{"line":874,"column":5}},"329":{"start":{"line":862,"column":28},"end":{"line":862,"column":49}},"330":{"start":{"line":863,"column":28},"end":{"line":863,"column":68}},"331":{"start":{"line":864,"column":25},"end":{"line":864,"column":45}},"332":{"start":{"line":866,"column":30},"end":{"line":871,"column":8}},"333":{"start":{"line":867,"column":8},"end":{"line":869,"column":9}},"334":{"start":{"line":868,"column":10},"end":{"line":868,"column":60}},"335":{"start":{"line":870,"column":8},"end":{"line":870,"column":19}},"336":{"start":{"line":873,"column":6},"end":{"line":873,"column":68}},"337":{"start":{"line":876,"column":4},"end":{"line":876,"column":60}},"338":{"start":{"line":877,"column":4},"end":{"line":877,"column":16}},"339":{"start":{"line":882,"column":34},"end":{"line":911,"column":1}},"340":{"start":{"line":883,"column":2},"end":{"line":910,"column":3}},"341":{"start":{"line":884,"column":4},"end":{"line":884,"column":49}},"342":{"start":{"line":885,"column":4},"end":{"line":885,"column":56}},"343":{"start":{"line":888,"column":25},"end":{"line":888,"column":71}},"344":{"start":{"line":889,"column":25},"end":{"line":889,"column":51}},"345":{"start":{"line":890,"column":30},"end":{"line":890,"column":77}},"346":{"start":{"line":893,"column":4},"end":{"line":896,"column":5}},"347":{"start":{"line":894,"column":6},"end":{"line":894,"column":90}},"348":{"start":{"line":895,"column":6},"end":{"line":895,"column":18}},"349":{"start":{"line":898,"column":28},"end":{"line":903,"column":6}},"350":{"start":{"line":905,"column":4},"end":{"line":905,"column":80}},"351":{"start":{"line":906,"column":4},"end":{"line":906,"column":30}},"352":{"start":{"line":908,"column":4},"end":{"line":908,"column":57}},"353":{"start":{"line":909,"column":4},"end":{"line":909,"column":16}},"354":{"start":{"line":914,"column":37},"end":{"line":935,"column":1}},"355":{"start":{"line":915,"column":2},"end":{"line":915,"column":47}},"356":{"start":{"line":916,"column":2},"end":{"line":916,"column":34}},"357":{"start":{"line":917,"column":2},"end":{"line":917,"column":46}},"358":{"start":{"line":919,"column":2},"end":{"line":934,"column":3}},"359":{"start":{"line":920,"column":20},"end":{"line":920,"column":44}},"360":{"start":{"line":921,"column":4},"end":{"line":925,"column":7}},"361":{"start":{"line":926,"column":4},"end":{"line":926,"column":53}},"362":{"start":{"line":927,"column":4},"end":{"line":927,"column":16}},"363":{"start":{"line":929,"column":4},"end":{"line":932,"column":7}},"364":{"start":{"line":933,"column":4},"end":{"line":933,"column":17}},"365":{"start":{"line":938,"column":39},"end":{"line":974,"column":1}},"366":{"start":{"line":939,"column":18},"end":{"line":939,"column":27}},"367":{"start":{"line":940,"column":27},"end":{"line":940,"column":29}},"368":{"start":{"line":943,"column":2},"end":{"line":950,"column":3}},"369":{"start":{"line":944,"column":19},"end":{"line":944,"column":55}},"370":{"start":{"line":945,"column":4},"end":{"line":949,"column":5}},"371":{"start":{"line":946,"column":6},"end":{"line":946,"column":35}},"372":{"start":{"line":948,"column":6},"end":{"line":948,"column":37}},"373":{"start":{"line":953,"column":21},"end":{"line":953,"column":23}},"374":{"start":{"line":954,"column":18},"end":{"line":954,"column":20}},"375":{"start":{"line":956,"column":2},"end":{"line":959,"column":3}},"376":{"start":{"line":956,"column":15},"end":{"line":956,"column":16}},"377":{"start":{"line":957,"column":18},"end":{"line":957,"column":59}},"378":{"start":{"line":958,"column":4},"end":{"line":958,"column":24}},"379":{"start":{"line":962,"column":2},"end":{"line":971,"column":3}},"380":{"start":{"line":963,"column":22},"end":{"line":963,"column":60}},"381":{"start":{"line":963,"column":38},"end":{"line":963,"column":59}},"382":{"start":{"line":964,"column":22},"end":{"line":964,"column":102}},"383":{"start":{"line":966,"column":4},"end":{"line":970,"column":7}},"384":{"start":{"line":967,"column":19},"end":{"line":967,"column":29}},"385":{"start":{"line":968,"column":6},"end":{"line":968,"column":32}},"386":{"start":{"line":969,"column":6},"end":{"line":969,"column":48}},"387":{"start":{"line":973,"column":2},"end":{"line":973,"column":17}},"388":{"start":{"line":977,"column":36},"end":{"line":992,"column":1}},"389":{"start":{"line":978,"column":2},"end":{"line":991,"column":3}},"390":{"start":{"line":979,"column":21},"end":{"line":979,"column":47}},"391":{"start":{"line":980,"column":21},"end":{"line":980,"column":43}},"392":{"start":{"line":982,"column":4},"end":{"line":984,"column":5}},"393":{"start":{"line":983,"column":6},"end":{"line":983,"column":41}},"394":{"start":{"line":986,"column":22},"end":{"line":986,"column":37}},"395":{"start":{"line":987,"column":4},"end":{"line":987,"column":31}},"396":{"start":{"line":989,"column":4},"end":{"line":989,"column":63}},"397":{"start":{"line":990,"column":4},"end":{"line":990,"column":16}},"398":{"start":{"line":995,"column":27},"end":{"line":1089,"column":1}},"399":{"start":{"line":996,"column":2},"end":{"line":1088,"column":3}},"400":{"start":{"line":998,"column":22},"end":{"line":998,"column":62}},"401":{"start":{"line":999,"column":4},"end":{"line":1001,"column":5}},"402":{"start":{"line":1000,"column":6},"end":{"line":1000,"column":41}},"403":{"start":{"line":1003,"column":22},"end":{"line":1003,"column":38}},"404":{"start":{"line":1004,"column":22},"end":{"line":1004,"column":41}},"405":{"start":{"line":1007,"column":24},"end":{"line":1007,"column":68}},"406":{"start":{"line":1010,"column":18},"end":{"line":1010,"column":32}},"407":{"start":{"line":1011,"column":21},"end":{"line":1011,"column":47}},"408":{"start":{"line":1012,"column":28},"end":{"line":1012,"column":64}},"409":{"start":{"line":1015,"column":4},"end":{"line":1018,"column":7}},"410":{"start":{"line":1021,"column":4},"end":{"line":1069,"column":5}},"411":{"start":{"line":1022,"column":29},"end":{"line":1022,"column":54}},"412":{"start":{"line":1024,"column":6},"end":{"line":1043,"column":9}},"413":{"start":{"line":1046,"column":6},"end":{"line":1046,"column":27}},"414":{"start":{"line":1049,"column":22},"end":{"line":1049,"column":68}},"415":{"start":{"line":1050,"column":6},"end":{"line":1068,"column":7}},"416":{"start":{"line":1051,"column":8},"end":{"line":1067,"column":11}},"417":{"start":{"line":1061,"column":10},"end":{"line":1066,"column":13}},"418":{"start":{"line":1072,"column":4},"end":{"line":1078,"column":6}},"419":{"start":{"line":1081,"column":4},"end":{"line":1086,"column":7}},"420":{"start":{"line":1087,"column":4},"end":{"line":1087,"column":16}}},"fnMap":{"0":{"name":"(anonymous_0)","decl":{"start":{"line":73,"column":32},"end":{"line":73,"column":33}},"loc":{"start":{"line":73,"column":64},"end":{"line":98,"column":1}},"line":73},"1":{"name":"(anonymous_1)","decl":{"start":{"line":83,"column":44},"end":{"line":83,"column":45}},"loc":{"start":{"line":83,"column":52},"end":{"line":88,"column":5}},"line":83},"2":{"name":"(anonymous_2)","decl":{"start":{"line":90,"column":27},"end":{"line":90,"column":28}},"loc":{"start":{"line":91,"column":6},"end":{"line":92,"column":31}},"line":91},"3":{"name":"(anonymous_3)","decl":{"start":{"line":102,"column":31},"end":{"line":102,"column":32}},"loc":{"start":{"line":102,"column":53},"end":{"line":133,"column":1}},"line":102},"4":{"name":"(anonymous_4)","decl":{"start":{"line":106,"column":23},"end":{"line":106,"column":24}},"loc":{"start":{"line":106,"column":43},"end":{"line":132,"column":3}},"line":106},"5":{"name":"(anonymous_5)","decl":{"start":{"line":108,"column":24},"end":{"line":108,"column":25}},"loc":{"start":{"line":108,"column":31},"end":{"line":108,"column":51}},"line":108},"6":{"name":"(anonymous_6)","decl":{"start":{"line":115,"column":41},"end":{"line":115,"column":42}},"loc":{"start":{"line":115,"column":53},"end":{"line":129,"column":5}},"line":115},"7":{"name":"(anonymous_7)","decl":{"start":{"line":139,"column":27},"end":{"line":139,"column":28}},"loc":{"start":{"line":139,"column":49},"end":{"line":192,"column":1}},"line":139},"8":{"name":"(anonymous_8)","decl":{"start":{"line":142,"column":28},"end":{"line":142,"column":29}},"loc":{"start":{"line":142,"column":40},"end":{"line":184,"column":3}},"line":142},"9":{"name":"(anonymous_9)","decl":{"start":{"line":157,"column":20},"end":{"line":157,"column":21}},"loc":{"start":{"line":157,"column":44},"end":{"line":165,"column":9}},"line":157},"10":{"name":"(anonymous_10)","decl":{"start":{"line":168,"column":49},"end":{"line":168,"column":50}},"loc":{"start":{"line":168,"column":63},"end":{"line":171,"column":7}},"line":168},"11":{"name":"(anonymous_11)","decl":{"start":{"line":179,"column":26},"end":{"line":179,"column":27}},"loc":{"start":{"line":179,"column":37},"end":{"line":179,"column":82}},"line":179},"12":{"name":"(anonymous_12)","decl":{"start":{"line":195,"column":33},"end":{"line":195,"column":34}},"loc":{"start":{"line":195,"column":54},"end":{"line":199,"column":1}},"line":195},"13":{"name":"(anonymous_13)","decl":{"start":{"line":202,"column":31},"end":{"line":202,"column":32}},"loc":{"start":{"line":202,"column":52},"end":{"line":209,"column":1}},"line":202},"14":{"name":"(anonymous_14)","decl":{"start":{"line":212,"column":31},"end":{"line":212,"column":32}},"loc":{"start":{"line":212,"column":50},"end":{"line":214,"column":1}},"line":212},"15":{"name":"(anonymous_15)","decl":{"start":{"line":217,"column":37},"end":{"line":217,"column":38}},"loc":{"start":{"line":217,"column":88},"end":{"line":270,"column":1}},"line":217},"16":{"name":"(anonymous_16)","decl":{"start":{"line":238,"column":61},"end":{"line":238,"column":62}},"loc":{"start":{"line":239,"column":12},"end":{"line":239,"column":60}},"line":239},"17":{"name":"(anonymous_17)","decl":{"start":{"line":276,"column":37},"end":{"line":276,"column":38}},"loc":{"start":{"line":276,"column":74},"end":{"line":330,"column":1}},"line":276},"18":{"name":"(anonymous_18)","decl":{"start":{"line":333,"column":38},"end":{"line":333,"column":39}},"loc":{"start":{"line":333,"column":70},"end":{"line":406,"column":1}},"line":333},"19":{"name":"(anonymous_19)","decl":{"start":{"line":409,"column":33},"end":{"line":409,"column":34}},"loc":{"start":{"line":409,"column":52},"end":{"line":414,"column":3}},"line":409},"20":{"name":"(anonymous_20)","decl":{"start":{"line":417,"column":28},"end":{"line":417,"column":29}},"loc":{"start":{"line":417,"column":39},"end":{"line":444,"column":1}},"line":417},"21":{"name":"(anonymous_21)","decl":{"start":{"line":422,"column":16},"end":{"line":422,"column":17}},"loc":{"start":{"line":422,"column":24},"end":{"line":432,"column":3}},"line":422},"22":{"name":"(anonymous_22)","decl":{"start":{"line":447,"column":26},"end":{"line":447,"column":27}},"loc":{"start":{"line":447,"column":49},"end":{"line":467,"column":1}},"line":447},"23":{"name":"(anonymous_23)","decl":{"start":{"line":470,"column":33},"end":{"line":470,"column":34}},"loc":{"start":{"line":470,"column":55},"end":{"line":476,"column":3}},"line":470},"24":{"name":"(anonymous_24)","decl":{"start":{"line":472,"column":25},"end":{"line":472,"column":26}},"loc":{"start":{"line":472,"column":39},"end":{"line":475,"column":5}},"line":472},"25":{"name":"(anonymous_25)","decl":{"start":{"line":473,"column":45},"end":{"line":473,"column":46}},"loc":{"start":{"line":473,"column":55},"end":{"line":473,"column":84}},"line":473},"26":{"name":"(anonymous_26)","decl":{"start":{"line":479,"column":37},"end":{"line":479,"column":38}},"loc":{"start":{"line":479,"column":62},"end":{"line":486,"column":1}},"line":479},"27":{"name":"(anonymous_27)","decl":{"start":{"line":482,"column":23},"end":{"line":482,"column":24}},"loc":{"start":{"line":482,"column":37},"end":{"line":485,"column":3}},"line":482},"28":{"name":"(anonymous_28)","decl":{"start":{"line":483,"column":37},"end":{"line":483,"column":38}},"loc":{"start":{"line":483,"column":47},"end":{"line":483,"column":76}},"line":483},"29":{"name":"(anonymous_29)","decl":{"start":{"line":489,"column":29},"end":{"line":489,"column":30}},"loc":{"start":{"line":489,"column":50},"end":{"line":576,"column":1}},"line":489},"30":{"name":"(anonymous_30)","decl":{"start":{"line":529,"column":27},"end":{"line":529,"column":28}},"loc":{"start":{"line":529,"column":41},"end":{"line":560,"column":5}},"line":529},"31":{"name":"(anonymous_31)","decl":{"start":{"line":580,"column":30},"end":{"line":580,"column":31}},"loc":{"start":{"line":580,"column":59},"end":{"line":591,"column":1}},"line":580},"32":{"name":"(anonymous_32)","decl":{"start":{"line":594,"column":35},"end":{"line":594,"column":36}},"loc":{"start":{"line":594,"column":64},"end":{"line":605,"column":1}},"line":594},"33":{"name":"(anonymous_33)","decl":{"start":{"line":608,"column":32},"end":{"line":608,"column":33}},"loc":{"start":{"line":608,"column":50},"end":{"line":620,"column":1}},"line":608},"34":{"name":"(anonymous_34)","decl":{"start":{"line":623,"column":36},"end":{"line":623,"column":37}},"loc":{"start":{"line":623,"column":70},"end":{"line":669,"column":1}},"line":623},"35":{"name":"(anonymous_35)","decl":{"start":{"line":643,"column":56},"end":{"line":643,"column":57}},"loc":{"start":{"line":643,"column":75},"end":{"line":661,"column":5}},"line":643},"36":{"name":"(anonymous_36)","decl":{"start":{"line":672,"column":34},"end":{"line":672,"column":35}},"loc":{"start":{"line":672,"column":87},"end":{"line":679,"column":1}},"line":672},"37":{"name":"(anonymous_37)","decl":{"start":{"line":682,"column":40},"end":{"line":682,"column":41}},"loc":{"start":{"line":682,"column":62},"end":{"line":689,"column":1}},"line":682},"38":{"name":"(anonymous_38)","decl":{"start":{"line":684,"column":29},"end":{"line":684,"column":30}},"loc":{"start":{"line":684,"column":42},"end":{"line":688,"column":3}},"line":684},"39":{"name":"(anonymous_39)","decl":{"start":{"line":692,"column":32},"end":{"line":692,"column":33}},"loc":{"start":{"line":692,"column":52},"end":{"line":711,"column":1}},"line":692},"40":{"name":"(anonymous_40)","decl":{"start":{"line":715,"column":33},"end":{"line":715,"column":34}},"loc":{"start":{"line":715,"column":45},"end":{"line":744,"column":1}},"line":715},"41":{"name":"(anonymous_41)","decl":{"start":{"line":750,"column":32},"end":{"line":750,"column":33}},"loc":{"start":{"line":756,"column":25},"end":{"line":773,"column":1}},"line":756},"42":{"name":"(anonymous_42)","decl":{"start":{"line":777,"column":37},"end":{"line":777,"column":38}},"loc":{"start":{"line":783,"column":53},"end":{"line":837,"column":1}},"line":783},"43":{"name":"(anonymous_43)","decl":{"start":{"line":819,"column":47},"end":{"line":819,"column":48}},"loc":{"start":{"line":819,"column":55},"end":{"line":826,"column":5}},"line":819},"44":{"name":"(anonymous_44)","decl":{"start":{"line":840,"column":34},"end":{"line":840,"column":35}},"loc":{"start":{"line":840,"column":77},"end":{"line":879,"column":1}},"line":840},"45":{"name":"(anonymous_45)","decl":{"start":{"line":866,"column":54},"end":{"line":866,"column":55}},"loc":{"start":{"line":866,"column":61},"end":{"line":871,"column":7}},"line":866},"46":{"name":"(anonymous_46)","decl":{"start":{"line":882,"column":34},"end":{"line":882,"column":35}},"loc":{"start":{"line":882,"column":62},"end":{"line":911,"column":1}},"line":882},"47":{"name":"(anonymous_47)","decl":{"start":{"line":914,"column":37},"end":{"line":914,"column":38}},"loc":{"start":{"line":914,"column":78},"end":{"line":935,"column":1}},"line":914},"48":{"name":"(anonymous_48)","decl":{"start":{"line":938,"column":39},"end":{"line":938,"column":40}},"loc":{"start":{"line":938,"column":96},"end":{"line":974,"column":1}},"line":938},"49":{"name":"(anonymous_49)","decl":{"start":{"line":963,"column":32},"end":{"line":963,"column":33}},"loc":{"start":{"line":963,"column":38},"end":{"line":963,"column":59}},"line":963},"50":{"name":"(anonymous_50)","decl":{"start":{"line":966,"column":22},"end":{"line":966,"column":23}},"loc":{"start":{"line":966,"column":29},"end":{"line":970,"column":5}},"line":966},"51":{"name":"(anonymous_51)","decl":{"start":{"line":977,"column":36},"end":{"line":977,"column":37}},"loc":{"start":{"line":977,"column":63},"end":{"line":992,"column":1}},"line":977},"52":{"name":"(anonymous_52)","decl":{"start":{"line":995,"column":27},"end":{"line":995,"column":28}},"loc":{"start":{"line":995,"column":55},"end":{"line":1089,"column":1}},"line":995},"53":{"name":"(anonymous_53)","decl":{"start":{"line":1060,"column":17},"end":{"line":1060,"column":18}},"loc":{"start":{"line":1060,"column":26},"end":{"line":1067,"column":9}},"line":1060}},"branchMap":{"0":{"loc":{"start":{"line":73,"column":39},"end":{"line":73,"column":59}},"type":"default-arg","locations":[{"start":{"line":73,"column":57},"end":{"line":73,"column":59}}],"line":73},"1":{"loc":{"start":{"line":91,"column":6},"end":{"line":92,"column":31}},"type":"binary-expr","locations":[{"start":{"line":91,"column":6},"end":{"line":91,"column":30}},{"start":{"line":92,"column":6},"end":{"line":92,"column":31}}],"line":91},"2":{"loc":{"start":{"line":111,"column":25},"end":{"line":113,"column":10}},"type":"cond-expr","locations":[{"start":{"line":112,"column":8},"end":{"line":112,"column":44}},{"start":{"line":113,"column":8},"end":{"line":113,"column":10}}],"line":111},"3":{"loc":{"start":{"line":117,"column":26},"end":{"line":121,"column":7}},"type":"binary-expr","locations":[{"start":{"line":117,"column":26},"end":{"line":117,"column":59}},{"start":{"line":117,"column":63},"end":{"line":121,"column":7}}],"line":117},"4":{"loc":{"start":{"line":176,"column":6},"end":{"line":181,"column":7}},"type":"if","locations":[{"start":{"line":176,"column":6},"end":{"line":181,"column":7}},{"start":{},"end":{}}],"line":176},"5":{"loc":{"start":{"line":198,"column":9},"end":{"line":198,"column":65}},"type":"cond-expr","locations":[{"start":{"line":198,"column":36},"end":{"line":198,"column":58}},{"start":{"line":198,"column":61},"end":{"line":198,"column":65}}],"line":198},"6":{"loc":{"start":{"line":205,"column":2},"end":{"line":207,"column":3}},"type":"if","locations":[{"start":{"line":205,"column":2},"end":{"line":207,"column":3}},{"start":{},"end":{}}],"line":205},"7":{"loc":{"start":{"line":206,"column":11},"end":{"line":206,"column":48}},"type":"binary-expr","locations":[{"start":{"line":206,"column":11},"end":{"line":206,"column":40}},{"start":{"line":206,"column":44},"end":{"line":206,"column":48}}],"line":206},"8":{"loc":{"start":{"line":220,"column":4},"end":{"line":222,"column":5}},"type":"if","locations":[{"start":{"line":220,"column":4},"end":{"line":222,"column":5}},{"start":{},"end":{}}],"line":220},"9":{"loc":{"start":{"line":231,"column":4},"end":{"line":257,"column":5}},"type":"if","locations":[{"start":{"line":231,"column":4},"end":{"line":257,"column":5}},{"start":{},"end":{}}],"line":231},"10":{"loc":{"start":{"line":235,"column":6},"end":{"line":256,"column":7}},"type":"if","locations":[{"start":{"line":235,"column":6},"end":{"line":256,"column":7}},{"start":{},"end":{}}],"line":235},"11":{"loc":{"start":{"line":237,"column":8},"end":{"line":248,"column":9}},"type":"if","locations":[{"start":{"line":237,"column":8},"end":{"line":248,"column":9}},{"start":{},"end":{}}],"line":237},"12":{"loc":{"start":{"line":317,"column":4},"end":{"line":323,"column":5}},"type":"if","locations":[{"start":{"line":317,"column":4},"end":{"line":323,"column":5}},{"start":{},"end":{}}],"line":317},"13":{"loc":{"start":{"line":347,"column":4},"end":{"line":380,"column":5}},"type":"if","locations":[{"start":{"line":347,"column":4},"end":{"line":380,"column":5}},{"start":{"line":365,"column":11},"end":{"line":380,"column":5}}],"line":347},"14":{"loc":{"start":{"line":384,"column":4},"end":{"line":399,"column":5}},"type":"if","locations":[{"start":{"line":384,"column":4},"end":{"line":399,"column":5}},{"start":{},"end":{}}],"line":384},"15":{"loc":{"start":{"line":387,"column":26},"end":{"line":389,"column":26}},"type":"cond-expr","locations":[{"start":{"line":388,"column":10},"end":{"line":388,"column":34}},{"start":{"line":389,"column":10},"end":{"line":389,"column":26}}],"line":387},"16":{"loc":{"start":{"line":397,"column":52},"end":{"line":397,"column":94}},"type":"cond-expr","locations":[{"start":{"line":397,"column":84},"end":{"line":397,"column":89}},{"start":{"line":397,"column":92},"end":{"line":397,"column":94}}],"line":397},"17":{"loc":{"start":{"line":511,"column":4},"end":{"line":513,"column":5}},"type":"if","locations":[{"start":{"line":511,"column":4},"end":{"line":513,"column":5}},{"start":{},"end":{}}],"line":511},"18":{"loc":{"start":{"line":518,"column":25},"end":{"line":518,"column":64}},"type":"binary-expr","locations":[{"start":{"line":518,"column":25},"end":{"line":518,"column":42}},{"start":{"line":518,"column":46},"end":{"line":518,"column":64}}],"line":518},"19":{"loc":{"start":{"line":612,"column":4},"end":{"line":614,"column":5}},"type":"if","locations":[{"start":{"line":612,"column":4},"end":{"line":614,"column":5}},{"start":{},"end":{}}],"line":612},"20":{"loc":{"start":{"line":613,"column":13},"end":{"line":613,"column":43}},"type":"binary-expr","locations":[{"start":{"line":613,"column":13},"end":{"line":613,"column":37}},{"start":{"line":613,"column":41},"end":{"line":613,"column":43}}],"line":613},"21":{"loc":{"start":{"line":645,"column":6},"end":{"line":645,"column":74}},"type":"if","locations":[{"start":{"line":645,"column":6},"end":{"line":645,"column":74}},{"start":{},"end":{}}],"line":645},"22":{"loc":{"start":{"line":646,"column":6},"end":{"line":646,"column":92}},"type":"if","locations":[{"start":{"line":646,"column":6},"end":{"line":646,"column":92}},{"start":{},"end":{}}],"line":646},"23":{"loc":{"start":{"line":647,"column":6},"end":{"line":647,"column":74}},"type":"if","locations":[{"start":{"line":647,"column":6},"end":{"line":647,"column":74}},{"start":{},"end":{}}],"line":647},"24":{"loc":{"start":{"line":649,"column":6},"end":{"line":660,"column":7}},"type":"if","locations":[{"start":{"line":649,"column":6},"end":{"line":660,"column":7}},{"start":{},"end":{}}],"line":649},"25":{"loc":{"start":{"line":673,"column":2},"end":{"line":675,"column":3}},"type":"if","locations":[{"start":{"line":673,"column":2},"end":{"line":675,"column":3}},{"start":{},"end":{}}],"line":673},"26":{"loc":{"start":{"line":685,"column":4},"end":{"line":687,"column":5}},"type":"if","locations":[{"start":{"line":685,"column":4},"end":{"line":687,"column":5}},{"start":{},"end":{}}],"line":685},"27":{"loc":{"start":{"line":686,"column":15},"end":{"line":686,"column":45}},"type":"binary-expr","locations":[{"start":{"line":686,"column":15},"end":{"line":686,"column":39}},{"start":{"line":686,"column":43},"end":{"line":686,"column":45}}],"line":686},"28":{"loc":{"start":{"line":695,"column":4},"end":{"line":697,"column":5}},"type":"if","locations":[{"start":{"line":695,"column":4},"end":{"line":697,"column":5}},{"start":{},"end":{}}],"line":695},"29":{"loc":{"start":{"line":718,"column":4},"end":{"line":720,"column":5}},"type":"if","locations":[{"start":{"line":718,"column":4},"end":{"line":720,"column":5}},{"start":{},"end":{}}],"line":718},"30":{"loc":{"start":{"line":731,"column":4},"end":{"line":739,"column":5}},"type":"if","locations":[{"start":{"line":731,"column":4},"end":{"line":739,"column":5}},{"start":{"line":737,"column":11},"end":{"line":739,"column":5}}],"line":731},"31":{"loc":{"start":{"line":768,"column":11},"end":{"line":768,"column":32}},"type":"binary-expr","locations":[{"start":{"line":768,"column":11},"end":{"line":768,"column":26}},{"start":{"line":768,"column":30},"end":{"line":768,"column":32}}],"line":768},"32":{"loc":{"start":{"line":779,"column":2},"end":{"line":779,"column":28}},"type":"default-arg","locations":[{"start":{"line":779,"column":24},"end":{"line":779,"column":28}}],"line":779},"33":{"loc":{"start":{"line":781,"column":2},"end":{"line":781,"column":30}},"type":"default-arg","locations":[{"start":{"line":781,"column":28},"end":{"line":781,"column":30}}],"line":781},"34":{"loc":{"start":{"line":782,"column":2},"end":{"line":782,"column":31}},"type":"default-arg","locations":[{"start":{"line":782,"column":26},"end":{"line":782,"column":31}}],"line":782},"35":{"loc":{"start":{"line":799,"column":4},"end":{"line":804,"column":5}},"type":"if","locations":[{"start":{"line":799,"column":4},"end":{"line":804,"column":5}},{"start":{},"end":{}}],"line":799},"36":{"loc":{"start":{"line":812,"column":4},"end":{"line":814,"column":5}},"type":"if","locations":[{"start":{"line":812,"column":4},"end":{"line":814,"column":5}},{"start":{},"end":{}}],"line":812},"37":{"loc":{"start":{"line":846,"column":4},"end":{"line":848,"column":5}},"type":"if","locations":[{"start":{"line":846,"column":4},"end":{"line":848,"column":5}},{"start":{},"end":{}}],"line":846},"38":{"loc":{"start":{"line":853,"column":4},"end":{"line":856,"column":5}},"type":"if","locations":[{"start":{"line":853,"column":4},"end":{"line":856,"column":5}},{"start":{},"end":{}}],"line":853},"39":{"loc":{"start":{"line":853,"column":8},"end":{"line":853,"column":74}},"type":"binary-expr","locations":[{"start":{"line":853,"column":8},"end":{"line":853,"column":35}},{"start":{"line":853,"column":39},"end":{"line":853,"column":74}}],"line":853},"40":{"loc":{"start":{"line":861,"column":4},"end":{"line":874,"column":5}},"type":"if","locations":[{"start":{"line":861,"column":4},"end":{"line":874,"column":5}},{"start":{},"end":{}}],"line":861},"41":{"loc":{"start":{"line":867,"column":8},"end":{"line":869,"column":9}},"type":"if","locations":[{"start":{"line":867,"column":8},"end":{"line":869,"column":9}},{"start":{},"end":{}}],"line":867},"42":{"loc":{"start":{"line":867,"column":12},"end":{"line":867,"column":48}},"type":"binary-expr","locations":[{"start":{"line":867,"column":12},"end":{"line":867,"column":35}},{"start":{"line":867,"column":39},"end":{"line":867,"column":48}}],"line":867},"43":{"loc":{"start":{"line":890,"column":30},"end":{"line":890,"column":77}},"type":"binary-expr","locations":[{"start":{"line":890,"column":30},"end":{"line":890,"column":71}},{"start":{"line":890,"column":75},"end":{"line":890,"column":77}}],"line":890},"44":{"loc":{"start":{"line":893,"column":4},"end":{"line":896,"column":5}},"type":"if","locations":[{"start":{"line":893,"column":4},"end":{"line":896,"column":5}},{"start":{},"end":{}}],"line":893},"45":{"loc":{"start":{"line":945,"column":4},"end":{"line":949,"column":5}},"type":"if","locations":[{"start":{"line":945,"column":4},"end":{"line":949,"column":5}},{"start":{"line":947,"column":11},"end":{"line":949,"column":5}}],"line":945},"46":{"loc":{"start":{"line":982,"column":4},"end":{"line":984,"column":5}},"type":"if","locations":[{"start":{"line":982,"column":4},"end":{"line":984,"column":5}},{"start":{},"end":{}}],"line":982},"47":{"loc":{"start":{"line":999,"column":4},"end":{"line":1001,"column":5}},"type":"if","locations":[{"start":{"line":999,"column":4},"end":{"line":1001,"column":5}},{"start":{},"end":{}}],"line":999},"48":{"loc":{"start":{"line":1021,"column":4},"end":{"line":1069,"column":5}},"type":"if","locations":[{"start":{"line":1021,"column":4},"end":{"line":1069,"column":5}},{"start":{},"end":{}}],"line":1021},"49":{"loc":{"start":{"line":1050,"column":6},"end":{"line":1068,"column":7}},"type":"if","locations":[{"start":{"line":1050,"column":6},"end":{"line":1068,"column":7}},{"start":{},"end":{}}],"line":1050},"50":{"loc":{"start":{"line":1050,"column":10},"end":{"line":1050,"column":58}},"type":"binary-expr","locations":[{"start":{"line":1050,"column":10},"end":{"line":1050,"column":26}},{"start":{"line":1050,"column":30},"end":{"line":1050,"column":58}}],"line":1050}},"s":{"0":1,"1":0,"2":0,"3":0,"4":0,"5":0,"6":0,"7":0,"8":0,"9":0,"10":0,"11":1,"12":0,"13":0,"14":0,"15":0,"16":0,"17":0,"18":0,"19":0,"20":0,"21":0,"22":0,"23":1,"24":1,"25":1,"26":0,"27":0,"28":0,"29":0,"30":0,"31":0,"32":0,"33":0,"34":0,"35":0,"36":0,"37":0,"38":0,"39":0,"40":0,"41":0,"42":0,"43":0,"44":0,"45":0,"46":0,"47":0,"48":0,"49":0,"50":0,"51":0,"52":0,"53":0,"54":0,"55":0,"56":0,"57":0,"58":1,"59":0,"60":0,"61":0,"62":1,"63":0,"64":0,"65":0,"66":0,"67":0,"68":1,"69":0,"70":1,"71":0,"72":0,"73":0,"74":0,"75":0,"76":0,"77":0,"78":0,"79":0,"80":0,"81":0,"82":0,"83":0,"84":0,"85":0,"86":0,"87":0,"88":0,"89":1,"90":1,"91":0,"92":0,"93":0,"94":0,"95":0,"96":0,"97":0,"98":0,"99":0,"100":0,"101":0,"102":0,"103":0,"104":0,"105":0,"106":0,"107":0,"108":0,"109":0,"110":1,"111":0,"112":0,"113":0,"114":0,"115":0,"116":0,"117":0,"118":0,"119":0,"120":0,"121":0,"122":0,"123":0,"124":0,"125":0,"126":0,"127":0,"128":0,"129":0,"130":0,"131":0,"132":0,"133":0,"134":0,"135":0,"136":0,"137":1,"138":0,"139":0,"140":1,"141":0,"142":0,"143":0,"144":0,"145":0,"146":0,"147":0,"148":0,"149":0,"150":0,"151":0,"152":0,"153":0,"154":0,"155":0,"156":1,"157":0,"158":0,"159":0,"160":0,"161":0,"162":0,"163":0,"164":0,"165":1,"166":0,"167":0,"168":0,"169":0,"170":0,"171":1,"172":0,"173":0,"174":0,"175":0,"176":0,"177":0,"178":1,"179":0,"180":0,"181":0,"182":0,"183":0,"184":0,"185":0,"186":0,"187":0,"188":0,"189":0,"190":0,"191":0,"192":0,"193":0,"194":0,"195":0,"196":0,"197":0,"198":0,"199":0,"200":0,"201":0,"202":0,"203":0,"204":0,"205":0,"206":0,"207":0,"208":0,"209":0,"210":0,"211":0,"212":1,"213":0,"214":0,"215":0,"216":0,"217":0,"218":0,"219":1,"220":0,"221":0,"222":0,"223":0,"224":0,"225":0,"226":1,"227":0,"228":0,"229":0,"230":0,"231":0,"232":0,"233":0,"234":0,"235":1,"236":0,"237":0,"238":0,"239":0,"240":0,"241":0,"242":0,"243":0,"244":0,"245":0,"246":0,"247":0,"248":0,"249":0,"250":0,"251":0,"252":0,"253":0,"254":0,"255":0,"256":0,"257":0,"258":0,"259":1,"260":0,"261":0,"262":0,"263":0,"264":1,"265":0,"266":0,"267":0,"268":0,"269":1,"270":0,"271":0,"272":0,"273":0,"274":0,"275":0,"276":0,"277":0,"278":0,"279":1,"280":0,"281":0,"282":0,"283":0,"284":0,"285":0,"286":0,"287":0,"288":0,"289":0,"290":0,"291":0,"292":0,"293":1,"294":0,"295":0,"296":0,"297":0,"298":0,"299":1,"300":0,"301":0,"302":0,"303":0,"304":0,"305":0,"306":0,"307":0,"308":0,"309":0,"310":0,"311":0,"312":0,"313":0,"314":0,"315":0,"316":1,"317":0,"318":0,"319":0,"320":0,"321":0,"322":0,"323":0,"324":0,"325":0,"326":0,"327":0,"328":0,"329":0,"330":0,"331":0,"332":0,"333":0,"334":0,"335":0,"336":0,"337":0,"338":0,"339":1,"340":0,"341":0,"342":0,"343":0,"344":0,"345":0,"346":0,"347":0,"348":0,"349":0,"350":0,"351":0,"352":0,"353":0,"354":1,"355":0,"356":0,"357":0,"358":0,"359":0,"360":0,"361":0,"362":0,"363":0,"364":0,"365":1,"366":0,"367":0,"368":0,"369":0,"370":0,"371":0,"372":0,"373":0,"374":0,"375":0,"376":0,"377":0,"378":0,"379":0,"380":0,"381":0,"382":0,"383":0,"384":0,"385":0,"386":0,"387":0,"388":1,"389":0,"390":0,"391":0,"392":0,"393":0,"394":0,"395":0,"396":0,"397":0,"398":1,"399":0,"400":0,"401":0,"402":0,"403":0,"404":0,"405":0,"406":0,"407":0,"408":0,"409":0,"410":0,"411":0,"412":0,"413":0,"414":0,"415":0,"416":0,"417":0,"418":0,"419":0,"420":0},"f":{"0":0,"1":0,"2":0,"3":0,"4":0,"5":0,"6":0,"7":0,"8":0,"9":0,"10":0,"11":0,"12":0,"13":0,"14":0,"15":0,"16":0,"17":0,"18":0,"19":0,"20":0,"21":0,"22":0,"23":0,"24":0,"25":0,"26":0,"27":0,"28":0,"29":0,"30":0,"31":0,"32":0,"33":0,"34":0,"35":0,"36":0,"37":0,"38":0,"39":0,"40":0,"41":0,"42":0,"43":0,"44":0,"45":0,"46":0,"47":0,"48":0,"49":0,"50":0,"51":0,"52":0,"53":0},"b":{"0":[0],"1":[0,0],"2":[0,0],"3":[0,0],"4":[0,0],"5":[0,0],"6":[0,0],"7":[0,0],"8":[0,0],"9":[0,0],"10":[0,0],"11":[0,0],"12":[0,0],"13":[0,0],"14":[0,0],"15":[0,0],"16":[0,0],"17":[0,0],"18":[0,0],"19":[0,0],"20":[0,0],"21":[0,0],"22":[0,0],"23":[0,0],"24":[0,0],"25":[0,0],"26":[0,0],"27":[0,0],"28":[0,0],"29":[0,0],"30":[0,0],"31":[0,0],"32":[0],"33":[0],"34":[0],"35":[0,0],"36":[0,0],"37":[0,0],"38":[0,0],"39":[0,0],"40":[0,0],"41":[0,0],"42":[0,0],"43":[0,0],"44":[0,0],"45":[0,0],"46":[0,0],"47":[0,0],"48":[0,0],"49":[0,0],"50":[0,0]},"_coverageSchema":"1a1c01bbd47fc00a2c39e90264f33305004495a9","hash":"869fc2afeb5799ca52f9c1165c25ebccc244711d"}
coverage/coverage-final.json:,"/Users/<USER>/Desktop/satbana/services/notificationService.ts": {"path":"/Users/<USER>/Desktop/satbana/services/notificationService.ts","statementMap":{"0":{"start":{"line":11,"column":44},"end":{"line":11,"column":46}},"1":{"start":{"line":13,"column":42},"end":{"line":25,"column":1}},"2":{"start":{"line":14,"column":2},"end":{"line":14,"column":84}},"3":{"start":{"line":15,"column":2},"end":{"line":22,"column":5}},"4":{"start":{"line":16,"column":4},"end":{"line":21,"column":5}},"5":{"start":{"line":17,"column":6},"end":{"line":17,"column":70}},"6":{"start":{"line":18,"column":6},"end":{"line":18,"column":20}},"7":{"start":{"line":20,"column":6},"end":{"line":20,"column":85}},"8":{"start":{"line":23,"column":2},"end":{"line":23,"column":29}},"9":{"start":{"line":24,"column":2},"end":{"line":24,"column":52}},"10":{"start":{"line":27,"column":41},"end":{"line":62,"column":1}},"11":{"start":{"line":28,"column":2},"end":{"line":28,"column":53}},"12":{"start":{"line":31,"column":12},"end":{"line":35,"column":3}},"13":{"start":{"line":38,"column":22},"end":{"line":55,"column":3}},"14":{"start":{"line":40,"column":6},"end":{"line":43,"column":7}},"15":{"start":{"line":41,"column":8},"end":{"line":41,"column":71}},"16":{"start":{"line":42,"column":8},"end":{"line":42,"column":15}},"17":{"start":{"line":44,"column":20},"end":{"line":44,"column":40}},"18":{"start":{"line":45,"column":6},"end":{"line":45,"column":22}},"19":{"start":{"line":49,"column":6},"end":{"line":53,"column":7}},"20":{"start":{"line":50,"column":8},"end":{"line":50,"column":88}},"21":{"start":{"line":52,"column":8},"end":{"line":52,"column":72}},"22":{"start":{"line":58,"column":2},"end":{"line":58,"column":56}},"23":{"start":{"line":60,"column":2},"end":{"line":60,"column":54}},"24":{"start":{"line":61,"column":2},"end":{"line":61,"column":21}},"25":{"start":{"line":64,"column":34},"end":{"line":80,"column":1}},"26":{"start":{"line":65,"column":15},"end":{"line":65,"column":31}},"27":{"start":{"line":66,"column":2},"end":{"line":66,"column":55}},"28":{"start":{"line":66,"column":13},"end":{"line":66,"column":55}},"29":{"start":{"line":68,"column":12},"end":{"line":73,"column":3}},"30":{"start":{"line":75,"column":19},"end":{"line":75,"column":35}},"31":{"start":{"line":76,"column":2},"end":{"line":79,"column":24}},"32":{"start":{"line":76,"column":35},"end":{"line":79,"column":3}},"33":{"start":{"line":82,"column":38},"end":{"line":85,"column":1}},"34":{"start":{"line":83,"column":26},"end":{"line":83,"column":66}},"35":{"start":{"line":84,"column":2},"end":{"line":84,"column":51}},"36":{"start":{"line":87,"column":42},"end":{"line":124,"column":1}},"37":{"start":{"line":88,"column":2},"end":{"line":123,"column":3}},"38":{"start":{"line":89,"column":17},"end":{"line":89,"column":33}},"39":{"start":{"line":90,"column":4},"end":{"line":90,"column":24}},"40":{"start":{"line":90,"column":15},"end":{"line":90,"column":24}},"41":{"start":{"line":92,"column":4},"end":{"line":92,"column":65}},"42":{"start":{"line":94,"column":12},"end":{"line":98,"column":5}},"43":{"start":{"line":101,"column":4},"end":{"line":114,"column":5}},"44":{"start":{"line":102,"column":27},"end":{"line":104,"column":30}},"45":{"start":{"line":103,"column":34},"end":{"line":103,"column":41}},"46":{"start":{"line":104,"column":25},"end":{"line":104,"column":29}},"47":{"start":{"line":106,"column":6},"end":{"line":106,"column":63}},"48":{"start":{"line":108,"column":6},"end":{"line":113,"column":7}},"49":{"start":{"line":109,"column":8},"end":{"line":109,"column":56}},"50":{"start":{"line":111,"column":8},"end":{"line":111,"column":50}},"51":{"start":{"line":112,"column":8},"end":{"line":112,"column":17}},"52":{"start":{"line":116,"column":21},"end":{"line":116,"column":48}},"53":{"start":{"line":117,"column":18},"end":{"line":117,"column":39}},"54":{"start":{"line":118,"column":4},"end":{"line":118,"column":52}},"55":{"start":{"line":119,"column":4},"end":{"line":119,"column":17}},"56":{"start":{"line":121,"column":4},"end":{"line":121,"column":56}},"57":{"start":{"line":122,"column":4},"end":{"line":122,"column":13}},"58":{"start":{"line":126,"column":40},"end":{"line":159,"column":1}},"59":{"start":{"line":127,"column":2},"end":{"line":127,"column":76}},"60":{"start":{"line":128,"column":2},"end":{"line":158,"column":3}},"61":{"start":{"line":129,"column":24},"end":{"line":129,"column":40}},"62":{"start":{"line":130,"column":4},"end":{"line":133,"column":5}},"63":{"start":{"line":131,"column":6},"end":{"line":131,"column":71}},"64":{"start":{"line":132,"column":6},"end":{"line":132,"column":22}},"65":{"start":{"line":135,"column":29},"end":{"line":135,"column":60}},"66":{"start":{"line":136,"column":14},"end":{"line":140,"column":5}},"67":{"start":{"line":142,"column":24},"end":{"line":152,"column":6}},"68":{"start":{"line":143,"column":28},"end":{"line":146,"column":27}},"69":{"start":{"line":143,"column":54},"end":{"line":146,"column":7}},"70":{"start":{"line":148,"column":6},"end":{"line":148,"column":82}},"71":{"start":{"line":149,"column":6},"end":{"line":149,"column":30}},"72":{"start":{"line":151,"column":6},"end":{"line":151,"column":63}},"73":{"start":{"line":154,"column":4},"end":{"line":154,"column":23}},"74":{"start":{"line":156,"column":4},"end":{"line":156,"column":70}},"75":{"start":{"line":157,"column":4},"end":{"line":157,"column":16}},"76":{"start":{"line":161,"column":39},"end":{"line":180,"column":1}},"77":{"start":{"line":167,"column":23},"end":{"line":177,"column":3}},"78":{"start":{"line":179,"column":2},"end":{"line":179,"column":62}},"79":{"start":{"line":182,"column":39},"end":{"line":201,"column":1}},"80":{"start":{"line":183,"column":15},"end":{"line":183,"column":31}},"81":{"start":{"line":184,"column":2},"end":{"line":184,"column":55}},"82":{"start":{"line":184,"column":13},"end":{"line":184,"column":55}},"83":{"start":{"line":186,"column":2},"end":{"line":200,"column":3}},"84":{"start":{"line":187,"column":20},"end":{"line":187,"column":60}},"85":{"start":{"line":188,"column":21},"end":{"line":195,"column":5}},"86":{"start":{"line":196,"column":4},"end":{"line":196,"column":20}},"87":{"start":{"line":198,"column":4},"end":{"line":198,"column":66}},"88":{"start":{"line":199,"column":4},"end":{"line":199,"column":16}},"89":{"start":{"line":203,"column":42},"end":{"line":216,"column":1}},"90":{"start":{"line":204,"column":15},"end":{"line":204,"column":31}},"91":{"start":{"line":205,"column":2},"end":{"line":205,"column":55}},"92":{"start":{"line":205,"column":13},"end":{"line":205,"column":55}},"93":{"start":{"line":207,"column":2},"end":{"line":215,"column":3}},"94":{"start":{"line":208,"column":20},"end":{"line":208,"column":46}},"95":{"start":{"line":209,"column":4},"end":{"line":211,"column":7}},"96":{"start":{"line":213,"column":4},"end":{"line":213,"column":66}},"97":{"start":{"line":214,"column":4},"end":{"line":214,"column":16}},"98":{"start":{"line":218,"column":45},"end":{"line":231,"column":1}},"99":{"start":{"line":219,"column":2},"end":{"line":230,"column":5}},"100":{"start":{"line":220,"column":4},"end":{"line":220,"column":63}},"101":{"start":{"line":223,"column":4},"end":{"line":229,"column":7}},"102":{"start":{"line":233,"column":46},"end":{"line":240,"column":1}},"103":{"start":{"line":234,"column":2},"end":{"line":239,"column":5}},"104":{"start":{"line":235,"column":4},"end":{"line":235,"column":59}},"105":{"start":{"line":236,"column":4},"end":{"line":238,"column":5}},"106":{"start":{"line":237,"column":6},"end":{"line":237,"column":65}},"107":{"start":{"line":242,"column":37},"end":{"line":298,"column":1}},"108":{"start":{"line":246,"column":2},"end":{"line":297,"column":3}},"109":{"start":{"line":247,"column":4},"end":{"line":250,"column":7}},"110":{"start":{"line":252,"column":29},"end":{"line":252,"column":80}},"111":{"start":{"line":259,"column":8},"end":{"line":259,"column":24}},"112":{"start":{"line":261,"column":4},"end":{"line":268,"column":5}},"113":{"start":{"line":262,"column":6},"end":{"line":266,"column":9}},"114":{"start":{"line":267,"column":6},"end":{"line":267,"column":13}},"115":{"start":{"line":270,"column":4},"end":{"line":274,"column":7}},"116":{"start":{"line":276,"column":4},"end":{"line":291,"column":5}},"117":{"start":{"line":280,"column":8},"end":{"line":286,"column":11}},"118":{"start":{"line":287,"column":8},"end":{"line":287,"column":14}},"119":{"start":{"line":290,"column":8},"end":{"line":290,"column":81}},"120":{"start":{"line":293,"column":4},"end":{"line":296,"column":7}},"121":{"start":{"line":300,"column":39},"end":{"line":356,"column":1}},"122":{"start":{"line":301,"column":2},"end":{"line":301,"column":62}},"123":{"start":{"line":303,"column":2},"end":{"line":306,"column":3}},"124":{"start":{"line":304,"column":4},"end":{"line":304,"column":68}},"125":{"start":{"line":305,"column":4},"end":{"line":305,"column":52}},"126":{"start":{"line":308,"column":2},"end":{"line":355,"column":3}},"127":{"start":{"line":309,"column":4},"end":{"line":309,"column":57}},"128":{"start":{"line":310,"column":23},"end":{"line":310,"column":60}},"129":{"start":{"line":311,"column":4},"end":{"line":311,"column":59}},"130":{"start":{"line":314,"column":6},"end":{"line":315,"column":62}},"131":{"start":{"line":317,"column":4},"end":{"line":320,"column":5}},"132":{"start":{"line":318,"column":6},"end":{"line":318,"column":68}},"133":{"start":{"line":319,"column":6},"end":{"line":319,"column":62}},"134":{"start":{"line":322,"column":4},"end":{"line":322,"column":63}},"135":{"start":{"line":323,"column":18},"end":{"line":323,"column":46}},"136":{"start":{"line":324,"column":4},"end":{"line":324,"column":69}},"137":{"start":{"line":326,"column":4},"end":{"line":330,"column":5}},"138":{"start":{"line":327,"column":6},"end":{"line":327,"column":75}},"139":{"start":{"line":328,"column":6},"end":{"line":328,"column":63}},"140":{"start":{"line":329,"column":6},"end":{"line":329,"column":55}},"141":{"start":{"line":333,"column":4},"end":{"line":333,"column":74}},"142":{"start":{"line":334,"column":4},"end":{"line":344,"column":5}},"143":{"start":{"line":335,"column":6},"end":{"line":335,"column":68}},"144":{"start":{"line":336,"column":6},"end":{"line":340,"column":9}},"145":{"start":{"line":341,"column":6},"end":{"line":341,"column":77}},"146":{"start":{"line":343,"column":6},"end":{"line":343,"column":92}},"147":{"start":{"line":346,"column":4},"end":{"line":346,"column":64}},"148":{"start":{"line":347,"column":4},"end":{"line":347,"column":36}},"149":{"start":{"line":350,"column":4},"end":{"line":353,"column":7}},"150":{"start":{"line":354,"column":4},"end":{"line":354,"column":37}},"151":{"start":{"line":358,"column":37},"end":{"line":397,"column":1}},"152":{"start":{"line":364,"column":2},"end":{"line":364,"column":36}},"153":{"start":{"line":364,"column":29},"end":{"line":364,"column":36}},"154":{"start":{"line":366,"column":2},"end":{"line":396,"column":3}},"155":{"start":{"line":367,"column":4},"end":{"line":367,"column":54}},"156":{"start":{"line":370,"column":20},"end":{"line":382,"column":5}},"157":{"start":{"line":384,"column":4},"end":{"line":384,"column":64}},"158":{"start":{"line":387,"column":4},"end":{"line":387,"column":58}},"159":{"start":{"line":388,"column":4},"end":{"line":388,"column":61}},"160":{"start":{"line":390,"column":4},"end":{"line":390,"column":62}},"161":{"start":{"line":391,"column":4},"end":{"line":395,"column":7}},"162":{"start":{"line":399,"column":44},"end":{"line":420,"column":1}},"163":{"start":{"line":400,"column":2},"end":{"line":400,"column":36}},"164":{"start":{"line":400,"column":29},"end":{"line":400,"column":36}},"165":{"start":{"line":402,"column":2},"end":{"line":419,"column":3}},"166":{"start":{"line":403,"column":4},"end":{"line":403,"column":63}},"167":{"start":{"line":405,"column":25},"end":{"line":414,"column":5}},"168":{"start":{"line":416,"column":4},"end":{"line":416,"column":40}},"169":{"start":{"line":418,"column":4},"end":{"line":418,"column":68}},"170":{"start":{"line":422,"column":46},"end":{"line":438,"column":1}},"171":{"start":{"line":423,"column":2},"end":{"line":423,"column":42}},"172":{"start":{"line":423,"column":29},"end":{"line":423,"column":42}},"173":{"start":{"line":425,"column":2},"end":{"line":437,"column":3}},"174":{"start":{"line":426,"column":23},"end":{"line":430,"column":6}},"175":{"start":{"line":432,"column":4},"end":{"line":432,"column":64}},"176":{"start":{"line":433,"column":4},"end":{"line":433,"column":24}},"177":{"start":{"line":435,"column":4},"end":{"line":435,"column":71}},"178":{"start":{"line":436,"column":4},"end":{"line":436,"column":17}},"179":{"start":{"line":440,"column":41},"end":{"line":457,"column":1}},"180":{"start":{"line":441,"column":2},"end":{"line":441,"column":36}},"181":{"start":{"line":441,"column":29},"end":{"line":441,"column":36}},"182":{"start":{"line":444,"column":2},"end":{"line":447,"column":5}},"183":{"start":{"line":445,"column":4},"end":{"line":445,"column":62}},"184":{"start":{"line":450,"column":2},"end":{"line":453,"column":5}},"185":{"start":{"line":451,"column":4},"end":{"line":451,"column":63}},"186":{"start":{"line":456,"column":2},"end":{"line":456,"column":55}},"187":{"start":{"line":459,"column":31},"end":{"line":463,"column":1}},"188":{"start":{"line":460,"column":2},"end":{"line":462,"column":3}},"189":{"start":{"line":461,"column":4},"end":{"line":461,"column":57}},"190":{"start":{"line":465,"column":32},"end":{"line":536,"column":1}},"191":{"start":{"line":466,"column":2},"end":{"line":535,"column":3}},"192":{"start":{"line":467,"column":4},"end":{"line":471,"column":7}},"193":{"start":{"line":474,"column":20},"end":{"line":474,"column":80}},"194":{"start":{"line":476,"column":4},"end":{"line":482,"column":5}},"195":{"start":{"line":477,"column":6},"end":{"line":480,"column":9}},"196":{"start":{"line":481,"column":6},"end":{"line":481,"column":13}},"197":{"start":{"line":484,"column":22},"end":{"line":484,"column":50}},"198":{"start":{"line":486,"column":4},"end":{"line":492,"column":5}},"199":{"start":{"line":487,"column":6},"end":{"line":490,"column":9}},"200":{"start":{"line":491,"column":6},"end":{"line":491,"column":13}},"201":{"start":{"line":494,"column":4},"end":{"line":498,"column":7}},"202":{"start":{"line":501,"column":20},"end":{"line":506,"column":5}},"203":{"start":{"line":508,"column":21},"end":{"line":514,"column":6}},"204":{"start":{"line":516,"column":19},"end":{"line":516,"column":40}},"205":{"start":{"line":518,"column":4},"end":{"line":523,"column":7}},"206":{"start":{"line":526,"column":4},"end":{"line":533,"column":7}},"207":{"start":{"line":534,"column":4},"end":{"line":534,"column":16}}},"fnMap":{"0":{"name":"(anonymous_0)","decl":{"start":{"line":13,"column":42},"end":{"line":13,"column":43}},"loc":{"start":{"line":13,"column":54},"end":{"line":25,"column":1}},"line":13},"1":{"name":"(anonymous_1)","decl":{"start":{"line":15,"column":32},"end":{"line":15,"column":33}},"loc":{"start":{"line":15,"column":56},"end":{"line":22,"column":3}},"line":15},"2":{"name":"(anonymous_2)","decl":{"start":{"line":27,"column":41},"end":{"line":27,"column":42}},"loc":{"start":{"line":27,"column":96},"end":{"line":62,"column":1}},"line":27},"3":{"name":"(anonymous_3)","decl":{"start":{"line":39,"column":4},"end":{"line":39,"column":5}},"loc":{"start":{"line":39,"column":18},"end":{"line":46,"column":5}},"line":39},"4":{"name":"(anonymous_4)","decl":{"start":{"line":47,"column":4},"end":{"line":47,"column":5}},"loc":{"start":{"line":47,"column":15},"end":{"line":54,"column":5}},"line":47},"5":{"name":"(anonymous_5)","decl":{"start":{"line":64,"column":34},"end":{"line":64,"column":35}},"loc":{"start":{"line":64,"column":86},"end":{"line":80,"column":1}},"line":64},"6":{"name":"(anonymous_6)","decl":{"start":{"line":76,"column":27},"end":{"line":76,"column":28}},"loc":{"start":{"line":76,"column":35},"end":{"line":79,"column":3}},"line":76},"7":{"name":"(anonymous_7)","decl":{"start":{"line":82,"column":38},"end":{"line":82,"column":39}},"loc":{"start":{"line":82,"column":87},"end":{"line":85,"column":1}},"line":82},"8":{"name":"(anonymous_8)","decl":{"start":{"line":87,"column":42},"end":{"line":87,"column":43}},"loc":{"start":{"line":87,"column":69},"end":{"line":124,"column":1}},"line":87},"9":{"name":"(anonymous_9)","decl":{"start":{"line":103,"column":16},"end":{"line":103,"column":17}},"loc":{"start":{"line":103,"column":34},"end":{"line":103,"column":41}},"line":103},"10":{"name":"(anonymous_10)","decl":{"start":{"line":104,"column":13},"end":{"line":104,"column":14}},"loc":{"start":{"line":104,"column":25},"end":{"line":104,"column":29}},"line":104},"11":{"name":"(anonymous_11)","decl":{"start":{"line":126,"column":40},"end":{"line":126,"column":41}},"loc":{"start":{"line":126,"column":101},"end":{"line":159,"column":1}},"line":126},"12":{"name":"(anonymous_12)","decl":{"start":{"line":132,"column":13},"end":{"line":132,"column":14}},"loc":{"start":{"line":132,"column":19},"end":{"line":132,"column":21}},"line":132},"13":{"name":"(anonymous_13)","decl":{"start":{"line":142,"column":38},"end":{"line":142,"column":39}},"loc":{"start":{"line":142,"column":52},"end":{"line":150,"column":5}},"line":142},"14":{"name":"(anonymous_14)","decl":{"start":{"line":143,"column":46},"end":{"line":143,"column":47}},"loc":{"start":{"line":143,"column":54},"end":{"line":146,"column":7}},"line":143},"15":{"name":"(anonymous_15)","decl":{"start":{"line":150,"column":7},"end":{"line":150,"column":8}},"loc":{"start":{"line":150,"column":18},"end":{"line":152,"column":5}},"line":150},"16":{"name":"(anonymous_16)","decl":{"start":{"line":161,"column":39},"end":{"line":161,"column":40}},"loc":{"start":{"line":166,"column":5},"end":{"line":180,"column":1}},"line":166},"17":{"name":"(anonymous_17)","decl":{"start":{"line":182,"column":39},"end":{"line":182,"column":40}},"loc":{"start":{"line":182,"column":82},"end":{"line":201,"column":1}},"line":182},"18":{"name":"(anonymous_18)","decl":{"start":{"line":203,"column":42},"end":{"line":203,"column":43}},"loc":{"start":{"line":203,"column":107},"end":{"line":216,"column":1}},"line":203},"19":{"name":"(anonymous_19)","decl":{"start":{"line":218,"column":45},"end":{"line":218,"column":46}},"loc":{"start":{"line":218,"column":51},"end":{"line":231,"column":1}},"line":218},"20":{"name":"(anonymous_20)","decl":{"start":{"line":219,"column":31},"end":{"line":219,"column":32}},"loc":{"start":{"line":219,"column":54},"end":{"line":230,"column":3}},"line":219},"21":{"name":"(anonymous_21)","decl":{"start":{"line":233,"column":46},"end":{"line":233,"column":47}},"loc":{"start":{"line":233,"column":94},"end":{"line":240,"column":1}},"line":233},"22":{"name":"(anonymous_22)","decl":{"start":{"line":234,"column":45},"end":{"line":234,"column":46}},"loc":{"start":{"line":234,"column":62},"end":{"line":239,"column":3}},"line":234},"23":{"name":"(anonymous_23)","decl":{"start":{"line":242,"column":37},"end":{"line":242,"column":38}},"loc":{"start":{"line":245,"column":5},"end":{"line":298,"column":1}},"line":245},"24":{"name":"(anonymous_24)","decl":{"start":{"line":300,"column":39},"end":{"line":300,"column":40}},"loc":{"start":{"line":300,"column":51},"end":{"line":356,"column":1}},"line":300},"25":{"name":"(anonymous_25)","decl":{"start":{"line":358,"column":37},"end":{"line":358,"column":38}},"loc":{"start":{"line":363,"column":6},"end":{"line":397,"column":1}},"line":363},"26":{"name":"(anonymous_26)","decl":{"start":{"line":399,"column":44},"end":{"line":399,"column":45}},"loc":{"start":{"line":399,"column":68},"end":{"line":420,"column":1}},"line":399},"27":{"name":"(anonymous_27)","decl":{"start":{"line":422,"column":46},"end":{"line":422,"column":47}},"loc":{"start":{"line":422,"column":58},"end":{"line":438,"column":1}},"line":422},"28":{"name":"(anonymous_28)","decl":{"start":{"line":440,"column":41},"end":{"line":440,"column":42}},"loc":{"start":{"line":440,"column":65},"end":{"line":457,"column":1}},"line":440},"29":{"name":"(anonymous_29)","decl":{"start":{"line":444,"column":60},"end":{"line":444,"column":61}},"loc":{"start":{"line":444,"column":78},"end":{"line":447,"column":3}},"line":444},"30":{"name":"(anonymous_30)","decl":{"start":{"line":450,"column":55},"end":{"line":450,"column":56}},"loc":{"start":{"line":450,"column":73},"end":{"line":453,"column":3}},"line":450},"31":{"name":"(anonymous_31)","decl":{"start":{"line":459,"column":31},"end":{"line":459,"column":32}},"loc":{"start":{"line":459,"column":37},"end":{"line":463,"column":1}},"line":459},"32":{"name":"(anonymous_32)","decl":{"start":{"line":465,"column":32},"end":{"line":465,"column":33}},"loc":{"start":{"line":465,"column":78},"end":{"line":536,"column":1}},"line":465}},"branchMap":{"0":{"loc":{"start":{"line":40,"column":6},"end":{"line":43,"column":7}},"type":"if","locations":[{"start":{"line":40,"column":6},"end":{"line":43,"column":7}},{"start":{},"end":{}}],"line":40},"1":{"loc":{"start":{"line":49,"column":6},"end":{"line":53,"column":7}},"type":"if","locations":[{"start":{"line":49,"column":6},"end":{"line":53,"column":7}},{"start":{"line":51,"column":13},"end":{"line":53,"column":7}}],"line":49},"2":{"loc":{"start":{"line":49,"column":10},"end":{"line":49,"column":74}},"type":"binary-expr","locations":[{"start":{"line":49,"column":10},"end":{"line":49,"column":44}},{"start":{"line":49,"column":48},"end":{"line":49,"column":74}}],"line":49},"3":{"loc":{"start":{"line":64,"column":41},"end":{"line":64,"column":56}},"type":"default-arg","locations":[{"start":{"line":64,"column":54},"end":{"line":64,"column":56}}],"line":64},"4":{"loc":{"start":{"line":66,"column":2},"end":{"line":66,"column":55}},"type":"if","locations":[{"start":{"line":66,"column":2},"end":{"line":66,"column":55}},{"start":{},"end":{}}],"line":66},"5":{"loc":{"start":{"line":87,"column":49},"end":{"line":87,"column":64}},"type":"default-arg","locations":[{"start":{"line":87,"column":60},"end":{"line":87,"column":64}}],"line":87},"6":{"loc":{"start":{"line":90,"column":4},"end":{"line":90,"column":24}},"type":"if","locations":[{"start":{"line":90,"column":4},"end":{"line":90,"column":24}},{"start":{},"end":{}}],"line":90},"7":{"loc":{"start":{"line":101,"column":4},"end":{"line":114,"column":5}},"type":"if","locations":[{"start":{"line":101,"column":4},"end":{"line":114,"column":5}},{"start":{},"end":{}}],"line":101},"8":{"loc":{"start":{"line":108,"column":6},"end":{"line":113,"column":7}},"type":"if","locations":[{"start":{"line":108,"column":6},"end":{"line":113,"column":7}},{"start":{"line":110,"column":13},"end":{"line":113,"column":7}}],"line":108},"9":{"loc":{"start":{"line":130,"column":4},"end":{"line":133,"column":5}},"type":"if","locations":[{"start":{"line":130,"column":4},"end":{"line":133,"column":5}},{"start":{},"end":{}}],"line":130},"10":{"loc":{"start":{"line":184,"column":2},"end":{"line":184,"column":55}},"type":"if","locations":[{"start":{"line":184,"column":2},"end":{"line":184,"column":55}},{"start":{},"end":{}}],"line":184},"11":{"loc":{"start":{"line":188,"column":21},"end":{"line":195,"column":5}},"type":"binary-expr","locations":[{"start":{"line":188,"column":21},"end":{"line":188,"column":57}},{"start":{"line":188,"column":61},"end":{"line":195,"column":5}}],"line":188},"12":{"loc":{"start":{"line":205,"column":2},"end":{"line":205,"column":55}},"type":"if","locations":[{"start":{"line":205,"column":2},"end":{"line":205,"column":55}},{"start":{},"end":{}}],"line":205},"13":{"loc":{"start":{"line":236,"column":4},"end":{"line":238,"column":5}},"type":"if","locations":[{"start":{"line":236,"column":4},"end":{"line":238,"column":5}},{"start":{},"end":{}}],"line":236},"14":{"loc":{"start":{"line":252,"column":29},"end":{"line":252,"column":80}},"type":"binary-expr","locations":[{"start":{"line":252,"column":29},"end":{"line":252,"column":46}},{"start":{"line":252,"column":50},"end":{"line":252,"column":74}},{"start":{"line":252,"column":78},"end":{"line":252,"column":80}}],"line":252},"15":{"loc":{"start":{"line":261,"column":4},"end":{"line":268,"column":5}},"type":"if","locations":[{"start":{"line":261,"column":4},"end":{"line":268,"column":5}},{"start":{},"end":{}}],"line":261},"16":{"loc":{"start":{"line":261,"column":8},"end":{"line":261,"column":25}},"type":"binary-expr","locations":[{"start":{"line":261,"column":8},"end":{"line":261,"column":13}},{"start":{"line":261,"column":17},"end":{"line":261,"column":25}}],"line":261},"17":{"loc":{"start":{"line":276,"column":4},"end":{"line":291,"column":5}},"type":"switch","locations":[{"start":{"line":277,"column":6},"end":{"line":277,"column":38}},{"start":{"line":278,"column":6},"end":{"line":278,"column":48}},{"start":{"line":279,"column":6},"end":{"line":287,"column":14}},{"start":{"line":289,"column":6},"end":{"line":290,"column":81}}],"line":276},"18":{"loc":{"start":{"line":282,"column":21},"end":{"line":282,"column":36}},"type":"binary-expr","locations":[{"start":{"line":282,"column":21},"end":{"line":282,"column":30}},{"start":{"line":282,"column":34},"end":{"line":282,"column":36}}],"line":282},"19":{"loc":{"start":{"line":283,"column":26},"end":{"line":283,"column":46}},"type":"binary-expr","locations":[{"start":{"line":283,"column":26},"end":{"line":283,"column":40}},{"start":{"line":283,"column":44},"end":{"line":283,"column":46}}],"line":283},"20":{"loc":{"start":{"line":284,"column":24},"end":{"line":284,"column":42}},"type":"binary-expr","locations":[{"start":{"line":284,"column":24},"end":{"line":284,"column":36}},{"start":{"line":284,"column":40},"end":{"line":284,"column":42}}],"line":284},"21":{"loc":{"start":{"line":303,"column":2},"end":{"line":306,"column":3}},"type":"if","locations":[{"start":{"line":303,"column":2},"end":{"line":306,"column":3}},{"start":{},"end":{}}],"line":303},"22":{"loc":{"start":{"line":314,"column":6},"end":{"line":315,"column":62}},"type":"binary-expr","locations":[{"start":{"line":314,"column":6},"end":{"line":314,"column":61}},{"start":{"line":315,"column":6},"end":{"line":315,"column":62}}],"line":314},"23":{"loc":{"start":{"line":317,"column":4},"end":{"line":320,"column":5}},"type":"if","locations":[{"start":{"line":317,"column":4},"end":{"line":320,"column":5}},{"start":{},"end":{}}],"line":317},"24":{"loc":{"start":{"line":324,"column":39},"end":{"line":324,"column":67}},"type":"cond-expr","locations":[{"start":{"line":324,"column":47},"end":{"line":324,"column":56}},{"start":{"line":324,"column":59},"end":{"line":324,"column":67}}],"line":324},"25":{"loc":{"start":{"line":326,"column":4},"end":{"line":330,"column":5}},"type":"if","locations":[{"start":{"line":326,"column":4},"end":{"line":330,"column":5}},{"start":{},"end":{}}],"line":326},"26":{"loc":{"start":{"line":326,"column":8},"end":{"line":326,"column":33}},"type":"binary-expr","locations":[{"start":{"line":326,"column":8},"end":{"line":326,"column":13}},{"start":{"line":326,"column":17},"end":{"line":326,"column":33}}],"line":326},"27":{"loc":{"start":{"line":334,"column":4},"end":{"line":344,"column":5}},"type":"if","locations":[{"start":{"line":334,"column":4},"end":{"line":344,"column":5}},{"start":{"line":342,"column":11},"end":{"line":344,"column":5}}],"line":334},"28":{"loc":{"start":{"line":364,"column":2},"end":{"line":364,"column":36}},"type":"if","locations":[{"start":{"line":364,"column":2},"end":{"line":364,"column":36}},{"start":{},"end":{}}],"line":364},"29":{"loc":{"start":{"line":400,"column":2},"end":{"line":400,"column":36}},"type":"if","locations":[{"start":{"line":400,"column":2},"end":{"line":400,"column":36}},{"start":{},"end":{}}],"line":400},"30":{"loc":{"start":{"line":406,"column":13},"end":{"line":406,"column":52}},"type":"binary-expr","locations":[{"start":{"line":406,"column":13},"end":{"line":406,"column":46}},{"start":{"line":406,"column":50},"end":{"line":406,"column":52}}],"line":406},"31":{"loc":{"start":{"line":407,"column":12},"end":{"line":407,"column":50}},"type":"binary-expr","locations":[{"start":{"line":407,"column":12},"end":{"line":407,"column":44}},{"start":{"line":407,"column":48},"end":{"line":407,"column":50}}],"line":407},"32":{"loc":{"start":{"line":408,"column":12},"end":{"line":408,"column":49}},"type":"binary-expr","locations":[{"start":{"line":408,"column":12},"end":{"line":408,"column":36}},{"start":{"line":408,"column":40},"end":{"line":408,"column":49}}],"line":408},"33":{"loc":{"start":{"line":423,"column":2},"end":{"line":423,"column":42}},"type":"if","locations":[{"start":{"line":423,"column":2},"end":{"line":423,"column":42}},{"start":{},"end":{}}],"line":423},"34":{"loc":{"start":{"line":441,"column":2},"end":{"line":441,"column":36}},"type":"if","locations":[{"start":{"line":441,"column":2},"end":{"line":441,"column":36}},{"start":{},"end":{}}],"line":441},"35":{"loc":{"start":{"line":460,"column":2},"end":{"line":462,"column":3}},"type":"if","locations":[{"start":{"line":460,"column":2},"end":{"line":462,"column":3}},{"start":{},"end":{}}],"line":460},"36":{"loc":{"start":{"line":476,"column":4},"end":{"line":482,"column":5}},"type":"if","locations":[{"start":{"line":476,"column":4},"end":{"line":482,"column":5}},{"start":{},"end":{}}],"line":476},"37":{"loc":{"start":{"line":486,"column":4},"end":{"line":492,"column":5}},"type":"if","locations":[{"start":{"line":486,"column":4},"end":{"line":492,"column":5}},{"start":{},"end":{}}],"line":486}},"s":{"0":1,"1":1,"2":0,"3":0,"4":0,"5":0,"6":0,"7":0,"8":0,"9":0,"10":1,"11":0,"12":0,"13":0,"14":0,"15":0,"16":0,"17":0,"18":0,"19":0,"20":0,"21":0,"22":0,"23":0,"24":0,"25":1,"26":0,"27":0,"28":0,"29":0,"30":0,"31":0,"32":0,"33":1,"34":0,"35":0,"36":1,"37":0,"38":0,"39":0,"40":0,"41":0,"42":0,"43":0,"44":0,"45":0,"46":0,"47":0,"48":0,"49":0,"50":0,"51":0,"52":0,"53":0,"54":0,"55":0,"56":0,"57":0,"58":1,"59":0,"60":0,"61":0,"62":0,"63":0,"64":0,"65":0,"66":0,"67":0,"68":0,"69":0,"70":0,"71":0,"72":0,"73":0,"74":0,"75":0,"76":1,"77":0,"78":0,"79":1,"80":0,"81":0,"82":0,"83":0,"84":0,"85":0,"86":0,"87":0,"88":0,"89":1,"90":0,"91":0,"92":0,"93":0,"94":0,"95":0,"96":0,"97":0,"98":1,"99":0,"100":0,"101":0,"102":1,"103":0,"104":0,"105":0,"106":0,"107":1,"108":0,"109":0,"110":0,"111":0,"112":0,"113":0,"114":0,"115":0,"116":0,"117":0,"118":0,"119":0,"120":0,"121":1,"122":0,"123":0,"124":0,"125":0,"126":0,"127":0,"128":0,"129":0,"130":0,"131":0,"132":0,"133":0,"134":0,"135":0,"136":0,"137":0,"138":0,"139":0,"140":0,"141":0,"142":0,"143":0,"144":0,"145":0,"146":0,"147":0,"148":0,"149":0,"150":0,"151":1,"152":0,"153":0,"154":0,"155":0,"156":0,"157":0,"158":0,"159":0,"160":0,"161":0,"162":1,"163":0,"164":0,"165":0,"166":0,"167":0,"168":0,"169":0,"170":1,"171":0,"172":0,"173":0,"174":0,"175":0,"176":0,"177":0,"178":0,"179":1,"180":0,"181":0,"182":0,"183":0,"184":0,"185":0,"186":0,"187":1,"188":0,"189":0,"190":1,"191":0,"192":0,"193":0,"194":0,"195":0,"196":0,"197":0,"198":0,"199":0,"200":0,"201":0,"202":0,"203":0,"204":0,"205":0,"206":0,"207":0},"f":{"0":0,"1":0,"2":0,"3":0,"4":0,"5":0,"6":0,"7":0,"8":0,"9":0,"10":0,"11":0,"12":0,"13":0,"14":0,"15":0,"16":0,"17":0,"18":0,"19":0,"20":0,"21":0,"22":0,"23":0,"24":0,"25":0,"26":0,"27":0,"28":0,"29":0,"30":0,"31":0,"32":0},"b":{"0":[0,0],"1":[0,0],"2":[0,0],"3":[0],"4":[0,0],"5":[0],"6":[0,0],"7":[0,0],"8":[0,0],"9":[0,0],"10":[0,0],"11":[0,0],"12":[0,0],"13":[0,0],"14":[0,0,0],"15":[0,0],"16":[0,0],"17":[0,0,0,0],"18":[0,0],"19":[0,0],"20":[0,0],"21":[0,0],"22":[0,0],"23":[0,0],"24":[0,0],"25":[0,0],"26":[0,0],"27":[0,0],"28":[0,0],"29":[0,0],"30":[0,0],"31":[0,0],"32":[0,0],"33":[0,0],"34":[0,0],"35":[0,0],"36":[0,0],"37":[0,0]},"_coverageSchema":"1a1c01bbd47fc00a2c39e90264f33305004495a9","hash":"5f458e2c834a03d6ad922fd52b42e0c15d6ffc3c"}
coverage/coverage-final.json:,"/Users/<USER>/Desktop/satbana/types/notifications.ts": {"path":"/Users/<USER>/Desktop/satbana/types/notifications.ts","statementMap":{"0":{"start":{"line":23,"column":80},"end":{"line":97,"column":1}}},"fnMap":{},"branchMap":{},"s":{"0":1},"f":{},"b":{},"_coverageSchema":"1a1c01bbd47fc00a2c39e90264f33305004495a9","hash":"a9ea60f76e7f1a5cdde9a1fb2c3568a747de012f"}
coverage/coverage-final.json:,"/Users/<USER>/Desktop/satbana/utils/cacheManager.ts": {"path":"/Users/<USER>/Desktop/satbana/utils/cacheManager.ts","statementMap":{"0":{"start":{"line":1,"column":21},"end":{"line":19,"column":1}},"1":{"start":{"line":5,"column":4},"end":{"line":5,"column":39}},"2":{"start":{"line":9,"column":4},"end":{"line":9,"column":31}},"3":{"start":{"line":13,"column":4},"end":{"line":13,"column":27}},"4":{"start":{"line":17,"column":4},"end":{"line":17,"column":23}}},"fnMap":{"0":{"name":"(anonymous_0)","decl":{"start":{"line":4,"column":2},"end":{"line":4,"column":3}},"loc":{"start":{"line":4,"column":25},"end":{"line":6,"column":3}},"line":4},"1":{"name":"(anonymous_1)","decl":{"start":{"line":8,"column":2},"end":{"line":8,"column":3}},"loc":{"start":{"line":8,"column":37},"end":{"line":10,"column":3}},"line":8},"2":{"name":"(anonymous_2)","decl":{"start":{"line":12,"column":2},"end":{"line":12,"column":3}},"loc":{"start":{"line":12,"column":28},"end":{"line":14,"column":3}},"line":12},"3":{"name":"(anonymous_3)","decl":{"start":{"line":16,"column":2},"end":{"line":16,"column":3}},"loc":{"start":{"line":16,"column":16},"end":{"line":18,"column":3}},"line":16}},"branchMap":{"0":{"loc":{"start":{"line":5,"column":11},"end":{"line":5,"column":38}},"type":"binary-expr","locations":[{"start":{"line":5,"column":11},"end":{"line":5,"column":30}},{"start":{"line":5,"column":34},"end":{"line":5,"column":38}}],"line":5}},"s":{"0":1,"1":0,"2":0,"3":0,"4":0},"f":{"0":0,"1":0,"2":0,"3":0},"b":{"0":[0,0]},"_coverageSchema":"1a1c01bbd47fc00a2c39e90264f33305004495a9","hash":"d7c2e1e125794db1060ba69c30aae36274653eb2"}
coverage/coverage-final.json:,"/Users/<USER>/Desktop/satbana/utils/firebaseCleanup.ts": {"path":"/Users/<USER>/Desktop/satbana/utils/firebaseCleanup.ts","statementMap":{"0":{"start":{"line":7,"column":36},"end":{"line":7,"column":38}},"1":{"start":{"line":8,"column":26},"end":{"line":8,"column":27}},"2":{"start":{"line":10,"column":31},"end":{"line":22,"column":1}},"3":{"start":{"line":11,"column":13},"end":{"line":11,"column":43}},"4":{"start":{"line":12,"column":2},"end":{"line":12,"column":97}},"5":{"start":{"line":14,"column":2},"end":{"line":18,"column":5}},"6":{"start":{"line":20,"column":2},"end":{"line":20,"column":72}},"7":{"start":{"line":21,"column":2},"end":{"line":21,"column":12}},"8":{"start":{"line":24,"column":30},"end":{"line":41,"column":1}},"9":{"start":{"line":25,"column":2},"end":{"line":25,"column":83}},"10":{"start":{"line":27,"column":2},"end":{"line":35,"column":5}},"11":{"start":{"line":28,"column":4},"end":{"line":34,"column":5}},"12":{"start":{"line":29,"column":6},"end":{"line":29,"column":107}},"13":{"start":{"line":30,"column":6},"end":{"line":30,"column":24}},"14":{"start":{"line":31,"column":6},"end":{"line":31,"column":68}},"15":{"start":{"line":33,"column":6},"end":{"line":33,"column":71}},"16":{"start":{"line":37,"column":19},"end":{"line":37,"column":39}},"17":{"start":{"line":38,"column":2},"end":{"line":38,"column":21}},"18":{"start":{"line":39,"column":2},"end":{"line":39,"column":26}},"19":{"start":{"line":40,"column":2},"end":{"line":40,"column":77}},"20":{"start":{"line":43,"column":34},"end":{"line":48,"column":1}},"21":{"start":{"line":44,"column":2},"end":{"line":44,"column":92}},"22":{"start":{"line":45,"column":2},"end":{"line":45,"column":21}},"23":{"start":{"line":46,"column":2},"end":{"line":46,"column":26}},"24":{"start":{"line":47,"column":2},"end":{"line":47,"column":45}}},"fnMap":{"0":{"name":"(anonymous_0)","decl":{"start":{"line":10,"column":31},"end":{"line":10,"column":32}},"loc":{"start":{"line":10,"column":86},"end":{"line":22,"column":1}},"line":10},"1":{"name":"(anonymous_1)","decl":{"start":{"line":24,"column":30},"end":{"line":24,"column":31}},"loc":{"start":{"line":24,"column":36},"end":{"line":41,"column":1}},"line":24},"2":{"name":"(anonymous_2)","decl":{"start":{"line":27,"column":24},"end":{"line":27,"column":25}},"loc":{"start":{"line":27,"column":40},"end":{"line":35,"column":3}},"line":27},"3":{"name":"(anonymous_3)","decl":{"start":{"line":43,"column":34},"end":{"line":43,"column":35}},"loc":{"start":{"line":43,"column":40},"end":{"line":48,"column":1}},"line":43}},"branchMap":{"0":{"loc":{"start":{"line":10,"column":57},"end":{"line":10,"column":81}},"type":"default-arg","locations":[{"start":{"line":10,"column":72},"end":{"line":10,"column":81}}],"line":10}},"s":{"0":1,"1":1,"2":1,"3":0,"4":0,"5":0,"6":0,"7":0,"8":1,"9":0,"10":0,"11":0,"12":0,"13":0,"14":0,"15":0,"16":0,"17":0,"18":0,"19":0,"20":1,"21":0,"22":0,"23":0,"24":0},"f":{"0":0,"1":0,"2":0,"3":0},"b":{"0":[0]},"_coverageSchema":"1a1c01bbd47fc00a2c39e90264f33305004495a9","hash":"a2c3fcaa3ae8ccf9a24ce5fb1f4eade85cd4c6da"}
coverage/lcov-report/screens/LoginScreen.tsx.html:        &lt;Text style={styles.title}&gt;Welcome to satbana&lt;/Text&gt;
coverage/lcov-report/services/firebaseService.js.html:<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">// /Users/<USER>/Desktop/satbana/services/firebaseService.js
documentation/# satbana-Change-Log.txt:satbana_Change_Log.txt
documentation/# satbana-Change-Log.txt:Under "iOS app configuration" (for your bundle ID com.company.satbana)
documentation/# satbana-Change-Log.txt: LOG  Running "com.company.satbana" with {"rootTag":1,"initialProps":{}}
documentation/# satbana-Change-Log.txt:  "Successfully sent notification: projects/satbana-2052f/messages/1734619302139820"
documentation/# satbana-Change-Log.txt:LOG  Running "com.company.satbana" with {"rootTag":1,"initialProps":{}}
documentation/# satbana-Change-Log.txt:  "Successfully sent notification: projects/satbana-2052f/messages/1734620975857983"
documentation/# satbana-Change-Log.txt:  "Successfully sent notification: projects/satbana-2052f/messages/1734621124654562"
documentation/# satbana-Change-Log.txt: LOG  Running "com.company.satbana" with {"rootTag":1,"initialProps":{}}
documentation/# satbana-Change-Log.txt:LOG  Running "com.company.satbana" with {"rootTag":11,"initialProps":{}}
documentation/# satbana-Change-Log.txt: LOG  Running "com.company.satbana" with {"rootTag":1,"initialProps":{}}
documentation/# satbana-Change-Log.txt: LOG  Running "com.company.satbana" with {"rootTag":1,"initialProps":{}}
documentation/# satbana-Change-Log.txt:  LOG  Running "com.company.satbana" with {"rootTag":11,"initialProps":{}}
documentation/# satbana-Change-Log.txt:     at AppNavigator (http://192.168.1.32:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:120301:111)
documentation/# satbana-Change-Log.txt:     at EnsureSingleNavigator (http://192.168.1.32:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:123818:24)
documentation/# satbana-Change-Log.txt:     at BaseNavigationContainer (http://192.168.1.32:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:123416:28)
documentation/# satbana-Change-Log.txt:     at ThemeProvider (http://192.168.1.32:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:128888:21)
documentation/# satbana-Change-Log.txt:     at NavigationContainerInner (http://192.168.1.32:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:128778:26)
documentation/# satbana-Change-Log.txt:     at View (http://192.168.1.32:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:57331:43)
documentation/# satbana-Change-Log.txt:     at SafeAreaProvider (http://192.168.1.32:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:183182:24)
documentation/# satbana-Change-Log.txt:     at App (http://192.168.1.32:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:120228:43)
documentation/# satbana-Change-Log.txt:     at View (http://192.168.1.32:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:57331:43)
documentation/# satbana-Change-Log.txt:     at View (http://192.168.1.32:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:57331:43)
documentation/# satbana-Change-Log.txt:     at View (http://192.168.1.32:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:57331:43)
documentation/# satbana-Change-Log.txt:     at AppContainer (http://192.168.1.32:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:57196:36)
documentation/# satbana-Change-Log.txt:     at com.company.satbana(RootComponent) (http://192.168.1.32:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:105780:28)
documentation/# satbana-Change-Log.txt:     at AppNavigator (http://192.168.1.32:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:120301:111)
documentation/# satbana-Change-Log.txt:     at EnsureSingleNavigator (http://192.168.1.32:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:123818:24)
documentation/# satbana-Change-Log.txt:     at BaseNavigationContainer (http://192.168.1.32:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:123416:28)
documentation/# satbana-Change-Log.txt:     at ThemeProvider (http://192.168.1.32:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:128888:21)
documentation/# satbana-Change-Log.txt:     at NavigationContainerInner (http://192.168.1.32:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:128778:26)
documentation/# satbana-Change-Log.txt:     at View (http://192.168.1.32:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:57331:43)
documentation/# satbana-Change-Log.txt:     at SafeAreaProvider (http://192.168.1.32:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:183182:24)
documentation/# satbana-Change-Log.txt:     at App (http://192.168.1.32:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:120228:43)
documentation/# satbana-Change-Log.txt:     at View (http://192.168.1.32:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:57331:43)
documentation/# satbana-Change-Log.txt:     at View (http://192.168.1.32:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:57331:43)
documentation/# satbana-Change-Log.txt:     at View (http://192.168.1.32:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:57331:43)
documentation/# satbana-Change-Log.txt:     at AppContainer (http://192.168.1.32:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:57196:36)
documentation/# satbana-Change-Log.txt:     at com.company.satbana(RootComponent) (http://192.168.1.32:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:105780:28)
documentation/# satbana-Change-Log.txt: LOG  Running "com.company.satbana" with {"rootTag":1,"initialProps":{}}
documentation/# satbana-Change-Log.txt:  LOG  Running "com.company.satbana" with {"rootTag":11,"initialProps":{}}
documentation/# satbana-Change-Log.txt: LOG  Running "com.company.satbana" with {"rootTag":1,"initialProps":{}}
documentation/# satbana-Change-Log.txt:  "Successfully sent notification: projects/satbana-2052f/messages/1734619302139820"
documentation/# satbana-Change-Log.txt:LOG  Running "com.company.satbana" with {"rootTag":1,"initialProps":{}}
documentation/# satbana-Change-Log.txt:  "Successfully sent notification: projects/satbana-2052f/messages/1734620975857983"
documentation/# satbana-Change-Log.txt:  "Successfully sent notification: projects/satbana-2052f/messages/1734621124654562"
documentation/# satbana-Change-Log.txt: LOG  Running "com.company.satbana" with {"rootTag":1,"initialProps":{}}
documentation/# satbana-Change-Log.txt:LOG  Running "com.company.satbana" with {"rootTag":11,"initialProps":{}}
documentation/# satbana-Change-Log.txt: LOG  Running "com.company.satbana" with {"rootTag":1,"initialProps":{}}
documentation/# satbana-Change-Log.txt: LOG  Running "com.company.satbana" with {"rootTag":1,"initialProps":{}}
documentation/# satbana-Change-Log.txt:  LOG  Running "com.company.satbana" with {"rootTag":11,"initialProps":{}}
documentation/# satbana-Change-Log.txt:     at AppNavigator (http://192.168.1.32:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:120301:111)
documentation/# satbana-Change-Log.txt:     at EnsureSingleNavigator (http://192.168.1.32:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:123818:24)
documentation/# satbana-Change-Log.txt:     at BaseNavigationContainer (http://192.168.1.32:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:123416:28)
documentation/# satbana-Change-Log.txt:     at ThemeProvider (http://192.168.1.32:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:128888:21)
documentation/# satbana-Change-Log.txt:     at NavigationContainerInner (http://192.168.1.32:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:128778:26)
documentation/# satbana-Change-Log.txt:     at View (http://192.168.1.32:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:57331:43)
documentation/# satbana-Change-Log.txt:     at SafeAreaProvider (http://192.168.1.32:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:183182:24)
documentation/# satbana-Change-Log.txt:     at App (http://192.168.1.32:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:120228:43)
documentation/# satbana-Change-Log.txt:     at View (http://192.168.1.32:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:57331:43)
documentation/# satbana-Change-Log.txt:     at View (http://192.168.1.32:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:57331:43)
documentation/# satbana-Change-Log.txt:     at View (http://192.168.1.32:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:57331:43)
documentation/# satbana-Change-Log.txt:     at AppContainer (http://192.168.1.32:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:57196:36)
documentation/# satbana-Change-Log.txt:     at com.company.satbana(RootComponent) (http://192.168.1.32:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:105780:28)
documentation/# satbana-Change-Log.txt:     at AppNavigator (http://192.168.1.32:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:120301:111)
documentation/# satbana-Change-Log.txt:     at EnsureSingleNavigator (http://192.168.1.32:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:123818:24)
documentation/# satbana-Change-Log.txt:     at BaseNavigationContainer (http://192.168.1.32:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:123416:28)
documentation/# satbana-Change-Log.txt:     at ThemeProvider (http://192.168.1.32:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:128888:21)
documentation/# satbana-Change-Log.txt:     at NavigationContainerInner (http://192.168.1.32:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:128778:26)
documentation/# satbana-Change-Log.txt:     at View (http://192.168.1.32:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:57331:43)
documentation/# satbana-Change-Log.txt:     at SafeAreaProvider (http://192.168.1.32:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:183182:24)
documentation/# satbana-Change-Log.txt:     at App (http://192.168.1.32:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:120228:43)
documentation/# satbana-Change-Log.txt:     at View (http://192.168.1.32:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:57331:43)
documentation/# satbana-Change-Log.txt:     at View (http://192.168.1.32:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:57331:43)
documentation/# satbana-Change-Log.txt:     at View (http://192.168.1.32:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:57331:43)
documentation/# satbana-Change-Log.txt:     at AppContainer (http://192.168.1.32:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:57196:36)
documentation/# satbana-Change-Log.txt:     at com.company.satbana(RootComponent) (http://192.168.1.32:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:105780:28)
documentation/# satbana-Change-Log.txt: LOG  Running "com.company.satbana" with {"rootTag":1,"initialProps":{}}
documentation/# satbana-Change-Log.txt:  LOG  Running "com.company.satbana" with {"rootTag":11,"initialProps":{}}
documentation/# satbana-Change-Log.txt:  "Successfully sent notification: projects/satbana-2052f/messages/1734619302139820"
documentation/# satbana-Change-Log.txt: LOG  Running "com.company.satbana" with {"rootTag":1,"initialProps":{}}
documentation/# satbana-Change-Log.txt:Under "iOS app configuration" (for your bundle ID com.company.satbana)
documentation/# satbana-Change-Log.txt: LOG  Running "com.company.satbana" with {"rootTag":1,"initialProps":{}}
documentation/# satbana-Change-Log.txt:  "Successfully sent notification: projects/satbana-2052f/messages/1734619302139820"
documentation/# satbana-Change-Log.txt:LOG  Running "com.company.satbana" with {"rootTag":1,"initialProps":{}}
documentation/# satbana-Change-Log.txt:  "Successfully sent notification: projects/satbana-2052f/messages/1734620975857983"
documentation/# satbana-Change-Log.txt:  "Successfully sent notification: projects/satbana-2052f/messages/1734621124654562"
documentation/# satbana-Change-Log.txt: LOG  Running "com.company.satbana" with {"rootTag":1,"initialProps":{}}
documentation/# satbana-Change-Log.txt:LOG  Running "com.company.satbana" with {"rootTag":11,"initialProps":{}}
documentation/# satbana-Change-Log.txt: LOG  Running "com.company.satbana" with {"rootTag":1,"initialProps":{}}
documentation/EXPO_TO_RN_MIGRATION.md:- [x] Move admin files to satbana-admin project
documentation/FileStructure.md:    ios/satbana/AppDelegate.mm - iOS app delegate implementation
documentation/FileStructure.md:    ios/satbana/AppDelegate.h - iOS app delegate header
documentation/FileStructure.md:    ios/satbana/AppDelegate.m - iOS app delegate implementation (Objective-C)
documentation/database_structure_analysis.md:Omers-MBP:satbana-admin omeryazici$ node databaseStructureScanner.mjs
documentation/filelist.yaml:├── # satbana-Change-Log.txt
documentation/filelist.yaml:│   │   │               ├── Pods-satbana.xcscheme
documentation/filelist.yaml:│   │   │   ├── Pods-satbana
documentation/filelist.yaml:│   │   │   │   ├── Pods-satbana-Info.plist
documentation/filelist.yaml:│   │   │   │   ├── Pods-satbana-acknowledgements.markdown
documentation/filelist.yaml:│   │   │   │   ├── Pods-satbana-acknowledgements.plist
documentation/filelist.yaml:│   │   │   │   ├── Pods-satbana-dummy.m
documentation/filelist.yaml:│   │   │   │   ├── Pods-satbana-frameworks.sh
documentation/filelist.yaml:│   │   │   │   ├── Pods-satbana-resources.sh
documentation/filelist.yaml:│   │   │   │   ├── Pods-satbana-umbrella.h
documentation/filelist.yaml:│   │   │   │   ├── Pods-satbana.debug.xcconfig
documentation/filelist.yaml:│   │   │   │   ├── Pods-satbana.modulemap
documentation/filelist.yaml:│   │   │   │   ├── Pods-satbana.release.xcconfig
documentation/filelist.yaml:│   ├── satbana
documentation/filelist.yaml:│   │   ├── satbana-Bridging-Header.h
documentation/filelist.yaml:│   │   └── satbana.entitlements
documentation/filelist.yaml:│   ├── satbana.xcodeproj
documentation/filelist.yaml:│   │           └── satbana.xcscheme
documentation/filelist.yaml:│   └── satbana.xcworkspace
documentation/testLog.txt: LOG  Debug - currentUser: {"_redirectEventId": undefined, "apiKey": "AIzaSyBOHewmZjlRWGu1jeEI-uBSkAn56xLMlec", "appName": "[DEFAULT]", "createdAt": "**********022", "displayName": undefined, "email": "<EMAIL>", "emailVerified": false, "isAnonymous": false, "lastLoginAt": "**********022", "phoneNumber": undefined, "photoURL": undefined, "providerData": [[Object]], "stsTokenManager": {"accessToken": "eyJhbGciOiJSUzI1NiIsImtpZCI6IjgxYjUyMjFlN2E1ZGUwZTVhZjQ5N2UzNzVhNzRiMDZkODJiYTc4OGIiLCJ0eXAiOiJKV1QifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.H_zOceORoH_aHhP1g66GbzUqpMNsdQBxJsRRWJCPeSzYaf-GnY_tyd3azcQOgQK6iXQ1o7bmjdNAHjzfjcMWpLqzTRRnTcE3Bapvr2ijeG6u-5cs1DbVDt2Dl7paWVkCXnxGJFugp93CglWfURGywm_ZyusLX1SbLiQI13MUI33IbIdAZpNkkIIsmoh7CnasofG2K3zjGO8WzGO1YRCWSLgQLWzO02xAp1KXQfnUbhtm50HZtaXxNYu3fBeABm4tGkGbgUYLwEcpKqvcDg3a0L6paFYFYHLBKfaVTnlwPR3jz-417a4FlrF8Hby0H9rA8Ftrxxg2xDoRFTON2DqiiQ", "expirationTime": 1737624716251, "refreshToken": "AMf-vBxeXh6squOf0d4IjIQmBTYJ5i3FdI8KCy5j0XaUlnTTWS72RrY-Ietd-G04VMBvqvzHaSPnyOYhOLEu6OtcmEW85TSKFGVov0C6IoGP1Qr-SF9wnbbflhQ1ZA9Ee9RYAqfBKFRHXiMO6gLZ1LS2EGZ-psfY3ZLoYCswTWVhTBf7NVaM6CCs_Wti401-mcB5NXrTdHo8QCOwYO3UWh_SyyjrCtUThw"}, "tenantId": undefined, "uid": "fcYeWrztRZhb1VGKt66lHTLyHBP2"}
documentation/testLog.txt: LOG  Debug - currentUser: {"_redirectEventId": undefined, "apiKey": "AIzaSyBOHewmZjlRWGu1jeEI-uBSkAn56xLMlec", "appName": "[DEFAULT]", "createdAt": "**********022", "displayName": undefined, "email": "<EMAIL>", "emailVerified": false, "isAnonymous": false, "lastLoginAt": "**********022", "phoneNumber": undefined, "photoURL": undefined, "providerData": [[Object]], "stsTokenManager": {"accessToken": "eyJhbGciOiJSUzI1NiIsImtpZCI6IjgxYjUyMjFlN2E1ZGUwZTVhZjQ5N2UzNzVhNzRiMDZkODJiYTc4OGIiLCJ0eXAiOiJKV1QifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.H_zOceORoH_aHhP1g66GbzUqpMNsdQBxJsRRWJCPeSzYaf-GnY_tyd3azcQOgQK6iXQ1o7bmjdNAHjzfjcMWpLqzTRRnTcE3Bapvr2ijeG6u-5cs1DbVDt2Dl7paWVkCXnxGJFugp93CglWfURGywm_ZyusLX1SbLiQI13MUI33IbIdAZpNkkIIsmoh7CnasofG2K3zjGO8WzGO1YRCWSLgQLWzO02xAp1KXQfnUbhtm50HZtaXxNYu3fBeABm4tGkGbgUYLwEcpKqvcDg3a0L6paFYFYHLBKfaVTnlwPR3jz-417a4FlrF8Hby0H9rA8Ftrxxg2xDoRFTON2DqiiQ", "expirationTime": 1737624716251, "refreshToken": "AMf-vBxeXh6squOf0d4IjIQmBTYJ5i3FdI8KCy5j0XaUlnTTWS72RrY-Ietd-G04VMBvqvzHaSPnyOYhOLEu6OtcmEW85TSKFGVov0C6IoGP1Qr-SF9wnbbflhQ1ZA9Ee9RYAqfBKFRHXiMO6gLZ1LS2EGZ-psfY3ZLoYCswTWVhTBf7NVaM6CCs_Wti401-mcB5NXrTdHo8QCOwYO3UWh_SyyjrCtUThw"}, "tenantId": undefined, "uid": "fcYeWrztRZhb1VGKt66lHTLyHBP2"}
documentation/testLog.txt: LOG  Debug - currentUser: {"_redirectEventId": undefined, "apiKey": "AIzaSyBOHewmZjlRWGu1jeEI-uBSkAn56xLMlec", "appName": "[DEFAULT]", "createdAt": "**********022", "displayName": undefined, "email": "<EMAIL>", "emailVerified": false, "isAnonymous": false, "lastLoginAt": "**********022", "phoneNumber": undefined, "photoURL": undefined, "providerData": [[Object]], "stsTokenManager": {"accessToken": "eyJhbGciOiJSUzI1NiIsImtpZCI6IjgxYjUyMjFlN2E1ZGUwZTVhZjQ5N2UzNzVhNzRiMDZkODJiYTc4OGIiLCJ0eXAiOiJKV1QifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.H_zOceORoH_aHhP1g66GbzUqpMNsdQBxJsRRWJCPeSzYaf-GnY_tyd3azcQOgQK6iXQ1o7bmjdNAHjzfjcMWpLqzTRRnTcE3Bapvr2ijeG6u-5cs1DbVDt2Dl7paWVkCXnxGJFugp93CglWfURGywm_ZyusLX1SbLiQI13MUI33IbIdAZpNkkIIsmoh7CnasofG2K3zjGO8WzGO1YRCWSLgQLWzO02xAp1KXQfnUbhtm50HZtaXxNYu3fBeABm4tGkGbgUYLwEcpKqvcDg3a0L6paFYFYHLBKfaVTnlwPR3jz-417a4FlrF8Hby0H9rA8Ftrxxg2xDoRFTON2DqiiQ", "expirationTime": 1737624716251, "refreshToken": "AMf-vBxeXh6squOf0d4IjIQmBTYJ5i3FdI8KCy5j0XaUlnTTWS72RrY-Ietd-G04VMBvqvzHaSPnyOYhOLEu6OtcmEW85TSKFGVov0C6IoGP1Qr-SF9wnbbflhQ1ZA9Ee9RYAqfBKFRHXiMO6gLZ1LS2EGZ-psfY3ZLoYCswTWVhTBf7NVaM6CCs_Wti401-mcB5NXrTdHo8QCOwYO3UWh_SyyjrCtUThw"}, "tenantId": undefined, "uid": "fcYeWrztRZhb1VGKt66lHTLyHBP2"}
documentation/testLog.txt: LOG  Debug - currentUser: {"_redirectEventId": undefined, "apiKey": "AIzaSyBOHewmZjlRWGu1jeEI-uBSkAn56xLMlec", "appName": "[DEFAULT]", "createdAt": "**********022", "displayName": undefined, "email": "<EMAIL>", "emailVerified": false, "isAnonymous": false, "lastLoginAt": "**********022", "phoneNumber": undefined, "photoURL": undefined, "providerData": [[Object]], "stsTokenManager": {"accessToken": "eyJhbGciOiJSUzI1NiIsImtpZCI6IjgxYjUyMjFlN2E1ZGUwZTVhZjQ5N2UzNzVhNzRiMDZkODJiYTc4OGIiLCJ0eXAiOiJKV1QifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.H_zOceORoH_aHhP1g66GbzUqpMNsdQBxJsRRWJCPeSzYaf-GnY_tyd3azcQOgQK6iXQ1o7bmjdNAHjzfjcMWpLqzTRRnTcE3Bapvr2ijeG6u-5cs1DbVDt2Dl7paWVkCXnxGJFugp93CglWfURGywm_ZyusLX1SbLiQI13MUI33IbIdAZpNkkIIsmoh7CnasofG2K3zjGO8WzGO1YRCWSLgQLWzO02xAp1KXQfnUbhtm50HZtaXxNYu3fBeABm4tGkGbgUYLwEcpKqvcDg3a0L6paFYFYHLBKfaVTnlwPR3jz-417a4FlrF8Hby0H9rA8Ftrxxg2xDoRFTON2DqiiQ", "expirationTime": 1737624716251, "refreshToken": "AMf-vBxeXh6squOf0d4IjIQmBTYJ5i3FdI8KCy5j0XaUlnTTWS72RrY-Ietd-G04VMBvqvzHaSPnyOYhOLEu6OtcmEW85TSKFGVov0C6IoGP1Qr-SF9wnbbflhQ1ZA9Ee9RYAqfBKFRHXiMO6gLZ1LS2EGZ-psfY3ZLoYCswTWVhTBf7NVaM6CCs_Wti401-mcB5NXrTdHo8QCOwYO3UWh_SyyjrCtUThw"}, "tenantId": undefined, "uid": "fcYeWrztRZhb1VGKt66lHTLyHBP2"}
documentation/testLog.txt: LOG  Debug - currentUser: {"_redirectEventId": undefined, "apiKey": "AIzaSyBOHewmZjlRWGu1jeEI-uBSkAn56xLMlec", "appName": "[DEFAULT]", "createdAt": "**********022", "displayName": undefined, "email": "<EMAIL>", "emailVerified": false, "isAnonymous": false, "lastLoginAt": "**********022", "phoneNumber": undefined, "photoURL": undefined, "providerData": [[Object]], "stsTokenManager": {"accessToken": "eyJhbGciOiJSUzI1NiIsImtpZCI6IjgxYjUyMjFlN2E1ZGUwZTVhZjQ5N2UzNzVhNzRiMDZkODJiYTc4OGIiLCJ0eXAiOiJKV1QifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.H_zOceORoH_aHhP1g66GbzUqpMNsdQBxJsRRWJCPeSzYaf-GnY_tyd3azcQOgQK6iXQ1o7bmjdNAHjzfjcMWpLqzTRRnTcE3Bapvr2ijeG6u-5cs1DbVDt2Dl7paWVkCXnxGJFugp93CglWfURGywm_ZyusLX1SbLiQI13MUI33IbIdAZpNkkIIsmoh7CnasofG2K3zjGO8WzGO1YRCWSLgQLWzO02xAp1KXQfnUbhtm50HZtaXxNYu3fBeABm4tGkGbgUYLwEcpKqvcDg3a0L6paFYFYHLBKfaVTnlwPR3jz-417a4FlrF8Hby0H9rA8Ftrxxg2xDoRFTON2DqiiQ", "expirationTime": 1737624716251, "refreshToken": "AMf-vBxeXh6squOf0d4IjIQmBTYJ5i3FdI8KCy5j0XaUlnTTWS72RrY-Ietd-G04VMBvqvzHaSPnyOYhOLEu6OtcmEW85TSKFGVov0C6IoGP1Qr-SF9wnbbflhQ1ZA9Ee9RYAqfBKFRHXiMO6gLZ1LS2EGZ-psfY3ZLoYCswTWVhTBf7NVaM6CCs_Wti401-mcB5NXrTdHo8QCOwYO3UWh_SyyjrCtUThw"}, "tenantId": undefined, "uid": "fcYeWrztRZhb1VGKt66lHTLyHBP2"}
documentation/testLog.txt: LOG  Debug - currentUser: {"_redirectEventId": undefined, "apiKey": "AIzaSyBOHewmZjlRWGu1jeEI-uBSkAn56xLMlec", "appName": "[DEFAULT]", "createdAt": "**********022", "displayName": undefined, "email": "<EMAIL>", "emailVerified": false, "isAnonymous": false, "lastLoginAt": "**********022", "phoneNumber": undefined, "photoURL": undefined, "providerData": [[Object]], "stsTokenManager": {"accessToken": "eyJhbGciOiJSUzI1NiIsImtpZCI6IjgxYjUyMjFlN2E1ZGUwZTVhZjQ5N2UzNzVhNzRiMDZkODJiYTc4OGIiLCJ0eXAiOiJKV1QifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.H_zOceORoH_aHhP1g66GbzUqpMNsdQBxJsRRWJCPeSzYaf-GnY_tyd3azcQOgQK6iXQ1o7bmjdNAHjzfjcMWpLqzTRRnTcE3Bapvr2ijeG6u-5cs1DbVDt2Dl7paWVkCXnxGJFugp93CglWfURGywm_ZyusLX1SbLiQI13MUI33IbIdAZpNkkIIsmoh7CnasofG2K3zjGO8WzGO1YRCWSLgQLWzO02xAp1KXQfnUbhtm50HZtaXxNYu3fBeABm4tGkGbgUYLwEcpKqvcDg3a0L6paFYFYHLBKfaVTnlwPR3jz-417a4FlrF8Hby0H9rA8Ftrxxg2xDoRFTON2DqiiQ", "expirationTime": 1737624716251, "refreshToken": "AMf-vBxeXh6squOf0d4IjIQmBTYJ5i3FdI8KCy5j0XaUlnTTWS72RrY-Ietd-G04VMBvqvzHaSPnyOYhOLEu6OtcmEW85TSKFGVov0C6IoGP1Qr-SF9wnbbflhQ1ZA9Ee9RYAqfBKFRHXiMO6gLZ1LS2EGZ-psfY3ZLoYCswTWVhTBf7NVaM6CCs_Wti401-mcB5NXrTdHo8QCOwYO3UWh_SyyjrCtUThw"}, "tenantId": undefined, "uid": "fcYeWrztRZhb1VGKt66lHTLyHBP2"}
documentation/testLog.txt: LOG  Debug - currentUser: {"_redirectEventId": undefined, "apiKey": "AIzaSyBOHewmZjlRWGu1jeEI-uBSkAn56xLMlec", "appName": "[DEFAULT]", "createdAt": "**********022", "displayName": undefined, "email": "<EMAIL>", "emailVerified": false, "isAnonymous": false, "lastLoginAt": "**********022", "phoneNumber": undefined, "photoURL": undefined, "providerData": [[Object]], "stsTokenManager": {"accessToken": "eyJhbGciOiJSUzI1NiIsImtpZCI6IjgxYjUyMjFlN2E1ZGUwZTVhZjQ5N2UzNzVhNzRiMDZkODJiYTc4OGIiLCJ0eXAiOiJKV1QifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.H_zOceORoH_aHhP1g66GbzUqpMNsdQBxJsRRWJCPeSzYaf-GnY_tyd3azcQOgQK6iXQ1o7bmjdNAHjzfjcMWpLqzTRRnTcE3Bapvr2ijeG6u-5cs1DbVDt2Dl7paWVkCXnxGJFugp93CglWfURGywm_ZyusLX1SbLiQI13MUI33IbIdAZpNkkIIsmoh7CnasofG2K3zjGO8WzGO1YRCWSLgQLWzO02xAp1KXQfnUbhtm50HZtaXxNYu3fBeABm4tGkGbgUYLwEcpKqvcDg3a0L6paFYFYHLBKfaVTnlwPR3jz-417a4FlrF8Hby0H9rA8Ftrxxg2xDoRFTON2DqiiQ", "expirationTime": 1737624716251, "refreshToken": "AMf-vBxeXh6squOf0d4IjIQmBTYJ5i3FdI8KCy5j0XaUlnTTWS72RrY-Ietd-G04VMBvqvzHaSPnyOYhOLEu6OtcmEW85TSKFGVov0C6IoGP1Qr-SF9wnbbflhQ1ZA9Ee9RYAqfBKFRHXiMO6gLZ1LS2EGZ-psfY3ZLoYCswTWVhTBf7NVaM6CCs_Wti401-mcB5NXrTdHo8QCOwYO3UWh_SyyjrCtUThw"}, "tenantId": undefined, "uid": "fcYeWrztRZhb1VGKt66lHTLyHBP2"}
documentation/testLog.txt: LOG  Debug - currentUser: {"_redirectEventId": undefined, "apiKey": "AIzaSyBOHewmZjlRWGu1jeEI-uBSkAn56xLMlec", "appName": "[DEFAULT]", "createdAt": "**********022", "displayName": undefined, "email": "<EMAIL>", "emailVerified": false, "isAnonymous": false, "lastLoginAt": "**********022", "phoneNumber": undefined, "photoURL": undefined, "providerData": [[Object]], "stsTokenManager": {"accessToken": "eyJhbGciOiJSUzI1NiIsImtpZCI6IjgxYjUyMjFlN2E1ZGUwZTVhZjQ5N2UzNzVhNzRiMDZkODJiYTc4OGIiLCJ0eXAiOiJKV1QifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.H_zOceORoH_aHhP1g66GbzUqpMNsdQBxJsRRWJCPeSzYaf-GnY_tyd3azcQOgQK6iXQ1o7bmjdNAHjzfjcMWpLqzTRRnTcE3Bapvr2ijeG6u-5cs1DbVDt2Dl7paWVkCXnxGJFugp93CglWfURGywm_ZyusLX1SbLiQI13MUI33IbIdAZpNkkIIsmoh7CnasofG2K3zjGO8WzGO1YRCWSLgQLWzO02xAp1KXQfnUbhtm50HZtaXxNYu3fBeABm4tGkGbgUYLwEcpKqvcDg3a0L6paFYFYHLBKfaVTnlwPR3jz-417a4FlrF8Hby0H9rA8Ftrxxg2xDoRFTON2DqiiQ", "expirationTime": 1737624716251, "refreshToken": "AMf-vBxeXh6squOf0d4IjIQmBTYJ5i3FdI8KCy5j0XaUlnTTWS72RrY-Ietd-G04VMBvqvzHaSPnyOYhOLEu6OtcmEW85TSKFGVov0C6IoGP1Qr-SF9wnbbflhQ1ZA9Ee9RYAqfBKFRHXiMO6gLZ1LS2EGZ-psfY3ZLoYCswTWVhTBf7NVaM6CCs_Wti401-mcB5NXrTdHo8QCOwYO3UWh_SyyjrCtUThw"}, "tenantId": undefined, "uid": "fcYeWrztRZhb1VGKt66lHTLyHBP2"}
documentation/testLog.txt: LOG  [useAuthUser] Setting up auth state listener {"currentUser": {"_redirectEventId": undefined, "apiKey": "AIzaSyBOHewmZjlRWGu1jeEI-uBSkAn56xLMlec", "appName": "[DEFAULT]", "createdAt": "**********022", "displayName": undefined, "email": "<EMAIL>", "emailVerified": false, "isAnonymous": false, "lastLoginAt": "**********022", "phoneNumber": undefined, "photoURL": undefined, "providerData": [Array], "stsTokenManager": [Object], "tenantId": undefined, "uid": "fcYeWrztRZhb1VGKt66lHTLyHBP2"}, "hasAuth": true, "hasOnAuthStateChanged": true}
documentation/testLog.txt: LOG  [useAuthUser] Auth state changed {"user": {"_redirectEventId": undefined, "apiKey": "AIzaSyBOHewmZjlRWGu1jeEI-uBSkAn56xLMlec", "appName": "[DEFAULT]", "createdAt": "**********022", "displayName": undefined, "email": "<EMAIL>", "emailVerified": false, "isAnonymous": false, "lastLoginAt": "**********022", "phoneNumber": undefined, "photoURL": undefined, "providerData": [Array], "stsTokenManager": [Object], "tenantId": undefined, "uid": "fcYeWrztRZhb1VGKt66lHTLyHBP2"}}
documentation/testLog.txt: LOG  Debug - currentUser: {"_redirectEventId": undefined, "apiKey": "AIzaSyBOHewmZjlRWGu1jeEI-uBSkAn56xLMlec", "appName": "[DEFAULT]", "createdAt": "**********022", "displayName": undefined, "email": "<EMAIL>", "emailVerified": false, "isAnonymous": false, "lastLoginAt": "**********022", "phoneNumber": undefined, "photoURL": undefined, "providerData": [[Object]], "stsTokenManager": {"accessToken": "eyJhbGciOiJSUzI1NiIsImtpZCI6IjgxYjUyMjFlN2E1ZGUwZTVhZjQ5N2UzNzVhNzRiMDZkODJiYTc4OGIiLCJ0eXAiOiJKV1QifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.H_zOceORoH_aHhP1g66GbzUqpMNsdQBxJsRRWJCPeSzYaf-GnY_tyd3azcQOgQK6iXQ1o7bmjdNAHjzfjcMWpLqzTRRnTcE3Bapvr2ijeG6u-5cs1DbVDt2Dl7paWVkCXnxGJFugp93CglWfURGywm_ZyusLX1SbLiQI13MUI33IbIdAZpNkkIIsmoh7CnasofG2K3zjGO8WzGO1YRCWSLgQLWzO02xAp1KXQfnUbhtm50HZtaXxNYu3fBeABm4tGkGbgUYLwEcpKqvcDg3a0L6paFYFYHLBKfaVTnlwPR3jz-417a4FlrF8Hby0H9rA8Ftrxxg2xDoRFTON2DqiiQ", "expirationTime": 1737624716251, "refreshToken": "AMf-vBxeXh6squOf0d4IjIQmBTYJ5i3FdI8KCy5j0XaUlnTTWS72RrY-Ietd-G04VMBvqvzHaSPnyOYhOLEu6OtcmEW85TSKFGVov0C6IoGP1Qr-SF9wnbbflhQ1ZA9Ee9RYAqfBKFRHXiMO6gLZ1LS2EGZ-psfY3ZLoYCswTWVhTBf7NVaM6CCs_Wti401-mcB5NXrTdHo8QCOwYO3UWh_SyyjrCtUThw"}, "tenantId": undefined, "uid": "fcYeWrztRZhb1VGKt66lHTLyHBP2"}
documentation/testLog.txt: LOG  Debug - currentUser: {"_redirectEventId": undefined, "apiKey": "AIzaSyBOHewmZjlRWGu1jeEI-uBSkAn56xLMlec", "appName": "[DEFAULT]", "createdAt": "**********022", "displayName": undefined, "email": "<EMAIL>", "emailVerified": false, "isAnonymous": false, "lastLoginAt": "**********022", "phoneNumber": undefined, "photoURL": undefined, "providerData": [[Object]], "stsTokenManager": {"accessToken": "eyJhbGciOiJSUzI1NiIsImtpZCI6IjgxYjUyMjFlN2E1ZGUwZTVhZjQ5N2UzNzVhNzRiMDZkODJiYTc4OGIiLCJ0eXAiOiJKV1QifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.H_zOceORoH_aHhP1g66GbzUqpMNsdQBxJsRRWJCPeSzYaf-GnY_tyd3azcQOgQK6iXQ1o7bmjdNAHjzfjcMWpLqzTRRnTcE3Bapvr2ijeG6u-5cs1DbVDt2Dl7paWVkCXnxGJFugp93CglWfURGywm_ZyusLX1SbLiQI13MUI33IbIdAZpNkkIIsmoh7CnasofG2K3zjGO8WzGO1YRCWSLgQLWzO02xAp1KXQfnUbhtm50HZtaXxNYu3fBeABm4tGkGbgUYLwEcpKqvcDg3a0L6paFYFYHLBKfaVTnlwPR3jz-417a4FlrF8Hby0H9rA8Ftrxxg2xDoRFTON2DqiiQ", "expirationTime": 1737624716251, "refreshToken": "AMf-vBxeXh6squOf0d4IjIQmBTYJ5i3FdI8KCy5j0XaUlnTTWS72RrY-Ietd-G04VMBvqvzHaSPnyOYhOLEu6OtcmEW85TSKFGVov0C6IoGP1Qr-SF9wnbbflhQ1ZA9Ee9RYAqfBKFRHXiMO6gLZ1LS2EGZ-psfY3ZLoYCswTWVhTBf7NVaM6CCs_Wti401-mcB5NXrTdHo8QCOwYO3UWh_SyyjrCtUThw"}, "tenantId": undefined, "uid": "fcYeWrztRZhb1VGKt66lHTLyHBP2"}
documentation/testLog.txt: LOG  Debug - currentUser: {"_redirectEventId": undefined, "apiKey": "AIzaSyBOHewmZjlRWGu1jeEI-uBSkAn56xLMlec", "appName": "[DEFAULT]", "createdAt": "**********022", "displayName": undefined, "email": "<EMAIL>", "emailVerified": false, "isAnonymous": false, "lastLoginAt": "**********022", "phoneNumber": undefined, "photoURL": undefined, "providerData": [[Object]], "stsTokenManager": {"accessToken": "eyJhbGciOiJSUzI1NiIsImtpZCI6IjgxYjUyMjFlN2E1ZGUwZTVhZjQ5N2UzNzVhNzRiMDZkODJiYTc4OGIiLCJ0eXAiOiJKV1QifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.H_zOceORoH_aHhP1g66GbzUqpMNsdQBxJsRRWJCPeSzYaf-GnY_tyd3azcQOgQK6iXQ1o7bmjdNAHjzfjcMWpLqzTRRnTcE3Bapvr2ijeG6u-5cs1DbVDt2Dl7paWVkCXnxGJFugp93CglWfURGywm_ZyusLX1SbLiQI13MUI33IbIdAZpNkkIIsmoh7CnasofG2K3zjGO8WzGO1YRCWSLgQLWzO02xAp1KXQfnUbhtm50HZtaXxNYu3fBeABm4tGkGbgUYLwEcpKqvcDg3a0L6paFYFYHLBKfaVTnlwPR3jz-417a4FlrF8Hby0H9rA8Ftrxxg2xDoRFTON2DqiiQ", "expirationTime": 1737624716251, "refreshToken": "AMf-vBxeXh6squOf0d4IjIQmBTYJ5i3FdI8KCy5j0XaUlnTTWS72RrY-Ietd-G04VMBvqvzHaSPnyOYhOLEu6OtcmEW85TSKFGVov0C6IoGP1Qr-SF9wnbbflhQ1ZA9Ee9RYAqfBKFRHXiMO6gLZ1LS2EGZ-psfY3ZLoYCswTWVhTBf7NVaM6CCs_Wti401-mcB5NXrTdHo8QCOwYO3UWh_SyyjrCtUThw"}, "tenantId": undefined, "uid": "fcYeWrztRZhb1VGKt66lHTLyHBP2"}
documentation/testLog.txt: LOG  Debug - currentUser: {"_redirectEventId": undefined, "apiKey": "AIzaSyBOHewmZjlRWGu1jeEI-uBSkAn56xLMlec", "appName": "[DEFAULT]", "createdAt": "**********022", "displayName": undefined, "email": "<EMAIL>", "emailVerified": false, "isAnonymous": false, "lastLoginAt": "**********022", "phoneNumber": undefined, "photoURL": undefined, "providerData": [[Object]], "stsTokenManager": {"accessToken": "eyJhbGciOiJSUzI1NiIsImtpZCI6IjgxYjUyMjFlN2E1ZGUwZTVhZjQ5N2UzNzVhNzRiMDZkODJiYTc4OGIiLCJ0eXAiOiJKV1QifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.H_zOceORoH_aHhP1g66GbzUqpMNsdQBxJsRRWJCPeSzYaf-GnY_tyd3azcQOgQK6iXQ1o7bmjdNAHjzfjcMWpLqzTRRnTcE3Bapvr2ijeG6u-5cs1DbVDt2Dl7paWVkCXnxGJFugp93CglWfURGywm_ZyusLX1SbLiQI13MUI33IbIdAZpNkkIIsmoh7CnasofG2K3zjGO8WzGO1YRCWSLgQLWzO02xAp1KXQfnUbhtm50HZtaXxNYu3fBeABm4tGkGbgUYLwEcpKqvcDg3a0L6paFYFYHLBKfaVTnlwPR3jz-417a4FlrF8Hby0H9rA8Ftrxxg2xDoRFTON2DqiiQ", "expirationTime": 1737624716251, "refreshToken": "AMf-vBxeXh6squOf0d4IjIQmBTYJ5i3FdI8KCy5j0XaUlnTTWS72RrY-Ietd-G04VMBvqvzHaSPnyOYhOLEu6OtcmEW85TSKFGVov0C6IoGP1Qr-SF9wnbbflhQ1ZA9Ee9RYAqfBKFRHXiMO6gLZ1LS2EGZ-psfY3ZLoYCswTWVhTBf7NVaM6CCs_Wti401-mcB5NXrTdHo8QCOwYO3UWh_SyyjrCtUThw"}, "tenantId": undefined, "uid": "fcYeWrztRZhb1VGKt66lHTLyHBP2"}
firebase-debug.log:  /Users/<USER>/Desktop/satbana
firebaseConfig.ts:    authDomain: "satbana-2052f.firebaseapp.com",
firebaseConfig.ts:    projectId: "satbana-2052f",
firebaseConfig.ts:    storageBucket: "satbana-2052f.firebasestorage.app",
functions/.firebaserc:    "default": "satbana-2052f"
index.js:  const APP_NAME = 'com.company.satbana';
ios/GoogleService-Info.plist:   <string>com.company.satbana</string>
ios/GoogleService-Info.plist:   <string>satbana-2052f</string>
ios/GoogleService-Info.plist:   <string>satbana-2052f.firebasestorage.app</string>
ios/Podfile:target 'satbana' do
ios/full_pod_install_log.txt:Podfile location: /Users/<USER>/Desktop/satbana/ios/Podfile
ios/full_pod_install_log.txt:Current working directory: /Users/<USER>/Desktop/satbana/ios
ios/full_pod_install_log.txt:  /Users/<USER>/Desktop/satbana/node_modules/@react-native-community/cli/build/bin.js
ios/full_pod_install_log.txt:Auto-linking React Native modules for target `satbana`: RNCAsyncStorage, RNCMaskedView, RNCPushNotificationIOS, RNFBApp, RNFBAuth, RNFBFirestore, RNFBMessaging, RNGestureHandler, RNReanimated, RNScreens, react-native-maps, react-native-netinfo, and react-native-safe-area-context
ios/full_pod_install_log.txt:  Using `ARCHS` setting to build architectures of target `Pods-satbana`: (``)
ios/full_pod_install_log.txt:    - Installing target `Pods-satbana` iOS 16.0
ios/full_pod_install_log.txt:      - Generating Info.plist file at `Pods/Target Support Files/Pods-satbana/Pods-satbana-Info.plist`
ios/full_pod_install_log.txt:      - Generating module map file at `Pods/Target Support Files/Pods-satbana/Pods-satbana.modulemap`
ios/full_pod_install_log.txt:      - Generating umbrella header at `Pods/Target Support Files/Pods-satbana/Pods-satbana-umbrella.h`
ios/full_pod_install_log.txt:      - Generating dummy source at `Pods/Target Support Files/Pods-satbana/Pods-satbana-dummy.m`
ios/full_pod_install_log.txt:[POST_INSTALL] Pods project path: /Users/<USER>/Desktop/satbana/ios/Pods/Pods.xcodeproj
ios/full_pod_install_log.txt:  99. Pods-satbana
ios/full_pod_install_log.txt:Setting CLANG_CXX_LANGUAGE_STANDARD to gnu++17 on /Users/<USER>/Desktop/satbana/ios/satbana.xcodeproj
ios/full_pod_install_log.txt:/Users/<USER>/Desktop/satbana/ios/Podfile:325:in `block (4 levels) in from_ruby'
ios/full_pod_install_log.txt:/Users/<USER>/Desktop/satbana/ios/Podfile:323:in `each'
ios/full_pod_install_log.txt:/Users/<USER>/Desktop/satbana/ios/Podfile:323:in `block (3 levels) in from_ruby'
ios/full_pod_install_log.txt:/Users/<USER>/Desktop/satbana/ios/Podfile:325:in `block (4 levels) in from_ruby'
ios/full_pod_install_log.txt:/Users/<USER>/Desktop/satbana/ios/Podfile:323:in `each'
ios/full_pod_install_log.txt:/Users/<USER>/Desktop/satbana/ios/Podfile:323:in `block (3 levels) in from_ruby'
ios/satBana.xcodeproj/project.pbxproj:          0B919E7137673EC803417BF1 /* Pods_satbana.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = ABDE4854359670EF05A325F7 /* Pods_satbana.framework */; };
ios/satBana.xcodeproj/project.pbxproj:          93FB14AF2D53FE3D00AA412E /* satbana_temp_icon_1024 2.png in Resources */ = {isa = PBXBuildFile; fileRef = 93FB14AE2D53FE3D00AA412E /* satbana_temp_icon_1024 2.png */; };
ios/satBana.xcodeproj/project.pbxproj:          06A36C8129544413B1C2AFB6 /* SplashScreen.storyboard */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = SplashScreen.storyboard; path = satbana/SplashScreen.storyboard; sourceTree = "<group>"; };
ios/satBana.xcodeproj/project.pbxproj:          11229B2A50AC520142646527 /* Pods-satbana.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-satbana.release.xcconfig"; path = "Target Support Files/Pods-satbana/Pods-satbana.release.xcconfig"; sourceTree = "<group>"; };
ios/satBana.xcodeproj/project.pbxproj:          13B07F961A680F5B00A75B9A /* satbana.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = satbana.app; sourceTree = BUILT_PRODUCTS_DIR; };
ios/satBana.xcodeproj/project.pbxproj:          13B07FAF1A68108700A75B9A /* AppDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AppDelegate.h; path = satbana/AppDelegate.h; sourceTree = "<group>"; };
ios/satBana.xcodeproj/project.pbxproj:          13B07FB01A68108700A75B9A /* AppDelegate.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; name = AppDelegate.mm; path = satbana/AppDelegate.mm; sourceTree = "<group>"; };
ios/satBana.xcodeproj/project.pbxproj:          13B07FB51A68108700A75B9A /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; name = Images.xcassets; path = satbana/Images.xcassets; sourceTree = "<group>"; };
ios/satBana.xcodeproj/project.pbxproj:          13B07FB61A68108700A75B9A /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = Info.plist; path = satbana/Info.plist; sourceTree = "<group>"; };
ios/satBana.xcodeproj/project.pbxproj:          13B07FB71A68108700A75B9A /* main.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = main.m; path = satbana/main.m; sourceTree = "<group>"; };
ios/satBana.xcodeproj/project.pbxproj:          6539CA3C9226BFC185830A26 /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xml; name = PrivacyInfo.xcprivacy; path = satbana/PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
ios/satBana.xcodeproj/project.pbxproj:          93FB14AE2D53FE3D00AA412E /* satbana_temp_icon_1024 2.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "satbana_temp_icon_1024 2.png"; sourceTree = "<group>"; };
ios/satBana.xcodeproj/project.pbxproj:          9526388552A543A8B12D0192 /* noop-file.swift */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 4; includeInIndex = 0; lastKnownFileType = sourcecode.swift; name = "noop-file.swift"; path = "satbana/noop-file.swift"; sourceTree = "<group>"; };
ios/satBana.xcodeproj/project.pbxproj:          AA286B85B6C04FC6940260E9 /* SplashScreen.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; name = SplashScreen.storyboard; path = satbana/SplashScreen.storyboard; sourceTree = "<group>"; };
ios/satBana.xcodeproj/project.pbxproj:          ABDE4854359670EF05A325F7 /* Pods_satbana.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_satbana.framework; sourceTree = BUILT_PRODUCTS_DIR; };
ios/satBana.xcodeproj/project.pbxproj:          C05A312890E84B808E175EB8 /* satbana-Bridging-Header.h */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 4; includeInIndex = 0; lastKnownFileType = sourcecode.c.h; name = "satbana-Bridging-Header.h"; path = "satbana/satbana-Bridging-Header.h"; sourceTree = "<group>"; };
ios/satBana.xcodeproj/project.pbxproj:          D08DA3B3D87B2BB3100D7841 /* Pods-satbana.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-satbana.debug.xcconfig"; path = "Target Support Files/Pods-satbana/Pods-satbana.debug.xcconfig"; sourceTree = "<group>"; };
ios/satBana.xcodeproj/project.pbxproj:          D3E2CE13E2974E978F86907C /* noop-file.swift */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 4; includeInIndex = 0; lastKnownFileType = sourcecode.swift; name = "noop-file.swift"; path = "satbana/noop-file.swift"; sourceTree = "<group>"; };
ios/satBana.xcodeproj/project.pbxproj:          FAC715A2D49A985799AEE119 /* ExpoModulesProvider.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ExpoModulesProvider.swift; path = "Pods/Target Support Files/Pods-satbana/ExpoModulesProvider.swift"; sourceTree = "<group>"; };
ios/satBana.xcodeproj/project.pbxproj:                          0B919E7137673EC803417BF1 /* Pods_satbana.framework in Frameworks */,
ios/satBana.xcodeproj/project.pbxproj:          13B07FAE1A68108700A75B9A /* satbana */ = {
ios/satBana.xcodeproj/project.pbxproj:                          C05A312890E84B808E175EB8 /* satbana-Bridging-Header.h */,
ios/satBana.xcodeproj/project.pbxproj:                          93FB14AE2D53FE3D00AA412E /* satbana_temp_icon_1024 2.png */,
ios/satBana.xcodeproj/project.pbxproj:                  name = satbana;
ios/satBana.xcodeproj/project.pbxproj:                          ABDE4854359670EF05A325F7 /* Pods_satbana.framework */,
ios/satBana.xcodeproj/project.pbxproj:                          13B07FAE1A68108700A75B9A /* satbana */,
ios/satBana.xcodeproj/project.pbxproj:                          13B07F961A680F5B00A75B9A /* satbana.app */,
ios/satBana.xcodeproj/project.pbxproj:          92DBD88DE9BF7D494EA9DA96 /* satbana */ = {
ios/satBana.xcodeproj/project.pbxproj:                  name = satbana;
ios/satBana.xcodeproj/project.pbxproj:                  path = satbana/Supporting;
ios/satBana.xcodeproj/project.pbxproj:                          D08DA3B3D87B2BB3100D7841 /* Pods-satbana.debug.xcconfig */,
ios/satBana.xcodeproj/project.pbxproj:                          11229B2A50AC520142646527 /* Pods-satbana.release.xcconfig */,
ios/satBana.xcodeproj/project.pbxproj:                          92DBD88DE9BF7D494EA9DA96 /* satbana */,
ios/satBana.xcodeproj/project.pbxproj:          13B07F861A680F5B00A75B9A /* satbana */ = {
ios/satBana.xcodeproj/project.pbxproj:                  buildConfigurationList = 13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "satbana" */;
ios/satBana.xcodeproj/project.pbxproj:                  name = satbana;
ios/satBana.xcodeproj/project.pbxproj:                  productName = satbana;
ios/satBana.xcodeproj/project.pbxproj:                  productReference = 13B07F961A680F5B00A75B9A /* satbana.app */;
ios/satBana.xcodeproj/project.pbxproj:                  buildConfigurationList = 83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "satbana" */;
ios/satBana.xcodeproj/project.pbxproj:                          13B07F861A680F5B00A75B9A /* satbana */,
ios/satBana.xcodeproj/project.pbxproj:                          93FB14AF2D53FE3D00AA412E /* satbana_temp_icon_1024 2.png in Resources */,
ios/satBana.xcodeproj/project.pbxproj:                          "$(DERIVED_FILE_DIR)/Pods-satbana-checkManifestLockResult.txt",
ios/satBana.xcodeproj/project.pbxproj:                          "${PODS_ROOT}/Target Support Files/Pods-satbana/Pods-satbana-frameworks.sh",
ios/satBana.xcodeproj/project.pbxproj:                  shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-satbana/Pods-satbana-frameworks.sh\"\n";
ios/satBana.xcodeproj/project.pbxproj:                          "${PODS_ROOT}/Target Support Files/Pods-satbana/Pods-satbana-resources.sh",
ios/satBana.xcodeproj/project.pbxproj:                  shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-satbana/Pods-satbana-resources.sh\"\n";
ios/satBana.xcodeproj/project.pbxproj:                  shellScript = "# This script configures Expo modules and generates the modules provider file.\nbash -l -c \"./Pods/Target\\ Support\\ Files/Pods-satbana/expo-configure-project.sh\"\n";
ios/satBana.xcodeproj/project.pbxproj:                  baseConfigurationReference = D08DA3B3D87B2BB3100D7841 /* Pods-satbana.debug.xcconfig */;
ios/satBana.xcodeproj/project.pbxproj:                          CODE_SIGN_ENTITLEMENTS = satbana/satbana.entitlements;
ios/satBana.xcodeproj/project.pbxproj:                          DEVELOPMENT_ASSET_PATHS = "$(SRCROOT)/../assets $(SRCROOT)/satbana/Images.xcassets";
ios/satBana.xcodeproj/project.pbxproj:                          INFOPLIST_FILE = satbana/Info.plist;
ios/satBana.xcodeproj/project.pbxproj:                          INFOPLIST_KEY_CFBundleDisplayName = satbana;
ios/satBana.xcodeproj/project.pbxproj:                          PRODUCT_BUNDLE_IDENTIFIER = com.company.satbana;
ios/satBana.xcodeproj/project.pbxproj:                          PRODUCT_NAME = satbana;
ios/satBana.xcodeproj/project.pbxproj:                          SWIFT_OBJC_BRIDGING_HEADER = "satbana/satbana-Bridging-Header.h";
ios/satBana.xcodeproj/project.pbxproj:                  baseConfigurationReference = 11229B2A50AC520142646527 /* Pods-satbana.release.xcconfig */;
ios/satBana.xcodeproj/project.pbxproj:                          CODE_SIGN_ENTITLEMENTS = satbana/satbana.entitlements;
ios/satBana.xcodeproj/project.pbxproj:                          DEVELOPMENT_ASSET_PATHS = "$(SRCROOT)/../assets $(SRCROOT)/satbana/Images.xcassets";
ios/satBana.xcodeproj/project.pbxproj:                          INFOPLIST_FILE = satbana/Info.plist;
ios/satBana.xcodeproj/project.pbxproj:                          INFOPLIST_KEY_CFBundleDisplayName = satbana;
ios/satBana.xcodeproj/project.pbxproj:                          PRODUCT_BUNDLE_IDENTIFIER = com.company.satbana;
ios/satBana.xcodeproj/project.pbxproj:                          PRODUCT_NAME = satbana;
ios/satBana.xcodeproj/project.pbxproj:                          SWIFT_OBJC_BRIDGING_HEADER = "satbana/satbana-Bridging-Header.h";
ios/satBana.xcodeproj/project.pbxproj:          13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "satbana" */ = {
ios/satBana.xcodeproj/project.pbxproj:          83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "satbana" */ = {
ios/satBana.xcworkspace/contents.xcworkspacedata:      location = "group:satbana.xcodeproj">
ios/satBana/AppDelegate.mm:  self.moduleName = @"com.company.satbana";
ios/satBana/GoogleService-Info.plist:   <string>com.company.satbana</string>
ios/satBana/GoogleService-Info.plist:   <string>satbana-2052f</string>
ios/satBana/GoogleService-Info.plist:   <string>satbana-2052f.firebasestorage.app</string>
ios/satBana/Images.xcassets/AppIcon.appiconset/Contents.json:      "filename" : "satbana_temp_icon_1024.png",
ios/satBana/Images.xcassets/SplashScreen.imageset/Contents.json:      "filename" : "satbana_temp_icon_1024.png",
ios/satBana/Info.plist: <string>satbana</string>
ios/satBana/Info.plist:                         <string>com.company.satbana</string>
jest/tests/screens/LoginScreen.test.tsx:    expect(getByText('Welcome to satbana')).toBeTruthy();
package-lock.json:  "name": "satbana",
package-lock.json:      "name": "satbana",
package.json:  "name": "satbana",
screens/LoginScreen.tsx:        <Text style={styles.title}>Welcome to satbana</Text>
services/firebaseService.js:// /Users/<USER>/Desktop/satbana/services/firebaseService.js
(END)
