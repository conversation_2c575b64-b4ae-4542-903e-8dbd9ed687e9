Setup Two Simulators:
Simulator 1: Posting owner 
Simulator 2: Offer owner 

Create Test Data:


On Simulator 1: posting owner modifies the posting and saves changes and navigates to posting detail screen




 LOG  [EditPostingScreen][Save] Starting update: {"postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:26:31.283Z", "updates": {"description": "Searching for basic fishing equipment - rod, reel, and tackle box. Must be in good working condition. ", "latitude": 37.7917130364994, "longitude": -122.4467312534528, "title": "Fishing Gear"}}
 LOG  [updatePostingDetails] Starting update: {"postingId": "Z7cW3OyoEsojfIreWcAD", "updatedData": {"description": "Searching for basic fishing equipment - rod, reel, and tackle box. Must be in good working condition. ", "latitude": 37.7917130364994, "longitude": -122.4467312534528, "title": "Fishing Gear"}}
 LOG  [EditPostingScreen][Debug] Hook initialization: {"hookSetup": {"error": false, "hasPostingDetails": true, "hookImport": "function", "loading": false, "postingId": "Z7cW3OyoEsojfIreWcAD"}, "timestamp": "2025-06-09T10:26:31.284Z"}
 LOG  [EditPostingScreen] Extracted params: {"hasDesc": false, "hasLocation": false, "hasName": false, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:26:31.285Z"}
 LOG  [updatePostingDetails] Detected title change: {"from": "Fishing Gear11", "to": "Fishing Gear"}
 LOG  [updatePostingDetails] Detected description change
 LOG  [updatePostingDetails] Posting updated successfully
 LOG  [updatePostingDetails] Preparing notifications for changes: ["title", "description"]
 LOG  [updatePostingDetails] Found 1 users with favorite
 LOG  [updatePostingDetails] Creating notification for user ybEyGDxbvleCEXdTvrA2CgVTJ1y2
 LOG  [updatePostingDetails] Notification payload: {"body": "\"Fishing Gear11\" has been updated (title and description)", "createdAt": {"_methodName": "serverTimestamp"}, "postingId": "Z7cW3OyoEsojfIreWcAD", "read": false, "recipientId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "senderId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "title": "Posting Updated", "type": "FAVORITE_POSTING_UPDATE"}
 LOG  [updatePostingDetails] Sending all notifications
 LOG  [OfferDetail][NOTIFICATION_DB_UPDATE] {"count": 1, "timestamp": "2025-06-09T10:26:32.188Z"}
 LOG  [OfferDetail][NOTIFICATION_DB_ADDED] {"notification": {"body": "\"Fishing Gear11\" has been updated (title and description)", "createdAt": [Object], "postingId": "Z7cW3OyoEsojfIreWcAD", "read": false, "recipientId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "senderId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "title": "Posting Updated", "type": "FAVORITE_POSTING_UPDATE"}, "timestamp": "2025-06-09T10:26:32.189Z"}
 LOG  [OfferDetail][NOTIFICATION_MATCHING_DEBUG] {"current": {"offerId": "tK7kgW3yDfZoF3IWeedl", "postingId": null, "reliablePostingId": null}, "matches": {"offerIdMatch": false, "postingIdMatch": false, "reliablePostingIdMatch": false}, "notification": {"offerId": undefined, "postingId": "Z7cW3OyoEsojfIreWcAD", "type": "FAVORITE_POSTING_UPDATE"}, "timestamp": "2025-06-09T10:26:32.190Z"}
 LOG  [updatePostingDetails] All notifications processed
 LOG  [EditPostingScreen][Save] Update successful: {"postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:26:32.226Z"}
 LOG  [Navigation] Configuring screen: LoginScreen
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: Home
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: PostingDetail
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Rendering headerLeft for: PostingDetail
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Rendering headerLeft for: EditPosting
 LOG  [Navigation] Can go back status: true
 LOG  [usePostingDetails][Focus] Screen focused, setting up subscription
 LOG  [usePostingDetails][Setup] Creating references
 LOG  [usePostingDetails][Setup] Pre-snapshot listener setup: {"postingId": "Z7cW3OyoEsojfIreWcAD", "ref": "postings/Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:26:32.285Z"}
 LOG  [ListenerStateManager] Starting transition for postings_1749464792285_1749464792285: inactive -> initializing
 LOG  [ListenerRegistry][postings] Registered: {"collection": "postings", "id": "1749464792285", "registryId": "postings_1749464792285_1749464792285", "state": "active", "timestamp": "2025-06-09T10:26:32.285Z"}
 LOG  [ListenerStateManager] Completing transition for postings_1749464792285_1749464792285
 LOG  [ListenerRegistry][Component] Registered listener with component: {"componentId": "PostingDetail_Z7cW3OyoEsojfIreWcAD", "listenerId": "postings_1749464792285_1749464792285", "timestamp": "2025-06-09T10:26:32.285Z"}
 LOG  [ListenerRegistry][postings] Created: {"componentId": "PostingDetail_Z7cW3OyoEsojfIreWcAD", "listenerId": "postings_1749464792285", "timestamp": "2025-06-09T10:26:32.285Z"}
 LOG  [useOffers][Focus] Screen focused, setting up subscription
 LOG  [useOffers][Subscribe] Setting up subscription {"postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:26:32.286Z"}
 LOG  [ListenerStateManager] Starting transition for offers_1749464792286_1749464792286: inactive -> initializing
 LOG  [ListenerRegistry][offers] Registered: {"collection": "offers", "id": "1749464792286", "registryId": "offers_1749464792286_1749464792286", "state": "active", "timestamp": "2025-06-09T10:26:32.286Z"}
 LOG  [ListenerStateManager] Completing transition for offers_1749464792286_1749464792286
 LOG  [ListenerRegistry][Component] Registered listener with component: {"componentId": "PostingDetail_Z7cW3OyoEsojfIreWcAD", "listenerId": "offers_1749464792286_1749464792286", "timestamp": "2025-06-09T10:26:32.286Z"}
 LOG  [ListenerRegistry][offers] Created: {"componentId": "PostingDetail_Z7cW3OyoEsojfIreWcAD", "listenerId": "offers_1749464792286", "timestamp": "2025-06-09T10:26:32.286Z"}
 LOG  [useFavorites][Focus] Screen focused, fetching favorite status
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-09T10:25:52.124Z"}, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:26:32.288Z"}
 LOG  [usePostingDetails][Reducer] Processing action: {"currentState": {"error": null, "loading": false, "postingDetails": {"createdAt": [Timestamp], "description": "11Searching for basic fishing equipment - rod, reel, and tackle box. Must be in good working condition. ", "id": "Z7cW3OyoEsojfIreWcAD", "lastUpdated": [Timestamp], "latitude": 37.7917130364994, "longitude": -122.4467312534528, "postingStatus": "Active", "searchTerms": [Array], "title": "Fishing Gear11", "titleLower": "hedere", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}}, "metrics": {"timeSinceStart": 40165, "timestamp": "2025-06-09T10:26:32.289Z", "updateCount": 5}, "type": "FETCH_START"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": true, "timestamp": "2025-06-09T10:26:32.289Z"}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_Z7cW3OyoEsojfIreWcAD_1749464772993", "offers_Z7cW3OyoEsojfIreWcAD_1749464772993"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749464772993"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749464772993"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-09T10:26:32.292Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 40169, "timestamp": "2025-06-09T10:26:32.292Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 40169, "timestamp": "2025-06-09T10:26:32.292Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749464792292", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749464792292", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_Z7cW3OyoEsojfIreWcAD_1749464792292", "postingSubId": "posting_Z7cW3OyoEsojfIreWcAD_1749464792292", "totalTime": 40170}
 LOG  [PostingDetail][StateSync] Checking state update conditions: {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "hasDetails": true, "offersCount": 2, "postingLoading": true, "timestamp": "2025-06-09T10:26:32.293Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [EditPostingScreen][Debug] Hook initialization: {"hookSetup": {"error": false, "hasPostingDetails": true, "hookImport": "function", "loading": false, "postingId": "Z7cW3OyoEsojfIreWcAD"}, "timestamp": "2025-06-09T10:26:32.294Z"}
 LOG  [EditPostingScreen] Extracted params: {"hasDesc": false, "hasLocation": false, "hasName": false, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:26:32.294Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-09T10:25:52.124Z"}, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:26:32.437Z"}
 LOG  [usePostingDetails][Reducer] Processing action: {"currentState": {"error": null, "loading": true, "postingDetails": {"createdAt": [Timestamp], "description": "11Searching for basic fishing equipment - rod, reel, and tackle box. Must be in good working condition. ", "id": "Z7cW3OyoEsojfIreWcAD", "lastUpdated": [Timestamp], "latitude": 37.7917130364994, "longitude": -122.4467312534528, "postingStatus": "Active", "searchTerms": [Array], "title": "Fishing Gear11", "titleLower": "hedere", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}}, "metrics": {"timeSinceStart": 40313, "timestamp": "2025-06-09T10:26:32.437Z", "updateCount": 6}, "type": "UPDATE_POSTING"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-09T10:26:32.438Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isFavorite": false, "isOwner": true, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:26:32.438Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_Z7cW3OyoEsojfIreWcAD_1749464792292", "offers_Z7cW3OyoEsojfIreWcAD_1749464792292"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749464792292"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749464792292"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-09T10:26:32.456Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 40333, "timestamp": "2025-06-09T10:26:32.456Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 40333, "timestamp": "2025-06-09T10:26:32.456Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749464792456", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749464792456", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_Z7cW3OyoEsojfIreWcAD_1749464792456", "postingSubId": "posting_Z7cW3OyoEsojfIreWcAD_1749464792456", "totalTime": 40335}
 LOG  [PostingDetail][StateSync] Checking state update conditions: {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "hasDetails": true, "offersCount": 2, "postingLoading": false, "timestamp": "2025-06-09T10:26:32.458Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Updating state with: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isOwnerCheck": true, "offersCount": 2, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:26:32.458Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Processing offers: {"count": 2, "timestamp": "2025-06-09T10:26:32.458Z", "userOffers": 0}
 LOG  [PostingDetail][StateSync] State updated: {"hasUserOffer": false, "isOwner": true, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:26:32.458Z"}
 LOG  [PostingDetail][StateVerification] {"auth": {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "expectedOwner": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "posting": {"id": "Z7cW3OyoEsojfIreWcAD", "loaded": true}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": false, "isOwner": true, "lastUpdate": 1749464772969, "offersCount": 2, "postingDetailsId": "Z7cW3OyoEsojfIreWcAD", "userOffer": null}, "timestamp": "2025-06-09T10:26:32.458Z"}
 LOG  [PostingDetail] Posting details loaded for edit: {"hasDetails": true, "hasLocation": false, "isOwner": true, "timestamp": "2025-06-09T10:26:32.459Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-09T10:25:52.124Z"}, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:26:32.460Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-09T10:26:32.460Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isFavorite": false, "isOwner": true, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:26:32.460Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_Z7cW3OyoEsojfIreWcAD_1749464792456", "offers_Z7cW3OyoEsojfIreWcAD_1749464792456"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749464792456"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749464792456"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-09T10:26:32.469Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 40346, "timestamp": "2025-06-09T10:26:32.469Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 40346, "timestamp": "2025-06-09T10:26:32.469Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749464792469", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749464792469", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_Z7cW3OyoEsojfIreWcAD_1749464792469", "postingSubId": "posting_Z7cW3OyoEsojfIreWcAD_1749464792469", "totalTime": 40346}
 LOG  [PostingDetail][StateVerification] {"auth": {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "expectedOwner": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "posting": {"id": "Z7cW3OyoEsojfIreWcAD", "loaded": true}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": false, "isOwner": true, "lastUpdate": 1749464792460, "offersCount": 2, "postingDetailsId": "Z7cW3OyoEsojfIreWcAD", "userOffer": null}, "timestamp": "2025-06-09T10:26:32.469Z"}
 LOG  [PostingDetail][StateDebug] {"auth": {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "performance": {"timeSinceLastUpdate": 10}, "props": {"postingId": "Z7cW3OyoEsojfIreWcAD", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": false, "isOwner": true, "lastUpdate": 1749464792460, "offersCount": 2, "postingDetailsId": "Z7cW3OyoEsojfIreWcAD", "userOffer": null}, "subscriptions": {"metrics": {"lastUpdate": "2025-06-09T10:25:52.123Z", "subscriptionCount": 0, "totalTime": 40347}, "types": []}, "timestamp": "2025-06-09T10:26:32.470Z"}
 LOG  [useOffers][Update] {"changes": [{"id": "IgyIrlkc18WdziXD3nEO", "timestamp": "2025-06-09T10:26:32.556Z", "type": "added"}, {"id": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:26:32.556Z", "type": "added"}], "count": 2, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:26:32.556Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-09T10:25:52.124Z"}, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:26:32.558Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-09T10:26:32.558Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isFavorite": false, "isOwner": true, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:26:32.558Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_Z7cW3OyoEsojfIreWcAD_1749464792469", "offers_Z7cW3OyoEsojfIreWcAD_1749464792469"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749464792469"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749464792469"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-09T10:26:32.566Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 40443, "timestamp": "2025-06-09T10:26:32.566Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 40444, "timestamp": "2025-06-09T10:26:32.567Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749464792567", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749464792567", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_Z7cW3OyoEsojfIreWcAD_1749464792567", "postingSubId": "posting_Z7cW3OyoEsojfIreWcAD_1749464792567", "totalTime": 40444}
 LOG  [PostingDetail][StateSync] Checking state update conditions: {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "hasDetails": true, "offersCount": 2, "postingLoading": false, "timestamp": "2025-06-09T10:26:32.567Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Updating state with: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isOwnerCheck": true, "offersCount": 2, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:26:32.567Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Processing offers: {"count": 2, "timestamp": "2025-06-09T10:26:32.567Z", "userOffers": 0}
 LOG  [PostingDetail][StateSync] State updated: {"hasUserOffer": false, "isOwner": true, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:26:32.568Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-09T10:25:52.124Z"}, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:26:32.568Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-09T10:26:32.569Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isFavorite": false, "isOwner": true, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:26:32.569Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_Z7cW3OyoEsojfIreWcAD_1749464792567", "offers_Z7cW3OyoEsojfIreWcAD_1749464792567"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749464792567"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749464792567"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-09T10:26:32.578Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 40455, "timestamp": "2025-06-09T10:26:32.578Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 40455, "timestamp": "2025-06-09T10:26:32.578Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749464792578", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749464792578", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_Z7cW3OyoEsojfIreWcAD_1749464792578", "postingSubId": "posting_Z7cW3OyoEsojfIreWcAD_1749464792578", "totalTime": 40456}
 LOG  [PostingDetail][StateVerification] {"auth": {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "expectedOwner": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "posting": {"id": "Z7cW3OyoEsojfIreWcAD", "loaded": true}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": false, "isOwner": true, "lastUpdate": 1749464792568, "offersCount": 2, "postingDetailsId": "Z7cW3OyoEsojfIreWcAD", "userOffer": null}, "timestamp": "2025-06-09T10:26:32.579Z"}
 LOG  [PostingDetail][StateDebug] {"auth": {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "performance": {"timeSinceLastUpdate": 11}, "props": {"postingId": "Z7cW3OyoEsojfIreWcAD", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": false, "isOwner": true, "lastUpdate": 1749464792568, "offersCount": 2, "postingDetailsId": "Z7cW3OyoEsojfIreWcAD", "userOffer": null}, "subscriptions": {"metrics": {"lastUpdate": "2025-06-09T10:25:52.123Z", "subscriptionCount": 0, "totalTime": 40456}, "types": []}, "timestamp": "2025-06-09T10:26:32.579Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-09T10:25:52.124Z"}, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:26:32.581Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-09T10:26:32.581Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isFavorite": false, "isOwner": true, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:26:32.582Z", "userOffer": null}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-09T10:25:52.124Z"}, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:26:32.584Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-09T10:26:32.584Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isFavorite": false, "isOwner": true, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:26:32.584Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_Z7cW3OyoEsojfIreWcAD_1749464792578", "offers_Z7cW3OyoEsojfIreWcAD_1749464792578"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749464792578"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749464792578"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-09T10:26:32.592Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 40469, "timestamp": "2025-06-09T10:26:32.592Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 40469, "timestamp": "2025-06-09T10:26:32.592Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749464792593", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749464792593", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_Z7cW3OyoEsojfIreWcAD_1749464792593", "postingSubId": "posting_Z7cW3OyoEsojfIreWcAD_1749464792593", "totalTime": 40470}
 LOG  [Navigation] Rendering headerLeft for: PostingDetail
 LOG  [Navigation] Can go back status: true
 LOG  [EditPostingScreen][Navigation] Screen cleanup: {"flowMetrics": {"dataLoadComplete": 434849882.679583, "dataLoadStart": 434849882.569666, "events": [[Object]], "flowMetrics": undefined, "navigationComplete": 434849865.388791, "totalDuration": "15704.902"}, "timestamp": "2025-06-09T10:26:32.847Z"}
 LOG  [Navigation] Rendering headerLeft for: PostingDetail
 LOG  [Navigation] Can go back status: true
 LOG  Region changing: {"latitude": 37.789928518703825, "latitudeDelta": 0.05507661000969932, "longitude": -122.4373169628512, "longitudeDelta": 0.034503704621840825}
 LOG  VirtualizedList: You have a large list that is slow to update - make sure your renderItem function renders components that follow React performance best practices like PureComponent, shouldComponentUpdate, etc. {"contentLength": 652.3333740234375, "dt": 19585, "prevDt": 20610}
 LOG  Debounced region changed: {"latitude": 37.789928518703825, "latitudeDelta": 0.05507661000969932, "longitude": -122.4373169628512, "longitudeDelta": 0.034503704621840825}
 LOG  Fetching postings for new region
 LOG  [useControlledPostings] Fetching postings for region: {"latitude": 37.789928518703825, "latitudeDelta": 0.05507661000969932, "longitude": -122.4373169628512, "longitudeDelta": 0.034503704621840825}
 LOG  [fetchPostingsBySearch] Query results: 10
 LOG  [useControlledPostings] Fetched postings: 10
 LOG  [OfferDetail][NOTIFICATION_DB_UPDATE] {"count": 1, "timestamp": "2025-06-09T10:26:36.276Z"}





On Simulator 2: modification of a posting should display a refresh button in posting detail container while the offer owner is viewing the offer detail screen but the refresh button was NOT displayed.

On Simulator 2: offer owner clicks the chevron down button in posting detail container to expand the posting details container and displays the old posting title and description as expected





 LOG  [PostingDetailsSection] Toggling details: {"current": false, "new": true, "timestamp": "2025-06-09T10:27:31.468Z"}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": true, "isLoading": false, "postingDescription": "11Searching for basic fishing equipment - rod, reel, and tackle box. Must be in good working condition. ", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "Fishing Gear11", "timestamp": "2025-06-09T10:27:31.475Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T10:27:31.477Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offers22555", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "224555", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T10:27:31.498Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:27:31.542Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:27:31.542Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:27:31.542Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": true, "timestamp": "2025-06-09T10:27:31.543Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:27:31.543Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:27:31.544Z"}
 LOG  [OfferCache][GET] {"age": 93850, "found": true, "isExpired": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:27:31.544Z"}
 LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"cachedStatus": "pending", "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:27:31.544Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:27:31.544Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": "hotmails offers22555", "hookPrice": 224555, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offers22555", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "224555", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": true}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T10:27:31.546Z"}
 LOG  [OfferDetailsSection][OFFER_UPDATED] {"description": "hotmails offers22555", "offerId": "tK7kgW3yDfZoF3IWeedl", "price": 224555, "timestamp": "2025-06-09T10:27:31.551Z"}
 LOG  FlatList layout complete
