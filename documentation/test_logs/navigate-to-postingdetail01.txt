step 1: user clicks on a marker on the map



 LOG  [HomeScreen] Marker pressed: {"action": "marker-press", "coordinate": {"latitude": 37.7917130364994, "longitude": -122.4467312534528}, "id": "Z7cW3OyoEsojfIreWcAD", "target": 7969}
 LOG  [HomeScreen] Callout pressed for posting: Z7cW3OyoEsojfIreWcAD
 LOG  [Navigation] Configuring screen: LoginScreen
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: Home
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: PostingDetail
 LOG  [Navigation] Can go back: true
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-07T18:52:13.994Z"}, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-07T18:52:13.994Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": false, "loading": true, "timestamp": "2025-06-07T18:52:13.995Z"}
 LOG  [Navigation] Rendering headerLeft for: PostingDetail
 LOG  [Navigation] Can go back status: true
 LOG  [BackButton] Rendering back button
 LOG  [usePostingDetails][Focus] Screen focused, setting up subscription
 LOG  [usePostingDetails][Setup] Creating references
 LOG  [usePostingDetails][Setup] Pre-snapshot listener setup: {"postingId": "Z7cW3OyoEsojfIreWcAD", "ref": "postings/Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-07T18:52:14.020Z"}
 LOG  [ListenerStateManager] Starting transition for postings_1749322334020_1749322334020: inactive -> initializing
 LOG  [ListenerRegistry][postings] Registered: {"collection": "postings", "id": "1749322334020", "registryId": "postings_1749322334020_1749322334020", "state": "active", "timestamp": "2025-06-07T18:52:14.020Z"}
 LOG  [ListenerStateManager] Completing transition for postings_1749322334020_1749322334020
 LOG  [ListenerRegistry][Component] Registered listener with component: {"componentId": "PostingDetail_Z7cW3OyoEsojfIreWcAD", "listenerId": "postings_1749322334020_1749322334020", "timestamp": "2025-06-07T18:52:14.021Z"}
 LOG  [ListenerRegistry][postings] Created: {"componentId": "PostingDetail_Z7cW3OyoEsojfIreWcAD", "listenerId": "postings_1749322334020", "timestamp": "2025-06-07T18:52:14.021Z"}
 LOG  [useOffers][Focus] Screen focused, setting up subscription
 LOG  [useOffers][Subscribe] Setting up subscription {"postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-07T18:52:14.022Z"}
 LOG  [ListenerStateManager] Starting transition for offers_1749322334022_1749322334022: inactive -> initializing
 LOG  [ListenerRegistry][offers] Registered: {"collection": "offers", "id": "1749322334022", "registryId": "offers_1749322334022_1749322334022", "state": "active", "timestamp": "2025-06-07T18:52:14.022Z"}
 LOG  [ListenerStateManager] Completing transition for offers_1749322334022_1749322334022
 LOG  [ListenerRegistry][Component] Registered listener with component: {"componentId": "PostingDetail_Z7cW3OyoEsojfIreWcAD", "listenerId": "offers_1749322334022_1749322334022", "timestamp": "2025-06-07T18:52:14.022Z"}
 LOG  [ListenerRegistry][offers] Created: {"componentId": "PostingDetail_Z7cW3OyoEsojfIreWcAD", "listenerId": "offers_1749322334022", "timestamp": "2025-06-07T18:52:14.022Z"}
 LOG  [useFavorites][Focus] Screen focused, fetching favorite status
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 29, "timestamp": "2025-06-07T18:52:14.023Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 29, "timestamp": "2025-06-07T18:52:14.023Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749322334023", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749322334023", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_Z7cW3OyoEsojfIreWcAD_1749322334023", "postingSubId": "posting_Z7cW3OyoEsojfIreWcAD_1749322334023", "totalTime": 29}
 LOG  [PostingDetail][StateSync] Checking state update conditions: {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "hasDetails": false, "offersCount": 0, "postingLoading": true, "timestamp": "2025-06-07T18:52:14.023Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateVerification] {"auth": {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "expectedOwner": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "posting": {"id": "Z7cW3OyoEsojfIreWcAD", "loaded": false}, "state": {"activeTab": "active", "currentUserId": null, "isDescriptionExpanded": false, "isOwner": false, "lastUpdate": 1749322333994, "offersCount": 0, "postingDetailsId": "", "userOffer": null}, "timestamp": "2025-06-07T18:52:14.023Z"}
 LOG  [PostingDetail][StateDebug] {"auth": {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "performance": {"timeSinceLastUpdate": 30}, "props": {"postingId": "Z7cW3OyoEsojfIreWcAD", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "state": {"activeTab": "active", "currentUserId": null, "isDescriptionExpanded": false, "isOwner": false, "lastUpdate": 1749322333994, "offersCount": 0, "postingDetailsId": "", "userOffer": null}, "subscriptions": {"metrics": {"lastUpdate": "2025-06-07T18:52:13.994Z", "subscriptionCount": 0, "totalTime": 30}, "types": []}, "timestamp": "2025-06-07T18:52:14.024Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-07T18:52:13.994Z"}, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-07T18:52:14.025Z"}
 LOG  [usePostingDetails][Reducer] Processing action: {"currentState": {"error": null, "loading": true, "postingDetails": null}, "metrics": {"timeSinceStart": 32, "timestamp": "2025-06-07T18:52:14.026Z", "updateCount": 1}, "type": "FETCH_START"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": false, "loading": true, "timestamp": "2025-06-07T18:52:14.026Z"}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_Z7cW3OyoEsojfIreWcAD_1749322334023", "offers_Z7cW3OyoEsojfIreWcAD_1749322334023"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749322334023"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749322334023"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-07T18:52:14.028Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 34, "timestamp": "2025-06-07T18:52:14.028Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 34, "timestamp": "2025-06-07T18:52:14.028Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749322334028", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749322334028", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_Z7cW3OyoEsojfIreWcAD_1749322334028", "postingSubId": "posting_Z7cW3OyoEsojfIreWcAD_1749322334028", "totalTime": 34}
 LOG  [Navigation] Rendering headerLeft for: PostingDetail
 LOG  [Navigation] Can go back status: true
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-07T18:52:13.994Z"}, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-07T18:52:14.189Z"}
 LOG  [usePostingDetails][Reducer] Processing action: {"currentState": {"error": null, "loading": true, "postingDetails": null}, "metrics": {"timeSinceStart": 195, "timestamp": "2025-06-07T18:52:14.189Z", "updateCount": 2}, "type": "UPDATE_POSTING"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-07T18:52:14.189Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": null, "isFavorite": false, "isOwner": false, "postingId": "", "timestamp": "2025-06-07T18:52:14.190Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_Z7cW3OyoEsojfIreWcAD_1749322334028", "offers_Z7cW3OyoEsojfIreWcAD_1749322334028"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749322334028"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749322334028"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-07T18:52:14.206Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 212, "timestamp": "2025-06-07T18:52:14.206Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 213, "timestamp": "2025-06-07T18:52:14.207Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749322334207", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749322334207", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_Z7cW3OyoEsojfIreWcAD_1749322334207", "postingSubId": "posting_Z7cW3OyoEsojfIreWcAD_1749322334207", "totalTime": 213}
 LOG  [PostingDetail][StateSync] Checking state update conditions: {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "hasDetails": true, "offersCount": 0, "postingLoading": false, "timestamp": "2025-06-07T18:52:14.207Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Updating state with: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isOwnerCheck": true, "offersCount": 0, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-07T18:52:14.207Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Processing offers: {"count": 0, "timestamp": "2025-06-07T18:52:14.208Z", "userOffers": 0}
 LOG  [PostingDetail][StateSync] State updated: {"hasUserOffer": false, "isOwner": true, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-07T18:52:14.209Z"}
 LOG  [PostingDetail][StateVerification] {"auth": {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "expectedOwner": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "posting": {"id": "Z7cW3OyoEsojfIreWcAD", "loaded": true}, "state": {"activeTab": "active", "currentUserId": null, "isDescriptionExpanded": false, "isOwner": false, "lastUpdate": 1749322333994, "offersCount": 0, "postingDetailsId": "", "userOffer": null}, "timestamp": "2025-06-07T18:52:14.209Z"}
 LOG  [PostingDetail] Posting details loaded for edit: {"hasDetails": true, "hasLocation": true, "isOwner": false, "timestamp": "2025-06-07T18:52:14.211Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-07T18:52:13.994Z"}, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-07T18:52:14.212Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-07T18:52:14.212Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isFavorite": false, "isOwner": true, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-07T18:52:14.213Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_Z7cW3OyoEsojfIreWcAD_1749322334207", "offers_Z7cW3OyoEsojfIreWcAD_1749322334207"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749322334207"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749322334207"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-07T18:52:14.221Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 227, "timestamp": "2025-06-07T18:52:14.221Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 227, "timestamp": "2025-06-07T18:52:14.221Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749322334222", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749322334222", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_Z7cW3OyoEsojfIreWcAD_1749322334222", "postingSubId": "posting_Z7cW3OyoEsojfIreWcAD_1749322334222", "totalTime": 228}
 LOG  [PostingDetail][StateVerification] {"auth": {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "expectedOwner": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "posting": {"id": "Z7cW3OyoEsojfIreWcAD", "loaded": true}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": false, "isOwner": true, "lastUpdate": 1749322334212, "offersCount": 0, "postingDetailsId": "Z7cW3OyoEsojfIreWcAD", "userOffer": null}, "timestamp": "2025-06-07T18:52:14.222Z"}
 LOG  [PostingDetail][StateDebug] {"auth": {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "performance": {"timeSinceLastUpdate": 10}, "props": {"postingId": "Z7cW3OyoEsojfIreWcAD", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": false, "isOwner": true, "lastUpdate": 1749322334212, "offersCount": 0, "postingDetailsId": "Z7cW3OyoEsojfIreWcAD", "userOffer": null}, "subscriptions": {"metrics": {"lastUpdate": "2025-06-07T18:52:13.994Z", "subscriptionCount": 0, "totalTime": 228}, "types": []}, "timestamp": "2025-06-07T18:52:14.222Z"}
 LOG  [PostingDetail] Posting details loaded for edit: {"hasDetails": true, "hasLocation": true, "isOwner": true, "timestamp": "2025-06-07T18:52:14.223Z"}
 LOG  [useOffers][Update] {"changes": [{"id": "IgyIrlkc18WdziXD3nEO", "timestamp": "2025-06-07T18:52:14.231Z", "type": "added"}, {"id": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T18:52:14.231Z", "type": "added"}], "count": 2, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-07T18:52:14.231Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-07T18:52:13.994Z"}, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-07T18:52:14.232Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-07T18:52:14.233Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isFavorite": false, "isOwner": true, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-07T18:52:14.233Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_Z7cW3OyoEsojfIreWcAD_1749322334222", "offers_Z7cW3OyoEsojfIreWcAD_1749322334222"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749322334222"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749322334222"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-07T18:52:14.241Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 247, "timestamp": "2025-06-07T18:52:14.241Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 247, "timestamp": "2025-06-07T18:52:14.241Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749322334241", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749322334241", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_Z7cW3OyoEsojfIreWcAD_1749322334241", "postingSubId": "posting_Z7cW3OyoEsojfIreWcAD_1749322334241", "totalTime": 248}
 LOG  [PostingDetail][StateSync] Checking state update conditions: {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "hasDetails": true, "offersCount": 2, "postingLoading": false, "timestamp": "2025-06-07T18:52:14.242Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Updating state with: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isOwnerCheck": true, "offersCount": 2, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-07T18:52:14.242Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Processing offers: {"count": 2, "timestamp": "2025-06-07T18:52:14.242Z", "userOffers": 0}
 LOG  [PostingDetail][StateSync] State updated: {"hasUserOffer": false, "isOwner": true, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-07T18:52:14.242Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-07T18:52:13.994Z"}, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-07T18:52:14.243Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-07T18:52:14.244Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isFavorite": false, "isOwner": true, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-07T18:52:14.244Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_Z7cW3OyoEsojfIreWcAD_1749322334241", "offers_Z7cW3OyoEsojfIreWcAD_1749322334241"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749322334241"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749322334241"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-07T18:52:14.252Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 258, "timestamp": "2025-06-07T18:52:14.252Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 258, "timestamp": "2025-06-07T18:52:14.252Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749322334252", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749322334252", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_Z7cW3OyoEsojfIreWcAD_1749322334252", "postingSubId": "posting_Z7cW3OyoEsojfIreWcAD_1749322334252", "totalTime": 259}
 LOG  [PostingDetail][StateVerification] {"auth": {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "expectedOwner": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "posting": {"id": "Z7cW3OyoEsojfIreWcAD", "loaded": true}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": false, "isOwner": true, "lastUpdate": 1749322334243, "offersCount": 2, "postingDetailsId": "Z7cW3OyoEsojfIreWcAD", "userOffer": null}, "timestamp": "2025-06-07T18:52:14.253Z"}
 LOG  [PostingDetail][StateDebug] {"auth": {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "performance": {"timeSinceLastUpdate": 10}, "props": {"postingId": "Z7cW3OyoEsojfIreWcAD", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": false, "isOwner": true, "lastUpdate": 1749322334243, "offersCount": 2, "postingDetailsId": "Z7cW3OyoEsojfIreWcAD", "userOffer": null}, "subscriptions": {"metrics": {"lastUpdate": "2025-06-07T18:52:13.994Z", "subscriptionCount": 0, "totalTime": 259}, "types": []}, "timestamp": "2025-06-07T18:52:14.253Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-07T18:52:13.994Z"}, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-07T18:52:14.255Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-07T18:52:14.255Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isFavorite": false, "isOwner": true, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-07T18:52:14.255Z", "userOffer": null}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-07T18:52:13.994Z"}, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-07T18:52:14.257Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-07T18:52:14.258Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isFavorite": false, "isOwner": true, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-07T18:52:14.258Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_Z7cW3OyoEsojfIreWcAD_1749322334252", "offers_Z7cW3OyoEsojfIreWcAD_1749322334252"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749322334252"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749322334252"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-07T18:52:14.266Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 272, "timestamp": "2025-06-07T18:52:14.266Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 272, "timestamp": "2025-06-07T18:52:14.266Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749322334266", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749322334266", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_Z7cW3OyoEsojfIreWcAD_1749322334266", "postingSubId": "posting_Z7cW3OyoEsojfIreWcAD_1749322334266", "totalTime": 272}
 LOG  [Navigation] Rendering headerLeft for: PostingDetail
 LOG  [Navigation] Can go back status: true





