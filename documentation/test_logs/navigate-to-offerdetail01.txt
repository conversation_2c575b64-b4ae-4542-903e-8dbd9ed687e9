step 1: user clicks on an offer in posting detail screen




 LOG  [PostingDetail][Navigation] Attempting safe navigation
 LOG  [PostingDetail][Navigation] Checking listeners: {"activeCount": 2, "byCollection": {"offers": 1, "postings": 1}, "timestamp": "2025-06-07T19:07:55.148Z"}
 LOG  [PostingDetail][Navigation] Cleaning up hooks
 LOG  [PostingDetail] Unsubscribing from posting
 LOG  [usePostingDetails][Unsubscribe] Cleaning up listener: {"listenerId": "postings_1749322334020_1749322334020", "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-07T19:07:55.150Z"}
 LOG  [ListenerRegistry][Component] Unregistered listener from component: {"componentId": "PostingDetail_Z7cW3OyoEsojfIreWcAD", "listenerId": "postings_1749322334020_1749322334020", "timestamp": "2025-06-07T19:07:55.150Z"}
 LOG  [PostingDetail] Successfully unsubscribed from posting
 LOG  [PostingDetail] Unsubscribing from offers
 LOG  [useOffers][Unsubscribe] Cleaning up subscription: {"postingId": "Z7cW3OyoEsojfIreWcAD", "subscriptionId": "offers_1749322334022_1749322334022", "timestamp": "2025-06-07T19:07:55.152Z"}
 LOG  [ListenerRegistry][Component] Unregistered listener from component: {"componentId": "PostingDetail_Z7cW3OyoEsojfIreWcAD", "listenerId": "offers_1749322334022_1749322334022", "timestamp": "2025-06-07T19:07:55.153Z"}
 LOG  [PostingDetail] Successfully unsubscribed from offers
 LOG  [ListenerRegistry] Cleanup metrics: {"groupId": null, "metrics": {"duration": 4, "startTime": 1749323275152, "success": true}, "subscriptionId": "postings_1749322334020_1749322334020", "timestamp": "2025-06-07T19:07:55.156Z"}
 LOG  [ListenerRegistry] Cleanup metrics: {"groupId": null, "metrics": {"duration": 6, "startTime": 1749323275153, "success": true}, "subscriptionId": "offers_1749322334022_1749322334022", "timestamp": "2025-06-07T19:07:55.159Z"}
 LOG  [PostingDetail][Navigation] Post-cleanup verification: {"activeListeners": 0, "byCollection": {"offers": 0, "postings": 0}, "timestamp": "2025-06-07T19:07:55.262Z"}
 LOG  [PostingDetail][Navigation] All listeners cleaned up successfully
 LOG  [Navigation] Configuring screen: LoginScreen
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: Home
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: PostingDetail
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: OfferDetail
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Rendering headerLeft for: PostingDetail
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Rendering headerLeft for: OfferDetail
 LOG  [Navigation] Can go back status: true
 LOG  [BackButton] Rendering back button
 LOG  [OfferDetail][STATUS_TRACKING] {"hasInitialOffer": false, "hasOfferDetails": false, "initialOfferStatus": undefined, "offerId": "tK7kgW3yDfZoF3IWeedl", "offerStatus": "active", "postingStatus": "Active", "timestamp": "2025-06-07T19:07:55.319Z"}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDetailsExpanded": false, "isLoading": true, "postingDescription": "", "postingOwnerId": null, "postingStatus": "Active", "postingTitle": "", "timestamp": "2025-06-07T19:07:55.320Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isCurrentUserPostingOwner": false, "postingOwnerId": null, "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T19:07:55.321Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "hookDescription": undefined, "hookPrice": undefined, "isLoading": true, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "", "offerOwnerId": null, "offerPrice": "0", "offerStatus": "active", "restrictionEndDate": null, "userScore": undefined, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isCurrentUserOfferOwner": false, "offerOwnerId": null, "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T19:07:55.330Z"}
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingOwnerId": null, "shouldShowAbusiveButton": false, "timestamp": "2025-06-07T19:07:55.342Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.344Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.344Z"}
 LOG  [OfferCache][GET] {"age": 27394994, "found": true, "isExpired": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.344Z"}
 LOG  [useOfferDetails][FETCHING_DATA] {"fetchCount": 1, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.344Z"}
 LOG  [OfferCache][GET] {"age": 27394994, "found": true, "isExpired": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.344Z"}
 LOG  [useOfferDetails][FETCH_OFFER_START] {"forceRefresh": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.345Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.345Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.345Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.345Z"}
 LOG  [OfferCache][GET] {"age": 27394995, "found": true, "isExpired": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.345Z"}
 LOG  [useOfferDetails][FETCHING_DATA] {"fetchCount": 1, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.345Z"}
 LOG  [OfferCache][GET] {"age": 27394995, "found": true, "isExpired": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.345Z"}
 LOG  [useOfferDetails][FETCH_OFFER_START] {"forceRefresh": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.346Z"}
 LOG  [useAuthUser] Setting up auth state listener {"currentUser": {"_redirectEventId": undefined, "apiKey": "AIzaSyBOHewmZjlRWGu1jeEI-uBSkAn56xLMlec", "appName": "[DEFAULT]", "createdAt": "1731681254940", "displayName": undefined, "email": "<EMAIL>", "emailVerified": false, "isAnonymous": false, "lastLoginAt": "1749321815951", "phoneNumber": undefined, "photoURL": undefined, "providerData": [Array], "stsTokenManager": [Object], "tenantId": undefined, "uid": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "hasAuth": true, "hasOnAuthStateChanged": true}
 LOG  [OfferDetail][MOUNT] {"cleanupStarted": {"isCleaningUp": false, "isMounted": true}, "lastAction": "MOUNT", "mountCount": 0, "params": {"offerId": "tK7kgW3yDfZoF3IWeedl", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingId": "Z7cW3OyoEsojfIreWcAD", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "timestamp": "2025-06-07T19:07:55.383Z"}
 LOG  [Messages Subscription] Setting up subscription for offerId: tK7kgW3yDfZoF3IWeedl
 LOG  === OfferDetail Screen Mounted ===
 LOG  Route params: {"offerId": "tK7kgW3yDfZoF3IWeedl", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingId": "Z7cW3OyoEsojfIreWcAD", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  Setting owner IDs from route params: {"offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [OfferDetail][MOUNT] {"cleanupStarted": {"isCleaningUp": false, "isMounted": true}, "lastAction": "MOUNT", "mountCount": 1, "params": {"offerId": "tK7kgW3yDfZoF3IWeedl", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingId": "Z7cW3OyoEsojfIreWcAD", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "timestamp": "2025-06-07T19:07:55.387Z"}
 LOG  Authenticated user: JIPkxLOiOrMcavtTItGBID0HMqH3
 LOG  [OfferDetail] Initializing with params: {"hasInitialOffer": false, "hasInitialPosting": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.387Z"}
 LOG  [OfferDetail] Setting postingOwnerId from route params: {"postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [OfferDetail] Setting offerOwnerId from route params: {"offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2"}
 LOG  OfferDetail refresh button states: {"showOfferRefresh": false, "showOfferRefreshExplicit": false, "showPostingRefresh": false, "showPostingRefreshExplicit": false, "timestamp": "2025-06-07T19:07:55.388Z"}
 LOG  [OfferDetail][NOTIFICATION_LISTENER_SETUP] {"offerId": "tK7kgW3yDfZoF3IWeedl", "postingId": undefined, "timestamp": "2025-06-07T19:07:55.388Z"}
 LOG  [OfferDetail][NOTIFICATION_DB_LISTENER_SETUP] {"offerId": "tK7kgW3yDfZoF3IWeedl", "postingId": undefined, "timestamp": "2025-06-07T19:07:55.388Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [usePostingDetails][Focus] Screen unfocused, cleaning up
 LOG  [ListenerRegistry][Component] Unregistered listener from component: {"componentId": "PostingDetail_Z7cW3OyoEsojfIreWcAD", "listenerId": "postings_1749322334020_1749322334020", "timestamp": "2025-06-07T19:07:55.388Z"}
 LOG  [ListenerRegistry] No entry found for postings_1749322334020_1749322334020
 LOG  [useOffers][Focus] Screen unfocused, cleaning up
 LOG  [ListenerRegistry][Component] Unregistered listener from component: {"componentId": "PostingDetail_Z7cW3OyoEsojfIreWcAD", "listenerId": "offers_1749322334022_1749322334022", "timestamp": "2025-06-07T19:07:55.389Z"}
 LOG  [ListenerRegistry] No entry found for offers_1749322334022_1749322334022
 LOG  [PostingDetailsSection] Render: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDetailsExpanded": false, "isLoading": true, "postingDescription": "", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "", "timestamp": "2025-06-07T19:07:55.391Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isCurrentUserPostingOwner": true, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T19:07:55.391Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "hookDescription": undefined, "hookPrice": undefined, "isLoading": true, "isOfferDetailsExpanded": false, "isPostingOwner": true, "offerDescription": "", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "0", "offerStatus": "active", "restrictionEndDate": null, "userScore": undefined, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isCurrentUserOfferOwner": false, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T19:07:55.395Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.401Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.401Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.401Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": false, "timestamp": "2025-06-07T19:07:55.402Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.402Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.402Z"}
 LOG  [OfferCache][GET] {"age": 27395052, "found": true, "isExpired": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.402Z"}
 LOG  [useOfferDetails][FETCHING_DATA] {"fetchCount": 1, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.402Z"}
 LOG  [OfferCache][GET] {"age": 27395052, "found": true, "isExpired": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.402Z"}
 LOG  [useOfferDetails][FETCH_OFFER_START] {"forceRefresh": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.403Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.403Z"}
 LOG  [useAuthUser] Auth state changed {"user": {"_redirectEventId": undefined, "apiKey": "AIzaSyBOHewmZjlRWGu1jeEI-uBSkAn56xLMlec", "appName": "[DEFAULT]", "createdAt": "1731681254940", "displayName": undefined, "email": "<EMAIL>", "emailVerified": false, "isAnonymous": false, "lastLoginAt": "1749321815951", "phoneNumber": undefined, "photoURL": undefined, "providerData": [Array], "stsTokenManager": [Object], "tenantId": undefined, "uid": "JIPkxLOiOrMcavtTItGBID0HMqH3"}}
 LOG  [OfferDetail][NOTIFICATION_DB_UPDATE] {"count": 0, "timestamp": "2025-06-07T19:07:55.410Z"}
 LOG  [Navigation] Rendering headerLeft for: PostingDetail
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Rendering headerLeft for: OfferDetail
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Rendering headerLeft for: OfferDetail
 LOG  [Navigation] Can go back status: true
 LOG  Region changing: {"latitude": 37.784979675952506, "latitudeDelta": 0.07448062054502458, "longitude": -122.43239999999999, "longitudeDelta": 0.053661654803107695}
 LOG  [Messages Subscription] Received snapshot update: {"empty": false, "metadata": {"fromCache": false, "hasPendingWrites": false}, "size": 1}
 LOG  [Messages Subscription] Processing discussion: {"id": "uFPwMJYrZ5wEt4cqPi35", "messageCount": 16}
 LOG  [Messages Subscription] Raw messages: {"count": 16, "messageKeys": ["read", "text", "senderId", "readAt", "timestamp", "id"], "sampleMessage": {"id": "1737359768014_ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "read": true, "readAt": [Object], "senderId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "text": "hotmails offer", "timestamp": [Object]}}
 LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["read", "text", "senderId", "readAt", "timestamp", "id"], "timestampType": "object"}
 LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["timestamp", "read", "readAt", "senderId", "id", "text"], "timestampType": "object"}
 LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["read", "id", "readAt", "text", "timestamp", "senderId"], "timestampType": "object"}
 LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["text", "readAt", "timestamp", "read", "senderId", "id"], "timestampType": "object"}
 LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["timestamp", "readAt", "text", "read", "id", "senderId"], "timestampType": "object"}
 LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["readAt", "read", "text", "timestamp", "senderId", "id"], "timestampType": "object"}
 LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["read", "id", "text", "readAt", "senderId", "timestamp"], "timestampType": "object"}
 LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["read", "senderId", "timestamp", "text", "id", "readAt"], "timestampType": "object"}
 LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["senderId", "text", "timestamp", "read", "readAt", "id"], "timestampType": "object"}
 LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["id", "text", "timestamp", "read", "readAt", "senderId"], "timestampType": "object"}
 LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["text", "timestamp", "senderId", "read", "readAt", "id"], "timestampType": "object"}
 LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["read", "text", "readAt", "timestamp", "id", "senderId"], "timestampType": "object"}
 LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["read", "text", "timestamp", "senderId", "readAt", "id"], "timestampType": "object"}
 LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["senderId", "read", "readAt", "text", "id", "timestamp"], "timestampType": "object"}
 LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["text", "read", "timestamp", "readAt", "senderId", "id"], "timestampType": "object"}
 LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["id", "text", "senderId", "read", "readAt", "timestamp"], "timestampType": "object"}
 LOG  [Messages Subscription] Validation summary: {"firstValidMessage": {"id": "1737359768014_ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "senderId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "timestamp": [Object]}, "invalidMessages": 0, "totalMessages": 16, "validMessages": 16}
 LOG  [Messages Subscription] Processing messages: {"hasLegacyMessages": false, "messageTypes": [{"hasId": true, "hasTimestamp": true, "timestampType": "object"}, {"hasId": true, "hasTimestamp": true, "timestampType": "object"}, {"hasId": true, "hasTimestamp": true, "timestampType": "object"}, {"hasId": true, "hasTimestamp": true, "timestampType": "object"}, {"hasId": true, "hasTimestamp": true, "timestampType": "object"}, {"hasId": true, "hasTimestamp": true, "timestampType": "object"}, {"hasId": true, "hasTimestamp": true, "timestampType": "object"}, {"hasId": true, "hasTimestamp": true, "timestampType": "object"}, {"hasId": true, "hasTimestamp": true, "timestampType": "object"}, {"hasId": true, "hasTimestamp": true, "timestampType": "object"}, {"hasId": true, "hasTimestamp": true, "timestampType": "object"}, {"hasId": true, "hasTimestamp": true, "timestampType": "object"}, {"hasId": true, "hasTimestamp": true, "timestampType": "object"}, {"hasId": true, "hasTimestamp": true, "timestampType": "object"}, {"hasId": true, "hasTimestamp": true, "timestampType": "object"}, {"hasId": true, "hasTimestamp": true, "timestampType": "object"}], "newCount": 16, "prevCount": 0}
 LOG  [Messages Subscription] Updated messages state: {"firstMessage": {"id": "1746608040263_JIPkxLOiOrMcavtTItGBID0HMqH3", "senderId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "text": "kou", "timestamp": [Object]}, "hasLegacyMessages": false, "lastMessage": {"id": "1737359768014_ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "senderId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "text": "hotmails offer", "timestamp": [Object]}, "newCount": 16, "previousCount": 0}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDetailsExpanded": false, "isLoading": true, "postingDescription": "", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "", "timestamp": "2025-06-07T19:07:55.531Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isCurrentUserPostingOwner": true, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T19:07:55.532Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "hookDescription": undefined, "hookPrice": undefined, "isLoading": true, "isOfferDetailsExpanded": false, "isPostingOwner": true, "offerDescription": "", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "0", "offerStatus": "active", "restrictionEndDate": null, "userScore": undefined, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isCurrentUserOfferOwner": false, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T19:07:55.536Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.573Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.573Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.574Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": false, "timestamp": "2025-06-07T19:07:55.575Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.575Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.575Z"}
 LOG  [OfferCache][GET] {"age": 27395225, "found": true, "isExpired": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.575Z"}
 LOG  [useOfferDetails][FETCHING_DATA] {"fetchCount": 1, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.575Z"}
 LOG  [OfferCache][GET] {"age": 27395225, "found": true, "isExpired": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.575Z"}
 LOG  [useOfferDetails][FETCH_OFFER_START] {"forceRefresh": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.576Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.576Z"}
 LOG  Marking messages as read for authorized user: {"isOfferOwner": false, "isPostingOwner": true, "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  Initial load, scrolling to end: 16
 LOG  [PostingDetailsSection] Render: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDetailsExpanded": false, "isLoading": true, "postingDescription": "", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "", "timestamp": "2025-06-07T19:07:55.579Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isCurrentUserPostingOwner": true, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T19:07:55.579Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "hookDescription": undefined, "hookPrice": undefined, "isLoading": true, "isOfferDetailsExpanded": false, "isPostingOwner": true, "offerDescription": "", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "0", "offerStatus": "active", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isCurrentUserOfferOwner": false, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T19:07:55.584Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.604Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.604Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.604Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": false, "timestamp": "2025-06-07T19:07:55.604Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.604Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.605Z"}
 LOG  [OfferCache][GET] {"age": 27395255, "found": true, "isExpired": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.605Z"}
 LOG  [useOfferDetails][FETCHING_DATA] {"fetchCount": 1, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.605Z"}
 LOG  [OfferCache][GET] {"age": 27395255, "found": true, "isExpired": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.605Z"}
 LOG  [useOfferDetails][FETCH_OFFER_START] {"forceRefresh": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.605Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.605Z"}
 LOG  FlatList layout complete
 LOG  [OfferCache][SET] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.622Z"}
 LOG  [useOfferDetails][FETCH_OFFER_SUCCESS] {"offerId": "tK7kgW3yDfZoF3IWeedl", "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-07T19:07:55.622Z"}
 LOG  [OfferCache][SET] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.623Z"}
 LOG  [useOfferDetails][FETCH_OFFER_SUCCESS] {"offerId": "tK7kgW3yDfZoF3IWeedl", "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-07T19:07:55.623Z"}
 LOG  [OfferDetail][STATUS_TRACKING] {"hasInitialOffer": false, "hasOfferDetails": true, "initialOfferStatus": undefined, "offerId": "tK7kgW3yDfZoF3IWeedl", "offerStatus": "pending", "postingStatus": "Active", "timestamp": "2025-06-07T19:07:55.624Z"}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDetailsExpanded": false, "isLoading": true, "postingDescription": "Searching for basic fishing equipment - rod, reel, and tackle box. Must be in good working condition. ", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "Fishing Gear7777", "timestamp": "2025-06-07T19:07:55.624Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isCurrentUserPostingOwner": true, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T19:07:55.625Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "hookDescription": undefined, "hookPrice": undefined, "isLoading": true, "isOfferDetailsExpanded": false, "isPostingOwner": true, "offerDescription": "hotmails offers22", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "22", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isCurrentUserOfferOwner": false, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T19:07:55.629Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.647Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.647Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.648Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [OfferDetail][NOTIFICATION_LISTENER_CLEANUP] {"offerId": "tK7kgW3yDfZoF3IWeedl", "postingId": undefined, "timestamp": "2025-06-07T19:07:55.648Z"}
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": false, "timestamp": "2025-06-07T19:07:55.648Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.648Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.648Z"}
 LOG  [OfferCache][GET] {"age": 26, "found": true, "isExpired": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.649Z"}
 LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"cachedStatus": "pending", "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.650Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.650Z"}
 LOG  [OfferDetail][NOTIFICATION_LISTENER_SETUP] {"offerId": "tK7kgW3yDfZoF3IWeedl", "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-07T19:07:55.650Z"}
 LOG  [OfferDetail][NOTIFICATION_DB_LISTENER_SETUP] {"offerId": "tK7kgW3yDfZoF3IWeedl", "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-07T19:07:55.650Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDetailsExpanded": false, "isLoading": true, "postingDescription": "Searching for basic fishing equipment - rod, reel, and tackle box. Must be in good working condition. ", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "Fishing Gear7777", "timestamp": "2025-06-07T19:07:55.652Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isCurrentUserPostingOwner": true, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T19:07:55.652Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "hookDescription": undefined, "hookPrice": undefined, "isLoading": true, "isOfferDetailsExpanded": false, "isPostingOwner": true, "offerDescription": "hotmails offers22", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "22", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isCurrentUserOfferOwner": false, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T19:07:55.656Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.674Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.674Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.674Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": false, "timestamp": "2025-06-07T19:07:55.675Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.675Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.675Z"}
 LOG  [OfferCache][GET] {"age": 52, "found": true, "isExpired": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.675Z"}
 LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"cachedStatus": "pending", "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.675Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.675Z"}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDetailsExpanded": false, "isLoading": false, "postingDescription": "Searching for basic fishing equipment - rod, reel, and tackle box. Must be in good working condition. ", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "Fishing Gear7777", "timestamp": "2025-06-07T19:07:55.677Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isCurrentUserPostingOwner": true, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T19:07:55.677Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "hookDescription": undefined, "hookPrice": undefined, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": true, "offerDescription": "hotmails offers22", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "22", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isCurrentUserOfferOwner": false, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T19:07:55.681Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.700Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.700Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.700Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": false, "timestamp": "2025-06-07T19:07:55.701Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.701Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.701Z"}
 LOG  [OfferCache][GET] {"age": 78, "found": true, "isExpired": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.701Z"}
 LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"cachedStatus": "pending", "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.701Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.701Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "hookDescription": "hotmails offers22", "hookPrice": 22, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": true, "offerDescription": "hotmails offers22", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "22", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": true}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isCurrentUserOfferOwner": false, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T19:07:55.703Z"}
 LOG  [useOfferDetails][SETTING_UP_LISTENER] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.705Z"}
 LOG  [OfferDetailsSection][OFFER_UPDATED] {"description": "hotmails offers22", "offerId": "tK7kgW3yDfZoF3IWeedl", "price": 22, "timestamp": "2025-06-07T19:07:55.705Z"}
 LOG  [OfferCache][SET] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.706Z"}
 LOG  [useOfferDetails][FETCH_OFFER_SUCCESS] {"offerId": "tK7kgW3yDfZoF3IWeedl", "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-07T19:07:55.707Z"}
 LOG  [OfferDetail][NOTIFICATION_DB_UPDATE] {"count": 0, "timestamp": "2025-06-07T19:07:55.709Z"}
 LOG  [useOfferDetails][REAL_TIME_UPDATE] {"offerId": "tK7kgW3yDfZoF3IWeedl", "status": "pending", "timestamp": "2025-06-07T19:07:55.725Z"}
 LOG  [OfferCache][SET] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.725Z"}
 LOG  [OfferDetail][STATUS_TRACKING] {"hasInitialOffer": false, "hasOfferDetails": true, "initialOfferStatus": undefined, "offerId": "tK7kgW3yDfZoF3IWeedl", "offerStatus": "pending", "postingStatus": "Active", "timestamp": "2025-06-07T19:07:55.726Z"}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDetailsExpanded": false, "isLoading": false, "postingDescription": "Searching for basic fishing equipment - rod, reel, and tackle box. Must be in good working condition. ", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "Fishing Gear7777", "timestamp": "2025-06-07T19:07:55.726Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isCurrentUserPostingOwner": true, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T19:07:55.727Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "hookDescription": undefined, "hookPrice": undefined, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": true, "offerDescription": "hotmails offers22", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "22", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isCurrentUserOfferOwner": false, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T19:07:55.731Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.750Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.750Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.751Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": false, "timestamp": "2025-06-07T19:07:55.751Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.751Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.751Z"}
 LOG  [OfferCache][GET] {"age": 26, "found": true, "isExpired": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.751Z"}
 LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"cachedStatus": "pending", "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.751Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.752Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "hookDescription": "hotmails offers22", "hookPrice": 22, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": true, "offerDescription": "hotmails offers22", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "22", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": true}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isCurrentUserOfferOwner": false, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T19:07:55.753Z"}
 LOG  [OfferDetailsSection][OFFER_UPDATED] {"description": "hotmails offers22", "offerId": "tK7kgW3yDfZoF3IWeedl", "price": 22, "timestamp": "2025-06-07T19:07:55.757Z"}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDetailsExpanded": false, "isLoading": false, "postingDescription": "Searching for basic fishing equipment - rod, reel, and tackle box. Must be in good working condition. ", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "Fishing Gear7777", "timestamp": "2025-06-07T19:07:55.758Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isCurrentUserPostingOwner": true, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T19:07:55.759Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "hookDescription": undefined, "hookPrice": undefined, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": true, "offerDescription": "hotmails offers22", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "22", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isCurrentUserOfferOwner": false, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T19:07:55.763Z"}
 LOG  Debounced region changed: {"latitude": 37.784979675952506, "latitudeDelta": 0.07448062054502458, "longitude": -122.43239999999999, "longitudeDelta": 0.053661654803107695}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.782Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.782Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.782Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": false, "timestamp": "2025-06-07T19:07:55.782Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.782Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.783Z"}
 LOG  [OfferCache][GET] {"age": 58, "found": true, "isExpired": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.783Z"}
 LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"cachedStatus": "pending", "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.783Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.783Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "hookDescription": "hotmails offers22", "hookPrice": 22, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": true, "offerDescription": "hotmails offers22", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "22", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": true}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isCurrentUserOfferOwner": false, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T19:07:55.790Z"}
 LOG  Fetching postings for new region
 LOG  [OfferDetailsSection][OFFER_UPDATED] {"description": "hotmails offers22", "offerId": "tK7kgW3yDfZoF3IWeedl", "price": 22, "timestamp": "2025-06-07T19:07:55.792Z"}
 LOG  [useControlledPostings] Fetching postings for region: {"latitude": 37.784979675952506, "latitudeDelta": 0.07448062054502458, "longitude": -122.43239999999999, "longitudeDelta": 0.053661654803107695}
 LOG  [OfferCache][SET] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.911Z"}
 LOG  [useOfferDetails][FETCH_OFFER_SUCCESS] {"offerId": "tK7kgW3yDfZoF3IWeedl", "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-07T19:07:55.911Z"}
 LOG  [OfferCache][SET] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T19:07:55.913Z"}
 LOG  [useOfferDetails][FETCH_OFFER_SUCCESS] {"offerId": "tK7kgW3yDfZoF3IWeedl", "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-07T19:07:55.913Z"}
 LOG  [fetchPostingsBySearch] Query results: 10
 LOG  [useControlledPostings] Fetched postings: 10
 LOG  [Navigation] Rendering headerLeft for: PostingDetail
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Rendering headerLeft for: OfferDetail
 LOG  [Navigation] Can go back status: true

