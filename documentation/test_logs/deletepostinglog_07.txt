Setup Two Simulators:
Simulator 1: Posting owner 
Simulator 2: Offer owner 

Create Test Data:
On Simulator 1: Create a new posting with a distinctive title -done

 LOG  Posting added with ID: 6cxr3VsbMA4JefozqPVX
 LOG  [Navigation] Configuring screen: LoginScreen
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: Home
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Rendering headerLeft for: CreatePosting
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Configuring screen: LoginScreen
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: Home
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Rendering headerLeft for: CreatePosting
 LOG  [Navigation] Can go back status: true
 LOG  Region changing: {"latitude": 37.81230010153758, "latitudeDelta": 0.013764982899985512, "longitude": -122.42013183679437, "longitudeDelta": 0.008625926155474417}
 LOG  Debounced region changed: {"latitude": 37.81230010153758, "latitudeDelta": 0.013764982899985512, "longitude": -122.42013183679437, "longitudeDelta": 0.008625926155474417}
 LOG  Fetching postings for new region
 LOG  [useControlledPostings] Fetching postings for region: {"latitude": 37.81230010153758, "latitudeDelta": 0.013764982899985512, "longitude": -122.42013183679437, "longitudeDelta": 0.008625926155474417}
 LOG  [fetchPostingsBySearch] Query results: 1
 LOG  [useControlledPostings] Fetched postings: 1
 LOG  [useAuthUser] Cleaning up auth state listener





On Simulator 2: Submit an offer on that posting -done (the offer was immediately displayed under active offers tab after returning to posting detail screen)







 LOG  [PostingDetail][Navigation] Attempting safe navigation
 LOG  [PostingDetail][Navigation] Checking listeners: {"activeCount": 2, "byCollection": {"offers": 1, "postings": 1}, "timestamp": "2025-06-07T10:52:08.219Z"}
 LOG  [PostingDetail][Navigation] Cleaning up hooks
 LOG  [PostingDetail] Unsubscribing from posting
 LOG  [usePostingDetails][Unsubscribe] Cleaning up listener: {"listenerId": "postings_1749293522506_1749293522506", "postingId": "6cxr3VsbMA4JefozqPVX", "timestamp": "2025-06-07T10:52:08.221Z"}
 LOG  [ListenerRegistry][Component] Unregistered listener from component: {"componentId": "PostingDetail_6cxr3VsbMA4JefozqPVX", "listenerId": "postings_1749293522506_1749293522506", "timestamp": "2025-06-07T10:52:08.222Z"}
 LOG  [PostingDetail] Successfully unsubscribed from posting
 LOG  [PostingDetail] Unsubscribing from offers
 LOG  [useOffers][Unsubscribe] Cleaning up subscription: {"postingId": "6cxr3VsbMA4JefozqPVX", "subscriptionId": "offers_1749293522507_1749293522507", "timestamp": "2025-06-07T10:52:08.225Z"}
 LOG  [ListenerRegistry][Component] Unregistered listener from component: {"componentId": "PostingDetail_6cxr3VsbMA4JefozqPVX", "listenerId": "offers_1749293522507_1749293522507", "timestamp": "2025-06-07T10:52:08.226Z"}
 LOG  [PostingDetail] Successfully unsubscribed from offers
 LOG  [ListenerRegistry] Cleanup metrics: {"groupId": null, "metrics": {"duration": 5, "startTime": 1749293528224, "success": true}, "subscriptionId": "postings_1749293522506_1749293522506", "timestamp": "2025-06-07T10:52:08.229Z"}
 LOG  [ListenerRegistry] Cleanup metrics: {"groupId": null, "metrics": {"duration": 8, "startTime": 1749293528226, "success": true}, "subscriptionId": "offers_1749293522507_1749293522507", "timestamp": "2025-06-07T10:52:08.234Z"}
 LOG  [PostingDetail][Navigation] Post-cleanup verification: {"activeListeners": 0, "byCollection": {"offers": 0, "postings": 0}, "timestamp": "2025-06-07T10:52:08.328Z"}
 LOG  [PostingDetail][Navigation] All listeners cleaned up successfully
 LOG  [Navigation] Configuring screen: LoginScreen
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: Home
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: PostingDetail
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: MakeOffer
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Rendering headerLeft for: PostingDetail
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Rendering headerLeft for: MakeOffer
 LOG  [Navigation] Can go back status: true
 LOG  [BackButton] Rendering back button
 LOG  [MakeOffer][Verify] Checking for active listeners: {"stats": {"activeCleanups": 0, "activeScreens": Set {}, "byCollection": {"offers": 0, "postings": 0}, "lastCleanup": null, "timestamp": "2025-06-07T10:52:08.407Z", "totalListeners": 0}, "timestamp": "2025-06-07T10:52:08.407Z"}
 LOG  [MakeOffer] Checking user restrictions for: ybEyGDxbvleCEXdTvrA2CgVTJ1y2
 LOG  [usePostingDetails][Focus] Screen unfocused, cleaning up
 LOG  [ListenerRegistry][Component] Unregistered listener from component: {"componentId": "PostingDetail_6cxr3VsbMA4JefozqPVX", "listenerId": "postings_1749293522506_1749293522506", "timestamp": "2025-06-07T10:52:08.409Z"}
 LOG  [ListenerRegistry] No entry found for postings_1749293522506_1749293522506
 LOG  [useOffers][Focus] Screen unfocused, cleaning up
 LOG  [ListenerRegistry][Component] Unregistered listener from component: {"componentId": "PostingDetail_6cxr3VsbMA4JefozqPVX", "listenerId": "offers_1749293522507_1749293522507", "timestamp": "2025-06-07T10:52:08.410Z"}
 LOG  [ListenerRegistry] No entry found for offers_1749293522507_1749293522507
 LOG  [Navigation] Rendering headerLeft for: MakeOffer
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Rendering headerLeft for: PostingDetail
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Rendering headerLeft for: MakeOffer
 LOG  [Navigation] Can go back status: true
 LOG  Region changing: {"latitude": 37.8106059200526, "latitudeDelta": 0.019766430630447474, "longitude": -122.41975935267538, "longitudeDelta": 0.01609314918366067}
 LOG  Debounced region changed: {"latitude": 37.8106059200526, "latitudeDelta": 0.019766430630447474, "longitude": -122.41975935267538, "longitudeDelta": 0.01609314918366067}
 LOG  Fetching postings for new region
 LOG  [useControlledPostings] Fetching postings for region: {"latitude": 37.8106059200526, "latitudeDelta": 0.019766430630447474, "longitude": -122.41975935267538, "longitudeDelta": 0.01609314918366067}
 LOG  [Navigation] Rendering headerLeft for: PostingDetail
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Rendering headerLeft for: MakeOffer
 LOG  [Navigation] Can go back status: true
 LOG  [fetchPostingsBySearch] Query results: 3
 LOG  [useControlledPostings] Fetched postings: 3
 LOG  [MakeOffer][Verify] Checking for active listeners: {"stats": {"activeCleanups": 0, "activeScreens": Set {}, "byCollection": {"offers": 0, "postings": 0}, "lastCleanup": null, "timestamp": "2025-06-07T10:52:13.410Z", "totalListeners": 0}, "timestamp": "2025-06-07T10:52:13.410Z"}
 LOG  [MakeOffer][Verify] Checking for active listeners: {"stats": {"activeCleanups": 0, "activeScreens": Set {}, "byCollection": {"offers": 0, "postings": 0}, "lastCleanup": null, "timestamp": "2025-06-07T10:52:18.411Z", "totalListeners": 0}, "timestamp": "2025-06-07T10:52:18.411Z"}
 LOG  [MakeOffer][Verify] Checking for active listeners: {"stats": {"activeCleanups": 0, "activeScreens": Set {}, "byCollection": {"offers": 0, "postings": 0}, "lastCleanup": null, "timestamp": "2025-06-07T10:52:23.412Z", "totalListeners": 0}, "timestamp": "2025-06-07T10:52:23.412Z"}
 LOG  [MakeOffer] Submitting offer: {"description": "hotmails offer", "postingId": "6cxr3VsbMA4JefozqPVX", "price": "1111"}
 LOG  [MakeOffer] Creating offer with data: {"postingId": "6cxr3VsbMA4JefozqPVX", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "price": 1111}
 LOG  [addOfferAndDiscussion] Starting with data: {"currentUser": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingId": "6cxr3VsbMA4JefozqPVX", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "price": 1111, "userId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2"}
 LOG  [addOfferAndDiscussion] Offer created: {"offerId": "Np9YCjiwfJf7xT6wzXBK"}
 LOG  [addOfferAndDiscussion] Discussion created: {"discussionId": "6WOMdhbzTwhfNc3fHazf"}
 LOG  [addOfferAndDiscussion] Creating notification: {"body": "You received a new offer of $1111", "createdAt": {"_methodName": "serverTimestamp"}, "offerId": "Np9YCjiwfJf7xT6wzXBK", "postingId": "6cxr3VsbMA4JefozqPVX", "read": false, "recipientId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "senderId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "title": "New Offer", "type": "NEW_OFFER"}
 LOG  [addOfferAndDiscussion] Notification created successfully
 LOG  [addOfferAndDiscussion] Updated user favorites
 LOG  [MakeOffer] Offer created successfully: {"offerId": "Np9YCjiwfJf7xT6wzXBK"}
 LOG  [Navigation] Configuring screen: LoginScreen
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: Home
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: PostingDetail
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Rendering headerLeft for: PostingDetail
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Rendering headerLeft for: MakeOffer
 LOG  [Navigation] Can go back status: true
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-07T10:52:02.478Z"}, "postingId": "6cxr3VsbMA4JefozqPVX", "timestamp": "2025-06-07T10:52:24.793Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-07T10:52:24.793Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isFavorite": false, "isOwner": false, "postingId": "6cxr3VsbMA4JefozqPVX", "timestamp": "2025-06-07T10:52:24.793Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_6cxr3VsbMA4JefozqPVX_1749293523014", "offers_6cxr3VsbMA4JefozqPVX_1749293523014"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_6cxr3VsbMA4JefozqPVX_1749293523014"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_6cxr3VsbMA4JefozqPVX_1749293523014"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-07T10:52:24.808Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "6cxr3VsbMA4JefozqPVX", "timeSinceStart": 22332, "timestamp": "2025-06-07T10:52:24.809Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "6cxr3VsbMA4JefozqPVX", "timeSinceStart": 22332, "timestamp": "2025-06-07T10:52:24.809Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_6cxr3VsbMA4JefozqPVX_1749293544809", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_6cxr3VsbMA4JefozqPVX_1749293544809", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_6cxr3VsbMA4JefozqPVX_1749293544809", "postingSubId": "posting_6cxr3VsbMA4JefozqPVX_1749293544809", "totalTime": 22332}
 LOG  [usePostingDetails][Focus] Screen focused, setting up subscription
 LOG  [usePostingDetails][Setup] Creating references
 LOG  [usePostingDetails][Setup] Pre-snapshot listener setup: {"postingId": "6cxr3VsbMA4JefozqPVX", "ref": "postings/6cxr3VsbMA4JefozqPVX", "timestamp": "2025-06-07T10:52:24.810Z"}
 LOG  [ListenerStateManager] Starting transition for postings_1749293544810_1749293544810: inactive -> initializing
 LOG  [ListenerRegistry][postings] Registered: {"collection": "postings", "id": "1749293544810", "registryId": "postings_1749293544810_1749293544810", "state": "active", "timestamp": "2025-06-07T10:52:24.810Z"}
 LOG  [ListenerStateManager] Completing transition for postings_1749293544810_1749293544810
 LOG  [ListenerRegistry][Component] Registered listener with component: {"componentId": "PostingDetail_6cxr3VsbMA4JefozqPVX", "listenerId": "postings_1749293544810_1749293544810", "timestamp": "2025-06-07T10:52:24.810Z"}
 LOG  [ListenerRegistry][postings] Created: {"componentId": "PostingDetail_6cxr3VsbMA4JefozqPVX", "listenerId": "postings_1749293544810", "timestamp": "2025-06-07T10:52:24.810Z"}
 LOG  [useOffers][Focus] Screen focused, setting up subscription
 LOG  [useOffers][Subscribe] Setting up subscription {"postingId": "6cxr3VsbMA4JefozqPVX", "timestamp": "2025-06-07T10:52:24.813Z"}
 LOG  [ListenerStateManager] Starting transition for offers_1749293544813_1749293544813: inactive -> initializing
 LOG  [ListenerRegistry][offers] Registered: {"collection": "offers", "id": "1749293544813", "registryId": "offers_1749293544813_1749293544813", "state": "active", "timestamp": "2025-06-07T10:52:24.813Z"}
 LOG  [ListenerStateManager] Completing transition for offers_1749293544813_1749293544813
 LOG  [ListenerRegistry][Component] Registered listener with component: {"componentId": "PostingDetail_6cxr3VsbMA4JefozqPVX", "listenerId": "offers_1749293544813_1749293544813", "timestamp": "2025-06-07T10:52:24.814Z"}
 LOG  [ListenerRegistry][offers] Created: {"componentId": "PostingDetail_6cxr3VsbMA4JefozqPVX", "listenerId": "offers_1749293544813", "timestamp": "2025-06-07T10:52:24.814Z"}
 LOG  [useFavorites][Focus] Screen focused, fetching favorite status
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-07T10:52:02.478Z"}, "postingId": "6cxr3VsbMA4JefozqPVX", "timestamp": "2025-06-07T10:52:24.815Z"}
 LOG  [usePostingDetails][Reducer] Processing action: {"currentState": {"error": null, "loading": false, "postingDetails": {"createdAt": [Timestamp], "description": "test", "id": "6cxr3VsbMA4JefozqPVX", "latitude": 37.81130189153112, "longitude": -122.42037530683824, "postingStatus": "Active", "searchTerms": [Array], "title": "kous test post ", "titleLower": "kous test post ", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}}, "metrics": {"timeSinceStart": 22337, "timestamp": "2025-06-07T10:52:24.815Z", "updateCount": 3}, "type": "FETCH_START"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": true, "timestamp": "2025-06-07T10:52:24.816Z"}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_6cxr3VsbMA4JefozqPVX_1749293544809", "offers_6cxr3VsbMA4JefozqPVX_1749293544809"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_6cxr3VsbMA4JefozqPVX_1749293544809"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_6cxr3VsbMA4JefozqPVX_1749293544809"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-07T10:52:24.819Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "6cxr3VsbMA4JefozqPVX", "timeSinceStart": 22343, "timestamp": "2025-06-07T10:52:24.820Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "6cxr3VsbMA4JefozqPVX", "timeSinceStart": 22343, "timestamp": "2025-06-07T10:52:24.820Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_6cxr3VsbMA4JefozqPVX_1749293544820", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_6cxr3VsbMA4JefozqPVX_1749293544820", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_6cxr3VsbMA4JefozqPVX_1749293544820", "postingSubId": "posting_6cxr3VsbMA4JefozqPVX_1749293544820", "totalTime": 22343}
 LOG  [PostingDetail][StateSync] Checking state update conditions: {"currentUser": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hasDetails": true, "offersCount": 0, "postingLoading": true, "timestamp": "2025-06-07T10:52:24.820Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-07T10:52:02.478Z"}, "postingId": "6cxr3VsbMA4JefozqPVX", "timestamp": "2025-06-07T10:52:24.966Z"}
 LOG  [usePostingDetails][Reducer] Processing action: {"currentState": {"error": null, "loading": true, "postingDetails": {"createdAt": [Timestamp], "description": "test", "id": "6cxr3VsbMA4JefozqPVX", "latitude": 37.81130189153112, "longitude": -122.42037530683824, "postingStatus": "Active", "searchTerms": [Array], "title": "kous test post ", "titleLower": "kous test post ", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}}, "metrics": {"timeSinceStart": 22489, "timestamp": "2025-06-07T10:52:24.967Z", "updateCount": 4}, "type": "UPDATE_POSTING"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-07T10:52:24.968Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isFavorite": false, "isOwner": false, "postingId": "6cxr3VsbMA4JefozqPVX", "timestamp": "2025-06-07T10:52:24.969Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_6cxr3VsbMA4JefozqPVX_1749293544820", "offers_6cxr3VsbMA4JefozqPVX_1749293544820"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_6cxr3VsbMA4JefozqPVX_1749293544820"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_6cxr3VsbMA4JefozqPVX_1749293544820"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-07T10:52:24.982Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "6cxr3VsbMA4JefozqPVX", "timeSinceStart": 22506, "timestamp": "2025-06-07T10:52:24.983Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "6cxr3VsbMA4JefozqPVX", "timeSinceStart": 22506, "timestamp": "2025-06-07T10:52:24.983Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_6cxr3VsbMA4JefozqPVX_1749293544983", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_6cxr3VsbMA4JefozqPVX_1749293544983", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_6cxr3VsbMA4JefozqPVX_1749293544983", "postingSubId": "posting_6cxr3VsbMA4JefozqPVX_1749293544983", "totalTime": 22506}
 LOG  [PostingDetail][StateSync] Checking state update conditions: {"currentUser": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hasDetails": true, "offersCount": 0, "postingLoading": false, "timestamp": "2025-06-07T10:52:24.983Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Updating state with: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isOwnerCheck": false, "offersCount": 0, "postingId": "6cxr3VsbMA4JefozqPVX", "timestamp": "2025-06-07T10:52:24.983Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Processing offers: {"count": 0, "timestamp": "2025-06-07T10:52:24.984Z", "userOffers": 0}
 LOG  [PostingDetail][StateSync] State updated: {"hasUserOffer": false, "isOwner": false, "postingId": "6cxr3VsbMA4JefozqPVX", "timestamp": "2025-06-07T10:52:24.984Z"}
 LOG  [PostingDetail][StateVerification] {"auth": {"currentUser": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "expectedOwner": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "posting": {"id": "6cxr3VsbMA4JefozqPVX", "loaded": true}, "state": {"activeTab": "active", "currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDescriptionExpanded": false, "isOwner": false, "lastUpdate": 1749293522984, "offersCount": 0, "postingDetailsId": "6cxr3VsbMA4JefozqPVX", "userOffer": null}, "timestamp": "2025-06-07T10:52:24.984Z"}
 LOG  [PostingDetail] Posting details loaded for edit: {"hasDetails": true, "hasLocation": true, "isOwner": false, "timestamp": "2025-06-07T10:52:24.984Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-07T10:52:02.478Z"}, "postingId": "6cxr3VsbMA4JefozqPVX", "timestamp": "2025-06-07T10:52:24.985Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-07T10:52:24.986Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isFavorite": false, "isOwner": false, "postingId": "6cxr3VsbMA4JefozqPVX", "timestamp": "2025-06-07T10:52:24.986Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_6cxr3VsbMA4JefozqPVX_1749293544983", "offers_6cxr3VsbMA4JefozqPVX_1749293544983"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_6cxr3VsbMA4JefozqPVX_1749293544983"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_6cxr3VsbMA4JefozqPVX_1749293544983"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-07T10:52:24.993Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "6cxr3VsbMA4JefozqPVX", "timeSinceStart": 22516, "timestamp": "2025-06-07T10:52:24.993Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "6cxr3VsbMA4JefozqPVX", "timeSinceStart": 22516, "timestamp": "2025-06-07T10:52:24.993Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_6cxr3VsbMA4JefozqPVX_1749293544993", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_6cxr3VsbMA4JefozqPVX_1749293544993", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_6cxr3VsbMA4JefozqPVX_1749293544993", "postingSubId": "posting_6cxr3VsbMA4JefozqPVX_1749293544993", "totalTime": 22516}
 LOG  [PostingDetail][StateVerification] {"auth": {"currentUser": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "expectedOwner": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "posting": {"id": "6cxr3VsbMA4JefozqPVX", "loaded": true}, "state": {"activeTab": "active", "currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDescriptionExpanded": false, "isOwner": false, "lastUpdate": 1749293544985, "offersCount": 0, "postingDetailsId": "6cxr3VsbMA4JefozqPVX", "userOffer": null}, "timestamp": "2025-06-07T10:52:24.993Z"}
 LOG  [PostingDetail][StateDebug] {"auth": {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2"}, "performance": {"timeSinceLastUpdate": 9}, "props": {"postingId": "6cxr3VsbMA4JefozqPVX", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "state": {"activeTab": "active", "currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDescriptionExpanded": false, "isOwner": false, "lastUpdate": 1749293544985, "offersCount": 0, "postingDetailsId": "6cxr3VsbMA4JefozqPVX", "userOffer": null}, "subscriptions": {"metrics": {"lastUpdate": "2025-06-07T10:52:02.477Z", "subscriptionCount": 0, "totalTime": 22517}, "types": []}, "timestamp": "2025-06-07T10:52:24.994Z"}
 LOG  [useOffers][Update] {"changes": [{"id": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:52:25.075Z", "type": "added"}], "count": 1, "postingId": "6cxr3VsbMA4JefozqPVX", "timestamp": "2025-06-07T10:52:25.075Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-07T10:52:02.478Z"}, "postingId": "6cxr3VsbMA4JefozqPVX", "timestamp": "2025-06-07T10:52:25.077Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-07T10:52:25.077Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isFavorite": false, "isOwner": false, "postingId": "6cxr3VsbMA4JefozqPVX", "timestamp": "2025-06-07T10:52:25.077Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_6cxr3VsbMA4JefozqPVX_1749293544993", "offers_6cxr3VsbMA4JefozqPVX_1749293544993"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_6cxr3VsbMA4JefozqPVX_1749293544993"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_6cxr3VsbMA4JefozqPVX_1749293544993"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-07T10:52:25.090Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "6cxr3VsbMA4JefozqPVX", "timeSinceStart": 22613, "timestamp": "2025-06-07T10:52:25.090Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "6cxr3VsbMA4JefozqPVX", "timeSinceStart": 22614, "timestamp": "2025-06-07T10:52:25.091Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_6cxr3VsbMA4JefozqPVX_1749293545091", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_6cxr3VsbMA4JefozqPVX_1749293545091", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_6cxr3VsbMA4JefozqPVX_1749293545091", "postingSubId": "posting_6cxr3VsbMA4JefozqPVX_1749293545091", "totalTime": 22614}
 LOG  [PostingDetail][StateSync] Checking state update conditions: {"currentUser": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hasDetails": true, "offersCount": 1, "postingLoading": false, "timestamp": "2025-06-07T10:52:25.091Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Updating state with: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isOwnerCheck": false, "offersCount": 1, "postingId": "6cxr3VsbMA4JefozqPVX", "timestamp": "2025-06-07T10:52:25.091Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Processing offers: {"count": 1, "timestamp": "2025-06-07T10:52:25.092Z", "userOffers": 1}
 LOG  [PostingDetail][StateSync] State updated: {"hasUserOffer": true, "isOwner": false, "postingId": "6cxr3VsbMA4JefozqPVX", "timestamp": "2025-06-07T10:52:25.093Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-07T10:52:02.478Z"}, "postingId": "6cxr3VsbMA4JefozqPVX", "timestamp": "2025-06-07T10:52:25.094Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-07T10:52:25.094Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isFavorite": false, "isOwner": false, "postingId": "6cxr3VsbMA4JefozqPVX", "timestamp": "2025-06-07T10:52:25.095Z", "userOffer": {"id": "Np9YCjiwfJf7xT6wzXBK", "status": "pending"}}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_6cxr3VsbMA4JefozqPVX_1749293545091", "offers_6cxr3VsbMA4JefozqPVX_1749293545091"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_6cxr3VsbMA4JefozqPVX_1749293545091"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_6cxr3VsbMA4JefozqPVX_1749293545091"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-07T10:52:25.103Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "6cxr3VsbMA4JefozqPVX", "timeSinceStart": 22626, "timestamp": "2025-06-07T10:52:25.103Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "6cxr3VsbMA4JefozqPVX", "timeSinceStart": 22626, "timestamp": "2025-06-07T10:52:25.103Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_6cxr3VsbMA4JefozqPVX_1749293545103", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_6cxr3VsbMA4JefozqPVX_1749293545103", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_6cxr3VsbMA4JefozqPVX_1749293545103", "postingSubId": "posting_6cxr3VsbMA4JefozqPVX_1749293545103", "totalTime": 22650}
 LOG  [PostingDetail][StateVerification] {"auth": {"currentUser": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "expectedOwner": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "posting": {"id": "6cxr3VsbMA4JefozqPVX", "loaded": true}, "state": {"activeTab": "active", "currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDescriptionExpanded": false, "isOwner": false, "lastUpdate": 1749293545094, "offersCount": 1, "postingDetailsId": "6cxr3VsbMA4JefozqPVX", "userOffer": {"id": "Np9YCjiwfJf7xT6wzXBK", "status": "pending"}}, "timestamp": "2025-06-07T10:52:25.128Z"}
 LOG  [PostingDetail][StateDebug] {"auth": {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2"}, "performance": {"timeSinceLastUpdate": 35}, "props": {"postingId": "6cxr3VsbMA4JefozqPVX", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "state": {"activeTab": "active", "currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDescriptionExpanded": false, "isOwner": false, "lastUpdate": 1749293545094, "offersCount": 1, "postingDetailsId": "6cxr3VsbMA4JefozqPVX", "userOffer": {"id": "Np9YCjiwfJf7xT6wzXBK", "status": "pending"}}, "subscriptions": {"metrics": {"lastUpdate": "2025-06-07T10:52:02.477Z", "subscriptionCount": 0, "totalTime": 22651}, "types": []}, "timestamp": "2025-06-07T10:52:25.128Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-07T10:52:02.478Z"}, "postingId": "6cxr3VsbMA4JefozqPVX", "timestamp": "2025-06-07T10:52:25.131Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-07T10:52:25.135Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isFavorite": true, "isOwner": false, "postingId": "6cxr3VsbMA4JefozqPVX", "timestamp": "2025-06-07T10:52:25.135Z", "userOffer": {"id": "Np9YCjiwfJf7xT6wzXBK", "status": "pending"}}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_6cxr3VsbMA4JefozqPVX_1749293545103", "offers_6cxr3VsbMA4JefozqPVX_1749293545103"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_6cxr3VsbMA4JefozqPVX_1749293545103"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_6cxr3VsbMA4JefozqPVX_1749293545103"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-07T10:52:25.141Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "6cxr3VsbMA4JefozqPVX", "timeSinceStart": 22664, "timestamp": "2025-06-07T10:52:25.141Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "6cxr3VsbMA4JefozqPVX", "timeSinceStart": 22665, "timestamp": "2025-06-07T10:52:25.142Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_6cxr3VsbMA4JefozqPVX_1749293545142", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_6cxr3VsbMA4JefozqPVX_1749293545142", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_6cxr3VsbMA4JefozqPVX_1749293545142", "postingSubId": "posting_6cxr3VsbMA4JefozqPVX_1749293545142", "totalTime": 22665}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-07T10:52:02.478Z"}, "postingId": "6cxr3VsbMA4JefozqPVX", "timestamp": "2025-06-07T10:52:25.143Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-07T10:52:25.143Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isFavorite": true, "isOwner": false, "postingId": "6cxr3VsbMA4JefozqPVX", "timestamp": "2025-06-07T10:52:25.143Z", "userOffer": {"id": "Np9YCjiwfJf7xT6wzXBK", "status": "pending"}}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_6cxr3VsbMA4JefozqPVX_1749293545142", "offers_6cxr3VsbMA4JefozqPVX_1749293545142"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_6cxr3VsbMA4JefozqPVX_1749293545142"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_6cxr3VsbMA4JefozqPVX_1749293545142"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-07T10:52:25.150Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "6cxr3VsbMA4JefozqPVX", "timeSinceStart": 22674, "timestamp": "2025-06-07T10:52:25.151Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "6cxr3VsbMA4JefozqPVX", "timeSinceStart": 22674, "timestamp": "2025-06-07T10:52:25.151Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_6cxr3VsbMA4JefozqPVX_1749293545151", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_6cxr3VsbMA4JefozqPVX_1749293545151", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_6cxr3VsbMA4JefozqPVX_1749293545151", "postingSubId": "posting_6cxr3VsbMA4JefozqPVX_1749293545151", "totalTime": 22674}
 LOG  [Navigation] Rendering headerLeft for: PostingDetail
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Rendering headerLeft for: PostingDetail
 LOG  [Navigation] Can go back status: true
 LOG  Region changing: {"latitude": 37.81060591986971, "latitudeDelta": 0.021936330405040394, "longitude": -122.41975935267538, "longitudeDelta": 0.01609314918366067}
 LOG  Debounced region changed: {"latitude": 37.81060591986971, "latitudeDelta": 0.021936330405040394, "longitude": -122.41975935267538, "longitudeDelta": 0.01609314918366067}
 LOG  Fetching postings for new region
 LOG  [useControlledPostings] Fetching postings for region: {"latitude": 37.81060591986971, "latitudeDelta": 0.021936330405040394, "longitude": -122.41975935267538, "longitudeDelta": 0.01609314918366067}
 LOG  [fetchPostingsBySearch] Query results: 4
 LOG  [useControlledPostings] Fetched postings: 4




Execute Test:
On Simulator 1: Delete the posting and observe logs






 LOG  === Starting Posting Deletion Flow ===
 LOG  PostingId: 6cxr3VsbMA4JefozqPVX
 LOG  [deletePosting] Starting deletion process {"postingId": "6cxr3VsbMA4JefozqPVX", "stack": "Error
    at ?anon_0_ (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:193399:25)
    at next (native)
    at asyncGeneratorStep (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:5917:19)
    at _next (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:5931:29)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:5936:14)
    at tryCallTwo (/Users/<USER>/react-native/sdks/hermes/build_iphonesimulator/lib/InternalBytecode/InternalBytecode.js:61:9)
    at doResolve (/Users/<USER>/react-native/sdks/hermes/build_iphonesimulator/lib/InternalBytecode/InternalBytecode.js:216:25)
    at Promise (/Users/<USER>/react-native/sdks/hermes/build_iphonesimulator/lib/InternalBytecode/InternalBytecode.js:82:14)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:5928:25)
    at apply (native)
    at deletePosting (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:193636:26)
    at ?anon_0_ (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:261386:56)
    at next (native)
    at asyncGeneratorStep (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:5917:19)
    at _next (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:5931:29)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:5936:14)
    at tryCallTwo (/Users/<USER>/react-native/sdks/hermes/build_iphonesimulator/lib/InternalBytecode/InternalBytecode.js:61:9)
    at doResolve (/Users/<USER>/react-native/sdks/hermes/build_iphonesimulator/lib/InternalBytecode/InternalBytecode.js:216:25)
    at Promise (/Users/<USER>/react-native/sdks/hermes/build_iphonesimulator/lib/InternalBytecode/InternalBytecode.js:82:14)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:5928:25)
    at apply (native)
    at onPress (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:261401:34)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:42025:21)
    at apply (native)
    at __invokeCallback (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:3648:23)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:3343:34)
    at __guard (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:3551:15)
    at invokeCallbackAndReturnFlushedQueue (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:3342:21)", "timestamp": "2025-06-07T10:54:09.383Z"}
 LOG  [deletePosting] Fetching active offers...
 LOG  [deletePosting] Found offers: {"count": 1, "postingId": "6cxr3VsbMA4JefozqPVX", "timestamp": "2025-06-07T10:54:09.576Z"}
 LOG  [deletePosting] Marking posting as deleted: {"postingId": "6cxr3VsbMA4JefozqPVX", "postingTitle": "kous test post ", "timestamp": "2025-06-07T10:54:09.579Z"}
 LOG  [deletePosting] Processing offer: {"offerId": "Np9YCjiwfJf7xT6wzXBK", "postingId": "6cxr3VsbMA4JefozqPVX", "timestamp": "2025-06-07T10:54:09.582Z"}
 LOG  [deletePosting] Added system message to discussion: {"discussionId": "6WOMdhbzTwhfNc3fHazf", "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:54:09.586Z"}
 LOG  [deletePosting] Creating notification for offer owner: {"offerId": "Np9YCjiwfJf7xT6wzXBK", "recipientId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "timestamp": "2025-06-07T10:54:09.586Z", "type": "SYSTEM_WITHDRAWAL"}
 LOG  [deletePosting] Committing batch updates...
 LOG  [Messages Subscription] Received snapshot update: {"empty": false, "metadata": {"fromCache": false, "hasPendingWrites": true}, "size": 1}
 LOG  [Messages Subscription] Processing discussion: {"id": "6WOMdhbzTwhfNc3fHazf", "messageCount": 1}
 LOG  [Messages Subscription] Raw messages: {"count": 1, "messageKeys": ["text", "senderId", "timestamp", "type", "read"], "sampleMessage": {"read": true, "senderId": "system", "text": "This offer has been automatically withdrawn because the posting was deleted.", "timestamp": {"nanoseconds": 0, "seconds": 1749293649}, "type": "system"}}
 LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["text", "senderId", "timestamp", "type", "read"], "timestampType": "object"}
 LOG  [Messages Subscription] Validation summary: {"firstValidMessage": {"id": undefined, "senderId": "system", "timestamp": {"nanoseconds": 0, "seconds": 1749293649}}, "invalidMessages": 0, "totalMessages": 1, "validMessages": 1}
 LOG  [Messages Subscription] Processing messages: {"hasLegacyMessages": true, "messageTypes": [{"hasId": false, "hasTimestamp": true, "timestampType": "object"}], "newCount": 1, "prevCount": 0}
 LOG  [Messages Subscription] Updated messages state: {"firstMessage": {"id": undefined, "senderId": "system", "text": "This offer has been automatically withdrawn because the posting was deleted.", "timestamp": {"nanoseconds": 0, "seconds": 1749293649}}, "hasLegacyMessages": true, "lastMessage": {"id": undefined, "senderId": "system", "text": "This offer has been automatically withdrawn because the posting was deleted.", "timestamp": {"nanoseconds": 0, "seconds": 1749293649}}, "newCount": 1, "previousCount": 0}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDetailsExpanded": false, "isLoading": false, "postingDescription": "test", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "kous test post ", "timestamp": "2025-06-07T10:54:09.612Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isCurrentUserPostingOwner": true, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T10:54:09.613Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "hookDescription": undefined, "hookPrice": undefined, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": true, "offerDescription": "hotmails offer", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "1111", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isCurrentUserOfferOwner": false, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T10:54:09.617Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:54:09.631Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:54:09.631Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:54:09.631Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": false, "timestamp": "2025-06-07T10:54:09.632Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:54:09.632Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:54:09.632Z"}
 LOG  [OfferCache][GET] {"age": 52148, "found": true, "isExpired": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:54:09.632Z"}
 LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"cachedStatus": "pending", "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:54:09.633Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:54:09.633Z"}
 LOG  Marking messages as read for authorized user: {"isOfferOwner": false, "isPostingOwner": true, "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  Initial load, scrolling to end: 1
 LOG  OfferDetailsSection render: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "hookDescription": "hotmails offer", "hookPrice": 1111, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": true, "offerDescription": "hotmails offer", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "1111", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": true}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isCurrentUserOfferOwner": false, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T10:54:09.635Z"}
 LOG  [OfferDetailsSection][OFFER_UPDATED] {"description": "hotmails offer", "offerId": "Np9YCjiwfJf7xT6wzXBK", "price": 1111, "timestamp": "2025-06-07T10:54:09.638Z"}
 LOG  [useOfferDetails][REAL_TIME_UPDATE] {"offerId": "Np9YCjiwfJf7xT6wzXBK", "status": "withdrawn", "timestamp": "2025-06-07T10:54:09.638Z"}
 LOG  [OfferCache][SET] {"offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:54:09.639Z"}
 LOG  [OfferDetail][STATUS_TRACKING] {"hasInitialOffer": false, "hasOfferDetails": true, "initialOfferStatus": undefined, "offerId": "Np9YCjiwfJf7xT6wzXBK", "offerStatus": "withdrawn", "postingStatus": "Active", "timestamp": "2025-06-07T10:54:09.640Z"}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDetailsExpanded": false, "isLoading": false, "postingDescription": "test", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "kous test post ", "timestamp": "2025-06-07T10:54:09.641Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isCurrentUserPostingOwner": true, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T10:54:09.642Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "hookDescription": undefined, "hookPrice": undefined, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": true, "offerDescription": "hotmails offer", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "1111", "offerStatus": "withdrawn", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isCurrentUserOfferOwner": false, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T10:54:09.646Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:54:09.654Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:54:09.654Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:54:09.654Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": false, "timestamp": "2025-06-07T10:54:09.654Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:54:09.654Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:54:09.654Z"}
 LOG  [OfferCache][GET] {"age": 16, "found": true, "isExpired": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:54:09.655Z"}
 LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"cachedStatus": "withdrawn", "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:54:09.655Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:54:09.655Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "hookDescription": "hotmails offer", "hookPrice": 1111, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": true, "offerDescription": "hotmails offer", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "1111", "offerStatus": "withdrawn", "restrictionEndDate": null, "userScore": 100, "usingHookData": true}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isCurrentUserOfferOwner": false, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T10:54:09.656Z"}
 LOG  [OfferDetailsSection][OFFER_UPDATED] {"description": "hotmails offer", "offerId": "Np9YCjiwfJf7xT6wzXBK", "price": 1111, "timestamp": "2025-06-07T10:54:09.660Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-07T10:53:29.016Z"}, "postingId": "6cxr3VsbMA4JefozqPVX", "timestamp": "2025-06-07T10:54:09.661Z"}
 LOG  [usePostingDetails][Reducer] Processing action: {"currentState": {"error": null, "loading": false, "postingDetails": {"createdAt": [Timestamp], "description": "test", "id": "6cxr3VsbMA4JefozqPVX", "latitude": 37.81130189153112, "longitude": -122.42037530683824, "postingStatus": "Active", "searchTerms": [Array], "title": "kous test post ", "titleLower": "kous test post ", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}}, "metrics": {"timeSinceStart": 40645, "timestamp": "2025-06-07T10:54:09.661Z", "updateCount": 3}, "type": "UPDATE_POSTING"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-07T10:54:09.661Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isFavorite": false, "isOwner": true, "postingId": "6cxr3VsbMA4JefozqPVX", "timestamp": "2025-06-07T10:54:09.662Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_6cxr3VsbMA4JefozqPVX_1749293609346", "offers_6cxr3VsbMA4JefozqPVX_1749293609346"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_6cxr3VsbMA4JefozqPVX_1749293609346"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_6cxr3VsbMA4JefozqPVX_1749293609346"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-07T10:54:09.667Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "6cxr3VsbMA4JefozqPVX", "timeSinceStart": 40653, "timestamp": "2025-06-07T10:54:09.668Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "6cxr3VsbMA4JefozqPVX", "timeSinceStart": 40653, "timestamp": "2025-06-07T10:54:09.668Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_6cxr3VsbMA4JefozqPVX_1749293649668", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_6cxr3VsbMA4JefozqPVX_1749293649668", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_6cxr3VsbMA4JefozqPVX_1749293649668", "postingSubId": "posting_6cxr3VsbMA4JefozqPVX_1749293649668", "totalTime": 40653}
 LOG  [PostingDetail][StateSync] Checking state update conditions: {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "hasDetails": true, "offersCount": 1, "postingLoading": false, "timestamp": "2025-06-07T10:54:09.668Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Updating state with: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isOwnerCheck": true, "offersCount": 1, "postingId": "6cxr3VsbMA4JefozqPVX", "timestamp": "2025-06-07T10:54:09.668Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Processing offers: {"count": 1, "timestamp": "2025-06-07T10:54:09.668Z", "userOffers": 0}
 LOG  [PostingDetail][StateSync] State updated: {"hasUserOffer": false, "isOwner": true, "postingId": "6cxr3VsbMA4JefozqPVX", "timestamp": "2025-06-07T10:54:09.669Z"}
 LOG  [PostingDetail][StateVerification] {"auth": {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "expectedOwner": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "posting": {"id": "6cxr3VsbMA4JefozqPVX", "loaded": true}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": false, "isOwner": true, "lastUpdate": 1749293609206, "offersCount": 1, "postingDetailsId": "6cxr3VsbMA4JefozqPVX", "userOffer": null}, "timestamp": "2025-06-07T10:54:09.669Z"}
 LOG  [PostingDetail] Posting details loaded for edit: {"hasDetails": true, "hasLocation": false, "isOwner": true, "timestamp": "2025-06-07T10:54:09.669Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-07T10:53:29.016Z"}, "postingId": "6cxr3VsbMA4JefozqPVX", "timestamp": "2025-06-07T10:54:09.670Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-07T10:54:09.670Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isFavorite": false, "isOwner": true, "postingId": "6cxr3VsbMA4JefozqPVX", "timestamp": "2025-06-07T10:54:09.671Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_6cxr3VsbMA4JefozqPVX_1749293649668", "offers_6cxr3VsbMA4JefozqPVX_1749293649668"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_6cxr3VsbMA4JefozqPVX_1749293649668"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_6cxr3VsbMA4JefozqPVX_1749293649668"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-07T10:54:09.676Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "6cxr3VsbMA4JefozqPVX", "timeSinceStart": 40662, "timestamp": "2025-06-07T10:54:09.677Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "6cxr3VsbMA4JefozqPVX", "timeSinceStart": 40662, "timestamp": "2025-06-07T10:54:09.677Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_6cxr3VsbMA4JefozqPVX_1749293649677", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_6cxr3VsbMA4JefozqPVX_1749293649677", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_6cxr3VsbMA4JefozqPVX_1749293649677", "postingSubId": "posting_6cxr3VsbMA4JefozqPVX_1749293649677", "totalTime": 40662}
 LOG  [PostingDetail][StateVerification] {"auth": {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "expectedOwner": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "posting": {"id": "6cxr3VsbMA4JefozqPVX", "loaded": true}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": false, "isOwner": true, "lastUpdate": 1749293649670, "offersCount": 1, "postingDetailsId": "6cxr3VsbMA4JefozqPVX", "userOffer": null}, "timestamp": "2025-06-07T10:54:09.677Z"}
 LOG  [PostingDetail][StateDebug] {"auth": {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "performance": {"timeSinceLastUpdate": 7}, "props": {"postingId": "6cxr3VsbMA4JefozqPVX", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": false, "isOwner": true, "lastUpdate": 1749293649670, "offersCount": 1, "postingDetailsId": "6cxr3VsbMA4JefozqPVX", "userOffer": null}, "subscriptions": {"metrics": {"lastUpdate": "2025-06-07T10:53:29.015Z", "subscriptionCount": 0, "totalTime": 40662}, "types": []}, "timestamp": "2025-06-07T10:54:09.677Z"}
 LOG  [useOffers][Update] {"changes": [{"id": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:54:09.679Z", "type": "modified"}], "count": 1, "postingId": "6cxr3VsbMA4JefozqPVX", "timestamp": "2025-06-07T10:54:09.679Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-07T10:53:29.016Z"}, "postingId": "6cxr3VsbMA4JefozqPVX", "timestamp": "2025-06-07T10:54:09.680Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-07T10:54:09.681Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isFavorite": false, "isOwner": true, "postingId": "6cxr3VsbMA4JefozqPVX", "timestamp": "2025-06-07T10:54:09.681Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_6cxr3VsbMA4JefozqPVX_1749293649677", "offers_6cxr3VsbMA4JefozqPVX_1749293649677"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_6cxr3VsbMA4JefozqPVX_1749293649677"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_6cxr3VsbMA4JefozqPVX_1749293649677"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-07T10:54:09.686Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "6cxr3VsbMA4JefozqPVX", "timeSinceStart": 40671, "timestamp": "2025-06-07T10:54:09.686Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "6cxr3VsbMA4JefozqPVX", "timeSinceStart": 40671, "timestamp": "2025-06-07T10:54:09.686Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_6cxr3VsbMA4JefozqPVX_1749293649686", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_6cxr3VsbMA4JefozqPVX_1749293649686", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_6cxr3VsbMA4JefozqPVX_1749293649686", "postingSubId": "posting_6cxr3VsbMA4JefozqPVX_1749293649686", "totalTime": 40672}
 LOG  [PostingDetail][StateSync] Checking state update conditions: {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "hasDetails": true, "offersCount": 1, "postingLoading": false, "timestamp": "2025-06-07T10:54:09.687Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Updating state with: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isOwnerCheck": true, "offersCount": 1, "postingId": "6cxr3VsbMA4JefozqPVX", "timestamp": "2025-06-07T10:54:09.687Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Processing offers: {"count": 1, "timestamp": "2025-06-07T10:54:09.687Z", "userOffers": 0}
 LOG  [PostingDetail][StateSync] State updated: {"hasUserOffer": false, "isOwner": true, "postingId": "6cxr3VsbMA4JefozqPVX", "timestamp": "2025-06-07T10:54:09.687Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-07T10:53:29.016Z"}, "postingId": "6cxr3VsbMA4JefozqPVX", "timestamp": "2025-06-07T10:54:09.688Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-07T10:54:09.688Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isFavorite": false, "isOwner": true, "postingId": "6cxr3VsbMA4JefozqPVX", "timestamp": "2025-06-07T10:54:09.688Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_6cxr3VsbMA4JefozqPVX_1749293649686", "offers_6cxr3VsbMA4JefozqPVX_1749293649686"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_6cxr3VsbMA4JefozqPVX_1749293649686"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_6cxr3VsbMA4JefozqPVX_1749293649686"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-07T10:54:09.692Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "6cxr3VsbMA4JefozqPVX", "timeSinceStart": 40678, "timestamp": "2025-06-07T10:54:09.693Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "6cxr3VsbMA4JefozqPVX", "timeSinceStart": 40678, "timestamp": "2025-06-07T10:54:09.693Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_6cxr3VsbMA4JefozqPVX_1749293649693", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_6cxr3VsbMA4JefozqPVX_1749293649693", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_6cxr3VsbMA4JefozqPVX_1749293649693", "postingSubId": "posting_6cxr3VsbMA4JefozqPVX_1749293649693", "totalTime": 40678}
 LOG  [PostingDetail][StateVerification] {"auth": {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "expectedOwner": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "posting": {"id": "6cxr3VsbMA4JefozqPVX", "loaded": true}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": false, "isOwner": true, "lastUpdate": 1749293649688, "offersCount": 1, "postingDetailsId": "6cxr3VsbMA4JefozqPVX", "userOffer": null}, "timestamp": "2025-06-07T10:54:09.693Z"}
 LOG  [PostingDetail][StateDebug] {"auth": {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "performance": {"timeSinceLastUpdate": 5}, "props": {"postingId": "6cxr3VsbMA4JefozqPVX", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": false, "isOwner": true, "lastUpdate": 1749293649688, "offersCount": 1, "postingDetailsId": "6cxr3VsbMA4JefozqPVX", "userOffer": null}, "subscriptions": {"metrics": {"lastUpdate": "2025-06-07T10:53:29.015Z", "subscriptionCount": 0, "totalTime": 40678}, "types": []}, "timestamp": "2025-06-07T10:54:09.693Z"}
 LOG  FlatList layout complete
 LOG  Skipping load more: {"hasLastDoc": false, "hasMoreMessages": true, "lastDocId": undefined, "loadingMore": false}
 LOG  Skipping load more: {"hasLastDoc": false, "hasMoreMessages": true, "lastDocId": undefined, "loadingMore": false}
 LOG  FlatList layout complete
 LOG  [deletePosting] Notification document created: {"notificationId": "cGHRkhRGzzL9WHbkuXh8", "offerId": "Np9YCjiwfJf7xT6wzXBK", "recipientId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "timestamp": "2025-06-07T10:54:09.770Z"}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDetailsExpanded": false, "isLoading": false, "postingDescription": "test", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "kous test post ", "timestamp": "2025-06-07T10:54:09.811Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isCurrentUserPostingOwner": true, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T10:54:09.812Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "hookDescription": undefined, "hookPrice": undefined, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": true, "offerDescription": "hotmails offer", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "1111", "offerStatus": "withdrawn", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isCurrentUserOfferOwner": false, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T10:54:09.817Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:54:09.826Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:54:09.826Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:54:09.826Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": false, "timestamp": "2025-06-07T10:54:09.827Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:54:09.827Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:54:09.827Z"}
 LOG  [OfferCache][GET] {"age": 188, "found": true, "isExpired": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:54:09.827Z"}
 LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"cachedStatus": "withdrawn", "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:54:09.827Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:54:09.828Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "hookDescription": "hotmails offer", "hookPrice": 1111, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": true, "offerDescription": "hotmails offer", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "1111", "offerStatus": "withdrawn", "restrictionEndDate": null, "userScore": 100, "usingHookData": true}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isCurrentUserOfferOwner": false, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T10:54:09.831Z"}
 LOG  [OfferDetailsSection][OFFER_UPDATED] {"description": "hotmails offer", "offerId": "Np9YCjiwfJf7xT6wzXBK", "price": 1111, "timestamp": "2025-06-07T10:54:09.833Z"}
 LOG  [deletePosting] Sending notifications to offer owners... {"notificationCount": 1, "timestamp": "2025-06-07T10:54:09.892Z"}
 LOG  [deletePosting] Notification results: {"fulfilled": 1, "rejected": 0, "timestamp": "2025-06-07T10:54:09.892Z", "total": 1}
 LOG  [deletePosting] Notification 1 succeeded: {"notificationId": "cGHRkhRGzzL9WHbkuXh8", "timestamp": "2025-06-07T10:54:09.893Z"}
 LOG  [deletePosting] Process completed successfully: {"notificationsSent": 1, "offersProcessed": 1, "postingId": "6cxr3VsbMA4JefozqPVX", "timestamp": "2025-06-07T10:54:09.893Z"}
 LOG  Posting deleted successfully
 LOG  [Messages Subscription] Received snapshot update: {"empty": false, "metadata": {"fromCache": false, "hasPendingWrites": true}, "size": 1}
 LOG  [Messages Subscription] Processing discussion: {"id": "6WOMdhbzTwhfNc3fHazf", "messageCount": 1}
 LOG  [Messages Subscription] Raw messages: {"count": 1, "messageKeys": ["text", "senderId", "timestamp", "type", "read"], "sampleMessage": {"read": true, "senderId": "system", "text": "This offer has been automatically withdrawn because the posting was deleted.", "timestamp": {"nanoseconds": 0, "seconds": 1749293649}, "type": "system"}}
 LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["text", "senderId", "timestamp", "type", "read"], "timestampType": "object"}
 LOG  [Messages Subscription] Validation summary: {"firstValidMessage": {"id": undefined, "senderId": "system", "timestamp": {"nanoseconds": 0, "seconds": 1749293649}}, "invalidMessages": 0, "totalMessages": 1, "validMessages": 1}
 LOG  [Messages Subscription] Processing messages: {"hasLegacyMessages": true, "messageTypes": [{"hasId": false, "hasTimestamp": true, "timestampType": "object"}], "newCount": 1, "prevCount": 1}
 LOG  [Messages Subscription] Updated messages state: {"firstMessage": {"id": undefined, "senderId": "system", "text": "This offer has been automatically withdrawn because the posting was deleted.", "timestamp": {"nanoseconds": 0, "seconds": 1749293649}}, "hasLegacyMessages": true, "lastMessage": {"id": undefined, "senderId": "system", "text": "This offer has been automatically withdrawn because the posting was deleted.", "timestamp": {"nanoseconds": 0, "seconds": 1749293649}}, "newCount": 1, "previousCount": 1}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDetailsExpanded": false, "isLoading": false, "postingDescription": "test", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "kous test post ", "timestamp": "2025-06-07T10:54:09.899Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isCurrentUserPostingOwner": true, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T10:54:09.899Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "hookDescription": undefined, "hookPrice": undefined, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": true, "offerDescription": "hotmails offer", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "1111", "offerStatus": "withdrawn", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isCurrentUserOfferOwner": false, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T10:54:09.903Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:54:09.911Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:54:09.911Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:54:09.911Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": false, "timestamp": "2025-06-07T10:54:09.912Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:54:09.912Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:54:09.912Z"}
 LOG  [OfferCache][GET] {"age": 273, "found": true, "isExpired": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:54:09.912Z"}
 LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"cachedStatus": "withdrawn", "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:54:09.913Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:54:09.913Z"}
 LOG  Marking messages as read for authorized user: {"isOfferOwner": false, "isPostingOwner": true, "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  OfferDetailsSection render: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "hookDescription": "hotmails offer", "hookPrice": 1111, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": true, "offerDescription": "hotmails offer", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "1111", "offerStatus": "withdrawn", "restrictionEndDate": null, "userScore": 100, "usingHookData": true}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isCurrentUserOfferOwner": false, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T10:54:09.916Z"}
 LOG  [OfferDetailsSection][OFFER_UPDATED] {"description": "hotmails offer", "offerId": "Np9YCjiwfJf7xT6wzXBK", "price": 1111, "timestamp": "2025-06-07T10:54:09.918Z"}
 LOG  [useOfferDetails][REAL_TIME_UPDATE] {"offerId": "Np9YCjiwfJf7xT6wzXBK", "status": "withdrawn", "timestamp": "2025-06-07T10:54:09.932Z"}
 LOG  [OfferCache][SET] {"offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:54:09.932Z"}
 LOG  [OfferDetail][STATUS_TRACKING] {"hasInitialOffer": false, "hasOfferDetails": true, "initialOfferStatus": undefined, "offerId": "Np9YCjiwfJf7xT6wzXBK", "offerStatus": "withdrawn", "postingStatus": "Active", "timestamp": "2025-06-07T10:54:09.933Z"}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDetailsExpanded": false, "isLoading": false, "postingDescription": "test", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "kous test post ", "timestamp": "2025-06-07T10:54:09.934Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isCurrentUserPostingOwner": true, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T10:54:09.935Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "hookDescription": undefined, "hookPrice": undefined, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": true, "offerDescription": "hotmails offer", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "1111", "offerStatus": "withdrawn", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isCurrentUserOfferOwner": false, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T10:54:09.938Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:54:09.944Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:54:09.944Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:54:09.945Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": false, "timestamp": "2025-06-07T10:54:09.945Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:54:09.945Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:54:09.946Z"}
 LOG  [OfferCache][GET] {"age": 14, "found": true, "isExpired": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:54:09.946Z"}
 LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"cachedStatus": "withdrawn", "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:54:09.947Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:54:09.947Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "hookDescription": "hotmails offer", "hookPrice": 1111, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": true, "offerDescription": "hotmails offer", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "1111", "offerStatus": "withdrawn", "restrictionEndDate": null, "userScore": 100, "usingHookData": true}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isCurrentUserOfferOwner": false, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T10:54:09.949Z"}
 LOG  [OfferDetailsSection][OFFER_UPDATED] {"description": "hotmails offer", "offerId": "Np9YCjiwfJf7xT6wzXBK", "price": 1111, "timestamp": "2025-06-07T10:54:09.951Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-07T10:53:29.016Z"}, "postingId": "6cxr3VsbMA4JefozqPVX", "timestamp": "2025-06-07T10:54:09.952Z"}
 LOG  [usePostingDetails][Reducer] Processing action: {"currentState": {"error": null, "loading": false, "postingDetails": {"createdAt": [Timestamp], "deletedAt": null, "description": "test", "id": "6cxr3VsbMA4JefozqPVX", "latitude": 37.81130189153112, "longitude": -122.42037530683824, "postingStatus": "Deleted", "searchTerms": [Array], "title": "kous test post ", "titleLower": "kous test post ", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}}, "metrics": {"timeSinceStart": 40936, "timestamp": "2025-06-07T10:54:09.952Z", "updateCount": 4}, "type": "UPDATE_POSTING"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-07T10:54:09.953Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isFavorite": false, "isOwner": true, "postingId": "6cxr3VsbMA4JefozqPVX", "timestamp": "2025-06-07T10:54:09.953Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_6cxr3VsbMA4JefozqPVX_1749293649693", "offers_6cxr3VsbMA4JefozqPVX_1749293649693"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_6cxr3VsbMA4JefozqPVX_1749293649693"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_6cxr3VsbMA4JefozqPVX_1749293649693"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-07T10:54:09.957Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "6cxr3VsbMA4JefozqPVX", "timeSinceStart": 40942, "timestamp": "2025-06-07T10:54:09.957Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "6cxr3VsbMA4JefozqPVX", "timeSinceStart": 40942, "timestamp": "2025-06-07T10:54:09.957Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_6cxr3VsbMA4JefozqPVX_1749293649957", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_6cxr3VsbMA4JefozqPVX_1749293649957", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_6cxr3VsbMA4JefozqPVX_1749293649957", "postingSubId": "posting_6cxr3VsbMA4JefozqPVX_1749293649957", "totalTime": 40942}
 LOG  [PostingDetail][StateSync] Checking state update conditions: {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "hasDetails": true, "offersCount": 1, "postingLoading": false, "timestamp": "2025-06-07T10:54:09.957Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Updating state with: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isOwnerCheck": true, "offersCount": 1, "postingId": "6cxr3VsbMA4JefozqPVX", "timestamp": "2025-06-07T10:54:09.957Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Processing offers: {"count": 1, "timestamp": "2025-06-07T10:54:09.958Z", "userOffers": 0}
 LOG  [PostingDetail][StateSync] State updated: {"hasUserOffer": false, "isOwner": true, "postingId": "6cxr3VsbMA4JefozqPVX", "timestamp": "2025-06-07T10:54:09.958Z"}
 LOG  [PostingDetail][StateVerification] {"auth": {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "expectedOwner": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "posting": {"id": "6cxr3VsbMA4JefozqPVX", "loaded": true}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": false, "isOwner": true, "lastUpdate": 1749293649688, "offersCount": 1, "postingDetailsId": "6cxr3VsbMA4JefozqPVX", "userOffer": null}, "timestamp": "2025-06-07T10:54:09.958Z"}
 LOG  [PostingDetail] Posting details loaded for edit: {"hasDetails": true, "hasLocation": false, "isOwner": true, "timestamp": "2025-06-07T10:54:09.958Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-07T10:53:29.016Z"}, "postingId": "6cxr3VsbMA4JefozqPVX", "timestamp": "2025-06-07T10:54:09.960Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-07T10:54:09.960Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isFavorite": false, "isOwner": true, "postingId": "6cxr3VsbMA4JefozqPVX", "timestamp": "2025-06-07T10:54:09.960Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_6cxr3VsbMA4JefozqPVX_1749293649957", "offers_6cxr3VsbMA4JefozqPVX_1749293649957"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_6cxr3VsbMA4JefozqPVX_1749293649957"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_6cxr3VsbMA4JefozqPVX_1749293649957"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-07T10:54:09.964Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "6cxr3VsbMA4JefozqPVX", "timeSinceStart": 40949, "timestamp": "2025-06-07T10:54:09.964Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "6cxr3VsbMA4JefozqPVX", "timeSinceStart": 40950, "timestamp": "2025-06-07T10:54:09.965Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_6cxr3VsbMA4JefozqPVX_1749293649965", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_6cxr3VsbMA4JefozqPVX_1749293649965", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_6cxr3VsbMA4JefozqPVX_1749293649965", "postingSubId": "posting_6cxr3VsbMA4JefozqPVX_1749293649965", "totalTime": 40950}
 LOG  [PostingDetail][StateVerification] {"auth": {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "expectedOwner": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "posting": {"id": "6cxr3VsbMA4JefozqPVX", "loaded": true}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": false, "isOwner": true, "lastUpdate": 1749293649960, "offersCount": 1, "postingDetailsId": "6cxr3VsbMA4JefozqPVX", "userOffer": null}, "timestamp": "2025-06-07T10:54:09.965Z"}
 LOG  [PostingDetail][StateDebug] {"auth": {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "performance": {"timeSinceLastUpdate": 5}, "props": {"postingId": "6cxr3VsbMA4JefozqPVX", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": false, "isOwner": true, "lastUpdate": 1749293649960, "offersCount": 1, "postingDetailsId": "6cxr3VsbMA4JefozqPVX", "userOffer": null}, "subscriptions": {"metrics": {"lastUpdate": "2025-06-07T10:53:29.015Z", "subscriptionCount": 0, "totalTime": 40950}, "types": []}, "timestamp": "2025-06-07T10:54:09.965Z"}
 LOG  [useOffers][Update] {"changes": [{"id": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:54:09.966Z", "type": "modified"}], "count": 1, "postingId": "6cxr3VsbMA4JefozqPVX", "timestamp": "2025-06-07T10:54:09.966Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-07T10:53:29.016Z"}, "postingId": "6cxr3VsbMA4JefozqPVX", "timestamp": "2025-06-07T10:54:09.967Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-07T10:54:09.967Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isFavorite": false, "isOwner": true, "postingId": "6cxr3VsbMA4JefozqPVX", "timestamp": "2025-06-07T10:54:09.968Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_6cxr3VsbMA4JefozqPVX_1749293649965", "offers_6cxr3VsbMA4JefozqPVX_1749293649965"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_6cxr3VsbMA4JefozqPVX_1749293649965"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_6cxr3VsbMA4JefozqPVX_1749293649965"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-07T10:54:09.971Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "6cxr3VsbMA4JefozqPVX", "timeSinceStart": 40956, "timestamp": "2025-06-07T10:54:09.971Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "6cxr3VsbMA4JefozqPVX", "timeSinceStart": 40957, "timestamp": "2025-06-07T10:54:09.972Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_6cxr3VsbMA4JefozqPVX_1749293649972", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_6cxr3VsbMA4JefozqPVX_1749293649972", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_6cxr3VsbMA4JefozqPVX_1749293649972", "postingSubId": "posting_6cxr3VsbMA4JefozqPVX_1749293649972", "totalTime": 40958}
 LOG  [PostingDetail][StateSync] Checking state update conditions: {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "hasDetails": true, "offersCount": 1, "postingLoading": false, "timestamp": "2025-06-07T10:54:09.973Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Updating state with: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isOwnerCheck": true, "offersCount": 1, "postingId": "6cxr3VsbMA4JefozqPVX", "timestamp": "2025-06-07T10:54:09.973Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Processing offers: {"count": 1, "timestamp": "2025-06-07T10:54:09.973Z", "userOffers": 0}
 LOG  [PostingDetail][StateSync] State updated: {"hasUserOffer": false, "isOwner": true, "postingId": "6cxr3VsbMA4JefozqPVX", "timestamp": "2025-06-07T10:54:09.973Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-07T10:53:29.016Z"}, "postingId": "6cxr3VsbMA4JefozqPVX", "timestamp": "2025-06-07T10:54:09.974Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-07T10:54:09.974Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isFavorite": false, "isOwner": true, "postingId": "6cxr3VsbMA4JefozqPVX", "timestamp": "2025-06-07T10:54:09.974Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_6cxr3VsbMA4JefozqPVX_1749293649972", "offers_6cxr3VsbMA4JefozqPVX_1749293649972"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_6cxr3VsbMA4JefozqPVX_1749293649972"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_6cxr3VsbMA4JefozqPVX_1749293649972"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-07T10:54:09.978Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "6cxr3VsbMA4JefozqPVX", "timeSinceStart": 40964, "timestamp": "2025-06-07T10:54:09.979Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "6cxr3VsbMA4JefozqPVX", "timeSinceStart": 40964, "timestamp": "2025-06-07T10:54:09.979Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_6cxr3VsbMA4JefozqPVX_1749293649979", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_6cxr3VsbMA4JefozqPVX_1749293649979", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_6cxr3VsbMA4JefozqPVX_1749293649979", "postingSubId": "posting_6cxr3VsbMA4JefozqPVX_1749293649979", "totalTime": 40964}
 LOG  [PostingDetail][StateVerification] {"auth": {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "expectedOwner": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "posting": {"id": "6cxr3VsbMA4JefozqPVX", "loaded": true}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": false, "isOwner": true, "lastUpdate": 1749293649974, "offersCount": 1, "postingDetailsId": "6cxr3VsbMA4JefozqPVX", "userOffer": null}, "timestamp": "2025-06-07T10:54:09.979Z"}
 LOG  [PostingDetail][StateDebug] {"auth": {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "performance": {"timeSinceLastUpdate": 5}, "props": {"postingId": "6cxr3VsbMA4JefozqPVX", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": false, "isOwner": true, "lastUpdate": 1749293649974, "offersCount": 1, "postingDetailsId": "6cxr3VsbMA4JefozqPVX", "userOffer": null}, "subscriptions": {"metrics": {"lastUpdate": "2025-06-07T10:53:29.015Z", "subscriptionCount": 0, "totalTime": 40964}, "types": []}, "timestamp": "2025-06-07T10:54:09.979Z"}




 On Simulator 2: a notification was received regarding to the deletion of the posting and notification bell had a red dot. when i tapped the notification bell icon in home screen, i navigated to the notifications screen. i was able to see the new notification and tapped on it, it navigated to the offer detail screen. the offer detail screen was displayed correctly with deletion info as the offer owner was NOT ALLOWED to send discussion message and the withdrawal banner was displayed. 





 LOG  [NotificationsScreen][handleNotificationPress] Starting with: {"hasData": true, "notificationId": "cGHRkhRGzzL9WHbkuXh8", "offerId": "Np9YCjiwfJf7xT6wzXBK", "stack": "Error
    at ?anon_0_ (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:272677:29)
    at next (native)
    at asyncGeneratorStep (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:5917:19)
    at _next (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:5931:29)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:5936:14)
    at tryCallTwo (/Users/<USER>/react-native/sdks/hermes/build_iphonesimulator/lib/InternalBytecode/InternalBytecode.js:61:9)
    at doResolve (/Users/<USER>/react-native/sdks/hermes/build_iphonesimulator/lib/InternalBytecode/InternalBytecode.js:216:25)
    at Promise (/Users/<USER>/react-native/sdks/hermes/build_iphonesimulator/lib/InternalBytecode/InternalBytecode.js:82:14)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:5928:25)
    at apply (native)
    at handleNotificationPress (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:272787:27)
    at onPress (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:272822:41)
    at _performTransitionSideEffects (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:79291:22)
    at _receiveSignal (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:79241:45)
    at onResponderRelease (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:79097:34)
    at apply (native)
    at invokeGuardedCallbackProd (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:79798:21)
    at apply (native)
    at invokeGuardedCallback (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:79974:42)
    at apply (native)
    at invokeGuardedCallbackAndCatchFirstError (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:79988:36)
    at executeDispatch (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:80065:48)
    at executeDispatchesInOrder (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:80087:26)
    at executeDispatchesAndRelease (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:81858:35)
    at executeDispatchesAndReleaseTopLevel (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:81865:43)
    at forEach (native)
    at forEachAccumulated (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:80679:22)
    at runEventsInBatch (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:81878:27)
    at runExtractedPluginEventsInBatch (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:81988:25)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:81959:42)
    at batchedUpdates$1 (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:95400:20)
    at batchedUpdates (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:81834:36)
    at _receiveRootNodeIDEvent (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:81958:23)
    at receiveTouches (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:82047:34)
    at apply (native)
    at __callFunction (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:3612:36)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:3334:31)
    at __guard (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:3551:15)
    at callFunctionReturnFlushedQueue (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:3333:21)", "type": "SYSTEM_WITHDRAWAL"}
 LOG  Notification snapshot received, count: 439
 LOG  Received 439 notifications
 LOG  [NotificationsScreen] Processing SYSTEM_WITHDRAWAL notification: {"notification": {"createdAt": [Object], "id": "cGHRkhRGzzL9WHbkuXh8", "offerId": "Np9YCjiwfJf7xT6wzXBK", "postingId": "6cxr3VsbMA4JefozqPVX", "read": false, "recipientId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "senderId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "type": "SYSTEM_WITHDRAWAL"}, "timestamp": "2025-06-07T10:55:51.007Z"}
 LOG  [NotificationsScreen] Navigating to OfferDetail for withdrawn offer: {"offerId": "Np9YCjiwfJf7xT6wzXBK", "postingId": "6cxr3VsbMA4JefozqPVX", "timestamp": "2025-06-07T10:55:51.010Z", "type": "SYSTEM_WITHDRAWAL"}
 LOG  [Navigation] Configuring screen: LoginScreen
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: Home
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: Notifications
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: OfferDetail
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Rendering headerLeft for: Notifications
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Rendering headerLeft for: OfferDetail
 LOG  [Navigation] Can go back status: true
 LOG  [BackButton] Rendering back button
 LOG  [OfferDetail][STATUS_TRACKING] {"hasInitialOffer": false, "hasOfferDetails": false, "initialOfferStatus": undefined, "offerId": "Np9YCjiwfJf7xT6wzXBK", "offerStatus": "active", "postingStatus": "Active", "timestamp": "2025-06-07T10:55:51.063Z"}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": false, "isLoading": true, "postingDescription": "", "postingOwnerId": null, "postingStatus": "Active", "postingTitle": "", "timestamp": "2025-06-07T10:55:51.068Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": null, "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T10:55:51.068Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": true, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "", "offerOwnerId": null, "offerPrice": "0", "offerStatus": "active", "restrictionEndDate": null, "userScore": undefined, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": false, "offerOwnerId": null, "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T10:55:51.076Z"}
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": null, "shouldShowAbusiveButton": false, "timestamp": "2025-06-07T10:55:51.087Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.087Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.087Z"}
 LOG  [useOfferDetails][FETCHING_DATA] {"fetchCount": 1, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.088Z"}
 LOG  [useOfferDetails][FETCH_OFFER_START] {"forceRefresh": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.089Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.090Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.090Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.090Z"}
 LOG  [useOfferDetails][FETCHING_DATA] {"fetchCount": 1, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.090Z"}
 LOG  [useOfferDetails][FETCH_OFFER_START] {"forceRefresh": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.090Z"}
 LOG  [useAuthUser] Setting up auth state listener {"currentUser": {"_redirectEventId": undefined, "apiKey": "AIzaSyBOHewmZjlRWGu1jeEI-uBSkAn56xLMlec", "appName": "[DEFAULT]", "createdAt": "1731681044939", "displayName": undefined, "email": "<EMAIL>", "emailVerified": false, "isAnonymous": false, "lastLoginAt": "1749293433275", "phoneNumber": undefined, "photoURL": undefined, "providerData": [Array], "stsTokenManager": [Object], "tenantId": undefined, "uid": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2"}, "hasAuth": true, "hasOnAuthStateChanged": true}
 LOG  [OfferDetail][MOUNT] {"cleanupStarted": {"isCleaningUp": false, "isMounted": true}, "lastAction": "MOUNT", "mountCount": 0, "params": {"offerId": "Np9YCjiwfJf7xT6wzXBK", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingId": "6cxr3VsbMA4JefozqPVX", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "timestamp": "2025-06-07T10:55:51.097Z"}
 LOG  [Messages Subscription] Setting up subscription for offerId: Np9YCjiwfJf7xT6wzXBK
 LOG  === OfferDetail Screen Mounted ===
 LOG  Route params: {"offerId": "Np9YCjiwfJf7xT6wzXBK", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingId": "6cxr3VsbMA4JefozqPVX", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  Setting owner IDs from route params: {"offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [OfferDetail][MOUNT] {"cleanupStarted": {"isCleaningUp": false, "isMounted": true}, "lastAction": "MOUNT", "mountCount": 1, "params": {"offerId": "Np9YCjiwfJf7xT6wzXBK", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingId": "6cxr3VsbMA4JefozqPVX", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "timestamp": "2025-06-07T10:55:51.098Z"}
 LOG  Authenticated user: ybEyGDxbvleCEXdTvrA2CgVTJ1y2
 LOG  [OfferDetail] Initializing with params: {"hasInitialOffer": false, "hasInitialPosting": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.099Z"}
 LOG  [OfferDetail] Setting postingOwnerId from route params: {"postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [OfferDetail] Setting offerOwnerId from route params: {"offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2"}
 LOG  OfferDetail refresh button states: {"showOfferRefresh": false, "showOfferRefreshExplicit": false, "showPostingRefresh": false, "showPostingRefreshExplicit": false, "timestamp": "2025-06-07T10:55:51.100Z"}
 LOG  [OfferDetail][NOTIFICATION_LISTENER_SETUP] {"offerId": "Np9YCjiwfJf7xT6wzXBK", "postingId": undefined, "timestamp": "2025-06-07T10:55:51.101Z"}
 LOG  [OfferDetail][NOTIFICATION_DB_LISTENER_SETUP] {"offerId": "Np9YCjiwfJf7xT6wzXBK", "postingId": undefined, "timestamp": "2025-06-07T10:55:51.101Z", "userId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2"}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": false, "isLoading": true, "postingDescription": "", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "", "timestamp": "2025-06-07T10:55:51.103Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T10:55:51.104Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": true, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "0", "offerStatus": "active", "restrictionEndDate": null, "userScore": undefined, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T10:55:51.107Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.116Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.117Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.117Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": true, "timestamp": "2025-06-07T10:55:51.117Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.117Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.118Z"}
 LOG  [useOfferDetails][FETCHING_DATA] {"fetchCount": 1, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.119Z"}
 LOG  [useOfferDetails][FETCH_OFFER_START] {"forceRefresh": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.119Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.119Z"}
 LOG  [useAuthUser] Auth state changed {"user": {"_redirectEventId": undefined, "apiKey": "AIzaSyBOHewmZjlRWGu1jeEI-uBSkAn56xLMlec", "appName": "[DEFAULT]", "createdAt": "1731681044939", "displayName": undefined, "email": "<EMAIL>", "emailVerified": false, "isAnonymous": false, "lastLoginAt": "1749293433275", "phoneNumber": undefined, "photoURL": undefined, "providerData": [Array], "stsTokenManager": [Object], "tenantId": undefined, "uid": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2"}}
 LOG  [OfferDetail][NOTIFICATION_DB_UPDATE] {"count": 0, "timestamp": "2025-06-07T10:55:51.150Z"}
 LOG  [Navigation] Rendering headerLeft for: Notifications
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Rendering headerLeft for: OfferDetail
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Rendering headerLeft for: OfferDetail
 LOG  [Navigation] Can go back status: true
 LOG  Region changing: {"latitude": 37.8106059200526, "latitudeDelta": 0.019766430630447474, "longitude": -122.41975935267538, "longitudeDelta": 0.01609314918366067}
 LOG  [Messages Subscription] Received snapshot update: {"empty": false, "metadata": {"fromCache": false, "hasPendingWrites": false}, "size": 1}
 LOG  [Messages Subscription] Processing discussion: {"id": "6WOMdhbzTwhfNc3fHazf", "messageCount": 1}
 LOG  [Messages Subscription] Raw messages: {"count": 1, "messageKeys": ["text", "type", "senderId", "read", "timestamp"], "sampleMessage": {"read": true, "senderId": "system", "text": "This offer has been automatically withdrawn because the posting was deleted.", "timestamp": {"nanoseconds": 0, "seconds": 1749293649}, "type": "system"}}
 LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["text", "type", "senderId", "read", "timestamp"], "timestampType": "object"}
 LOG  [Messages Subscription] Validation summary: {"firstValidMessage": {"id": undefined, "senderId": "system", "timestamp": {"nanoseconds": 0, "seconds": 1749293649}}, "invalidMessages": 0, "totalMessages": 1, "validMessages": 1}
 LOG  [Messages Subscription] Processing messages: {"hasLegacyMessages": true, "messageTypes": [{"hasId": false, "hasTimestamp": true, "timestampType": "object"}], "newCount": 1, "prevCount": 0}
 LOG  [Messages Subscription] Updated messages state: {"firstMessage": {"id": undefined, "senderId": "system", "text": "This offer has been automatically withdrawn because the posting was deleted.", "timestamp": {"nanoseconds": 0, "seconds": 1749293649}}, "hasLegacyMessages": true, "lastMessage": {"id": undefined, "senderId": "system", "text": "This offer has been automatically withdrawn because the posting was deleted.", "timestamp": {"nanoseconds": 0, "seconds": 1749293649}}, "newCount": 1, "previousCount": 0}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": false, "isLoading": true, "postingDescription": "", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "", "timestamp": "2025-06-07T10:55:51.465Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T10:55:51.466Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": true, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "0", "offerStatus": "active", "restrictionEndDate": null, "userScore": undefined, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T10:55:51.475Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.491Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.491Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.492Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": true, "timestamp": "2025-06-07T10:55:51.492Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.492Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.492Z"}
 LOG  [useOfferDetails][FETCHING_DATA] {"fetchCount": 1, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.493Z"}
 LOG  [useOfferDetails][FETCH_OFFER_START] {"forceRefresh": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.493Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.493Z"}
 LOG  Marking messages as read for authorized user: {"isOfferOwner": true, "isPostingOwner": false, "userId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2"}
 LOG  Initial load, scrolling to end: 1
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": false, "isLoading": true, "postingDescription": "", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "", "timestamp": "2025-06-07T10:55:51.500Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T10:55:51.500Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": true, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "0", "offerStatus": "active", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T10:55:51.504Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.516Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.517Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.517Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": true, "timestamp": "2025-06-07T10:55:51.518Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.519Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.519Z"}
 LOG  [useOfferDetails][FETCHING_DATA] {"fetchCount": 1, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.519Z"}
 LOG  [useOfferDetails][FETCH_OFFER_START] {"forceRefresh": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.519Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.520Z"}
 LOG  FlatList layout complete
 LOG  Skipping load more: {"hasLastDoc": false, "hasMoreMessages": true, "lastDocId": undefined, "loadingMore": false}
 LOG  Debounced region changed: {"latitude": 37.8106059200526, "latitudeDelta": 0.019766430630447474, "longitude": -122.41975935267538, "longitudeDelta": 0.01609314918366067}
 LOG  Fetching postings for new region
 LOG  [useControlledPostings] Fetching postings for region: {"latitude": 37.8106059200526, "latitudeDelta": 0.019766430630447474, "longitude": -122.41975935267538, "longitudeDelta": 0.01609314918366067}
 LOG  [OfferCache][SET] {"offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.551Z"}
 LOG  [useOfferDetails][FETCH_OFFER_SUCCESS] {"offerId": "Np9YCjiwfJf7xT6wzXBK", "postingId": "6cxr3VsbMA4JefozqPVX", "timestamp": "2025-06-07T10:55:51.551Z"}
 LOG  [OfferCache][SET] {"offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.551Z"}
 LOG  [useOfferDetails][FETCH_OFFER_SUCCESS] {"offerId": "Np9YCjiwfJf7xT6wzXBK", "postingId": "6cxr3VsbMA4JefozqPVX", "timestamp": "2025-06-07T10:55:51.552Z"}
 LOG  [OfferDetail][STATUS_TRACKING] {"hasInitialOffer": false, "hasOfferDetails": true, "initialOfferStatus": undefined, "offerId": "Np9YCjiwfJf7xT6wzXBK", "offerStatus": "withdrawn", "postingStatus": "Deleted", "timestamp": "2025-06-07T10:55:51.553Z"}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": false, "isLoading": true, "postingDescription": "test", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Deleted", "postingTitle": "kous test post ", "timestamp": "2025-06-07T10:55:51.554Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T10:55:51.555Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": true, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offer", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "1111", "offerStatus": "withdrawn", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T10:55:51.558Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.565Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.565Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.566Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [OfferDetail][NOTIFICATION_LISTENER_CLEANUP] {"offerId": "Np9YCjiwfJf7xT6wzXBK", "postingId": undefined, "timestamp": "2025-06-07T10:55:51.567Z"}
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": false, "timestamp": "2025-06-07T10:55:51.567Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.568Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.568Z"}
 LOG  [OfferCache][GET] {"age": 17, "found": true, "isExpired": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.569Z"}
 LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"cachedStatus": "withdrawn", "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.592Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.592Z"}
 LOG  [OfferDetail][NOTIFICATION_LISTENER_SETUP] {"offerId": "Np9YCjiwfJf7xT6wzXBK", "postingId": "6cxr3VsbMA4JefozqPVX", "timestamp": "2025-06-07T10:55:51.593Z"}
 LOG  [OfferDetail][NOTIFICATION_DB_LISTENER_SETUP] {"offerId": "Np9YCjiwfJf7xT6wzXBK", "postingId": "6cxr3VsbMA4JefozqPVX", "timestamp": "2025-06-07T10:55:51.593Z", "userId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2"}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": false, "isLoading": true, "postingDescription": "test", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Deleted", "postingTitle": "kous test post ", "timestamp": "2025-06-07T10:55:51.595Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T10:55:51.595Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": true, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offer", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "1111", "offerStatus": "withdrawn", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T10:55:51.599Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.606Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.607Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.608Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": false, "timestamp": "2025-06-07T10:55:51.608Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.608Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.608Z"}
 LOG  [OfferCache][GET] {"age": 57, "found": true, "isExpired": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.609Z"}
 LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"cachedStatus": "withdrawn", "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.609Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.610Z"}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": false, "isLoading": false, "postingDescription": "test", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Deleted", "postingTitle": "kous test post ", "timestamp": "2025-06-07T10:55:51.612Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T10:55:51.613Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offer", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "1111", "offerStatus": "withdrawn", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T10:55:51.616Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.623Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.623Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.623Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": false, "timestamp": "2025-06-07T10:55:51.623Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.624Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.624Z"}
 LOG  [OfferCache][GET] {"age": 72, "found": true, "isExpired": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.624Z"}
 LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"cachedStatus": "withdrawn", "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.624Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.624Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": "hotmails offer", "hookPrice": 1111, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offer", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "1111", "offerStatus": "withdrawn", "restrictionEndDate": null, "userScore": 100, "usingHookData": true}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T10:55:51.627Z"}
 LOG  [useOfferDetails][SETTING_UP_LISTENER] {"offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.630Z"}
 LOG  [OfferDetailsSection][OFFER_UPDATED] {"description": "hotmails offer", "offerId": "Np9YCjiwfJf7xT6wzXBK", "price": 1111, "timestamp": "2025-06-07T10:55:51.630Z"}
 LOG  [OfferCache][SET] {"offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.631Z"}
 LOG  [useOfferDetails][FETCH_OFFER_SUCCESS] {"offerId": "Np9YCjiwfJf7xT6wzXBK", "postingId": "6cxr3VsbMA4JefozqPVX", "timestamp": "2025-06-07T10:55:51.632Z"}
 LOG  [OfferDetail][NOTIFICATION_DB_UPDATE] {"count": 0, "timestamp": "2025-06-07T10:55:51.634Z"}
 LOG  FlatList layout complete
 LOG  Skipping load more: {"hasLastDoc": false, "hasMoreMessages": true, "lastDocId": undefined, "loadingMore": false}
 LOG  [Navigation] Rendering headerLeft for: Notifications
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Rendering headerLeft for: OfferDetail
 LOG  [Navigation] Can go back status: true
 LOG  [useOfferDetails][REAL_TIME_UPDATE] {"offerId": "Np9YCjiwfJf7xT6wzXBK", "status": "withdrawn", "timestamp": "2025-06-07T10:55:51.681Z"}
 LOG  [OfferCache][SET] {"offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.681Z"}
 LOG  [OfferDetail][STATUS_TRACKING] {"hasInitialOffer": false, "hasOfferDetails": true, "initialOfferStatus": undefined, "offerId": "Np9YCjiwfJf7xT6wzXBK", "offerStatus": "withdrawn", "postingStatus": "Deleted", "timestamp": "2025-06-07T10:55:51.682Z"}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": false, "isLoading": false, "postingDescription": "test", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Deleted", "postingTitle": "kous test post ", "timestamp": "2025-06-07T10:55:51.683Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T10:55:51.683Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offer", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "1111", "offerStatus": "withdrawn", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T10:55:51.687Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.694Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.695Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.695Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": false, "timestamp": "2025-06-07T10:55:51.695Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.695Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.717Z"}
 LOG  [OfferCache][GET] {"age": 37, "found": true, "isExpired": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.718Z"}
 LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"cachedStatus": "withdrawn", "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.718Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.718Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": "hotmails offer", "hookPrice": 1111, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offer", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "1111", "offerStatus": "withdrawn", "restrictionEndDate": null, "userScore": 100, "usingHookData": true}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T10:55:51.721Z"}
 LOG  [OfferDetailsSection][OFFER_UPDATED] {"description": "hotmails offer", "offerId": "Np9YCjiwfJf7xT6wzXBK", "price": 1111, "timestamp": "2025-06-07T10:55:51.723Z"}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": false, "isLoading": false, "postingDescription": "test", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Deleted", "postingTitle": "kous test post ", "timestamp": "2025-06-07T10:55:51.727Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T10:55:51.727Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offer", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "1111", "offerStatus": "withdrawn", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T10:55:51.731Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.739Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.739Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.740Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": false, "timestamp": "2025-06-07T10:55:51.741Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.741Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.741Z"}
 LOG  [OfferCache][GET] {"age": 60, "found": true, "isExpired": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.741Z"}
 LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"cachedStatus": "withdrawn", "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.741Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.742Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": "hotmails offer", "hookPrice": 1111, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offer", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "1111", "offerStatus": "withdrawn", "restrictionEndDate": null, "userScore": 100, "usingHookData": true}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T10:55:51.743Z"}
 LOG  [OfferDetailsSection][OFFER_UPDATED] {"description": "hotmails offer", "offerId": "Np9YCjiwfJf7xT6wzXBK", "price": 1111, "timestamp": "2025-06-07T10:55:51.745Z"}
 LOG  [fetchPostingsBySearch] Query results: 2
 LOG  [useControlledPostings] Fetched postings: 2
 LOG  [OfferCache][SET] {"offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.900Z"}
 LOG  [useOfferDetails][FETCH_OFFER_SUCCESS] {"offerId": "Np9YCjiwfJf7xT6wzXBK", "postingId": "6cxr3VsbMA4JefozqPVX", "timestamp": "2025-06-07T10:55:51.900Z"}
 LOG  [OfferCache][SET] {"offerId": "Np9YCjiwfJf7xT6wzXBK", "timestamp": "2025-06-07T10:55:51.903Z"}
 LOG  [useOfferDetails][FETCH_OFFER_SUCCESS] {"offerId": "Np9YCjiwfJf7xT6wzXBK", "postingId": "6cxr3VsbMA4JefozqPVX", "timestamp": "2025-06-07T10:55:51.903Z"}
