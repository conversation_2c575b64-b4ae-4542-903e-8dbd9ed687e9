Setup Two Simulators:
Simulator 1: Posting owner 
Simulator 2: Offer owner 

Create Test Data:
On Simulator 1: posting owner clicks edit posting button and navigates to edit posting screen



 LOG  [PostingDetail][Navigation] Navigating to edit: {"postingDetails": {"createdAt": [Object], "description": "Searching for basic fishing equipment - rod, reel, and tackle box. Must be in good working condition. ", "id": "Z7cW3OyoEsojfIreWcAD", "lastUpdated": [Object], "latitude": 37.7917130364994, "longitude": -122.4467312534528, "postingStatus": "Active", "searchTerms": ["hedere", "hed", "hede", "heder", "ede", "eder", "edere", "der", "dere", "ere"], "title": "Fishing Gear", "titleLower": "hedere", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-08T11:12:45.050Z"}
 LOG  [Navigation] Configuring screen: LoginScreen
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: Home
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: PostingDetail
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: EditPosting
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Rendering headerLeft for: PostingDetail
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Rendering headerLeft for: EditPosting
 LOG  [Navigation] Can go back status: true
 LOG  [BackButton] Rendering back button
 LOG  [EditPostingScreen][Debug] Hook initialization: {"hookSetup": {"error": false, "hasPostingDetails": true, "hookImport": "function", "loading": false, "postingId": "Z7cW3OyoEsojfIreWcAD"}, "timestamp": "2025-06-08T11:12:45.109Z"}
 LOG  [EditPostingScreen] Extracted params: {"hasDesc": false, "hasLocation": false, "hasName": false, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-08T11:12:45.110Z"}
 LOG  [EditPostingScreen][Navigation] Screen mounted: {"incoming": {"flowMetrics": undefined, "navigationDuration": "0.000", "preciseTime": {"mount": 351236036.59925, "start": undefined}}, "screen": {"mountTime": "351236036.599", "postingId": "Z7cW3OyoEsojfIreWcAD"}, "timestamp": "2025-06-08T11:12:45.126Z"}
 LOG  [EditPostingScreen][Data] Load complete: {"postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-08T11:12:45.126Z", "timing": {"complete": "351236036.949", "duration": "0.006", "start": "351236036.942"}}
 LOG  [EditPostingScreen] Updating from posting details: {"hasDesc": true, "hasTitle": true, "timestamp": "2025-06-08T11:12:45.126Z"}
 LOG  [usePostingDetails][Focus] Screen unfocused, cleaning up
 LOG  [ListenerRegistry][Component] Unregistered listener from component: {"componentId": "PostingDetail_Z7cW3OyoEsojfIreWcAD", "listenerId": "postings_1749381153970_1749381153970", "timestamp": "2025-06-08T11:12:45.127Z"}
 LOG  [useOffers][Focus] Screen unfocused, cleaning up
 LOG  [ListenerRegistry][Component] Unregistered listener from component: {"componentId": "PostingDetail_Z7cW3OyoEsojfIreWcAD", "listenerId": "offers_1749381153971_1749381153971", "timestamp": "2025-06-08T11:12:45.127Z"}
 LOG  [EditPostingScreen][Debug] Hook initialization: {"hookSetup": {"error": false, "hasPostingDetails": true, "hookImport": "function", "loading": false, "postingId": "Z7cW3OyoEsojfIreWcAD"}, "timestamp": "2025-06-08T11:12:45.128Z"}
 LOG  [EditPostingScreen] Extracted params: {"hasDesc": false, "hasLocation": false, "hasName": false, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-08T11:12:45.129Z"}
 LOG  [ListenerRegistry] Cleanup metrics: {"groupId": null, "metrics": {"duration": 9, "startTime": 1749381165127, "success": true}, "subscriptionId": "postings_1749381153970_1749381153970", "timestamp": "2025-06-08T11:12:45.136Z"}
 LOG  [ListenerRegistry] Cleanup metrics: {"groupId": null, "metrics": {"duration": 11, "startTime": 1749381165127, "success": true}, "subscriptionId": "offers_1749381153971_1749381153971", "timestamp": "2025-06-08T11:12:45.138Z"}
 LOG  [Navigation] Rendering headerLeft for: PostingDetail
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Rendering headerLeft for: EditPosting
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Rendering headerLeft for: EditPosting
 LOG  [Navigation] Can go back status: true
 LOG  Region changing: {"latitude": 37.78806124389372, "latitudeDelta": 0.04998080323447596, "longitude": -122.43961376487296, "longitudeDelta": 0.03601157440407121}
 LOG  Debounced region changed: {"latitude": 37.78806124389372, "latitudeDelta": 0.04998080323447596, "longitude": -122.43961376487296, "longitudeDelta": 0.03601157440407121}
 LOG  Fetching postings for new region
 LOG  [useControlledPostings] Fetching postings for region: {"latitude": 37.78806124389372, "latitudeDelta": 0.04998080323447596, "longitude": -122.43961376487296, "longitudeDelta": 0.03601157440407121}
 LOG  [fetchPostingsBySearch] Query results: 10
 LOG  [useControlledPostings] Fetched postings: 10
 LOG  [Navigation] Rendering headerLeft for: PostingDetail
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Rendering headerLeft for: EditPosting
 LOG  [Navigation] Can go back status: true


On Simulator 1: posting owner modifies the posting and saves changes and navigates to posting detail screen


 LOG  [EditPostingScreen][Save] Starting update: {"postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-08T11:13:55.632Z", "updates": {"description": "11Searching for basic fishing equipment - rod, reel, and tackle box. Must be in good working condition. ", "latitude": 37.7917130364994, "longitude": -122.4467312534528, "title": "Fishing Gear11"}}
 LOG  [updatePostingDetails] Starting update: {"postingId": "Z7cW3OyoEsojfIreWcAD", "updatedData": {"description": "11Searching for basic fishing equipment - rod, reel, and tackle box. Must be in good working condition. ", "latitude": 37.7917130364994, "longitude": -122.4467312534528, "title": "Fishing Gear11"}}
 LOG  [EditPostingScreen][Debug] Hook initialization: {"hookSetup": {"error": false, "hasPostingDetails": true, "hookImport": "function", "loading": false, "postingId": "Z7cW3OyoEsojfIreWcAD"}, "timestamp": "2025-06-08T11:13:55.637Z"}
 LOG  [EditPostingScreen] Extracted params: {"hasDesc": false, "hasLocation": false, "hasName": false, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-08T11:13:55.638Z"}
 LOG  [updatePostingDetails] Detected title change: {"from": "Fishing Gear", "to": "Fishing Gear11"}
 LOG  [updatePostingDetails] Detected description change
 LOG  [updatePostingDetails] Posting updated successfully
 LOG  [updatePostingDetails] Preparing notifications for changes: ["title", "description"]
 LOG  [updatePostingDetails] Found 1 users with favorite
 LOG  [updatePostingDetails] Creating notification for user ybEyGDxbvleCEXdTvrA2CgVTJ1y2
 LOG  [updatePostingDetails] Notification payload: {"body": "\"Fishing Gear\" has been updated (title and description)", "createdAt": {"_methodName": "serverTimestamp"}, "postingId": "Z7cW3OyoEsojfIreWcAD", "read": false, "recipientId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "senderId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "title": "Posting Updated", "type": "FAVORITE_POSTING_UPDATE"}
 LOG  [updatePostingDetails] Sending all notifications
 LOG  [OfferDetail][NOTIFICATION_DB_UPDATE] {"count": 1, "timestamp": "2025-06-08T11:13:56.336Z"}
 LOG  [OfferDetail][NOTIFICATION_DB_ADDED] {"notification": {"body": "\"Fishing Gear\" has been updated (title and description)", "createdAt": [Object], "postingId": "Z7cW3OyoEsojfIreWcAD", "read": false, "recipientId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "senderId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "title": "Posting Updated", "type": "FAVORITE_POSTING_UPDATE"}, "timestamp": "2025-06-08T11:13:56.337Z"}
 LOG  [updatePostingDetails] All notifications processed
 LOG  [EditPostingScreen][Save] Update successful: {"postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-08T11:13:56.417Z"}
 LOG  [Navigation] Configuring screen: LoginScreen
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: Home
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: PostingDetail
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Rendering headerLeft for: PostingDetail
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Rendering headerLeft for: EditPosting
 LOG  [Navigation] Can go back status: true
 LOG  [usePostingDetails][Focus] Screen focused, setting up subscription
 LOG  [usePostingDetails][Setup] Creating references
 LOG  [usePostingDetails][Setup] Pre-snapshot listener setup: {"postingId": "Z7cW3OyoEsojfIreWcAD", "ref": "postings/Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-08T11:13:56.473Z"}
 LOG  [ListenerStateManager] Starting transition for postings_1749381236473_1749381236473: inactive -> initializing
 LOG  [ListenerRegistry][postings] Registered: {"collection": "postings", "id": "1749381236473", "registryId": "postings_1749381236473_1749381236473", "state": "active", "timestamp": "2025-06-08T11:13:56.473Z"}
 LOG  [ListenerStateManager] Completing transition for postings_1749381236473_1749381236473
 LOG  [ListenerRegistry][Component] Registered listener with component: {"componentId": "PostingDetail_Z7cW3OyoEsojfIreWcAD", "listenerId": "postings_1749381236473_1749381236473", "timestamp": "2025-06-08T11:13:56.473Z"}
 LOG  [ListenerRegistry][postings] Created: {"componentId": "PostingDetail_Z7cW3OyoEsojfIreWcAD", "listenerId": "postings_1749381236473", "timestamp": "2025-06-08T11:13:56.474Z"}
 LOG  [useOffers][Focus] Screen focused, setting up subscription
 LOG  [useOffers][Subscribe] Setting up subscription {"postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-08T11:13:56.475Z"}
 LOG  [ListenerStateManager] Starting transition for offers_1749381236475_1749381236475: inactive -> initializing
 LOG  [ListenerRegistry][offers] Registered: {"collection": "offers", "id": "1749381236475", "registryId": "offers_1749381236475_1749381236475", "state": "active", "timestamp": "2025-06-08T11:13:56.475Z"}
 LOG  [ListenerStateManager] Completing transition for offers_1749381236475_1749381236475
 LOG  [ListenerRegistry][Component] Registered listener with component: {"componentId": "PostingDetail_Z7cW3OyoEsojfIreWcAD", "listenerId": "offers_1749381236475_1749381236475", "timestamp": "2025-06-08T11:13:56.475Z"}
 LOG  [ListenerRegistry][offers] Created: {"componentId": "PostingDetail_Z7cW3OyoEsojfIreWcAD", "listenerId": "offers_1749381236475", "timestamp": "2025-06-08T11:13:56.475Z"}
 LOG  [useFavorites][Focus] Screen focused, fetching favorite status
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-08T11:10:47.772Z"}, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-08T11:13:56.477Z"}
 LOG  [usePostingDetails][Reducer] Processing action: {"currentState": {"error": null, "loading": false, "postingDetails": {"createdAt": [Timestamp], "description": "Searching for basic fishing equipment - rod, reel, and tackle box. Must be in good working condition. ", "id": "Z7cW3OyoEsojfIreWcAD", "lastUpdated": [Timestamp], "latitude": 37.7917130364994, "longitude": -122.4467312534528, "postingStatus": "Active", "searchTerms": [Array], "title": "Fishing Gear", "titleLower": "hedere", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}}, "metrics": {"timeSinceStart": 188705, "timestamp": "2025-06-08T11:13:56.477Z", "updateCount": 5}, "type": "FETCH_START"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": true, "timestamp": "2025-06-08T11:13:56.478Z"}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_Z7cW3OyoEsojfIreWcAD_1749381154205", "offers_Z7cW3OyoEsojfIreWcAD_1749381154205"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749381154205"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749381154205"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-08T11:13:56.481Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 188709, "timestamp": "2025-06-08T11:13:56.481Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 188710, "timestamp": "2025-06-08T11:13:56.482Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749381236482", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749381236482", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_Z7cW3OyoEsojfIreWcAD_1749381236482", "postingSubId": "posting_Z7cW3OyoEsojfIreWcAD_1749381236482", "totalTime": 188710}
 LOG  [PostingDetail][StateSync] Checking state update conditions: {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "hasDetails": true, "offersCount": 2, "postingLoading": true, "timestamp": "2025-06-08T11:13:56.482Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [EditPostingScreen][Debug] Hook initialization: {"hookSetup": {"error": false, "hasPostingDetails": true, "hookImport": "function", "loading": false, "postingId": "Z7cW3OyoEsojfIreWcAD"}, "timestamp": "2025-06-08T11:13:56.483Z"}
 LOG  [EditPostingScreen] Extracted params: {"hasDesc": false, "hasLocation": false, "hasName": false, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-08T11:13:56.483Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-08T11:10:47.772Z"}, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-08T11:13:56.585Z"}
 LOG  [usePostingDetails][Reducer] Processing action: {"currentState": {"error": null, "loading": true, "postingDetails": {"createdAt": [Timestamp], "description": "Searching for basic fishing equipment - rod, reel, and tackle box. Must be in good working condition. ", "id": "Z7cW3OyoEsojfIreWcAD", "lastUpdated": [Timestamp], "latitude": 37.7917130364994, "longitude": -122.4467312534528, "postingStatus": "Active", "searchTerms": [Array], "title": "Fishing Gear", "titleLower": "hedere", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}}, "metrics": {"timeSinceStart": 188813, "timestamp": "2025-06-08T11:13:56.585Z", "updateCount": 6}, "type": "UPDATE_POSTING"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-08T11:13:56.586Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isFavorite": false, "isOwner": true, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-08T11:13:56.587Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_Z7cW3OyoEsojfIreWcAD_1749381236482", "offers_Z7cW3OyoEsojfIreWcAD_1749381236482"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749381236482"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749381236482"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-08T11:13:56.603Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 188831, "timestamp": "2025-06-08T11:13:56.603Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 188831, "timestamp": "2025-06-08T11:13:56.603Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749381236603", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749381236603", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_Z7cW3OyoEsojfIreWcAD_1749381236603", "postingSubId": "posting_Z7cW3OyoEsojfIreWcAD_1749381236603", "totalTime": 188832}
 LOG  [PostingDetail][StateSync] Checking state update conditions: {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "hasDetails": true, "offersCount": 2, "postingLoading": false, "timestamp": "2025-06-08T11:13:56.605Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Updating state with: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isOwnerCheck": true, "offersCount": 2, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-08T11:13:56.605Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Processing offers: {"count": 2, "timestamp": "2025-06-08T11:13:56.605Z", "userOffers": 0}
 LOG  [PostingDetail][StateSync] State updated: {"hasUserOffer": false, "isOwner": true, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-08T11:13:56.605Z"}
 LOG  [PostingDetail][StateVerification] {"auth": {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "expectedOwner": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "posting": {"id": "Z7cW3OyoEsojfIreWcAD", "loaded": true}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": false, "isOwner": true, "lastUpdate": 1749381154183, "offersCount": 2, "postingDetailsId": "Z7cW3OyoEsojfIreWcAD", "userOffer": null}, "timestamp": "2025-06-08T11:13:56.605Z"}
 LOG  [PostingDetail] Posting details loaded for edit: {"hasDetails": true, "hasLocation": false, "isOwner": true, "timestamp": "2025-06-08T11:13:56.606Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-08T11:10:47.772Z"}, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-08T11:13:56.606Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-08T11:13:56.607Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isFavorite": false, "isOwner": true, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-08T11:13:56.607Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_Z7cW3OyoEsojfIreWcAD_1749381236603", "offers_Z7cW3OyoEsojfIreWcAD_1749381236603"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749381236603"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749381236603"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-08T11:13:56.616Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 188844, "timestamp": "2025-06-08T11:13:56.616Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 188844, "timestamp": "2025-06-08T11:13:56.616Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749381236616", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749381236616", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_Z7cW3OyoEsojfIreWcAD_1749381236616", "postingSubId": "posting_Z7cW3OyoEsojfIreWcAD_1749381236616", "totalTime": 188844}
 LOG  [PostingDetail][StateVerification] {"auth": {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "expectedOwner": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "posting": {"id": "Z7cW3OyoEsojfIreWcAD", "loaded": true}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": false, "isOwner": true, "lastUpdate": 1749381236606, "offersCount": 2, "postingDetailsId": "Z7cW3OyoEsojfIreWcAD", "userOffer": null}, "timestamp": "2025-06-08T11:13:56.616Z"}
 LOG  [PostingDetail][StateDebug] {"auth": {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "performance": {"timeSinceLastUpdate": 11}, "props": {"postingId": "Z7cW3OyoEsojfIreWcAD", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": false, "isOwner": true, "lastUpdate": 1749381236606, "offersCount": 2, "postingDetailsId": "Z7cW3OyoEsojfIreWcAD", "userOffer": null}, "subscriptions": {"metrics": {"lastUpdate": "2025-06-08T11:10:47.772Z", "subscriptionCount": 0, "totalTime": 188845}, "types": []}, "timestamp": "2025-06-08T11:13:56.617Z"}
 LOG  [useOffers][Update] {"changes": [{"id": "IgyIrlkc18WdziXD3nEO", "timestamp": "2025-06-08T11:13:56.673Z", "type": "added"}, {"id": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-08T11:13:56.673Z", "type": "added"}], "count": 2, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-08T11:13:56.673Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-08T11:10:47.772Z"}, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-08T11:13:56.675Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-08T11:13:56.675Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isFavorite": false, "isOwner": true, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-08T11:13:56.675Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_Z7cW3OyoEsojfIreWcAD_1749381236616", "offers_Z7cW3OyoEsojfIreWcAD_1749381236616"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749381236616"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749381236616"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-08T11:13:56.685Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 188913, "timestamp": "2025-06-08T11:13:56.685Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 188913, "timestamp": "2025-06-08T11:13:56.685Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749381236685", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749381236685", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_Z7cW3OyoEsojfIreWcAD_1749381236685", "postingSubId": "posting_Z7cW3OyoEsojfIreWcAD_1749381236685", "totalTime": 188913}
 LOG  [PostingDetail][StateSync] Checking state update conditions: {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "hasDetails": true, "offersCount": 2, "postingLoading": false, "timestamp": "2025-06-08T11:13:56.685Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Updating state with: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isOwnerCheck": true, "offersCount": 2, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-08T11:13:56.686Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Processing offers: {"count": 2, "timestamp": "2025-06-08T11:13:56.686Z", "userOffers": 0}
 LOG  [PostingDetail][StateSync] State updated: {"hasUserOffer": false, "isOwner": true, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-08T11:13:56.686Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-08T11:10:47.772Z"}, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-08T11:13:56.687Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-08T11:13:56.687Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isFavorite": false, "isOwner": true, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-08T11:13:56.687Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_Z7cW3OyoEsojfIreWcAD_1749381236685", "offers_Z7cW3OyoEsojfIreWcAD_1749381236685"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749381236685"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749381236685"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-08T11:13:56.696Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 188924, "timestamp": "2025-06-08T11:13:56.696Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 188924, "timestamp": "2025-06-08T11:13:56.696Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749381236696", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749381236696", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_Z7cW3OyoEsojfIreWcAD_1749381236696", "postingSubId": "posting_Z7cW3OyoEsojfIreWcAD_1749381236696", "totalTime": 188925}
 LOG  [PostingDetail][StateVerification] {"auth": {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "expectedOwner": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "posting": {"id": "Z7cW3OyoEsojfIreWcAD", "loaded": true}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": false, "isOwner": true, "lastUpdate": 1749381236687, "offersCount": 2, "postingDetailsId": "Z7cW3OyoEsojfIreWcAD", "userOffer": null}, "timestamp": "2025-06-08T11:13:56.697Z"}
 LOG  [PostingDetail][StateDebug] {"auth": {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "performance": {"timeSinceLastUpdate": 10}, "props": {"postingId": "Z7cW3OyoEsojfIreWcAD", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": false, "isOwner": true, "lastUpdate": 1749381236687, "offersCount": 2, "postingDetailsId": "Z7cW3OyoEsojfIreWcAD", "userOffer": null}, "subscriptions": {"metrics": {"lastUpdate": "2025-06-08T11:10:47.772Z", "subscriptionCount": 0, "totalTime": 188925}, "types": []}, "timestamp": "2025-06-08T11:13:56.697Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-08T11:10:47.772Z"}, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-08T11:13:56.699Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-08T11:13:56.699Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isFavorite": false, "isOwner": true, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-08T11:13:56.700Z", "userOffer": null}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-08T11:10:47.772Z"}, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-08T11:13:56.702Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-08T11:13:56.702Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isFavorite": false, "isOwner": true, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-08T11:13:56.702Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_Z7cW3OyoEsojfIreWcAD_1749381236696", "offers_Z7cW3OyoEsojfIreWcAD_1749381236696"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749381236696"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749381236696"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-08T11:13:56.710Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 188938, "timestamp": "2025-06-08T11:13:56.710Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 188938, "timestamp": "2025-06-08T11:13:56.710Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749381236710", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749381236710", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_Z7cW3OyoEsojfIreWcAD_1749381236710", "postingSubId": "posting_Z7cW3OyoEsojfIreWcAD_1749381236710", "totalTime": 188939}
 LOG  [Navigation] Rendering headerLeft for: PostingDetail
 LOG  [Navigation] Can go back status: true
 LOG  [EditPostingScreen][Navigation] Screen cleanup: {"flowMetrics": {"dataLoadComplete": 351236036.948541, "dataLoadStart": 351236036.9425, "events": [[Object]], "flowMetrics": undefined, "navigationComplete": 351236019.555416, "totalDuration": "71927.511"}, "timestamp": "2025-06-08T11:13:57.035Z"}
 LOG  [Navigation] Rendering headerLeft for: PostingDetail
 LOG  [Navigation] Can go back status: true
 LOG  Region changing: {"latitude": 37.78806124398657, "latitudeDelta": 0.057485002791040074, "longitude": -122.43961376487296, "longitudeDelta": 0.03601157440407121}
 LOG  Debounced region changed: {"latitude": 37.78806124398657, "latitudeDelta": 0.057485002791040074, "longitude": -122.43961376487296, "longitudeDelta": 0.03601157440407121}
 LOG  Fetching postings for new region
 LOG  [useControlledPostings] Fetching postings for region: {"latitude": 37.78806124398657, "latitudeDelta": 0.057485002791040074, "longitude": -122.43961376487296, "longitudeDelta": 0.03601157440407121}
 LOG  [fetchPostingsBySearch] Query results: 10
 LOG  [useControlledPostings] Fetched postings: 10
 LOG  [OfferDetail][NOTIFICATION_DB_UPDATE] {"count": 1, "timestamp": "2025-06-08T11:13:57.752Z"}





On Simulator 2: modification of a posting should display a refresh button in posting detail container while the offer owner is viewing the offer detail screen but the refresh button was NOT displayed.

On Simulator 2: offer owner clicks the chevron down button in posting detail container to expand the posting details container and displays the posting title and posting description but Title is shown as "No title"  and description is shown as "No description"




 LOG  [PostingDetailsSection] Toggling details: {"current": false, "new": true, "timestamp": "2025-06-08T11:15:41.149Z"}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": true, "isLoading": false, "postingDescription": "", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "", "timestamp": "2025-06-08T11:15:41.163Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-08T11:15:41.165Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offers22555", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "224555", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-08T11:15:41.199Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-08T11:15:41.246Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-08T11:15:41.246Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-08T11:15:41.246Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": true, "timestamp": "2025-06-08T11:15:41.247Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-08T11:15:41.247Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-08T11:15:41.247Z"}
 LOG  [OfferCache][GET] {"age": 308840, "found": true, "isExpired": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-08T11:15:41.248Z"}
 LOG  [useOfferDetails][FETCHING_DATA] {"fetchCount": 1, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-08T11:15:41.248Z"}
 LOG  [OfferCache][GET] {"age": 308840, "found": true, "isExpired": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-08T11:15:41.248Z"}
 LOG  [useOfferDetails][FETCH_OFFER_START] {"forceRefresh": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-08T11:15:41.248Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-08T11:15:41.248Z"}
 LOG  FlatList layout complete
 LOG  Skipping load more: {"hasLastDoc": false, "hasMoreMessages": true, "lastDocId": undefined, "loadingMore": false}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": true, "isLoading": false, "postingDescription": "", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "", "timestamp": "2025-06-08T11:15:41.268Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-08T11:15:41.269Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offers22555", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "224555", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-08T11:15:41.282Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-08T11:15:41.312Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-08T11:15:41.312Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-08T11:15:41.312Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": true, "timestamp": "2025-06-08T11:15:41.313Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-08T11:15:41.313Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-08T11:15:41.314Z"}
 LOG  [OfferCache][GET] {"age": 308906, "found": true, "isExpired": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-08T11:15:41.314Z"}
 LOG  [useOfferDetails][FETCHING_DATA] {"fetchCount": 1, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-08T11:15:41.314Z"}
 LOG  [OfferCache][GET] {"age": 308906, "found": true, "isExpired": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-08T11:15:41.314Z"}
 LOG  [useOfferDetails][FETCH_OFFER_START] {"forceRefresh": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-08T11:15:41.314Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-08T11:15:41.314Z"}
 LOG  [OfferCache][SET] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-08T11:15:41.501Z"}
 LOG  [useOfferDetails][FETCH_OFFER_SUCCESS] {"offerId": "tK7kgW3yDfZoF3IWeedl", "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-08T11:15:41.501Z"}
 LOG  [OfferCache][SET] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-08T11:15:41.503Z"}
 LOG  [useOfferDetails][FETCH_OFFER_SUCCESS] {"offerId": "tK7kgW3yDfZoF3IWeedl", "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-08T11:15:41.503Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": "hotmails offers22555", "hookPrice": 224555, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offers22555", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "224555", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": true}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-08T11:15:41.507Z"}
 LOG  [OfferDetailsSection][OFFER_UPDATED] {"description": "hotmails offers22555", "offerId": "tK7kgW3yDfZoF3IWeedl", "price": 224555, "timestamp": "2025-06-08T11:15:41.515Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": "hotmails offers22555", "hookPrice": 224555, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offers22555", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "224555", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": true}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-08T11:15:41.518Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": "hotmails offers22555", "hookPrice": 224555, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offers22555", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "224555", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": true}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-08T11:15:41.524Z"}