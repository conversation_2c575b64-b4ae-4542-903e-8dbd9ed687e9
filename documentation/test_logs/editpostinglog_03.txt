Setup Two Simulators:
Simulator 1: Posting owner 
Simulator 2: Offer owner 

Create Test Data:


On Simulator 1: posting owner modifies the posting and saves changes and navigates to posting detail screen




 LOG  [EditPostingScreen][Save] Starting update: {"postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:56:55.211Z", "updates": {"description": "11111Searching for basic fishing equipment - rod, reel, and tackle box. Must be in good working condition. ", "latitude": 37.7917130364994, "longitude": -122.4467312534528, "title": "Fishing Gear11111"}}
 LOG  [updatePostingDetails] Starting update: {"postingId": "Z7cW3OyoEsojfIreWcAD", "updatedData": {"description": "11111Searching for basic fishing equipment - rod, reel, and tackle box. Must be in good working condition. ", "latitude": 37.7917130364994, "longitude": -122.4467312534528, "title": "Fishing Gear11111"}}
 LOG  [EditPostingScreen][Debug] Hook initialization: {"hookSetup": {"error": false, "hasPostingDetails": true, "hookImport": "function", "loading": false, "postingId": "Z7cW3OyoEsojfIreWcAD"}, "timestamp": "2025-06-09T10:56:55.217Z"}
 LOG  [EditPostingScreen] Extracted params: {"hasDesc": false, "hasLocation": false, "hasName": false, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:56:55.219Z"}
 LOG  [updatePostingDetails] Detected title change: {"from": "Fishing Gear", "to": "Fishing Gear11111"}
 LOG  [updatePostingDetails] Detected description change
 LOG  [updatePostingDetails] Posting updated successfully
 LOG  [updatePostingDetails] Preparing notifications for changes: ["title", "description"]
 LOG  [updatePostingDetails] Found 1 users with favorite
 LOG  [updatePostingDetails] Creating notification for user ybEyGDxbvleCEXdTvrA2CgVTJ1y2
 LOG  [updatePostingDetails] Notification payload: {"body": "\"Fishing Gear\" has been updated (title and description)", "createdAt": {"_methodName": "serverTimestamp"}, "postingId": "Z7cW3OyoEsojfIreWcAD", "read": false, "recipientId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "senderId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "title": "Posting Updated", "type": "FAVORITE_POSTING_UPDATE"}
 LOG  [updatePostingDetails] Sending all notifications
 LOG  [OfferDetail][NOTIFICATION_DB_UPDATE] {"count": 1, "timestamp": "2025-06-09T10:56:55.904Z"}
 LOG  [OfferDetail][NOTIFICATION_DB_ADDED] {"notification": {"body": "\"Fishing Gear\" has been updated (title and description)", "createdAt": [Object], "postingId": "Z7cW3OyoEsojfIreWcAD", "read": false, "recipientId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "senderId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "title": "Posting Updated", "type": "FAVORITE_POSTING_UPDATE"}, "timestamp": "2025-06-09T10:56:55.904Z"}
 LOG  [OfferDetail][NOTIFICATION_MATCHING_DEBUG] {"current": {"finalPostingId": "Z7cW3OyoEsojfIreWcAD", "offerId": "tK7kgW3yDfZoF3IWeedl", "postingId": "Z7cW3OyoEsojfIreWcAD", "reliablePostingId": "Z7cW3OyoEsojfIreWcAD"}, "matches": {"finalPostingIdMatch": true, "offerIdMatch": false, "postingIdMatch": true}, "notification": {"offerId": undefined, "postingId": "Z7cW3OyoEsojfIreWcAD", "type": "FAVORITE_POSTING_UPDATE"}, "timestamp": "2025-06-09T10:56:55.905Z"}
 LOG  [OfferDetail][NOTIFICATION_DB_RELEVANT] {"matchedBy": "postingId", "offerId": undefined, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:56:55.906Z", "type": "FAVORITE_POSTING_UPDATE"}
 LOG  [OfferDetail][SHOWING_POSTING_REFRESH] {"timestamp": "2025-06-09T10:56:55.906Z"}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": false, "isLoading": false, "postingDescription": "Searching for basic fishing equipment - rod, reel, and tackle box. Must be in good working condition. ", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "Fishing Gear", "timestamp": "2025-06-09T10:56:55.909Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": true, "showRefresh": true, "timestamp": "2025-06-09T10:56:55.911Z"}
 LOG  [PostingDetailsSection][RENDER_REFRESH_BUTTON] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "showRefresh": true, "timestamp": "2025-06-09T10:56:55.914Z", "visible": true}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offers22555", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "224555", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T10:56:55.925Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:56:55.970Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:56:55.971Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:56:55.971Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": true, "timestamp": "2025-06-09T10:56:55.972Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:56:55.972Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:56:55.972Z"}
 LOG  [OfferCache][GET] {"age": 56878, "found": true, "isExpired": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:56:55.972Z"}
 LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"cachedStatus": "pending", "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:56:55.974Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:56:55.974Z"}
 LOG  OfferDetail refresh button states: {"showOfferRefresh": false, "showOfferRefreshExplicit": false, "showPostingRefresh": true, "showPostingRefreshExplicit": true, "timestamp": "2025-06-09T10:56:55.975Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": "hotmails offers22555", "hookPrice": 224555, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offers22555", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "224555", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": true}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T10:56:55.977Z"}
 LOG  [OfferDetailsSection][OFFER_UPDATED] {"description": "hotmails offers22555", "offerId": "tK7kgW3yDfZoF3IWeedl", "price": 224555, "timestamp": "2025-06-09T10:56:55.980Z"}
 LOG  [updatePostingDetails] All notifications processed
 LOG  [EditPostingScreen][Save] Update successful: {"postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:56:55.978Z"}
 LOG  [Navigation] Configuring screen: LoginScreen
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: Home
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: PostingDetail
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Rendering headerLeft for: PostingDetail
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Rendering headerLeft for: EditPosting
 LOG  [Navigation] Can go back status: true
 LOG  [usePostingDetails][Focus] Screen focused, setting up subscription
 LOG  [usePostingDetails][Setup] Creating references
 LOG  [usePostingDetails][Setup] Pre-snapshot listener setup: {"postingId": "Z7cW3OyoEsojfIreWcAD", "ref": "postings/Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:56:56.016Z"}
 LOG  [ListenerStateManager] Starting transition for postings_1749466616017_1749466616017: inactive -> initializing
 LOG  [ListenerRegistry][postings] Registered: {"collection": "postings", "id": "1749466616017", "registryId": "postings_1749466616017_1749466616017", "state": "active", "timestamp": "2025-06-09T10:56:56.017Z"}
 LOG  [ListenerStateManager] Completing transition for postings_1749466616017_1749466616017
 LOG  [ListenerRegistry][Component] Registered listener with component: {"componentId": "PostingDetail_Z7cW3OyoEsojfIreWcAD", "listenerId": "postings_1749466616017_1749466616017", "timestamp": "2025-06-09T10:56:56.018Z"}
 LOG  [ListenerRegistry][postings] Created: {"componentId": "PostingDetail_Z7cW3OyoEsojfIreWcAD", "listenerId": "postings_1749466616017", "timestamp": "2025-06-09T10:56:56.018Z"}
 LOG  [useOffers][Focus] Screen focused, setting up subscription
 LOG  [useOffers][Subscribe] Setting up subscription {"postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:56:56.018Z"}
 LOG  [ListenerStateManager] Starting transition for offers_1749466616018_1749466616018: inactive -> initializing
 LOG  [ListenerRegistry][offers] Registered: {"collection": "offers", "id": "1749466616018", "registryId": "offers_1749466616018_1749466616018", "state": "active", "timestamp": "2025-06-09T10:56:56.018Z"}
 LOG  [ListenerStateManager] Completing transition for offers_1749466616018_1749466616018
 LOG  [ListenerRegistry][Component] Registered listener with component: {"componentId": "PostingDetail_Z7cW3OyoEsojfIreWcAD", "listenerId": "offers_1749466616018_1749466616018", "timestamp": "2025-06-09T10:56:56.019Z"}
 LOG  [ListenerRegistry][offers] Created: {"componentId": "PostingDetail_Z7cW3OyoEsojfIreWcAD", "listenerId": "offers_1749466616018", "timestamp": "2025-06-09T10:56:56.019Z"}
 LOG  [useFavorites][Focus] Screen focused, fetching favorite status
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-09T10:51:13.862Z"}, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:56:56.021Z"}
 LOG  [usePostingDetails][Reducer] Processing action: {"currentState": {"error": null, "loading": false, "postingDetails": {"createdAt": [Timestamp], "description": "Searching for basic fishing equipment - rod, reel, and tackle box. Must be in good working condition. ", "id": "Z7cW3OyoEsojfIreWcAD", "lastUpdated": [Timestamp], "latitude": 37.7917130364994, "longitude": -122.4467312534528, "postingStatus": "Active", "searchTerms": [Array], "title": "Fishing Gear", "titleLower": "hedere", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}}, "metrics": {"timeSinceStart": 342159, "timestamp": "2025-06-09T10:56:56.021Z", "updateCount": 9}, "type": "FETCH_START"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": true, "timestamp": "2025-06-09T10:56:56.022Z"}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_Z7cW3OyoEsojfIreWcAD_1749466572172", "offers_Z7cW3OyoEsojfIreWcAD_1749466572172"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749466572172"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749466572172"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-09T10:56:56.027Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 342166, "timestamp": "2025-06-09T10:56:56.028Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 342166, "timestamp": "2025-06-09T10:56:56.028Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749466616028", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749466616028", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_Z7cW3OyoEsojfIreWcAD_1749466616028", "postingSubId": "posting_Z7cW3OyoEsojfIreWcAD_1749466616028", "totalTime": 342166}
 LOG  [PostingDetail][StateSync] Checking state update conditions: {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "hasDetails": true, "offersCount": 2, "postingLoading": true, "timestamp": "2025-06-09T10:56:56.028Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [EditPostingScreen][Debug] Hook initialization: {"hookSetup": {"error": false, "hasPostingDetails": true, "hookImport": "function", "loading": false, "postingId": "Z7cW3OyoEsojfIreWcAD"}, "timestamp": "2025-06-09T10:56:56.029Z"}
 LOG  [EditPostingScreen] Extracted params: {"hasDesc": false, "hasLocation": false, "hasName": false, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:56:56.030Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-09T10:51:13.862Z"}, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:56:56.130Z"}
 LOG  [usePostingDetails][Reducer] Processing action: {"currentState": {"error": null, "loading": true, "postingDetails": {"createdAt": [Timestamp], "description": "Searching for basic fishing equipment - rod, reel, and tackle box. Must be in good working condition. ", "id": "Z7cW3OyoEsojfIreWcAD", "lastUpdated": [Timestamp], "latitude": 37.7917130364994, "longitude": -122.4467312534528, "postingStatus": "Active", "searchTerms": [Array], "title": "Fishing Gear", "titleLower": "hedere", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}}, "metrics": {"timeSinceStart": 342269, "timestamp": "2025-06-09T10:56:56.131Z", "updateCount": 10}, "type": "UPDATE_POSTING"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-09T10:56:56.133Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isFavorite": false, "isOwner": true, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:56:56.133Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_Z7cW3OyoEsojfIreWcAD_1749466616028", "offers_Z7cW3OyoEsojfIreWcAD_1749466616028"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749466616028"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749466616028"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-09T10:56:56.148Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 342286, "timestamp": "2025-06-09T10:56:56.148Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 342286, "timestamp": "2025-06-09T10:56:56.148Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749466616148", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749466616148", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_Z7cW3OyoEsojfIreWcAD_1749466616148", "postingSubId": "posting_Z7cW3OyoEsojfIreWcAD_1749466616148", "totalTime": 342286}
 LOG  [PostingDetail][StateSync] Checking state update conditions: {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "hasDetails": true, "offersCount": 2, "postingLoading": false, "timestamp": "2025-06-09T10:56:56.148Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Updating state with: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isOwnerCheck": true, "offersCount": 2, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:56:56.149Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Processing offers: {"count": 2, "timestamp": "2025-06-09T10:56:56.149Z", "userOffers": 0}
 LOG  [PostingDetail][StateSync] State updated: {"hasUserOffer": false, "isOwner": true, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:56:56.151Z"}
 LOG  [PostingDetail][StateVerification] {"auth": {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "expectedOwner": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "posting": {"id": "Z7cW3OyoEsojfIreWcAD", "loaded": true}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": true, "isOwner": true, "lastUpdate": 1749466572149, "offersCount": 2, "postingDetailsId": "Z7cW3OyoEsojfIreWcAD", "userOffer": null}, "timestamp": "2025-06-09T10:56:56.151Z"}
 LOG  [PostingDetail] Posting details loaded for edit: {"hasDetails": true, "hasLocation": true, "isOwner": true, "timestamp": "2025-06-09T10:56:56.152Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-09T10:51:13.862Z"}, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:56:56.152Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-09T10:56:56.153Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isFavorite": false, "isOwner": true, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:56:56.153Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_Z7cW3OyoEsojfIreWcAD_1749466616148", "offers_Z7cW3OyoEsojfIreWcAD_1749466616148"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749466616148"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749466616148"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-09T10:56:56.160Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 342298, "timestamp": "2025-06-09T10:56:56.160Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 342298, "timestamp": "2025-06-09T10:56:56.160Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749466616160", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749466616160", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_Z7cW3OyoEsojfIreWcAD_1749466616160", "postingSubId": "posting_Z7cW3OyoEsojfIreWcAD_1749466616160", "totalTime": 342299}
 LOG  [PostingDetail][StateVerification] {"auth": {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "expectedOwner": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "posting": {"id": "Z7cW3OyoEsojfIreWcAD", "loaded": true}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": true, "isOwner": true, "lastUpdate": 1749466616152, "offersCount": 2, "postingDetailsId": "Z7cW3OyoEsojfIreWcAD", "userOffer": null}, "timestamp": "2025-06-09T10:56:56.161Z"}
 LOG  [PostingDetail][StateDebug] {"auth": {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "performance": {"timeSinceLastUpdate": 9}, "props": {"postingId": "Z7cW3OyoEsojfIreWcAD", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": true, "isOwner": true, "lastUpdate": 1749466616152, "offersCount": 2, "postingDetailsId": "Z7cW3OyoEsojfIreWcAD", "userOffer": null}, "subscriptions": {"metrics": {"lastUpdate": "2025-06-09T10:51:13.862Z", "subscriptionCount": 0, "totalTime": 342299}, "types": []}, "timestamp": "2025-06-09T10:56:56.161Z"}
 LOG  [useOffers][Update] {"changes": [{"id": "IgyIrlkc18WdziXD3nEO", "timestamp": "2025-06-09T10:56:56.221Z", "type": "added"}, {"id": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:56:56.221Z", "type": "added"}], "count": 2, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:56:56.221Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-09T10:51:13.862Z"}, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:56:56.222Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-09T10:56:56.222Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isFavorite": false, "isOwner": true, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:56:56.223Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_Z7cW3OyoEsojfIreWcAD_1749466616160", "offers_Z7cW3OyoEsojfIreWcAD_1749466616160"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749466616160"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749466616160"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-09T10:56:56.230Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 342368, "timestamp": "2025-06-09T10:56:56.230Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 342369, "timestamp": "2025-06-09T10:56:56.231Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749466616231", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749466616231", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_Z7cW3OyoEsojfIreWcAD_1749466616231", "postingSubId": "posting_Z7cW3OyoEsojfIreWcAD_1749466616231", "totalTime": 342369}
 LOG  [PostingDetail][StateSync] Checking state update conditions: {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "hasDetails": true, "offersCount": 2, "postingLoading": false, "timestamp": "2025-06-09T10:56:56.231Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Updating state with: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isOwnerCheck": true, "offersCount": 2, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:56:56.231Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Processing offers: {"count": 2, "timestamp": "2025-06-09T10:56:56.231Z", "userOffers": 0}
 LOG  [PostingDetail][StateSync] State updated: {"hasUserOffer": false, "isOwner": true, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:56:56.232Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-09T10:51:13.862Z"}, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:56:56.233Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-09T10:56:56.233Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isFavorite": false, "isOwner": true, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:56:56.233Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_Z7cW3OyoEsojfIreWcAD_1749466616231", "offers_Z7cW3OyoEsojfIreWcAD_1749466616231"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749466616231"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749466616231"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-09T10:56:56.241Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 342380, "timestamp": "2025-06-09T10:56:56.242Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 342380, "timestamp": "2025-06-09T10:56:56.242Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749466616242", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749466616242", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_Z7cW3OyoEsojfIreWcAD_1749466616242", "postingSubId": "posting_Z7cW3OyoEsojfIreWcAD_1749466616242", "totalTime": 342380}
 LOG  [PostingDetail][StateVerification] {"auth": {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "expectedOwner": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "posting": {"id": "Z7cW3OyoEsojfIreWcAD", "loaded": true}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": true, "isOwner": true, "lastUpdate": 1749466616233, "offersCount": 2, "postingDetailsId": "Z7cW3OyoEsojfIreWcAD", "userOffer": null}, "timestamp": "2025-06-09T10:56:56.243Z"}
 LOG  [PostingDetail][StateDebug] {"auth": {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "performance": {"timeSinceLastUpdate": 10}, "props": {"postingId": "Z7cW3OyoEsojfIreWcAD", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": true, "isOwner": true, "lastUpdate": 1749466616233, "offersCount": 2, "postingDetailsId": "Z7cW3OyoEsojfIreWcAD", "userOffer": null}, "subscriptions": {"metrics": {"lastUpdate": "2025-06-09T10:51:13.862Z", "subscriptionCount": 0, "totalTime": 342381}, "types": []}, "timestamp": "2025-06-09T10:56:56.243Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-09T10:51:13.862Z"}, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:56:56.245Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-09T10:56:56.245Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isFavorite": false, "isOwner": true, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:56:56.246Z", "userOffer": null}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-09T10:51:13.862Z"}, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:56:56.248Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-09T10:56:56.248Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isFavorite": false, "isOwner": true, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:56:56.248Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_Z7cW3OyoEsojfIreWcAD_1749466616242", "offers_Z7cW3OyoEsojfIreWcAD_1749466616242"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749466616242"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749466616242"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-09T10:56:56.255Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 342394, "timestamp": "2025-06-09T10:56:56.256Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 342394, "timestamp": "2025-06-09T10:56:56.256Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749466616256", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749466616256", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_Z7cW3OyoEsojfIreWcAD_1749466616256", "postingSubId": "posting_Z7cW3OyoEsojfIreWcAD_1749466616256", "totalTime": 342394}
 LOG  [Navigation] Rendering headerLeft for: PostingDetail
 LOG  [Navigation] Can go back status: true
 LOG  [EditPostingScreen][Navigation] Screen cleanup: {"flowMetrics": {"dataLoadComplete": 436676888.972916, "dataLoadStart": 436676888.968, "events": [[Object]], "flowMetrics": undefined, "navigationComplete": 436676870.963583, "totalDuration": "12361.668"}, "timestamp": "2025-06-09T10:56:56.579Z"}
 LOG  [Navigation] Rendering headerLeft for: PostingDetail
 LOG  [Navigation] Can go back status: true
 LOG  Region changing: {"latitude": 37.789618376392035, "latitudeDelta": 0.05928221557871183, "longitude": -122.43732451498394, "longitudeDelta": 0.034503704621826614}
 LOG  Debounced region changed: {"latitude": 37.789618376392035, "latitudeDelta": 0.05928221557871183, "longitude": -122.43732451498394, "longitudeDelta": 0.034503704621826614}
 LOG  Fetching postings for new region
 LOG  [useControlledPostings] Fetching postings for region: {"latitude": 37.789618376392035, "latitudeDelta": 0.05928221557871183, "longitude": -122.43732451498394, "longitudeDelta": 0.034503704621826614}
 LOG  [fetchPostingsBySearch] Query results: 10
 LOG  [useControlledPostings] Fetched postings: 10
 LOG  [OfferDetail][NOTIFICATION_DB_UPDATE] {"count": 1, "timestamp": "2025-06-09T10:56:57.486Z"}





On Simulator 2: modification of a posting should display a refresh button in posting detail container while the offer owner is viewing the offer detail screen but the refresh button was displayed SUCCESSFULLY.

On Simulator 2: offer owner clicks the chevron down button in posting detail container to expand the posting details container and displays the old posting title and description as expected




 LOG  [PostingDetailsSection] Toggling details: {"current": false, "new": true, "timestamp": "2025-06-09T10:57:44.144Z"}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": true, "isLoading": false, "postingDescription": "Searching for basic fishing equipment - rod, reel, and tackle box. Must be in good working condition. ", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "Fishing Gear", "timestamp": "2025-06-09T10:57:44.153Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": true, "showRefresh": true, "timestamp": "2025-06-09T10:57:44.155Z"}
 LOG  [PostingDetailsSection][RENDER_REFRESH_BUTTON] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "showRefresh": true, "timestamp": "2025-06-09T10:57:44.156Z", "visible": true}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offers22555", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "224555", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T10:57:44.181Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:57:44.242Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:57:44.242Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:57:44.242Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": true, "timestamp": "2025-06-09T10:57:44.243Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:57:44.243Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:57:44.244Z"}
 LOG  [OfferCache][GET] {"age": 105150, "found": true, "isExpired": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:57:44.244Z"}
 LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"cachedStatus": "pending", "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:57:44.244Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:57:44.244Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": "hotmails offers22555", "hookPrice": 224555, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offers22555", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "224555", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": true}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T10:57:44.246Z"}
 LOG  [OfferDetailsSection][OFFER_UPDATED] {"description": "hotmails offers22555", "offerId": "tK7kgW3yDfZoF3IWeedl", "price": 224555, "timestamp": "2025-06-09T10:57:44.251Z"}
 LOG  FlatList layout complete




On Simulator 2: offer owner clicks the refresh button and the posting details are updated SUCCESSFULLY





 LOG  [PostingDetailsSection][REFRESH_START] {"postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:58:14.877Z"}
 LOG  [useOfferDetails][REFRESH_START] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:14.878Z"}
 LOG  [OfferCache][CLEAR] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:14.878Z"}
 LOG  [useOfferDetails][FETCH_OFFER_START] {"forceRefresh": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:14.879Z"}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": true, "isLoading": false, "postingDescription": "Searching for basic fishing equipment - rod, reel, and tackle box. Must be in good working condition. ", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "Fishing Gear", "timestamp": "2025-06-09T10:58:14.884Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": true, "showRefresh": true, "timestamp": "2025-06-09T10:58:14.887Z"}
 LOG  [PostingDetailsSection][RENDER_REFRESH_BUTTON] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "showRefresh": true, "timestamp": "2025-06-09T10:58:14.887Z", "visible": true}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offers22555", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "224555", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T10:58:14.913Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:14.955Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:14.955Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:14.955Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": true, "timestamp": "2025-06-09T10:58:14.956Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:14.957Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:14.957Z"}
 LOG  [useOfferDetails][FETCHING_DATA] {"fetchCount": 1, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:14.957Z"}
 LOG  [useOfferDetails][FETCH_OFFER_START] {"forceRefresh": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:14.957Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:14.957Z"}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": true, "isLoading": true, "postingDescription": "Searching for basic fishing equipment - rod, reel, and tackle box. Must be in good working condition. ", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "Fishing Gear", "timestamp": "2025-06-09T10:58:14.959Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": true, "showRefresh": true, "timestamp": "2025-06-09T10:58:14.959Z"}
 LOG  [PostingDetailsSection][RENDER_REFRESH_BUTTON] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "showRefresh": true, "timestamp": "2025-06-09T10:58:14.960Z", "visible": true}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": true, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offers22555", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "224555", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T10:58:14.968Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:14.997Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:14.998Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:14.998Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": true, "timestamp": "2025-06-09T10:58:14.998Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:14.999Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:15.006Z"}
 LOG  [useOfferDetails][FETCHING_DATA] {"fetchCount": 1, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:15.006Z"}
 LOG  [useOfferDetails][FETCH_OFFER_START] {"forceRefresh": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:15.007Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:15.007Z"}
 LOG  [OfferCache][SET] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:15.323Z"}
 LOG  [useOfferDetails][FETCH_OFFER_SUCCESS] {"offerId": "tK7kgW3yDfZoF3IWeedl", "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:58:15.324Z"}
 LOG  [OfferDetail][STATUS_TRACKING] {"hasInitialOffer": false, "hasOfferDetails": true, "initialOfferStatus": undefined, "offerId": "tK7kgW3yDfZoF3IWeedl", "offerStatus": "pending", "postingStatus": "Active", "timestamp": "2025-06-09T10:58:15.327Z"}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": true, "isLoading": true, "postingDescription": "11111Searching for basic fishing equipment - rod, reel, and tackle box. Must be in good working condition. ", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "Fishing Gear11111", "timestamp": "2025-06-09T10:58:15.328Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": true, "showRefresh": true, "timestamp": "2025-06-09T10:58:15.330Z"}
 LOG  [PostingDetailsSection][RENDER_REFRESH_BUTTON] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "showRefresh": true, "timestamp": "2025-06-09T10:58:15.330Z", "visible": true}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": true, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offers22555", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "224555", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T10:58:15.349Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:15.392Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:15.393Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:15.393Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": true, "timestamp": "2025-06-09T10:58:15.393Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:15.395Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:15.396Z"}
 LOG  [OfferCache][GET] {"age": 72, "found": true, "isExpired": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:15.396Z"}
 LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"cachedStatus": "pending", "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:15.396Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:15.396Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": "hotmails offers22555", "hookPrice": 224555, "isLoading": true, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offers22555", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "224555", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": true}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T10:58:15.399Z"}
 LOG  [OfferDetailsSection][OFFER_UPDATED] {"description": "hotmails offers22555", "offerId": "tK7kgW3yDfZoF3IWeedl", "price": 224555, "timestamp": "2025-06-09T10:58:15.402Z"}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": true, "isLoading": true, "postingDescription": "11111Searching for basic fishing equipment - rod, reel, and tackle box. Must be in good working condition. ", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "Fishing Gear11111", "timestamp": "2025-06-09T10:58:15.403Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": true, "showRefresh": true, "timestamp": "2025-06-09T10:58:15.404Z"}
 LOG  [PostingDetailsSection][RENDER_REFRESH_BUTTON] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "showRefresh": true, "timestamp": "2025-06-09T10:58:15.404Z", "visible": true}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": true, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offers22555", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "224555", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T10:58:15.413Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:15.440Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:15.440Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:15.440Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": true, "timestamp": "2025-06-09T10:58:15.441Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:15.441Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:15.450Z"}
 LOG  [OfferCache][GET] {"age": 126, "found": true, "isExpired": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:15.450Z"}
 LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"cachedStatus": "pending", "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:15.451Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:15.451Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": "hotmails offers22555", "hookPrice": 224555, "isLoading": true, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offers22555", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "224555", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": true}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T10:58:15.454Z"}
 LOG  [useOfferDetails][NO_CHANGES] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:15.458Z"}
 LOG  [OfferDetailsSection][OFFER_UPDATED] {"description": "hotmails offers22555", "offerId": "tK7kgW3yDfZoF3IWeedl", "price": 224555, "timestamp": "2025-06-09T10:58:15.458Z"}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": true, "isLoading": true, "postingDescription": "11111Searching for basic fishing equipment - rod, reel, and tackle box. Must be in good working condition. ", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "Fishing Gear11111", "timestamp": "2025-06-09T10:58:15.459Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": true, "showRefresh": true, "timestamp": "2025-06-09T10:58:15.460Z"}
 LOG  [PostingDetailsSection][RENDER_REFRESH_BUTTON] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "showRefresh": true, "timestamp": "2025-06-09T10:58:15.460Z", "visible": true}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": true, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offers22555", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "224555", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T10:58:15.466Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:15.495Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:15.495Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:15.495Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": true, "timestamp": "2025-06-09T10:58:15.496Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:15.496Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:15.496Z"}
 LOG  [OfferCache][GET] {"age": 172, "found": true, "isExpired": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:15.496Z"}
 LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"cachedStatus": "pending", "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:15.496Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:15.497Z"}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": true, "isLoading": true, "postingDescription": "11111Searching for basic fishing equipment - rod, reel, and tackle box. Must be in good working condition. ", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "Fishing Gear11111", "timestamp": "2025-06-09T10:58:15.498Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": true, "showRefresh": true, "timestamp": "2025-06-09T10:58:15.499Z"}
 LOG  [PostingDetailsSection][RENDER_REFRESH_BUTTON] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "showRefresh": true, "timestamp": "2025-06-09T10:58:15.499Z", "visible": true}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": true, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offers22555", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "224555", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T10:58:15.506Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:15.534Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:15.534Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:15.534Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": true, "timestamp": "2025-06-09T10:58:15.534Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:15.535Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:15.535Z"}
 LOG  [OfferCache][GET] {"age": 211, "found": true, "isExpired": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:15.535Z"}
 LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"cachedStatus": "pending", "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:15.536Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:15.536Z"}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": true, "isLoading": false, "postingDescription": "11111Searching for basic fishing equipment - rod, reel, and tackle box. Must be in good working condition. ", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "Fishing Gear11111", "timestamp": "2025-06-09T10:58:15.540Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": true, "showRefresh": true, "timestamp": "2025-06-09T10:58:15.540Z"}
 LOG  [PostingDetailsSection][RENDER_REFRESH_BUTTON] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "showRefresh": true, "timestamp": "2025-06-09T10:58:15.541Z", "visible": true}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offers22555", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "224555", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T10:58:15.551Z"}
 LOG  [PostingDetailsSection][MARKING_NOTIFICATIONS_READ] {"postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:58:15.578Z"}
 LOG  [notificationService][MARK_RELEVANT_READ] {"offerId": undefined, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:58:15.578Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:15.579Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:15.579Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:15.579Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": true, "timestamp": "2025-06-09T10:58:15.580Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:15.580Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:15.580Z"}
 LOG  [OfferCache][GET] {"age": 257, "found": true, "isExpired": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:15.581Z"}
 LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"cachedStatus": "pending", "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:15.582Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:15.582Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": "hotmails offers22555", "hookPrice": 224555, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offers22555", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "224555", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": true}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T10:58:15.584Z"}
 LOG  [OfferDetailsSection][OFFER_UPDATED] {"description": "hotmails offers22555", "offerId": "tK7kgW3yDfZoF3IWeedl", "price": 224555, "timestamp": "2025-06-09T10:58:15.587Z"}
 LOG  [OfferCache][SET] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:15.589Z"}
 LOG  [useOfferDetails][FETCH_OFFER_SUCCESS] {"offerId": "tK7kgW3yDfZoF3IWeedl", "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:58:15.589Z"}
 LOG  [OfferCache][SET] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:15.589Z"}
 LOG  [useOfferDetails][FETCH_OFFER_SUCCESS] {"offerId": "tK7kgW3yDfZoF3IWeedl", "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:58:15.589Z"}
 LOG  [notificationService][MARK_RELEVANT_READ] Found notifications: {"count": 1, "offerId": undefined, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:58:15.693Z"}
 LOG  [OfferDetail][NOTIFICATION_DB_UPDATE] {"count": 0, "timestamp": "2025-06-09T10:58:15.701Z"}
 LOG  [notificationService][MARKED_READ] {"count": 1, "offerId": undefined, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:58:15.975Z"}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": true, "isLoading": false, "postingDescription": "11111Searching for basic fishing equipment - rod, reel, and tackle box. Must be in good working condition. ", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "Fishing Gear11111", "timestamp": "2025-06-09T10:58:15.982Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T10:58:15.984Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offers22555", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "224555", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T10:58:16.008Z"}
 LOG  [PostingDetailsSection][REFRESH_COMPLETE] {"postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:58:16.050Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:16.051Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:16.051Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:16.051Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": true, "timestamp": "2025-06-09T10:58:16.052Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:16.052Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:16.053Z"}
 LOG  [OfferCache][GET] {"age": 464, "found": true, "isExpired": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:16.053Z"}
 LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"cachedStatus": "pending", "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:16.053Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:58:16.053Z"}
 LOG  OfferDetail refresh button states: {"showOfferRefresh": false, "showOfferRefreshExplicit": false, "showPostingRefresh": false, "showPostingRefreshExplicit": false, "timestamp": "2025-06-09T10:58:16.053Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": "hotmails offers22555", "hookPrice": 224555, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offers22555", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "224555", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": true}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T10:58:16.057Z"}
 LOG  [OfferDetailsSection][OFFER_UPDATED] {"description": "hotmails offers22555", "offerId": "tK7kgW3yDfZoF3IWeedl", "price": 224555, "timestamp": "2025-06-09T10:58:16.060Z"}



 On simulator 2: offer owner clicks the back button in offer detail screen to navigate to posting details screen.





 LOG  [BackButton] Back button pressed
 LOG  [Navigation] Configuring screen: LoginScreen
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: Home
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: PostingDetail
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Rendering headerLeft for: PostingDetail
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Rendering headerLeft for: OfferDetail
 LOG  [Navigation] Can go back status: true
 LOG  [usePostingDetails][Focus] Screen focused, setting up subscription
 LOG  [usePostingDetails][Setup] Creating references
 LOG  [usePostingDetails][Setup] Pre-snapshot listener setup: {"postingId": "Z7cW3OyoEsojfIreWcAD", "ref": "postings/Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:59:06.911Z"}
 LOG  [ListenerStateManager] Starting transition for postings_1749466746911_1749466746911: inactive -> initializing
 LOG  [ListenerRegistry][postings] Registered: {"collection": "postings", "id": "1749466746911", "registryId": "postings_1749466746911_1749466746911", "state": "active", "timestamp": "2025-06-09T10:59:06.912Z"}
 LOG  [ListenerStateManager] Completing transition for postings_1749466746911_1749466746911
 LOG  [ListenerRegistry][Component] Registered listener with component: {"componentId": "PostingDetail_Z7cW3OyoEsojfIreWcAD", "listenerId": "postings_1749466746911_1749466746911", "timestamp": "2025-06-09T10:59:06.912Z"}
 LOG  [ListenerRegistry][postings] Created: {"componentId": "PostingDetail_Z7cW3OyoEsojfIreWcAD", "listenerId": "postings_1749466746911", "timestamp": "2025-06-09T10:59:06.912Z"}
 LOG  [useOffers][Focus] Screen focused, setting up subscription
 LOG  [useOffers][Subscribe] Setting up subscription {"postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:59:06.912Z"}
 LOG  [ListenerStateManager] Starting transition for offers_1749466746912_1749466746912: inactive -> initializing
 LOG  [ListenerRegistry][offers] Registered: {"collection": "offers", "id": "1749466746912", "registryId": "offers_1749466746912_1749466746912", "state": "active", "timestamp": "2025-06-09T10:59:06.912Z"}
 LOG  [ListenerStateManager] Completing transition for offers_1749466746912_1749466746912
 LOG  [ListenerRegistry][Component] Registered listener with component: {"componentId": "PostingDetail_Z7cW3OyoEsojfIreWcAD", "listenerId": "offers_1749466746912_1749466746912", "timestamp": "2025-06-09T10:59:06.913Z"}
 LOG  [ListenerRegistry][offers] Created: {"componentId": "PostingDetail_Z7cW3OyoEsojfIreWcAD", "listenerId": "offers_1749466746912", "timestamp": "2025-06-09T10:59:06.913Z"}
 LOG  [useFavorites][Focus] Screen focused, fetching favorite status
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-09T10:53:23.895Z"}, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:59:06.915Z"}
 LOG  [usePostingDetails][Reducer] Processing action: {"currentState": {"error": null, "loading": false, "postingDetails": {"createdAt": [Timestamp], "description": "Searching for basic fishing equipment - rod, reel, and tackle box. Must be in good working condition. ", "id": "Z7cW3OyoEsojfIreWcAD", "lastUpdated": [Timestamp], "latitude": 37.7917130364994, "longitude": -122.4467312534528, "postingStatus": "Active", "searchTerms": [Array], "title": "Fishing Gear", "titleLower": "hedere", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}}, "metrics": {"timeSinceStart": 343020, "timestamp": "2025-06-09T10:59:06.915Z", "updateCount": 3}, "type": "FETCH_START"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": true, "timestamp": "2025-06-09T10:59:06.916Z"}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_Z7cW3OyoEsojfIreWcAD_1749466404175", "offers_Z7cW3OyoEsojfIreWcAD_1749466404175"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749466404175"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749466404175"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-09T10:59:06.919Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 343025, "timestamp": "2025-06-09T10:59:06.920Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 343026, "timestamp": "2025-06-09T10:59:06.921Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749466746921", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749466746921", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_Z7cW3OyoEsojfIreWcAD_1749466746921", "postingSubId": "posting_Z7cW3OyoEsojfIreWcAD_1749466746921", "totalTime": 343026}
 LOG  [PostingDetail][StateSync] Checking state update conditions: {"currentUser": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hasDetails": true, "offersCount": 2, "postingLoading": true, "timestamp": "2025-06-09T10:59:06.921Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-09T10:53:23.895Z"}, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:59:07.031Z"}
 LOG  [usePostingDetails][Reducer] Processing action: {"currentState": {"error": null, "loading": true, "postingDetails": {"createdAt": [Timestamp], "description": "Searching for basic fishing equipment - rod, reel, and tackle box. Must be in good working condition. ", "id": "Z7cW3OyoEsojfIreWcAD", "lastUpdated": [Timestamp], "latitude": 37.7917130364994, "longitude": -122.4467312534528, "postingStatus": "Active", "searchTerms": [Array], "title": "Fishing Gear", "titleLower": "hedere", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}}, "metrics": {"timeSinceStart": 343136, "timestamp": "2025-06-09T10:59:07.031Z", "updateCount": 4}, "type": "UPDATE_POSTING"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-09T10:59:07.033Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isFavorite": true, "isOwner": false, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:59:07.034Z", "userOffer": {"id": "tK7kgW3yDfZoF3IWeedl", "status": "pending"}}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_Z7cW3OyoEsojfIreWcAD_1749466746921", "offers_Z7cW3OyoEsojfIreWcAD_1749466746921"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749466746921"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749466746921"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-09T10:59:07.053Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 343159, "timestamp": "2025-06-09T10:59:07.054Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 343159, "timestamp": "2025-06-09T10:59:07.054Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749466747054", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749466747054", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_Z7cW3OyoEsojfIreWcAD_1749466747054", "postingSubId": "posting_Z7cW3OyoEsojfIreWcAD_1749466747054", "totalTime": 343159}
 LOG  [PostingDetail][StateSync] Checking state update conditions: {"currentUser": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hasDetails": true, "offersCount": 2, "postingLoading": false, "timestamp": "2025-06-09T10:59:07.054Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Updating state with: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isOwnerCheck": false, "offersCount": 2, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:59:07.056Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Processing offers: {"count": 2, "timestamp": "2025-06-09T10:59:07.056Z", "userOffers": 1}
 LOG  [PostingDetail][StateSync] State updated: {"hasUserOffer": true, "isOwner": false, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:59:07.057Z"}
 LOG  [PostingDetail][StateVerification] {"auth": {"currentUser": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "expectedOwner": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "posting": {"id": "Z7cW3OyoEsojfIreWcAD", "loaded": true}, "state": {"activeTab": "active", "currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDescriptionExpanded": false, "isOwner": false, "lastUpdate": 1749466404126, "offersCount": 2, "postingDetailsId": "Z7cW3OyoEsojfIreWcAD", "userOffer": {"id": "tK7kgW3yDfZoF3IWeedl", "status": "pending"}}, "timestamp": "2025-06-09T10:59:07.057Z"}
 LOG  [PostingDetail] Posting details loaded for edit: {"hasDetails": true, "hasLocation": true, "isOwner": false, "timestamp": "2025-06-09T10:59:07.057Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-09T10:53:23.895Z"}, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:59:07.058Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-09T10:59:07.059Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isFavorite": true, "isOwner": false, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:59:07.060Z", "userOffer": {"id": "tK7kgW3yDfZoF3IWeedl", "status": "pending"}}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_Z7cW3OyoEsojfIreWcAD_1749466747054", "offers_Z7cW3OyoEsojfIreWcAD_1749466747054"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749466747054"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749466747054"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-09T10:59:07.069Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 343174, "timestamp": "2025-06-09T10:59:07.069Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 343175, "timestamp": "2025-06-09T10:59:07.070Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749466747070", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749466747070", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_Z7cW3OyoEsojfIreWcAD_1749466747070", "postingSubId": "posting_Z7cW3OyoEsojfIreWcAD_1749466747070", "totalTime": 343175}
 LOG  [PostingDetail][StateVerification] {"auth": {"currentUser": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "expectedOwner": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "posting": {"id": "Z7cW3OyoEsojfIreWcAD", "loaded": true}, "state": {"activeTab": "active", "currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDescriptionExpanded": false, "isOwner": false, "lastUpdate": 1749466747058, "offersCount": 2, "postingDetailsId": "Z7cW3OyoEsojfIreWcAD", "userOffer": {"id": "tK7kgW3yDfZoF3IWeedl", "status": "pending"}}, "timestamp": "2025-06-09T10:59:07.070Z"}
 LOG  [PostingDetail][StateDebug] {"auth": {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2"}, "performance": {"timeSinceLastUpdate": 12}, "props": {"postingId": "Z7cW3OyoEsojfIreWcAD", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "state": {"activeTab": "active", "currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDescriptionExpanded": false, "isOwner": false, "lastUpdate": 1749466747058, "offersCount": 2, "postingDetailsId": "Z7cW3OyoEsojfIreWcAD", "userOffer": {"id": "tK7kgW3yDfZoF3IWeedl", "status": "pending"}}, "subscriptions": {"metrics": {"lastUpdate": "2025-06-09T10:53:23.895Z", "subscriptionCount": 0, "totalTime": 343175}, "types": []}, "timestamp": "2025-06-09T10:59:07.070Z"}
 LOG  [useOffers][Update] {"changes": [{"id": "IgyIrlkc18WdziXD3nEO", "timestamp": "2025-06-09T10:59:07.097Z", "type": "added"}, {"id": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:07.097Z", "type": "added"}], "count": 2, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:59:07.097Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-09T10:53:23.895Z"}, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:59:07.099Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-09T10:59:07.099Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isFavorite": true, "isOwner": false, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:59:07.099Z", "userOffer": {"id": "tK7kgW3yDfZoF3IWeedl", "status": "pending"}}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_Z7cW3OyoEsojfIreWcAD_1749466747070", "offers_Z7cW3OyoEsojfIreWcAD_1749466747070"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749466747070"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749466747070"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-09T10:59:07.108Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 343213, "timestamp": "2025-06-09T10:59:07.108Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 343213, "timestamp": "2025-06-09T10:59:07.108Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749466747108", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749466747108", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_Z7cW3OyoEsojfIreWcAD_1749466747108", "postingSubId": "posting_Z7cW3OyoEsojfIreWcAD_1749466747108", "totalTime": 343213}
 LOG  [PostingDetail][StateSync] Checking state update conditions: {"currentUser": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hasDetails": true, "offersCount": 2, "postingLoading": false, "timestamp": "2025-06-09T10:59:07.109Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Updating state with: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isOwnerCheck": false, "offersCount": 2, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:59:07.109Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Processing offers: {"count": 2, "timestamp": "2025-06-09T10:59:07.109Z", "userOffers": 1}
 LOG  [PostingDetail][StateSync] State updated: {"hasUserOffer": true, "isOwner": false, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:59:07.110Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-09T10:53:23.895Z"}, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:59:07.111Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-09T10:59:07.112Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isFavorite": true, "isOwner": false, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:59:07.112Z", "userOffer": {"id": "tK7kgW3yDfZoF3IWeedl", "status": "pending"}}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_Z7cW3OyoEsojfIreWcAD_1749466747108", "offers_Z7cW3OyoEsojfIreWcAD_1749466747108"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749466747108"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749466747108"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-09T10:59:07.119Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 343224, "timestamp": "2025-06-09T10:59:07.119Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 343224, "timestamp": "2025-06-09T10:59:07.119Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749466747120", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749466747120", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_Z7cW3OyoEsojfIreWcAD_1749466747120", "postingSubId": "posting_Z7cW3OyoEsojfIreWcAD_1749466747120", "totalTime": 343225}
 LOG  [PostingDetail][StateVerification] {"auth": {"currentUser": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "expectedOwner": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "posting": {"id": "Z7cW3OyoEsojfIreWcAD", "loaded": true}, "state": {"activeTab": "active", "currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDescriptionExpanded": false, "isOwner": false, "lastUpdate": 1749466747111, "offersCount": 2, "postingDetailsId": "Z7cW3OyoEsojfIreWcAD", "userOffer": {"id": "tK7kgW3yDfZoF3IWeedl", "status": "pending"}}, "timestamp": "2025-06-09T10:59:07.120Z"}
 LOG  [PostingDetail][StateDebug] {"auth": {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2"}, "performance": {"timeSinceLastUpdate": 9}, "props": {"postingId": "Z7cW3OyoEsojfIreWcAD", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "state": {"activeTab": "active", "currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDescriptionExpanded": false, "isOwner": false, "lastUpdate": 1749466747111, "offersCount": 2, "postingDetailsId": "Z7cW3OyoEsojfIreWcAD", "userOffer": {"id": "tK7kgW3yDfZoF3IWeedl", "status": "pending"}}, "subscriptions": {"metrics": {"lastUpdate": "2025-06-09T10:53:23.895Z", "subscriptionCount": 0, "totalTime": 343225}, "types": []}, "timestamp": "2025-06-09T10:59:07.120Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-09T10:53:23.895Z"}, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:59:07.125Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-09T10:59:07.125Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isFavorite": true, "isOwner": false, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:59:07.126Z", "userOffer": {"id": "tK7kgW3yDfZoF3IWeedl", "status": "pending"}}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-09T10:53:23.895Z"}, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:59:07.128Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-09T10:59:07.128Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isFavorite": true, "isOwner": false, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:59:07.129Z", "userOffer": {"id": "tK7kgW3yDfZoF3IWeedl", "status": "pending"}}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_Z7cW3OyoEsojfIreWcAD_1749466747120", "offers_Z7cW3OyoEsojfIreWcAD_1749466747120"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749466747120"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749466747120"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-09T10:59:07.135Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 343240, "timestamp": "2025-06-09T10:59:07.135Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 343240, "timestamp": "2025-06-09T10:59:07.135Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749466747157", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749466747157", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_Z7cW3OyoEsojfIreWcAD_1749466747157", "postingSubId": "posting_Z7cW3OyoEsojfIreWcAD_1749466747157", "totalTime": 343263}
 LOG  [Navigation] Rendering headerLeft for: PostingDetail
 LOG  [Navigation] Can go back status: true
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:07.480Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:07.480Z"}
 LOG  Cleaning up OfferDetail component
 LOG  [useAuthUser] Cleaning up auth state listener
 LOG  [Messages Subscription] Cleaning up subscription
 LOG  [OfferDetail][UNMOUNT] {"cleanupStarted": {"isCleaningUp": false, "isMounted": true}, "lastAction": "UNMOUNT", "mountCount": 1, "timestamp": "2025-06-09T10:59:07.481Z"}
 LOG  [OfferDetail][UNMOUNT] {"cleanupStarted": {"isCleaningUp": false, "isMounted": true}, "lastAction": "UNMOUNT", "mountCount": 1, "timestamp": "2025-06-09T10:59:07.481Z"}
 LOG  [OfferDetail][CLEANUP_START] {"cleanupStarted": {"isCleaningUp": false, "isMounted": true}, "lastAction": "CLEANUP_START", "mountCount": 1, "timestamp": "2025-06-09T10:59:07.481Z"}
 LOG  [OfferDetail][CLEANUP_STATE] {"cleanupStarted": {"isCleaningUp": true, "isMounted": false}, "lastAction": "CLEANUP_STATE", "mountCount": 1, "timestamp": "2025-06-09T10:59:07.481Z"}
 LOG  [OfferDetail][CLEANUP_COMPLETE] {"cleanupStarted": {"isCleaningUp": true, "isMounted": false}, "lastAction": "CLEANUP_COMPLETE", "mountCount": 1, "timestamp": "2025-06-09T10:59:07.483Z"}
 LOG  [OfferDetail][NOTIFICATION_LISTENER_CLEANUP] {"offerId": "tK7kgW3yDfZoF3IWeedl", "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:59:07.483Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:07.483Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:07.483Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:07.484Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [Navigation] Rendering headerLeft for: PostingDetail
 LOG  [Navigation] Can go back status: true
 LOG  Region changing: {"latitude": 37.78602284472297, "latitudeDelta": 0.04388726327615444, "longitude": -122.43885855863637, "longitudeDelta": 0.03218629836729292}
 LOG  Debounced region changed: {"latitude": 37.78602284472297, "latitudeDelta": 0.04388726327615444, "longitude": -122.43885855863637, "longitudeDelta": 0.03218629836729292}
 LOG  Fetching postings for new region
 LOG  [useControlledPostings] Fetching postings for region: {"latitude": 37.78602284472297, "latitudeDelta": 0.04388726327615444, "longitude": -122.43885855863637, "longitudeDelta": 0.03218629836729292}
 LOG  [fetchPostingsBySearch] Query results: 10
 LOG  [useControlledPostings] Fetched postings: 10


 On simulator 2: offer owner clicks on the offer to navigate to the offer details screen:


 LOG  [PostingDetail][Navigation] Attempting safe navigation
 LOG  [PostingDetail][Navigation] Checking listeners: {"activeCount": 2, "byCollection": {"offers": 1, "postings": 1}, "timestamp": "2025-06-09T10:59:44.810Z"}
 LOG  [PostingDetail][Navigation] Cleaning up hooks
 LOG  [PostingDetail] Unsubscribing from posting
 LOG  [usePostingDetails][Unsubscribe] Cleaning up listener: {"listenerId": "postings_1749466746911_1749466746911", "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:59:44.811Z"}
 LOG  [ListenerRegistry][Component] Unregistered listener from component: {"componentId": "PostingDetail_Z7cW3OyoEsojfIreWcAD", "listenerId": "postings_1749466746911_1749466746911", "timestamp": "2025-06-09T10:59:44.812Z"}
 LOG  [PostingDetail] Successfully unsubscribed from posting
 LOG  [PostingDetail] Unsubscribing from offers
 LOG  [useOffers][Unsubscribe] Cleaning up subscription: {"postingId": "Z7cW3OyoEsojfIreWcAD", "subscriptionId": "offers_1749466746912_1749466746912", "timestamp": "2025-06-09T10:59:44.814Z"}
 LOG  [ListenerRegistry][Component] Unregistered listener from component: {"componentId": "PostingDetail_Z7cW3OyoEsojfIreWcAD", "listenerId": "offers_1749466746912_1749466746912", "timestamp": "2025-06-09T10:59:44.815Z"}
 LOG  [PostingDetail] Successfully unsubscribed from offers
 LOG  [ListenerRegistry] Cleanup metrics: {"groupId": null, "metrics": {"duration": 4, "startTime": 1749466784813, "success": true}, "subscriptionId": "postings_1749466746911_1749466746911", "timestamp": "2025-06-09T10:59:44.817Z"}
 LOG  [ListenerRegistry] Cleanup metrics: {"groupId": null, "metrics": {"duration": 6, "startTime": 1749466784815, "success": true}, "subscriptionId": "offers_1749466746912_1749466746912", "timestamp": "2025-06-09T10:59:44.821Z"}
 LOG  [PostingDetail][Navigation] Post-cleanup verification: {"activeListeners": 0, "byCollection": {"offers": 0, "postings": 0}, "timestamp": "2025-06-09T10:59:44.923Z"}
 LOG  [PostingDetail][Navigation] All listeners cleaned up successfully
 LOG  [Navigation] Configuring screen: LoginScreen
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: Home
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: PostingDetail
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: OfferDetail
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Rendering headerLeft for: PostingDetail
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Rendering headerLeft for: OfferDetail
 LOG  [Navigation] Can go back status: true
 LOG  [BackButton] Rendering back button
 LOG  [OfferDetail][RELIABLE_POSTING_ID_DEBUG] {"foundId": "Z7cW3OyoEsojfIreWcAD", "sources": {"initialOfferPostingId": undefined, "initialPostingId": undefined, "offerDetailsPostingId": undefined, "offerDetailsPostingPath": undefined, "routePostingId": "Z7cW3OyoEsojfIreWcAD"}, "timestamp": "2025-06-09T10:59:44.993Z"}
 LOG  [OfferDetail][STATUS_TRACKING] {"hasInitialOffer": false, "hasOfferDetails": false, "initialOfferStatus": undefined, "offerId": "tK7kgW3yDfZoF3IWeedl", "offerStatus": "active", "postingStatus": "Active", "timestamp": "2025-06-09T10:59:44.994Z"}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": false, "isLoading": true, "postingDescription": "", "postingOwnerId": null, "postingStatus": "Active", "postingTitle": "", "timestamp": "2025-06-09T10:59:44.995Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": null, "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T10:59:44.996Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": true, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "", "offerOwnerId": null, "offerPrice": "0", "offerStatus": "active", "restrictionEndDate": null, "userScore": undefined, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": false, "offerOwnerId": null, "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T10:59:45.001Z"}
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": null, "shouldShowAbusiveButton": false, "timestamp": "2025-06-09T10:59:45.017Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:45.017Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:45.017Z"}
 LOG  [OfferCache][GET] {"age": 89429, "found": true, "isExpired": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:45.018Z"}
 LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"cachedStatus": "pending", "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:45.018Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:45.018Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:45.018Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:45.018Z"}
 LOG  [OfferCache][GET] {"age": 89430, "found": true, "isExpired": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:45.019Z"}
 LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"cachedStatus": "pending", "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:45.020Z"}
 LOG  [useAuthUser] Setting up auth state listener {"currentUser": {"_redirectEventId": undefined, "apiKey": "AIzaSyBOHewmZjlRWGu1jeEI-uBSkAn56xLMlec", "appName": "[DEFAULT]", "createdAt": "1731681044939", "displayName": undefined, "email": "<EMAIL>", "emailVerified": false, "isAnonymous": false, "lastLoginAt": "1749466083404", "phoneNumber": undefined, "photoURL": undefined, "providerData": [Array], "stsTokenManager": [Object], "tenantId": undefined, "uid": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2"}, "hasAuth": true, "hasOnAuthStateChanged": true}
 LOG  [OfferDetail][MOUNT] {"cleanupStarted": {"isCleaningUp": false, "isMounted": true}, "lastAction": "MOUNT", "mountCount": 0, "params": {"offerId": "tK7kgW3yDfZoF3IWeedl", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingId": "Z7cW3OyoEsojfIreWcAD", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "timestamp": "2025-06-09T10:59:45.025Z"}
 LOG  [Messages Subscription] Setting up subscription for offerId: tK7kgW3yDfZoF3IWeedl
 LOG  === OfferDetail Screen Mounted ===
 LOG  Route params: {"offerId": "tK7kgW3yDfZoF3IWeedl", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingId": "Z7cW3OyoEsojfIreWcAD", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  Setting owner IDs from route params: {"offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [OfferDetail][MOUNT] {"cleanupStarted": {"isCleaningUp": false, "isMounted": true}, "lastAction": "MOUNT", "mountCount": 1, "params": {"offerId": "tK7kgW3yDfZoF3IWeedl", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingId": "Z7cW3OyoEsojfIreWcAD", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "timestamp": "2025-06-09T10:59:45.033Z"}
 LOG  Authenticated user: ybEyGDxbvleCEXdTvrA2CgVTJ1y2
 LOG  [OfferDetail] Initializing with params: {"hasInitialOffer": false, "hasInitialPosting": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:45.036Z"}
 LOG  [OfferDetail] Setting postingOwnerId from route params: {"postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [OfferDetail] Setting offerOwnerId from route params: {"offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2"}
 LOG  OfferDetail refresh button states: {"showOfferRefresh": false, "showOfferRefreshExplicit": false, "showPostingRefresh": false, "showPostingRefreshExplicit": false, "timestamp": "2025-06-09T10:59:45.036Z"}
 LOG  [OfferDetail][NOTIFICATION_LISTENER_SETUP] {"finalPostingId": "Z7cW3OyoEsojfIreWcAD", "hasPostingData": false, "initialPostingId": undefined, "offerDetailsPostingId": undefined, "offerId": "tK7kgW3yDfZoF3IWeedl", "postingId": "Z7cW3OyoEsojfIreWcAD", "reliablePostingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:59:45.037Z"}
 LOG  [OfferDetail][NOTIFICATION_DB_LISTENER_SETUP] {"offerId": "tK7kgW3yDfZoF3IWeedl", "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:59:45.038Z", "userId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2"}
 LOG  [usePostingDetails][Focus] Screen unfocused, cleaning up
 LOG  [ListenerRegistry][Component] Unregistered listener from component: {"componentId": "PostingDetail_Z7cW3OyoEsojfIreWcAD", "listenerId": "postings_1749466746911_1749466746911", "timestamp": "2025-06-09T10:59:45.038Z"}
 LOG  [ListenerRegistry] No entry found for postings_1749466746911_1749466746911
 LOG  [useOffers][Focus] Screen unfocused, cleaning up
 LOG  [ListenerRegistry][Component] Unregistered listener from component: {"componentId": "PostingDetail_Z7cW3OyoEsojfIreWcAD", "listenerId": "offers_1749466746912_1749466746912", "timestamp": "2025-06-09T10:59:45.038Z"}
 LOG  [ListenerRegistry] No entry found for offers_1749466746912_1749466746912
 LOG  [OfferDetail][STATUS_TRACKING] {"hasInitialOffer": false, "hasOfferDetails": true, "initialOfferStatus": undefined, "offerId": "tK7kgW3yDfZoF3IWeedl", "offerStatus": "pending", "postingStatus": "Active", "timestamp": "2025-06-09T10:59:45.040Z"}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": false, "isLoading": true, "postingDescription": "11111Searching for basic fishing equipment - rod, reel, and tackle box. Must be in good working condition. ", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "Fishing Gear11111", "timestamp": "2025-06-09T10:59:45.041Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T10:59:45.042Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": true, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offers22555", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "224555", "offerStatus": "pending", "restrictionEndDate": null, "userScore": undefined, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T10:59:45.047Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:45.057Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:45.057Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:45.057Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": true, "timestamp": "2025-06-09T10:59:45.058Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:45.058Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:45.072Z"}
 LOG  [OfferCache][GET] {"age": 89484, "found": true, "isExpired": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:45.073Z"}
 LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"cachedStatus": "pending", "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:45.073Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:45.073Z"}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": false, "isLoading": false, "postingDescription": "11111Searching for basic fishing equipment - rod, reel, and tackle box. Must be in good working condition. ", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "Fishing Gear11111", "timestamp": "2025-06-09T10:59:45.075Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T10:59:45.076Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offers22555", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "224555", "offerStatus": "pending", "restrictionEndDate": null, "userScore": undefined, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T10:59:45.080Z"}
 LOG  [useAuthUser] Auth state changed {"user": {"_redirectEventId": undefined, "apiKey": "AIzaSyBOHewmZjlRWGu1jeEI-uBSkAn56xLMlec", "appName": "[DEFAULT]", "createdAt": "1731681044939", "displayName": undefined, "email": "<EMAIL>", "emailVerified": false, "isAnonymous": false, "lastLoginAt": "1749466083404", "phoneNumber": undefined, "photoURL": undefined, "providerData": [Array], "stsTokenManager": [Object], "tenantId": undefined, "uid": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2"}}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:45.096Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:45.096Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:45.096Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": true, "timestamp": "2025-06-09T10:59:45.096Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:45.097Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:45.097Z"}
 LOG  [OfferCache][GET] {"age": 89508, "found": true, "isExpired": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:45.097Z"}
 LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"cachedStatus": "pending", "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:45.097Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:45.097Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": "hotmails offers22555", "hookPrice": 224555, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offers22555", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "224555", "offerStatus": "pending", "restrictionEndDate": null, "userScore": undefined, "usingHookData": true}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T10:59:45.099Z"}
 LOG  [OfferDetailsSection][OFFER_UPDATED] {"description": "hotmails offers22555", "offerId": "tK7kgW3yDfZoF3IWeedl", "price": 224555, "timestamp": "2025-06-09T10:59:45.102Z"}
 LOG  [OfferDetail][NOTIFICATION_DB_UPDATE] {"count": 0, "timestamp": "2025-06-09T10:59:45.105Z"}
 LOG  [Navigation] Rendering headerLeft for: PostingDetail
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Rendering headerLeft for: OfferDetail
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Rendering headerLeft for: OfferDetail
 LOG  [Navigation] Can go back status: true
 LOG  Region changing: {"latitude": 37.78602284508889, "latitudeDelta": 0.03954601933347135, "longitude": -122.43885855863637, "longitudeDelta": 0.03218629836729292}
 LOG  [Messages Subscription] Received snapshot update: {"empty": false, "metadata": {"fromCache": false, "hasPendingWrites": false}, "size": 1}
 LOG  [Messages Subscription] Processing discussion: {"id": "uFPwMJYrZ5wEt4cqPi35", "messageCount": 16}
 LOG  [Messages Subscription] Raw messages: {"count": 16, "messageKeys": ["read", "senderId", "timestamp", "id", "text", "readAt"], "sampleMessage": {"id": "1737359768014_ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "read": true, "readAt": [Object], "senderId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "text": "hotmails offer", "timestamp": [Object]}}
 LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["read", "senderId", "timestamp", "id", "text", "readAt"], "timestampType": "object"}
 LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["id", "read", "readAt", "senderId", "text", "timestamp"], "timestampType": "object"}
 LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["id", "senderId", "read", "timestamp", "text", "readAt"], "timestampType": "object"}
 LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["timestamp", "read", "senderId", "readAt", "text", "id"], "timestampType": "object"}
 LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["senderId", "readAt", "id", "text", "read", "timestamp"], "timestampType": "object"}
 LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["id", "text", "senderId", "readAt", "timestamp", "read"], "timestampType": "object"}
 LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["timestamp", "id", "senderId", "text", "read", "readAt"], "timestampType": "object"}
 LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["timestamp", "read", "id", "readAt", "text", "senderId"], "timestampType": "object"}
 LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["text", "readAt", "read", "senderId", "timestamp", "id"], "timestampType": "object"}
 LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["timestamp", "read", "readAt", "senderId", "id", "text"], "timestampType": "object"}
 LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["id", "text", "readAt", "senderId", "timestamp", "read"], "timestampType": "object"}
 LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["senderId", "readAt", "id", "timestamp", "read", "text"], "timestampType": "object"}
 LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["text", "id", "readAt", "senderId", "timestamp", "read"], "timestampType": "object"}
 LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["id", "text", "read", "readAt", "senderId", "timestamp"], "timestampType": "object"}
 LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["senderId", "text", "readAt", "timestamp", "read", "id"], "timestampType": "object"}
 LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["readAt", "senderId", "text", "timestamp", "read", "id"], "timestampType": "object"}
 LOG  [Messages Subscription] Validation summary: {"firstValidMessage": {"id": "1737359768014_ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "senderId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "timestamp": [Object]}, "invalidMessages": 0, "totalMessages": 16, "validMessages": 16}
 LOG  [Messages Subscription] Processing messages: {"hasLegacyMessages": false, "messageTypes": [{"hasId": true, "hasTimestamp": true, "timestampType": "object"}, {"hasId": true, "hasTimestamp": true, "timestampType": "object"}, {"hasId": true, "hasTimestamp": true, "timestampType": "object"}, {"hasId": true, "hasTimestamp": true, "timestampType": "object"}, {"hasId": true, "hasTimestamp": true, "timestampType": "object"}, {"hasId": true, "hasTimestamp": true, "timestampType": "object"}, {"hasId": true, "hasTimestamp": true, "timestampType": "object"}, {"hasId": true, "hasTimestamp": true, "timestampType": "object"}, {"hasId": true, "hasTimestamp": true, "timestampType": "object"}, {"hasId": true, "hasTimestamp": true, "timestampType": "object"}, {"hasId": true, "hasTimestamp": true, "timestampType": "object"}, {"hasId": true, "hasTimestamp": true, "timestampType": "object"}, {"hasId": true, "hasTimestamp": true, "timestampType": "object"}, {"hasId": true, "hasTimestamp": true, "timestampType": "object"}, {"hasId": true, "hasTimestamp": true, "timestampType": "object"}, {"hasId": true, "hasTimestamp": true, "timestampType": "object"}], "newCount": 16, "prevCount": 0}
 LOG  [Messages Subscription] Updated messages state: {"firstMessage": {"id": "1746608040263_JIPkxLOiOrMcavtTItGBID0HMqH3", "senderId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "text": "kou", "timestamp": [Object]}, "hasLegacyMessages": false, "lastMessage": {"id": "1737359768014_ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "senderId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "text": "hotmails offer", "timestamp": [Object]}, "newCount": 16, "previousCount": 0}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": false, "isLoading": false, "postingDescription": "11111Searching for basic fishing equipment - rod, reel, and tackle box. Must be in good working condition. ", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "Fishing Gear11111", "timestamp": "2025-06-09T10:59:45.220Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T10:59:45.220Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offers22555", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "224555", "offerStatus": "pending", "restrictionEndDate": null, "userScore": undefined, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T10:59:45.225Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:45.261Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:45.262Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:45.262Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": true, "timestamp": "2025-06-09T10:59:45.262Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:45.262Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:45.263Z"}
 LOG  [OfferCache][GET] {"age": 89674, "found": true, "isExpired": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:45.263Z"}
 LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"cachedStatus": "pending", "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:45.263Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:45.263Z"}
 LOG  Marking messages as read for authorized user: {"isOfferOwner": true, "isPostingOwner": false, "userId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2"}
 LOG  Initial load, scrolling to end: 16
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": "hotmails offers22555", "hookPrice": 224555, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offers22555", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "224555", "offerStatus": "pending", "restrictionEndDate": null, "userScore": undefined, "usingHookData": true}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T10:59:45.265Z"}
 LOG  [OfferDetailsSection][OFFER_UPDATED] {"description": "hotmails offers22555", "offerId": "tK7kgW3yDfZoF3IWeedl", "price": 224555, "timestamp": "2025-06-09T10:59:45.268Z"}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": false, "isLoading": false, "postingDescription": "11111Searching for basic fishing equipment - rod, reel, and tackle box. Must be in good working condition. ", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "Fishing Gear11111", "timestamp": "2025-06-09T10:59:45.272Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T10:59:45.272Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offers22555", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "224555", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T10:59:45.276Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:45.297Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:45.298Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:45.298Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": true, "timestamp": "2025-06-09T10:59:45.298Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:45.298Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:45.298Z"}
 LOG  [OfferCache][GET] {"age": 89710, "found": true, "isExpired": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:45.299Z"}
 LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"cachedStatus": "pending", "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:45.299Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:45.299Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": "hotmails offers22555", "hookPrice": 224555, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offers22555", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "224555", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": true}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T10:59:45.301Z"}
 LOG  [OfferDetailsSection][OFFER_UPDATED] {"description": "hotmails offers22555", "offerId": "tK7kgW3yDfZoF3IWeedl", "price": 224555, "timestamp": "2025-06-09T10:59:45.306Z"}
 LOG  FlatList layout complete
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": false, "isLoading": false, "postingDescription": "11111Searching for basic fishing equipment - rod, reel, and tackle box. Must be in good working condition. ", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "Fishing Gear11111", "timestamp": "2025-06-09T10:59:45.313Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T10:59:45.314Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offers22555", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "224555", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T10:59:45.317Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:45.379Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:45.379Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:45.379Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": true, "timestamp": "2025-06-09T10:59:45.380Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:45.380Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:45.380Z"}
 LOG  [OfferCache][GET] {"age": 89792, "found": true, "isExpired": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:45.381Z"}
 LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"cachedStatus": "pending", "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:45.381Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:45.383Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": "hotmails offers22555", "hookPrice": 224555, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offers22555", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "224555", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": true}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T10:59:45.385Z"}
 LOG  [OfferDetailsSection][OFFER_UPDATED] {"description": "hotmails offers22555", "offerId": "tK7kgW3yDfZoF3IWeedl", "price": 224555, "timestamp": "2025-06-09T10:59:45.388Z"}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": false, "isLoading": false, "postingDescription": "11111Searching for basic fishing equipment - rod, reel, and tackle box. Must be in good working condition. ", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "Fishing Gear11111", "timestamp": "2025-06-09T10:59:45.424Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T10:59:45.424Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offers22555", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "224555", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T10:59:45.429Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:45.458Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:45.460Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:45.460Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": true, "timestamp": "2025-06-09T10:59:45.460Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:45.462Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:45.462Z"}
 LOG  [OfferCache][GET] {"age": 89873, "found": true, "isExpired": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:45.462Z"}
 LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"cachedStatus": "pending", "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:45.462Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:45.463Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": "hotmails offers22555", "hookPrice": 224555, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offers22555", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "224555", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": true}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T10:59:45.471Z"}
 LOG  [OfferDetailsSection][OFFER_UPDATED] {"description": "hotmails offers22555", "offerId": "tK7kgW3yDfZoF3IWeedl", "price": 224555, "timestamp": "2025-06-09T10:59:45.475Z"}
 LOG  Debounced region changed: {"latitude": 37.78602284508889, "latitudeDelta": 0.03954601933347135, "longitude": -122.43885855863637, "longitudeDelta": 0.03218629836729292}
 LOG  Fetching postings for new region
 LOG  [useControlledPostings] Fetching postings for region: {"latitude": 37.78602284508889, "latitudeDelta": 0.03954601933347135, "longitude": -122.43885855863637, "longitudeDelta": 0.03218629836729292}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": false, "isLoading": false, "postingDescription": "11111Searching for basic fishing equipment - rod, reel, and tackle box. Must be in good working condition. ", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "Fishing Gear11111", "timestamp": "2025-06-09T10:59:45.495Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T10:59:45.495Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offers22555", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "224555", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T10:59:45.499Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:45.530Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:45.530Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:45.530Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": true, "timestamp": "2025-06-09T10:59:45.531Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:45.531Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:45.531Z"}
 LOG  [OfferCache][GET] {"age": 89942, "found": true, "isExpired": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:45.531Z"}
 LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"cachedStatus": "pending", "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:45.532Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:59:45.532Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": "hotmails offers22555", "hookPrice": 224555, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offers22555", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "224555", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": true}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T10:59:45.534Z"}
 LOG  [OfferDetailsSection][OFFER_UPDATED] {"description": "hotmails offers22555", "offerId": "tK7kgW3yDfZoF3IWeedl", "price": 224555, "timestamp": "2025-06-09T10:59:45.538Z"}
 LOG  [Navigation] Rendering headerLeft for: PostingDetail
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Rendering headerLeft for: OfferDetail
 LOG  [Navigation] Can go back status: true
 LOG  [fetchPostingsBySearch] Query results: 10
 LOG  [useControlledPostings] Fetched postings: 10


 On simulator 2: offer owner clicks the chevron down button in posting details section to reveal the posting title and description and sees the fresh data:



 LOG  [PostingDetailsSection] Toggling details: {"current": false, "new": true, "timestamp": "2025-06-09T11:00:47.189Z"}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": true, "isLoading": false, "postingDescription": "11111Searching for basic fishing equipment - rod, reel, and tackle box. Must be in good working condition. ", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "Fishing Gear11111", "timestamp": "2025-06-09T11:00:47.192Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T11:00:47.192Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offers22555", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "224555", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T11:00:47.202Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:00:47.239Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:00:47.240Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:00:47.240Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": true, "timestamp": "2025-06-09T11:00:47.240Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:00:47.240Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:00:47.242Z"}
 LOG  [OfferCache][GET] {"age": 151653, "found": true, "isExpired": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:00:47.242Z"}
 LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"cachedStatus": "pending", "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:00:47.242Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:00:47.243Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": "hotmails offers22555", "hookPrice": 224555, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offers22555", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "224555", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": true}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T11:00:47.245Z"}
 LOG  [OfferDetailsSection][OFFER_UPDATED] {"description": "hotmails offers22555", "offerId": "tK7kgW3yDfZoF3IWeedl", "price": 224555, "timestamp": "2025-06-09T11:00:47.250Z"}
 LOG  FlatList layout complete
 LOG  Skipping load more: {"hasLastDoc": false, "hasMoreMessages": true, "lastDocId": undefined, "loadingMore": false}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": true, "isLoading": false, "postingDescription": "11111Searching for basic fishing equipment - rod, reel, and tackle box. Must be in good working condition. ", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "Fishing Gear11111", "timestamp": "2025-06-09T11:00:47.265Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T11:00:47.266Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offers22555", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "224555", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T11:00:47.275Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:00:47.308Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:00:47.309Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:00:47.309Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": true, "timestamp": "2025-06-09T11:00:47.309Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:00:47.309Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:00:47.318Z"}
 LOG  [OfferCache][GET] {"age": 151730, "found": true, "isExpired": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:00:47.319Z"}
 LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"cachedStatus": "pending", "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:00:47.319Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:00:47.319Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": "hotmails offers22555", "hookPrice": 224555, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offers22555", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "224555", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": true}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T11:00:47.321Z"}
 LOG  [OfferDetailsSection][OFFER_UPDATED] {"description": "hotmails offers22555", "offerId": "tK7kgW3yDfZoF3IWeedl", "price": 224555, "timestamp": "2025-06-09T11:00:47.341Z"}





on simulator 2: in home screen, offer owner clicks on the notification bell icon to navigate to the notification screen and then clicks on the notification to navigate to the posting detail screen:





 LOG  [NotificationsScreen][handleNotificationPress] Starting with: {"hasData": false, "notificationId": "s2B2al8JvdhB3OXIJqJy", "offerId": undefined, "stack": "Error
    at ?anon_0_ (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:272676:29)
    at next (native)
    at asyncGeneratorStep (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:5917:19)
    at _next (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:5931:29)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:5936:14)
    at tryCallTwo (/Users/<USER>/react-native/sdks/hermes/build_iphonesimulator/lib/InternalBytecode/InternalBytecode.js:61:9)
    at doResolve (/Users/<USER>/react-native/sdks/hermes/build_iphonesimulator/lib/InternalBytecode/InternalBytecode.js:216:25)
    at Promise (/Users/<USER>/react-native/sdks/hermes/build_iphonesimulator/lib/InternalBytecode/InternalBytecode.js:82:14)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:5928:25)
    at apply (native)
    at handleNotificationPress (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:272786:27)
    at onPress (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:272821:41)
    at _performTransitionSideEffects (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:79291:22)
    at _receiveSignal (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:79241:45)
    at onResponderRelease (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:79097:34)
    at apply (native)
    at invokeGuardedCallbackProd (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:79798:21)
    at apply (native)
    at invokeGuardedCallback (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:79974:42)
    at apply (native)
    at invokeGuardedCallbackAndCatchFirstError (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:79988:36)
    at executeDispatch (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:80065:48)
    at executeDispatchesInOrder (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:80087:26)
    at executeDispatchesAndRelease (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:81858:35)
    at executeDispatchesAndReleaseTopLevel (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:81865:43)
    at forEach (native)
    at forEachAccumulated (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:80679:22)
    at runEventsInBatch (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:81878:27)
    at runExtractedPluginEventsInBatch (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:81988:25)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:81959:42)
    at batchedUpdates$1 (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:95400:20)
    at batchedUpdates (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:81834:36)
    at _receiveRootNodeIDEvent (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:81958:23)
    at receiveTouches (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:82047:34)
    at apply (native)
    at __callFunction (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:3612:36)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:3334:31)
    at __guard (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:3551:15)
    at callFunctionReturnFlushedQueue (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:3333:21)", "type": "FAVORITE_POSTING_UPDATE"}
 LOG  [Navigation] Configuring screen: LoginScreen
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: Home
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: Notifications
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: PostingDetail
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Rendering headerLeft for: Notifications
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Rendering headerLeft for: PostingDetail
 LOG  [Navigation] Can go back status: true
 LOG  [BackButton] Rendering back button
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-09T11:02:11.059Z"}, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T11:02:11.059Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": false, "loading": true, "timestamp": "2025-06-09T11:02:11.060Z"}
 LOG  [usePostingDetails][Focus] Screen focused, setting up subscription
 LOG  [usePostingDetails][Setup] Creating references
 LOG  [usePostingDetails][Setup] Pre-snapshot listener setup: {"postingId": "Z7cW3OyoEsojfIreWcAD", "ref": "postings/Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T11:02:11.070Z"}
 LOG  [ListenerStateManager] Starting transition for postings_1749466931070_1749466931070: inactive -> initializing
 LOG  [ListenerRegistry][postings] Registered: {"collection": "postings", "id": "1749466931070", "registryId": "postings_1749466931070_1749466931070", "state": "active", "timestamp": "2025-06-09T11:02:11.070Z"}
 LOG  [ListenerStateManager] Completing transition for postings_1749466931070_1749466931070
 LOG  [ListenerRegistry][Component] Registered listener with component: {"componentId": "PostingDetail_Z7cW3OyoEsojfIreWcAD", "listenerId": "postings_1749466931070_1749466931070", "timestamp": "2025-06-09T11:02:11.072Z"}
 LOG  [ListenerRegistry][postings] Created: {"componentId": "PostingDetail_Z7cW3OyoEsojfIreWcAD", "listenerId": "postings_1749466931070", "timestamp": "2025-06-09T11:02:11.072Z"}
 LOG  [useOffers][Focus] Screen focused, setting up subscription
 LOG  [useOffers][Subscribe] Setting up subscription {"postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T11:02:11.072Z"}
 LOG  [ListenerStateManager] Starting transition for offers_1749466931073_1749466931073: inactive -> initializing
 LOG  [ListenerRegistry][offers] Registered: {"collection": "offers", "id": "1749466931073", "registryId": "offers_1749466931073_1749466931073", "state": "active", "timestamp": "2025-06-09T11:02:11.073Z"}
 LOG  [ListenerStateManager] Completing transition for offers_1749466931073_1749466931073
 LOG  [ListenerRegistry][Component] Registered listener with component: {"componentId": "PostingDetail_Z7cW3OyoEsojfIreWcAD", "listenerId": "offers_1749466931073_1749466931073", "timestamp": "2025-06-09T11:02:11.073Z"}
 LOG  [ListenerRegistry][offers] Created: {"componentId": "PostingDetail_Z7cW3OyoEsojfIreWcAD", "listenerId": "offers_1749466931073", "timestamp": "2025-06-09T11:02:11.073Z"}
 LOG  [useFavorites][Focus] Screen focused, fetching favorite status
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 14, "timestamp": "2025-06-09T11:02:11.073Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 15, "timestamp": "2025-06-09T11:02:11.074Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749466931074", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749466931074", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_Z7cW3OyoEsojfIreWcAD_1749466931074", "postingSubId": "posting_Z7cW3OyoEsojfIreWcAD_1749466931074", "totalTime": 15}
 LOG  [PostingDetail][StateSync] Checking state update conditions: {"currentUser": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hasDetails": false, "offersCount": 0, "postingLoading": true, "timestamp": "2025-06-09T11:02:11.074Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateVerification] {"auth": {"currentUser": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "expectedOwner": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "posting": {"id": "Z7cW3OyoEsojfIreWcAD", "loaded": false}, "state": {"activeTab": "active", "currentUserId": null, "isDescriptionExpanded": false, "isOwner": false, "lastUpdate": 1749466931059, "offersCount": 0, "postingDetailsId": "", "userOffer": null}, "timestamp": "2025-06-09T11:02:11.074Z"}
 LOG  [PostingDetail][StateDebug] {"auth": {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2"}, "performance": {"timeSinceLastUpdate": 16}, "props": {"postingId": "Z7cW3OyoEsojfIreWcAD", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "state": {"activeTab": "active", "currentUserId": null, "isDescriptionExpanded": false, "isOwner": false, "lastUpdate": 1749466931059, "offersCount": 0, "postingDetailsId": "", "userOffer": null}, "subscriptions": {"metrics": {"lastUpdate": "2025-06-09T11:02:11.059Z", "subscriptionCount": 0, "totalTime": 16}, "types": []}, "timestamp": "2025-06-09T11:02:11.075Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-09T11:02:11.059Z"}, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T11:02:11.076Z"}
 LOG  [usePostingDetails][Reducer] Processing action: {"currentState": {"error": null, "loading": true, "postingDetails": null}, "metrics": {"timeSinceStart": 18, "timestamp": "2025-06-09T11:02:11.077Z", "updateCount": 1}, "type": "FETCH_START"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": false, "loading": true, "timestamp": "2025-06-09T11:02:11.077Z"}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_Z7cW3OyoEsojfIreWcAD_1749466931074", "offers_Z7cW3OyoEsojfIreWcAD_1749466931074"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749466931074"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749466931074"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-09T11:02:11.079Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 20, "timestamp": "2025-06-09T11:02:11.079Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 20, "timestamp": "2025-06-09T11:02:11.079Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749466931079", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749466931079", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_Z7cW3OyoEsojfIreWcAD_1749466931079", "postingSubId": "posting_Z7cW3OyoEsojfIreWcAD_1749466931079", "totalTime": 20}
 LOG  [Navigation] Rendering headerLeft for: PostingDetail
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Rendering headerLeft for: Notifications
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Rendering headerLeft for: PostingDetail
 LOG  [Navigation] Can go back status: true
 LOG  Region changing: {"latitude": 37.78602284508889, "latitudeDelta": 0.03954601933347135, "longitude": -122.43885855863637, "longitudeDelta": 0.03218629836729292}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-09T11:02:11.059Z"}, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T11:02:11.185Z"}
 LOG  [usePostingDetails][Reducer] Processing action: {"currentState": {"error": null, "loading": true, "postingDetails": null}, "metrics": {"timeSinceStart": 126, "timestamp": "2025-06-09T11:02:11.185Z", "updateCount": 2}, "type": "UPDATE_POSTING"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-09T11:02:11.186Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": null, "isFavorite": false, "isOwner": false, "postingId": "", "timestamp": "2025-06-09T11:02:11.186Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_Z7cW3OyoEsojfIreWcAD_1749466931079", "offers_Z7cW3OyoEsojfIreWcAD_1749466931079"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749466931079"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749466931079"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-09T11:02:11.199Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 140, "timestamp": "2025-06-09T11:02:11.199Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 141, "timestamp": "2025-06-09T11:02:11.200Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749466931201", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749466931201", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_Z7cW3OyoEsojfIreWcAD_1749466931201", "postingSubId": "posting_Z7cW3OyoEsojfIreWcAD_1749466931201", "totalTime": 142}
 LOG  [PostingDetail][StateSync] Checking state update conditions: {"currentUser": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hasDetails": true, "offersCount": 0, "postingLoading": false, "timestamp": "2025-06-09T11:02:11.201Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Updating state with: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isOwnerCheck": false, "offersCount": 0, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T11:02:11.201Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Processing offers: {"count": 0, "timestamp": "2025-06-09T11:02:11.202Z", "userOffers": 0}
 LOG  [PostingDetail][StateSync] State updated: {"hasUserOffer": false, "isOwner": false, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T11:02:11.202Z"}
 LOG  [PostingDetail][StateVerification] {"auth": {"currentUser": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "expectedOwner": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "posting": {"id": "Z7cW3OyoEsojfIreWcAD", "loaded": true}, "state": {"activeTab": "active", "currentUserId": null, "isDescriptionExpanded": false, "isOwner": false, "lastUpdate": 1749466931059, "offersCount": 0, "postingDetailsId": "", "userOffer": null}, "timestamp": "2025-06-09T11:02:11.202Z"}
 LOG  [PostingDetail] Posting details loaded for edit: {"hasDetails": true, "hasLocation": false, "isOwner": false, "timestamp": "2025-06-09T11:02:11.202Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-09T11:02:11.059Z"}, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T11:02:11.203Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-09T11:02:11.203Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isFavorite": false, "isOwner": false, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T11:02:11.204Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_Z7cW3OyoEsojfIreWcAD_1749466931201", "offers_Z7cW3OyoEsojfIreWcAD_1749466931201"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749466931201"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749466931201"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-09T11:02:11.211Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 152, "timestamp": "2025-06-09T11:02:11.211Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 152, "timestamp": "2025-06-09T11:02:11.211Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749466931211", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749466931211", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_Z7cW3OyoEsojfIreWcAD_1749466931211", "postingSubId": "posting_Z7cW3OyoEsojfIreWcAD_1749466931211", "totalTime": 153}
 LOG  [PostingDetail][StateVerification] {"auth": {"currentUser": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "expectedOwner": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "posting": {"id": "Z7cW3OyoEsojfIreWcAD", "loaded": true}, "state": {"activeTab": "active", "currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDescriptionExpanded": false, "isOwner": false, "lastUpdate": 1749466931203, "offersCount": 0, "postingDetailsId": "Z7cW3OyoEsojfIreWcAD", "userOffer": null}, "timestamp": "2025-06-09T11:02:11.212Z"}
 LOG  [PostingDetail][StateDebug] {"auth": {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2"}, "performance": {"timeSinceLastUpdate": 9}, "props": {"postingId": "Z7cW3OyoEsojfIreWcAD", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "state": {"activeTab": "active", "currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDescriptionExpanded": false, "isOwner": false, "lastUpdate": 1749466931203, "offersCount": 0, "postingDetailsId": "Z7cW3OyoEsojfIreWcAD", "userOffer": null}, "subscriptions": {"metrics": {"lastUpdate": "2025-06-09T11:02:11.059Z", "subscriptionCount": 0, "totalTime": 153}, "types": []}, "timestamp": "2025-06-09T11:02:11.212Z"}
 LOG  [useOffers][Update] {"changes": [{"id": "IgyIrlkc18WdziXD3nEO", "timestamp": "2025-06-09T11:02:11.279Z", "type": "added"}, {"id": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:02:11.279Z", "type": "added"}], "count": 2, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T11:02:11.279Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-09T11:02:11.059Z"}, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T11:02:11.281Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-09T11:02:11.282Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isFavorite": false, "isOwner": false, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T11:02:11.282Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_Z7cW3OyoEsojfIreWcAD_1749466931211", "offers_Z7cW3OyoEsojfIreWcAD_1749466931211"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749466931211"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749466931211"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-09T11:02:11.291Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 232, "timestamp": "2025-06-09T11:02:11.291Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 232, "timestamp": "2025-06-09T11:02:11.291Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749466931291", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749466931291", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_Z7cW3OyoEsojfIreWcAD_1749466931291", "postingSubId": "posting_Z7cW3OyoEsojfIreWcAD_1749466931291", "totalTime": 232}
 LOG  [PostingDetail][StateSync] Checking state update conditions: {"currentUser": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hasDetails": true, "offersCount": 2, "postingLoading": false, "timestamp": "2025-06-09T11:02:11.291Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Updating state with: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isOwnerCheck": false, "offersCount": 2, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T11:02:11.292Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Processing offers: {"count": 2, "timestamp": "2025-06-09T11:02:11.292Z", "userOffers": 1}
 LOG  [PostingDetail][StateSync] State updated: {"hasUserOffer": true, "isOwner": false, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T11:02:11.292Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-09T11:02:11.059Z"}, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T11:02:11.293Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-09T11:02:11.294Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isFavorite": false, "isOwner": false, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T11:02:11.295Z", "userOffer": {"id": "tK7kgW3yDfZoF3IWeedl", "status": "pending"}}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_Z7cW3OyoEsojfIreWcAD_1749466931291", "offers_Z7cW3OyoEsojfIreWcAD_1749466931291"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749466931291"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749466931291"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-09T11:02:11.304Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 245, "timestamp": "2025-06-09T11:02:11.304Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 245, "timestamp": "2025-06-09T11:02:11.304Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749466931304", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749466931304", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_Z7cW3OyoEsojfIreWcAD_1749466931304", "postingSubId": "posting_Z7cW3OyoEsojfIreWcAD_1749466931304", "totalTime": 245}
 LOG  [PostingDetail][StateVerification] {"auth": {"currentUser": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "expectedOwner": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "posting": {"id": "Z7cW3OyoEsojfIreWcAD", "loaded": true}, "state": {"activeTab": "active", "currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDescriptionExpanded": false, "isOwner": false, "lastUpdate": 1749466931293, "offersCount": 2, "postingDetailsId": "Z7cW3OyoEsojfIreWcAD", "userOffer": {"id": "tK7kgW3yDfZoF3IWeedl", "status": "pending"}}, "timestamp": "2025-06-09T11:02:11.304Z"}
 LOG  [PostingDetail][StateDebug] {"auth": {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2"}, "performance": {"timeSinceLastUpdate": 12}, "props": {"postingId": "Z7cW3OyoEsojfIreWcAD", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "state": {"activeTab": "active", "currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDescriptionExpanded": false, "isOwner": false, "lastUpdate": 1749466931293, "offersCount": 2, "postingDetailsId": "Z7cW3OyoEsojfIreWcAD", "userOffer": {"id": "tK7kgW3yDfZoF3IWeedl", "status": "pending"}}, "subscriptions": {"metrics": {"lastUpdate": "2025-06-09T11:02:11.059Z", "subscriptionCount": 0, "totalTime": 246}, "types": []}, "timestamp": "2025-06-09T11:02:11.305Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-09T11:02:11.059Z"}, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T11:02:11.307Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-09T11:02:11.308Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isFavorite": true, "isOwner": false, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T11:02:11.308Z", "userOffer": {"id": "tK7kgW3yDfZoF3IWeedl", "status": "pending"}}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_Z7cW3OyoEsojfIreWcAD_1749466931304", "offers_Z7cW3OyoEsojfIreWcAD_1749466931304"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749466931304"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749466931304"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-09T11:02:11.316Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 258, "timestamp": "2025-06-09T11:02:11.317Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 258, "timestamp": "2025-06-09T11:02:11.317Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749466931317", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749466931317", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_Z7cW3OyoEsojfIreWcAD_1749466931317", "postingSubId": "posting_Z7cW3OyoEsojfIreWcAD_1749466931317", "totalTime": 258}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-09T11:02:11.059Z"}, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T11:02:11.318Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-09T11:02:11.318Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isFavorite": true, "isOwner": false, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T11:02:11.318Z", "userOffer": {"id": "tK7kgW3yDfZoF3IWeedl", "status": "pending"}}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_Z7cW3OyoEsojfIreWcAD_1749466931317", "offers_Z7cW3OyoEsojfIreWcAD_1749466931317"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749466931317"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749466931317"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-09T11:02:11.325Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 266, "timestamp": "2025-06-09T11:02:11.325Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 266, "timestamp": "2025-06-09T11:02:11.325Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749466931325", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749466931325", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_Z7cW3OyoEsojfIreWcAD_1749466931325", "postingSubId": "posting_Z7cW3OyoEsojfIreWcAD_1749466931325", "totalTime": 267}
 LOG  Debounced region changed: {"latitude": 37.78602284508889, "latitudeDelta": 0.03954601933347135, "longitude": -122.43885855863637, "longitudeDelta": 0.03218629836729292}
 LOG  Fetching postings for new region
 LOG  [useControlledPostings] Fetching postings for region: {"latitude": 37.78602284508889, "latitudeDelta": 0.03954601933347135, "longitude": -122.43885855863637, "longitudeDelta": 0.03218629836729292}
 LOG  [Navigation] Rendering headerLeft for: Notifications
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Rendering headerLeft for: PostingDetail
 LOG  [Navigation] Can go back status: true
 LOG  [fetchPostingsBySearch] Query results: 10
 LOG  [useControlledPostings] Fetched postings: 10





on simulator 2: offer owner clicks on the offer in posting detail screen to navigate to the offer detail screen:





 LOG  [PostingDetail][Navigation] Attempting safe navigation
 LOG  [PostingDetail][Navigation] Checking listeners: {"activeCount": 2, "byCollection": {"offers": 1, "postings": 1}, "timestamp": "2025-06-09T11:03:40.106Z"}
 LOG  [PostingDetail][Navigation] Cleaning up hooks
 LOG  [PostingDetail] Unsubscribing from posting
 LOG  [usePostingDetails][Unsubscribe] Cleaning up listener: {"listenerId": "postings_1749466931070_1749466931070", "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T11:03:40.108Z"}
 LOG  [ListenerRegistry][Component] Unregistered listener from component: {"componentId": "PostingDetail_Z7cW3OyoEsojfIreWcAD", "listenerId": "postings_1749466931070_1749466931070", "timestamp": "2025-06-09T11:03:40.108Z"}
 LOG  [PostingDetail] Successfully unsubscribed from posting
 LOG  [PostingDetail] Unsubscribing from offers
 LOG  [useOffers][Unsubscribe] Cleaning up subscription: {"postingId": "Z7cW3OyoEsojfIreWcAD", "subscriptionId": "offers_1749466931073_1749466931073", "timestamp": "2025-06-09T11:03:40.111Z"}
 LOG  [ListenerRegistry][Component] Unregistered listener from component: {"componentId": "PostingDetail_Z7cW3OyoEsojfIreWcAD", "listenerId": "offers_1749466931073_1749466931073", "timestamp": "2025-06-09T11:03:40.111Z"}
 LOG  [PostingDetail] Successfully unsubscribed from offers
 LOG  [ListenerRegistry] Cleanup metrics: {"groupId": null, "metrics": {"duration": 6, "startTime": 1749467020109, "success": true}, "subscriptionId": "postings_1749466931070_1749466931070", "timestamp": "2025-06-09T11:03:40.115Z"}
 LOG  [ListenerRegistry] Cleanup metrics: {"groupId": null, "metrics": {"duration": 7, "startTime": 1749467020112, "success": true}, "subscriptionId": "offers_1749466931073_1749466931073", "timestamp": "2025-06-09T11:03:40.119Z"}
 LOG  [PostingDetail][Navigation] Post-cleanup verification: {"activeListeners": 0, "byCollection": {"offers": 0, "postings": 0}, "timestamp": "2025-06-09T11:03:40.220Z"}
 LOG  [PostingDetail][Navigation] All listeners cleaned up successfully
 LOG  [Navigation] Configuring screen: LoginScreen
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: Home
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: Notifications
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: PostingDetail
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: OfferDetail
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Rendering headerLeft for: Notifications
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Rendering headerLeft for: PostingDetail
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Rendering headerLeft for: OfferDetail
 LOG  [Navigation] Can go back status: true
 LOG  [BackButton] Rendering back button
 LOG  [OfferDetail][RELIABLE_POSTING_ID_DEBUG] {"foundId": "Z7cW3OyoEsojfIreWcAD", "sources": {"initialOfferPostingId": undefined, "initialPostingId": undefined, "offerDetailsPostingId": undefined, "offerDetailsPostingPath": undefined, "routePostingId": "Z7cW3OyoEsojfIreWcAD"}, "timestamp": "2025-06-09T11:03:40.302Z"}
 LOG  [OfferDetail][STATUS_TRACKING] {"hasInitialOffer": false, "hasOfferDetails": false, "initialOfferStatus": undefined, "offerId": "tK7kgW3yDfZoF3IWeedl", "offerStatus": "active", "postingStatus": "Active", "timestamp": "2025-06-09T11:03:40.302Z"}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": false, "isLoading": true, "postingDescription": "", "postingOwnerId": null, "postingStatus": "Active", "postingTitle": "", "timestamp": "2025-06-09T11:03:40.303Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": null, "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T11:03:40.304Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": true, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "", "offerOwnerId": null, "offerPrice": "0", "offerStatus": "active", "restrictionEndDate": null, "userScore": undefined, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": false, "offerOwnerId": null, "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T11:03:40.311Z"}
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": null, "shouldShowAbusiveButton": false, "timestamp": "2025-06-09T11:03:40.323Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.323Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.325Z"}
 LOG  [OfferCache][GET] {"age": 324736, "found": true, "isExpired": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.325Z"}
 LOG  [useOfferDetails][FETCHING_DATA] {"fetchCount": 1, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.325Z"}
 LOG  [OfferCache][GET] {"age": 324736, "found": true, "isExpired": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.325Z"}
 LOG  [useOfferDetails][FETCH_OFFER_START] {"forceRefresh": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.325Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.326Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.326Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.326Z"}
 LOG  [OfferCache][GET] {"age": 324737, "found": true, "isExpired": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.326Z"}
 LOG  [useOfferDetails][FETCHING_DATA] {"fetchCount": 1, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.326Z"}
 LOG  [OfferCache][GET] {"age": 324737, "found": true, "isExpired": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.326Z"}
 LOG  [useOfferDetails][FETCH_OFFER_START] {"forceRefresh": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.327Z"}
 LOG  [useAuthUser] Setting up auth state listener {"currentUser": {"_redirectEventId": undefined, "apiKey": "AIzaSyBOHewmZjlRWGu1jeEI-uBSkAn56xLMlec", "appName": "[DEFAULT]", "createdAt": "1731681044939", "displayName": undefined, "email": "<EMAIL>", "emailVerified": false, "isAnonymous": false, "lastLoginAt": "1749466083404", "phoneNumber": undefined, "photoURL": undefined, "providerData": [Array], "stsTokenManager": [Object], "tenantId": undefined, "uid": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2"}, "hasAuth": true, "hasOnAuthStateChanged": true}
 LOG  [OfferDetail][MOUNT] {"cleanupStarted": {"isCleaningUp": false, "isMounted": true}, "lastAction": "MOUNT", "mountCount": 0, "params": {"offerId": "tK7kgW3yDfZoF3IWeedl", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingId": "Z7cW3OyoEsojfIreWcAD", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "timestamp": "2025-06-09T11:03:40.334Z"}
 LOG  [Messages Subscription] Setting up subscription for offerId: tK7kgW3yDfZoF3IWeedl
 LOG  === OfferDetail Screen Mounted ===
 LOG  Route params: {"offerId": "tK7kgW3yDfZoF3IWeedl", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingId": "Z7cW3OyoEsojfIreWcAD", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  Setting owner IDs from route params: {"offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [OfferDetail][MOUNT] {"cleanupStarted": {"isCleaningUp": false, "isMounted": true}, "lastAction": "MOUNT", "mountCount": 1, "params": {"offerId": "tK7kgW3yDfZoF3IWeedl", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingId": "Z7cW3OyoEsojfIreWcAD", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "timestamp": "2025-06-09T11:03:40.335Z"}
 LOG  Authenticated user: ybEyGDxbvleCEXdTvrA2CgVTJ1y2
 LOG  [OfferDetail] Initializing with params: {"hasInitialOffer": false, "hasInitialPosting": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.335Z"}
 LOG  [OfferDetail] Setting postingOwnerId from route params: {"postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [OfferDetail] Setting offerOwnerId from route params: {"offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2"}
 LOG  OfferDetail refresh button states: {"showOfferRefresh": false, "showOfferRefreshExplicit": false, "showPostingRefresh": false, "showPostingRefreshExplicit": false, "timestamp": "2025-06-09T11:03:40.336Z"}
 LOG  [OfferDetail][NOTIFICATION_LISTENER_SETUP] {"finalPostingId": "Z7cW3OyoEsojfIreWcAD", "hasPostingData": false, "initialPostingId": undefined, "offerDetailsPostingId": undefined, "offerId": "tK7kgW3yDfZoF3IWeedl", "postingId": "Z7cW3OyoEsojfIreWcAD", "reliablePostingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T11:03:40.336Z"}
 LOG  [OfferDetail][NOTIFICATION_DB_LISTENER_SETUP] {"offerId": "tK7kgW3yDfZoF3IWeedl", "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T11:03:40.336Z", "userId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2"}
 LOG  [usePostingDetails][Focus] Screen unfocused, cleaning up
 LOG  [ListenerRegistry][Component] Unregistered listener from component: {"componentId": "PostingDetail_Z7cW3OyoEsojfIreWcAD", "listenerId": "postings_1749466931070_1749466931070", "timestamp": "2025-06-09T11:03:40.337Z"}
 LOG  [ListenerRegistry] No entry found for postings_1749466931070_1749466931070
 LOG  [useOffers][Focus] Screen unfocused, cleaning up
 LOG  [ListenerRegistry][Component] Unregistered listener from component: {"componentId": "PostingDetail_Z7cW3OyoEsojfIreWcAD", "listenerId": "offers_1749466931073_1749466931073", "timestamp": "2025-06-09T11:03:40.337Z"}
 LOG  [ListenerRegistry] No entry found for offers_1749466931073_1749466931073
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": false, "isLoading": true, "postingDescription": "", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "", "timestamp": "2025-06-09T11:03:40.339Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T11:03:40.340Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": true, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "0", "offerStatus": "active", "restrictionEndDate": null, "userScore": undefined, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T11:03:40.343Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.353Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.353Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.353Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": true, "timestamp": "2025-06-09T11:03:40.354Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.354Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.371Z"}
 LOG  [OfferCache][GET] {"age": 324783, "found": true, "isExpired": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.372Z"}
 LOG  [useOfferDetails][FETCHING_DATA] {"fetchCount": 1, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.372Z"}
 LOG  [OfferCache][GET] {"age": 324783, "found": true, "isExpired": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.372Z"}
 LOG  [useOfferDetails][FETCH_OFFER_START] {"forceRefresh": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.372Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.372Z"}
 LOG  [useAuthUser] Auth state changed {"user": {"_redirectEventId": undefined, "apiKey": "AIzaSyBOHewmZjlRWGu1jeEI-uBSkAn56xLMlec", "appName": "[DEFAULT]", "createdAt": "1731681044939", "displayName": undefined, "email": "<EMAIL>", "emailVerified": false, "isAnonymous": false, "lastLoginAt": "1749466083404", "phoneNumber": undefined, "photoURL": undefined, "providerData": [Array], "stsTokenManager": [Object], "tenantId": undefined, "uid": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2"}}
 LOG  [OfferDetail][NOTIFICATION_DB_UPDATE] {"count": 0, "timestamp": "2025-06-09T11:03:40.382Z"}
 LOG  [Navigation] Rendering headerLeft for: OfferDetail
 LOG  [Navigation] Can go back status: true
 LOG  [Messages Subscription] Received snapshot update: {"empty": false, "metadata": {"fromCache": false, "hasPendingWrites": false}, "size": 1}
 LOG  [Messages Subscription] Processing discussion: {"id": "uFPwMJYrZ5wEt4cqPi35", "messageCount": 16}
 LOG  [Messages Subscription] Raw messages: {"count": 16, "messageKeys": ["text", "read", "timestamp", "senderId", "id", "readAt"], "sampleMessage": {"id": "1737359768014_ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "read": true, "readAt": [Object], "senderId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "text": "hotmails offer", "timestamp": [Object]}}
 LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["text", "read", "timestamp", "senderId", "id", "readAt"], "timestampType": "object"}
 LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["senderId", "read", "text", "readAt", "timestamp", "id"], "timestampType": "object"}
 LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["senderId", "text", "read", "timestamp", "id", "readAt"], "timestampType": "object"}
 LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["id", "timestamp", "text", "read", "senderId", "readAt"], "timestampType": "object"}
 LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["read", "id", "text", "timestamp", "senderId", "readAt"], "timestampType": "object"}
 LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["text", "readAt", "timestamp", "id", "senderId", "read"], "timestampType": "object"}
 LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["readAt", "id", "text", "senderId", "timestamp", "read"], "timestampType": "object"}
 LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["read", "timestamp", "id", "readAt", "senderId", "text"], "timestampType": "object"}
 LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["text", "readAt", "read", "id", "senderId", "timestamp"], "timestampType": "object"}
 LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["text", "readAt", "senderId", "read", "id", "timestamp"], "timestampType": "object"}
 LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["timestamp", "read", "id", "text", "readAt", "senderId"], "timestampType": "object"}
 LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["id", "readAt", "senderId", "text", "read", "timestamp"], "timestampType": "object"}
 LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["id", "senderId", "timestamp", "readAt", "read", "text"], "timestampType": "object"}
 LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["senderId", "timestamp", "text", "read", "id", "readAt"], "timestampType": "object"}
 LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["readAt", "id", "read", "senderId", "text", "timestamp"], "timestampType": "object"}
 LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["readAt", "senderId", "read", "timestamp", "text", "id"], "timestampType": "object"}
 LOG  [Messages Subscription] Validation summary: {"firstValidMessage": {"id": "1737359768014_ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "senderId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "timestamp": [Object]}, "invalidMessages": 0, "totalMessages": 16, "validMessages": 16}
 LOG  [Messages Subscription] Processing messages: {"hasLegacyMessages": false, "messageTypes": [{"hasId": true, "hasTimestamp": true, "timestampType": "object"}, {"hasId": true, "hasTimestamp": true, "timestampType": "object"}, {"hasId": true, "hasTimestamp": true, "timestampType": "object"}, {"hasId": true, "hasTimestamp": true, "timestampType": "object"}, {"hasId": true, "hasTimestamp": true, "timestampType": "object"}, {"hasId": true, "hasTimestamp": true, "timestampType": "object"}, {"hasId": true, "hasTimestamp": true, "timestampType": "object"}, {"hasId": true, "hasTimestamp": true, "timestampType": "object"}, {"hasId": true, "hasTimestamp": true, "timestampType": "object"}, {"hasId": true, "hasTimestamp": true, "timestampType": "object"}, {"hasId": true, "hasTimestamp": true, "timestampType": "object"}, {"hasId": true, "hasTimestamp": true, "timestampType": "object"}, {"hasId": true, "hasTimestamp": true, "timestampType": "object"}, {"hasId": true, "hasTimestamp": true, "timestampType": "object"}, {"hasId": true, "hasTimestamp": true, "timestampType": "object"}, {"hasId": true, "hasTimestamp": true, "timestampType": "object"}], "newCount": 16, "prevCount": 0}
 LOG  [Messages Subscription] Updated messages state: {"firstMessage": {"id": "1746608040263_JIPkxLOiOrMcavtTItGBID0HMqH3", "senderId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "text": "kou", "timestamp": [Object]}, "hasLegacyMessages": false, "lastMessage": {"id": "1737359768014_ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "senderId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "text": "hotmails offer", "timestamp": [Object]}, "newCount": 16, "previousCount": 0}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": false, "isLoading": true, "postingDescription": "", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "", "timestamp": "2025-06-09T11:03:40.507Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T11:03:40.508Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": true, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "0", "offerStatus": "active", "restrictionEndDate": null, "userScore": undefined, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T11:03:40.512Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.548Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.548Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.548Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": true, "timestamp": "2025-06-09T11:03:40.549Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.549Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.549Z"}
 LOG  [OfferCache][GET] {"age": 324960, "found": true, "isExpired": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.549Z"}
 LOG  [useOfferDetails][FETCHING_DATA] {"fetchCount": 1, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.550Z"}
 LOG  [OfferCache][GET] {"age": 324961, "found": true, "isExpired": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.550Z"}
 LOG  [useOfferDetails][FETCH_OFFER_START] {"forceRefresh": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.551Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.551Z"}
 LOG  Marking messages as read for authorized user: {"isOfferOwner": true, "isPostingOwner": false, "userId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2"}
 LOG  Initial load, scrolling to end: 16
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": false, "isLoading": true, "postingDescription": "", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "", "timestamp": "2025-06-09T11:03:40.554Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T11:03:40.555Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": true, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "0", "offerStatus": "active", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T11:03:40.558Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.581Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.581Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.581Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": true, "timestamp": "2025-06-09T11:03:40.582Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.582Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.582Z"}
 LOG  [OfferCache][GET] {"age": 324993, "found": true, "isExpired": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.582Z"}
 LOG  [useOfferDetails][FETCHING_DATA] {"fetchCount": 1, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.582Z"}
 LOG  [OfferCache][GET] {"age": 324993, "found": true, "isExpired": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.582Z"}
 LOG  [useOfferDetails][FETCH_OFFER_START] {"forceRefresh": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.583Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.583Z"}
 LOG  [OfferCache][SET] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.594Z"}
 LOG  [useOfferDetails][FETCH_OFFER_SUCCESS] {"offerId": "tK7kgW3yDfZoF3IWeedl", "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T11:03:40.594Z"}
 LOG  [OfferCache][SET] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.595Z"}
 LOG  [useOfferDetails][FETCH_OFFER_SUCCESS] {"offerId": "tK7kgW3yDfZoF3IWeedl", "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T11:03:40.595Z"}
 LOG  [OfferDetail][STATUS_TRACKING] {"hasInitialOffer": false, "hasOfferDetails": true, "initialOfferStatus": undefined, "offerId": "tK7kgW3yDfZoF3IWeedl", "offerStatus": "pending", "postingStatus": "Active", "timestamp": "2025-06-09T11:03:40.596Z"}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": false, "isLoading": true, "postingDescription": "11111Searching for basic fishing equipment - rod, reel, and tackle box. Must be in good working condition. ", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "Fishing Gear11111", "timestamp": "2025-06-09T11:03:40.596Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T11:03:40.597Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": true, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offers22555", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "224555", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T11:03:40.600Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.620Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.620Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.620Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": true, "timestamp": "2025-06-09T11:03:40.621Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.621Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.626Z"}
 LOG  [OfferCache][GET] {"age": 31, "found": true, "isExpired": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.626Z"}
 LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"cachedStatus": "pending", "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.627Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.627Z"}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": false, "isLoading": true, "postingDescription": "11111Searching for basic fishing equipment - rod, reel, and tackle box. Must be in good working condition. ", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "Fishing Gear11111", "timestamp": "2025-06-09T11:03:40.628Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T11:03:40.630Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": true, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offers22555", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "224555", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T11:03:40.633Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.653Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.654Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.654Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": true, "timestamp": "2025-06-09T11:03:40.654Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.654Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.654Z"}
 LOG  [OfferCache][GET] {"age": 59, "found": true, "isExpired": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.654Z"}
 LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"cachedStatus": "pending", "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.655Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.655Z"}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": false, "isLoading": false, "postingDescription": "11111Searching for basic fishing equipment - rod, reel, and tackle box. Must be in good working condition. ", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "Fishing Gear11111", "timestamp": "2025-06-09T11:03:40.656Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T11:03:40.657Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offers22555", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "224555", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T11:03:40.668Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.688Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.689Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.689Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": true, "timestamp": "2025-06-09T11:03:40.689Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.689Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.689Z"}
 LOG  [OfferCache][GET] {"age": 95, "found": true, "isExpired": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.690Z"}
 LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"cachedStatus": "pending", "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.690Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.690Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": "hotmails offers22555", "hookPrice": 224555, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offers22555", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "224555", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": true}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T11:03:40.691Z"}
 LOG  [OfferDetailsSection][OFFER_UPDATED] {"description": "hotmails offers22555", "offerId": "tK7kgW3yDfZoF3IWeedl", "price": 224555, "timestamp": "2025-06-09T11:03:40.694Z"}
 LOG  [OfferCache][SET] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.696Z"}
 LOG  [useOfferDetails][FETCH_OFFER_SUCCESS] {"offerId": "tK7kgW3yDfZoF3IWeedl", "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T11:03:40.696Z"}
 LOG  FlatList layout complete
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": false, "isLoading": false, "postingDescription": "11111Searching for basic fishing equipment - rod, reel, and tackle box. Must be in good working condition. ", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "Fishing Gear11111", "timestamp": "2025-06-09T11:03:40.709Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T11:03:40.710Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offers22555", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "224555", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T11:03:40.715Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.750Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.750Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.750Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": true, "timestamp": "2025-06-09T11:03:40.751Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.751Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.754Z"}
 LOG  [OfferCache][GET] {"age": 58, "found": true, "isExpired": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.754Z"}
 LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"cachedStatus": "pending", "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.754Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.755Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": "hotmails offers22555", "hookPrice": 224555, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offers22555", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "224555", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": true}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T11:03:40.756Z"}
 LOG  [OfferDetailsSection][OFFER_UPDATED] {"description": "hotmails offers22555", "offerId": "tK7kgW3yDfZoF3IWeedl", "price": 224555, "timestamp": "2025-06-09T11:03:40.759Z"}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": false, "isLoading": false, "postingDescription": "11111Searching for basic fishing equipment - rod, reel, and tackle box. Must be in good working condition. ", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "Fishing Gear11111", "timestamp": "2025-06-09T11:03:40.872Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T11:03:40.874Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offers22555", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "224555", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T11:03:40.880Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.915Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.915Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.915Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": true, "timestamp": "2025-06-09T11:03:40.915Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.916Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.916Z"}
 LOG  [OfferCache][GET] {"age": 220, "found": true, "isExpired": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.916Z"}
 LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"cachedStatus": "pending", "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.916Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.916Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": "hotmails offers22555", "hookPrice": 224555, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offers22555", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "224555", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": true}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T11:03:40.918Z"}
 LOG  [OfferDetailsSection][OFFER_UPDATED] {"description": "hotmails offers22555", "offerId": "tK7kgW3yDfZoF3IWeedl", "price": 224555, "timestamp": "2025-06-09T11:03:40.921Z"}
 LOG  [Navigation] Rendering headerLeft for: Notifications
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Rendering headerLeft for: PostingDetail
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Rendering headerLeft for: OfferDetail
 LOG  [Navigation] Can go back status: true
 LOG  [OfferCache][SET] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.950Z"}
 LOG  [useOfferDetails][FETCH_OFFER_SUCCESS] {"offerId": "tK7kgW3yDfZoF3IWeedl", "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T11:03:40.950Z"}
 LOG  [OfferCache][SET] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.951Z"}
 LOG  [useOfferDetails][FETCH_OFFER_SUCCESS] {"offerId": "tK7kgW3yDfZoF3IWeedl", "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T11:03:40.951Z"}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": false, "isLoading": false, "postingDescription": "11111Searching for basic fishing equipment - rod, reel, and tackle box. Must be in good working condition. ", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "Fishing Gear11111", "timestamp": "2025-06-09T11:03:40.954Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T11:03:40.954Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offers22555", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "224555", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T11:03:40.958Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.984Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.985Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.985Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": true, "timestamp": "2025-06-09T11:03:40.985Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.985Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.985Z"}
 LOG  [OfferCache][GET] {"age": 34, "found": true, "isExpired": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.985Z"}
 LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"cachedStatus": "pending", "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.986Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T11:03:40.986Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": "hotmails offers22555", "hookPrice": 224555, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offers22555", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "224555", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": true}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T11:03:40.988Z"}
 LOG  [OfferDetailsSection][OFFER_UPDATED] {"description": "hotmails offers22555", "offerId": "tK7kgW3yDfZoF3IWeedl", "price": 224555, "timestamp": "2025-06-09T11:03:40.991Z"}