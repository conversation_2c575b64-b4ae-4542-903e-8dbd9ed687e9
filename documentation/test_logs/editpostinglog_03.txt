Setup Two Simulators:
Simulator 1: Posting owner 
Simulator 2: Offer owner 

Create Test Data:


On Simulator 1: posting owner modifies the posting and saves changes and navigates to posting detail screen



 LOG  [EditPostingScreen][Save] Starting update: {"postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:48:40.285Z", "updates": {"description": "123Searching for basic fishing equipment - rod, reel, and tackle box. Must be in good working condition. ", "latitude": 37.7917130364994, "longitude": -122.4467312534528, "title": "Fishing Gear123"}}
 LOG  [updatePostingDetails] Starting update: {"postingId": "Z7cW3OyoEsojfIreWcAD", "updatedData": {"description": "123Searching for basic fishing equipment - rod, reel, and tackle box. Must be in good working condition. ", "latitude": 37.7917130364994, "longitude": -122.4467312534528, "title": "Fishing Gear123"}}
 LOG  [EditPostingScreen][Debug] Hook initialization: {"hookSetup": {"error": false, "hasPostingDetails": true, "hookImport": "function", "loading": false, "postingId": "Z7cW3OyoEsojfIreWcAD"}, "timestamp": "2025-06-09T10:48:40.291Z"}
 LOG  [EditPostingScreen] Extracted params: {"hasDesc": false, "hasLocation": false, "hasName": false, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:48:40.292Z"}
 LOG  [updatePostingDetails] Detected title change: {"from": "Fishing Gear", "to": "Fishing Gear123"}
 LOG  [updatePostingDetails] Detected description change
 LOG  [updatePostingDetails] Posting updated successfully
 LOG  [updatePostingDetails] Preparing notifications for changes: ["title", "description"]
 LOG  [updatePostingDetails] Found 1 users with favorite
 LOG  [updatePostingDetails] Creating notification for user ybEyGDxbvleCEXdTvrA2CgVTJ1y2
 LOG  [updatePostingDetails] Notification payload: {"body": "\"Fishing Gear\" has been updated (title and description)", "createdAt": {"_methodName": "serverTimestamp"}, "postingId": "Z7cW3OyoEsojfIreWcAD", "read": false, "recipientId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "senderId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "title": "Posting Updated", "type": "FAVORITE_POSTING_UPDATE"}
 LOG  [updatePostingDetails] Sending all notifications
 LOG  [OfferDetail][NOTIFICATION_DB_UPDATE] {"count": 1, "timestamp": "2025-06-09T10:48:40.843Z"}
 LOG  [OfferDetail][NOTIFICATION_DB_ADDED] {"notification": {"body": "\"Fishing Gear\" has been updated (title and description)", "createdAt": [Object], "postingId": "Z7cW3OyoEsojfIreWcAD", "read": false, "recipientId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "senderId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "title": "Posting Updated", "type": "FAVORITE_POSTING_UPDATE"}, "timestamp": "2025-06-09T10:48:40.845Z"}
 LOG  [OfferDetail][NOTIFICATION_MATCHING_DEBUG] {"current": {"finalPostingId": "Z7cW3OyoEsojfIreWcAD", "offerId": "tK7kgW3yDfZoF3IWeedl", "postingId": "Z7cW3OyoEsojfIreWcAD", "reliablePostingId": "Z7cW3OyoEsojfIreWcAD"}, "matches": {"finalPostingIdMatch": true, "offerIdMatch": false, "postingIdMatch": true}, "notification": {"offerId": undefined, "postingId": "Z7cW3OyoEsojfIreWcAD", "type": "FAVORITE_POSTING_UPDATE"}, "timestamp": "2025-06-09T10:48:40.846Z"}
 LOG  [OfferDetail][NOTIFICATION_DB_RELEVANT] {"matchedBy": "postingId", "offerId": undefined, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:48:40.846Z", "type": "FAVORITE_POSTING_UPDATE"}
 LOG  [OfferDetail][SHOWING_POSTING_REFRESH] {"timestamp": "2025-06-09T10:48:40.847Z"}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": false, "isLoading": false, "postingDescription": "Searching for basic fishing equipment - rod, reel, and tackle box. Must be in good working condition. ", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "Fishing Gear", "timestamp": "2025-06-09T10:48:40.852Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": true, "showRefresh": true, "timestamp": "2025-06-09T10:48:40.854Z"}
 LOG  [PostingDetailsSection][RENDER_REFRESH_BUTTON] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "showRefresh": true, "timestamp": "2025-06-09T10:48:40.854Z", "visible": true}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offers22555", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "224555", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T10:48:40.868Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:48:40.910Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:48:40.910Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:48:40.910Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": true, "timestamp": "2025-06-09T10:48:40.911Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:48:40.911Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:48:40.911Z"}
 LOG  [OfferCache][GET] {"age": 31335, "found": true, "isExpired": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:48:40.912Z"}
 LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"cachedStatus": "pending", "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:48:40.912Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:48:40.912Z"}
 LOG  OfferDetail refresh button states: {"showOfferRefresh": false, "showOfferRefreshExplicit": false, "showPostingRefresh": true, "showPostingRefreshExplicit": true, "timestamp": "2025-06-09T10:48:40.912Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": "hotmails offers22555", "hookPrice": 224555, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offers22555", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "224555", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": true}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T10:48:40.914Z"}
 LOG  [OfferDetailsSection][OFFER_UPDATED] {"description": "hotmails offers22555", "offerId": "tK7kgW3yDfZoF3IWeedl", "price": 224555, "timestamp": "2025-06-09T10:48:40.918Z"}
 LOG  [updatePostingDetails] All notifications processed
 LOG  [EditPostingScreen][Save] Update successful: {"postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:48:40.925Z"}
 LOG  [Navigation] Configuring screen: LoginScreen
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: Home
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: PostingDetail
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Rendering headerLeft for: PostingDetail
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Rendering headerLeft for: EditPosting
 LOG  [Navigation] Can go back status: true
 LOG  [usePostingDetails][Focus] Screen focused, setting up subscription
 LOG  [usePostingDetails][Setup] Creating references
 LOG  [usePostingDetails][Setup] Pre-snapshot listener setup: {"postingId": "Z7cW3OyoEsojfIreWcAD", "ref": "postings/Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:48:40.961Z"}
 LOG  [ListenerStateManager] Starting transition for postings_1749466120961_1749466120961: inactive -> initializing
 LOG  [ListenerRegistry][postings] Registered: {"collection": "postings", "id": "1749466120961", "registryId": "postings_1749466120961_1749466120961", "state": "active", "timestamp": "2025-06-09T10:48:40.961Z"}
 LOG  [ListenerStateManager] Completing transition for postings_1749466120961_1749466120961
 LOG  [ListenerRegistry][Component] Registered listener with component: {"componentId": "PostingDetail_Z7cW3OyoEsojfIreWcAD", "listenerId": "postings_1749466120961_1749466120961", "timestamp": "2025-06-09T10:48:40.962Z"}
 LOG  [ListenerRegistry][postings] Created: {"componentId": "PostingDetail_Z7cW3OyoEsojfIreWcAD", "listenerId": "postings_1749466120961", "timestamp": "2025-06-09T10:48:40.962Z"}
 LOG  [useOffers][Focus] Screen focused, setting up subscription
 LOG  [useOffers][Subscribe] Setting up subscription {"postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:48:40.962Z"}
 LOG  [ListenerStateManager] Starting transition for offers_1749466120962_1749466120962: inactive -> initializing
 LOG  [ListenerRegistry][offers] Registered: {"collection": "offers", "id": "1749466120962", "registryId": "offers_1749466120962_1749466120962", "state": "active", "timestamp": "2025-06-09T10:48:40.963Z"}
 LOG  [ListenerStateManager] Completing transition for offers_1749466120962_1749466120962
 LOG  [ListenerRegistry][Component] Registered listener with component: {"componentId": "PostingDetail_Z7cW3OyoEsojfIreWcAD", "listenerId": "offers_1749466120962_1749466120962", "timestamp": "2025-06-09T10:48:40.964Z"}
 LOG  [ListenerRegistry][offers] Created: {"componentId": "PostingDetail_Z7cW3OyoEsojfIreWcAD", "listenerId": "offers_1749466120962", "timestamp": "2025-06-09T10:48:40.964Z"}
 LOG  [useFavorites][Focus] Screen focused, fetching favorite status
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-09T10:48:04.616Z"}, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:48:40.966Z"}
 LOG  [usePostingDetails][Reducer] Processing action: {"currentState": {"error": null, "loading": false, "postingDetails": {"createdAt": [Timestamp], "description": "Searching for basic fishing equipment - rod, reel, and tackle box. Must be in good working condition. ", "id": "Z7cW3OyoEsojfIreWcAD", "lastUpdated": [Timestamp], "latitude": 37.7917130364994, "longitude": -122.4467312534528, "postingStatus": "Active", "searchTerms": [Array], "title": "Fishing Gear", "titleLower": "hedere", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}}, "metrics": {"timeSinceStart": 36350, "timestamp": "2025-06-09T10:48:40.966Z", "updateCount": 5}, "type": "FETCH_START"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": true, "timestamp": "2025-06-09T10:48:40.966Z"}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_Z7cW3OyoEsojfIreWcAD_1749466095986", "offers_Z7cW3OyoEsojfIreWcAD_1749466095986"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749466095986"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749466095986"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-09T10:48:40.970Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 36354, "timestamp": "2025-06-09T10:48:40.970Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 36354, "timestamp": "2025-06-09T10:48:40.970Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749466120970", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749466120970", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_Z7cW3OyoEsojfIreWcAD_1749466120970", "postingSubId": "posting_Z7cW3OyoEsojfIreWcAD_1749466120970", "totalTime": 36355}
 LOG  [PostingDetail][StateSync] Checking state update conditions: {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "hasDetails": true, "offersCount": 2, "postingLoading": true, "timestamp": "2025-06-09T10:48:40.971Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [EditPostingScreen][Debug] Hook initialization: {"hookSetup": {"error": false, "hasPostingDetails": true, "hookImport": "function", "loading": false, "postingId": "Z7cW3OyoEsojfIreWcAD"}, "timestamp": "2025-06-09T10:48:40.974Z"}
 LOG  [EditPostingScreen] Extracted params: {"hasDesc": false, "hasLocation": false, "hasName": false, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:48:40.974Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-09T10:48:04.616Z"}, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:48:41.067Z"}
 LOG  [usePostingDetails][Reducer] Processing action: {"currentState": {"error": null, "loading": true, "postingDetails": {"createdAt": [Timestamp], "description": "Searching for basic fishing equipment - rod, reel, and tackle box. Must be in good working condition. ", "id": "Z7cW3OyoEsojfIreWcAD", "lastUpdated": [Timestamp], "latitude": 37.7917130364994, "longitude": -122.4467312534528, "postingStatus": "Active", "searchTerms": [Array], "title": "Fishing Gear", "titleLower": "hedere", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}}, "metrics": {"timeSinceStart": 36452, "timestamp": "2025-06-09T10:48:41.068Z", "updateCount": 6}, "type": "UPDATE_POSTING"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-09T10:48:41.069Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isFavorite": false, "isOwner": true, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:48:41.069Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_Z7cW3OyoEsojfIreWcAD_1749466120970", "offers_Z7cW3OyoEsojfIreWcAD_1749466120970"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749466120970"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749466120970"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-09T10:48:41.084Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 36468, "timestamp": "2025-06-09T10:48:41.084Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 36468, "timestamp": "2025-06-09T10:48:41.084Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749466121084", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749466121084", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_Z7cW3OyoEsojfIreWcAD_1749466121084", "postingSubId": "posting_Z7cW3OyoEsojfIreWcAD_1749466121084", "totalTime": 36469}
 LOG  [PostingDetail][StateSync] Checking state update conditions: {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "hasDetails": true, "offersCount": 2, "postingLoading": false, "timestamp": "2025-06-09T10:48:41.085Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Updating state with: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isOwnerCheck": true, "offersCount": 2, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:48:41.085Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Processing offers: {"count": 2, "timestamp": "2025-06-09T10:48:41.085Z", "userOffers": 0}
 LOG  [PostingDetail][StateSync] State updated: {"hasUserOffer": false, "isOwner": true, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:48:41.085Z"}
 LOG  [PostingDetail][StateVerification] {"auth": {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "expectedOwner": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "posting": {"id": "Z7cW3OyoEsojfIreWcAD", "loaded": true}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": false, "isOwner": true, "lastUpdate": 1749466095965, "offersCount": 2, "postingDetailsId": "Z7cW3OyoEsojfIreWcAD", "userOffer": null}, "timestamp": "2025-06-09T10:48:41.085Z"}
 LOG  [PostingDetail] Posting details loaded for edit: {"hasDetails": true, "hasLocation": false, "isOwner": true, "timestamp": "2025-06-09T10:48:41.087Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-09T10:48:04.616Z"}, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:48:41.088Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-09T10:48:41.088Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isFavorite": false, "isOwner": true, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:48:41.088Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_Z7cW3OyoEsojfIreWcAD_1749466121084", "offers_Z7cW3OyoEsojfIreWcAD_1749466121084"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749466121084"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749466121084"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-09T10:48:41.096Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 36480, "timestamp": "2025-06-09T10:48:41.096Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 36480, "timestamp": "2025-06-09T10:48:41.096Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749466121097", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749466121097", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_Z7cW3OyoEsojfIreWcAD_1749466121097", "postingSubId": "posting_Z7cW3OyoEsojfIreWcAD_1749466121097", "totalTime": 36481}
 LOG  [PostingDetail][StateVerification] {"auth": {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "expectedOwner": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "posting": {"id": "Z7cW3OyoEsojfIreWcAD", "loaded": true}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": false, "isOwner": true, "lastUpdate": 1749466121088, "offersCount": 2, "postingDetailsId": "Z7cW3OyoEsojfIreWcAD", "userOffer": null}, "timestamp": "2025-06-09T10:48:41.097Z"}
 LOG  [PostingDetail][StateDebug] {"auth": {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "performance": {"timeSinceLastUpdate": 9}, "props": {"postingId": "Z7cW3OyoEsojfIreWcAD", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": false, "isOwner": true, "lastUpdate": 1749466121088, "offersCount": 2, "postingDetailsId": "Z7cW3OyoEsojfIreWcAD", "userOffer": null}, "subscriptions": {"metrics": {"lastUpdate": "2025-06-09T10:48:04.616Z", "subscriptionCount": 0, "totalTime": 36481}, "types": []}, "timestamp": "2025-06-09T10:48:41.097Z"}
 LOG  [useOffers][Update] {"changes": [{"id": "IgyIrlkc18WdziXD3nEO", "timestamp": "2025-06-09T10:48:41.160Z", "type": "added"}, {"id": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:48:41.160Z", "type": "added"}], "count": 2, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:48:41.160Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-09T10:48:04.616Z"}, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:48:41.161Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-09T10:48:41.162Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isFavorite": false, "isOwner": true, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:48:41.163Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_Z7cW3OyoEsojfIreWcAD_1749466121097", "offers_Z7cW3OyoEsojfIreWcAD_1749466121097"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749466121097"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749466121097"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-09T10:48:41.171Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 36555, "timestamp": "2025-06-09T10:48:41.171Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 36555, "timestamp": "2025-06-09T10:48:41.171Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749466121171", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749466121171", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_Z7cW3OyoEsojfIreWcAD_1749466121171", "postingSubId": "posting_Z7cW3OyoEsojfIreWcAD_1749466121171", "totalTime": 36556}
 LOG  [PostingDetail][StateSync] Checking state update conditions: {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "hasDetails": true, "offersCount": 2, "postingLoading": false, "timestamp": "2025-06-09T10:48:41.172Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Updating state with: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isOwnerCheck": true, "offersCount": 2, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:48:41.172Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Processing offers: {"count": 2, "timestamp": "2025-06-09T10:48:41.172Z", "userOffers": 0}
 LOG  [PostingDetail][StateSync] State updated: {"hasUserOffer": false, "isOwner": true, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:48:41.172Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-09T10:48:04.616Z"}, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:48:41.173Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-09T10:48:41.173Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isFavorite": false, "isOwner": true, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:48:41.174Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_Z7cW3OyoEsojfIreWcAD_1749466121171", "offers_Z7cW3OyoEsojfIreWcAD_1749466121171"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749466121171"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749466121171"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-09T10:48:41.182Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 36566, "timestamp": "2025-06-09T10:48:41.182Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 36566, "timestamp": "2025-06-09T10:48:41.182Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749466121182", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749466121182", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_Z7cW3OyoEsojfIreWcAD_1749466121182", "postingSubId": "posting_Z7cW3OyoEsojfIreWcAD_1749466121182", "totalTime": 36566}
 LOG  [PostingDetail][StateVerification] {"auth": {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "expectedOwner": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "posting": {"id": "Z7cW3OyoEsojfIreWcAD", "loaded": true}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": false, "isOwner": true, "lastUpdate": 1749466121173, "offersCount": 2, "postingDetailsId": "Z7cW3OyoEsojfIreWcAD", "userOffer": null}, "timestamp": "2025-06-09T10:48:41.182Z"}
 LOG  [PostingDetail][StateDebug] {"auth": {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "performance": {"timeSinceLastUpdate": 10}, "props": {"postingId": "Z7cW3OyoEsojfIreWcAD", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": false, "isOwner": true, "lastUpdate": 1749466121173, "offersCount": 2, "postingDetailsId": "Z7cW3OyoEsojfIreWcAD", "userOffer": null}, "subscriptions": {"metrics": {"lastUpdate": "2025-06-09T10:48:04.616Z", "subscriptionCount": 0, "totalTime": 36567}, "types": []}, "timestamp": "2025-06-09T10:48:41.183Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-09T10:48:04.616Z"}, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:48:41.185Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-09T10:48:41.185Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isFavorite": false, "isOwner": true, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:48:41.186Z", "userOffer": null}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-09T10:48:04.616Z"}, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:48:41.188Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-09T10:48:41.188Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isFavorite": false, "isOwner": true, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:48:41.188Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_Z7cW3OyoEsojfIreWcAD_1749466121182", "offers_Z7cW3OyoEsojfIreWcAD_1749466121182"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749466121182"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749466121182"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-09T10:48:41.196Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 36580, "timestamp": "2025-06-09T10:48:41.196Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "Z7cW3OyoEsojfIreWcAD", "timeSinceStart": 36580, "timestamp": "2025-06-09T10:48:41.196Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_Z7cW3OyoEsojfIreWcAD_1749466121196", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_Z7cW3OyoEsojfIreWcAD_1749466121196", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_Z7cW3OyoEsojfIreWcAD_1749466121196", "postingSubId": "posting_Z7cW3OyoEsojfIreWcAD_1749466121196", "totalTime": 36580}
 LOG  [Navigation] Rendering headerLeft for: PostingDetail
 LOG  [Navigation] Can go back status: true
 LOG  [EditPostingScreen][Navigation] Screen cleanup: {"flowMetrics": {"dataLoadComplete": 436169816.684916, "dataLoadStart": 436169816.683041, "events": [[Object]], "flowMetrics": undefined, "navigationComplete": 436169801.587791, "totalDuration": "24378.434"}, "timestamp": "2025-06-09T10:48:41.532Z"}
 LOG  [Navigation] Rendering headerLeft for: PostingDetail
 LOG  [Navigation] Can go back status: true
 LOG  Region changing: {"latitude": 37.78497967609087, "latitudeDelta": 0.08566326232246979, "longitude": -122.43239999999999, "longitudeDelta": 0.053661654803107695}
 LOG  VirtualizedList: You have a large list that is slow to update - make sure your renderItem function renders components that follow React performance best practices like PureComponent, shouldComponentUpdate, etc. {"contentLength": 633.3333740234375, "dt": 25215, "prevDt": 11210}
 LOG  Debounced region changed: {"latitude": 37.78497967609087, "latitudeDelta": 0.08566326232246979, "longitude": -122.43239999999999, "longitudeDelta": 0.053661654803107695}
 LOG  Fetching postings for new region
 LOG  [useControlledPostings] Fetching postings for region: {"latitude": 37.78497967609087, "latitudeDelta": 0.08566326232246979, "longitude": -122.43239999999999, "longitudeDelta": 0.053661654803107695}
 LOG  [fetchPostingsBySearch] Query results: 10
 LOG  [useControlledPostings] Fetched postings: 10




On Simulator 2: modification of a posting should display a refresh button in posting detail container while the offer owner is viewing the offer detail screen but the refresh button was displayed SUCCESSFULLY.

On Simulator 2: offer owner clicks the chevron down button in posting detail container to expand the posting details container and displays the old posting title and description as expected







 LOG  [PostingDetailsSection] Toggling details: {"current": false, "new": true, "timestamp": "2025-06-09T10:49:39.650Z"}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": true, "isLoading": false, "postingDescription": "Searching for basic fishing equipment - rod, reel, and tackle box. Must be in good working condition. ", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "Fishing Gear", "timestamp": "2025-06-09T10:49:39.659Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": true, "showRefresh": true, "timestamp": "2025-06-09T10:49:39.662Z"}
 LOG  [PostingDetailsSection][RENDER_REFRESH_BUTTON] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "showRefresh": true, "timestamp": "2025-06-09T10:49:39.662Z", "visible": true}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offers22555", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "224555", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T10:49:39.691Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:49:39.731Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:49:39.731Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:49:39.731Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": true, "timestamp": "2025-06-09T10:49:39.732Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:49:39.732Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:49:39.733Z"}
 LOG  [OfferCache][GET] {"age": 90156, "found": true, "isExpired": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:49:39.733Z"}
 LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"cachedStatus": "pending", "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:49:39.733Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:49:39.734Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": "hotmails offers22555", "hookPrice": 224555, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offers22555", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "224555", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": true}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T10:49:39.737Z"}
 LOG  [OfferDetailsSection][OFFER_UPDATED] {"description": "hotmails offers22555", "offerId": "tK7kgW3yDfZoF3IWeedl", "price": 224555, "timestamp": "2025-06-09T10:49:39.741Z"}
 LOG  FlatList layout complete
 LOG  Skipping load more: {"hasLastDoc": false, "hasMoreMessages": true, "lastDocId": undefined, "loadingMore": false}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": true, "isLoading": false, "postingDescription": "Searching for basic fishing equipment - rod, reel, and tackle box. Must be in good working condition. ", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "Fishing Gear", "timestamp": "2025-06-09T10:49:39.754Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": true, "showRefresh": true, "timestamp": "2025-06-09T10:49:39.754Z"}
 LOG  [PostingDetailsSection][RENDER_REFRESH_BUTTON] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "showRefresh": true, "timestamp": "2025-06-09T10:49:39.755Z", "visible": true}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offers22555", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "224555", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T10:49:39.766Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:49:39.795Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:49:39.795Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:49:39.795Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": true, "timestamp": "2025-06-09T10:49:39.796Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:49:39.796Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:49:39.796Z"}
 LOG  [OfferCache][GET] {"age": 90219, "found": true, "isExpired": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:49:39.796Z"}
 LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"cachedStatus": "pending", "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:49:39.797Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:49:39.797Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": "hotmails offers22555", "hookPrice": 224555, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offers22555", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "224555", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": true}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T10:49:39.799Z"}
 LOG  [OfferDetailsSection][OFFER_UPDATED] {"description": "hotmails offers22555", "offerId": "tK7kgW3yDfZoF3IWeedl", "price": 224555, "timestamp": "2025-06-09T10:49:39.802Z"}





On Simulator 2: offer owner clicks the refresh button and the posting details are updated SUCCESSFULLY


 LOG  [PostingDetailsSection][REFRESH_START] {"postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:50:02.018Z"}
 LOG  [useOfferDetails][REFRESH_START] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:02.021Z"}
 LOG  [OfferCache][CLEAR] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:02.023Z"}
 LOG  [useOfferDetails][FETCH_OFFER_START] {"forceRefresh": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:02.024Z"}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": true, "isLoading": false, "postingDescription": "Searching for basic fishing equipment - rod, reel, and tackle box. Must be in good working condition. ", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "Fishing Gear", "timestamp": "2025-06-09T10:50:02.028Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": true, "showRefresh": true, "timestamp": "2025-06-09T10:50:02.030Z"}
 LOG  [PostingDetailsSection][RENDER_REFRESH_BUTTON] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "showRefresh": true, "timestamp": "2025-06-09T10:50:02.030Z", "visible": true}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offers22555", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "224555", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T10:50:02.055Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:02.097Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:02.097Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:02.097Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": true, "timestamp": "2025-06-09T10:50:02.098Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:02.099Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:02.099Z"}
 LOG  [useOfferDetails][FETCHING_DATA] {"fetchCount": 1, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:02.099Z"}
 LOG  [useOfferDetails][FETCH_OFFER_START] {"forceRefresh": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:02.100Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:02.100Z"}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": true, "isLoading": true, "postingDescription": "Searching for basic fishing equipment - rod, reel, and tackle box. Must be in good working condition. ", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "Fishing Gear", "timestamp": "2025-06-09T10:50:02.102Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": true, "showRefresh": true, "timestamp": "2025-06-09T10:50:02.102Z"}
 LOG  [PostingDetailsSection][RENDER_REFRESH_BUTTON] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "showRefresh": true, "timestamp": "2025-06-09T10:50:02.102Z", "visible": true}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": true, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offers22555", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "224555", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T10:50:02.112Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:02.140Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:02.140Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:02.140Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": true, "timestamp": "2025-06-09T10:50:02.141Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:02.141Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:02.156Z"}
 LOG  [useOfferDetails][FETCHING_DATA] {"fetchCount": 1, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:02.156Z"}
 LOG  [useOfferDetails][FETCH_OFFER_START] {"forceRefresh": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:02.157Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:02.157Z"}
 LOG  [OfferCache][SET] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:02.403Z"}
 LOG  [useOfferDetails][FETCH_OFFER_SUCCESS] {"offerId": "tK7kgW3yDfZoF3IWeedl", "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:50:02.404Z"}
 LOG  [OfferDetail][STATUS_TRACKING] {"hasInitialOffer": false, "hasOfferDetails": true, "initialOfferStatus": undefined, "offerId": "tK7kgW3yDfZoF3IWeedl", "offerStatus": "pending", "postingStatus": "Active", "timestamp": "2025-06-09T10:50:02.406Z"}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": true, "isLoading": true, "postingDescription": "123Searching for basic fishing equipment - rod, reel, and tackle box. Must be in good working condition. ", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "Fishing Gear123", "timestamp": "2025-06-09T10:50:02.408Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": true, "showRefresh": true, "timestamp": "2025-06-09T10:50:02.410Z"}
 LOG  [PostingDetailsSection][RENDER_REFRESH_BUTTON] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "showRefresh": true, "timestamp": "2025-06-09T10:50:02.410Z", "visible": true}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": true, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offers22555", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "224555", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T10:50:02.428Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:02.471Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:02.472Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:02.472Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": true, "timestamp": "2025-06-09T10:50:02.473Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:02.473Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:02.473Z"}
 LOG  [OfferCache][GET] {"age": 69, "found": true, "isExpired": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:02.473Z"}
 LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"cachedStatus": "pending", "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:02.475Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:02.476Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": "hotmails offers22555", "hookPrice": 224555, "isLoading": true, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offers22555", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "224555", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": true}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T10:50:02.479Z"}
 LOG  [OfferDetailsSection][OFFER_UPDATED] {"description": "hotmails offers22555", "offerId": "tK7kgW3yDfZoF3IWeedl", "price": 224555, "timestamp": "2025-06-09T10:50:02.482Z"}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": true, "isLoading": true, "postingDescription": "123Searching for basic fishing equipment - rod, reel, and tackle box. Must be in good working condition. ", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "Fishing Gear123", "timestamp": "2025-06-09T10:50:02.483Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": true, "showRefresh": true, "timestamp": "2025-06-09T10:50:02.484Z"}
 LOG  [PostingDetailsSection][RENDER_REFRESH_BUTTON] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "showRefresh": true, "timestamp": "2025-06-09T10:50:02.484Z", "visible": true}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": true, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offers22555", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "224555", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T10:50:02.492Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:02.519Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:02.520Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:02.520Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": true, "timestamp": "2025-06-09T10:50:02.522Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:02.522Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:02.532Z"}
 LOG  [OfferCache][GET] {"age": 128, "found": true, "isExpired": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:02.532Z"}
 LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"cachedStatus": "pending", "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:02.533Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:02.533Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": "hotmails offers22555", "hookPrice": 224555, "isLoading": true, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offers22555", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "224555", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": true}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T10:50:02.535Z"}
 LOG  [useOfferDetails][NO_CHANGES] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:02.539Z"}
 LOG  [OfferDetailsSection][OFFER_UPDATED] {"description": "hotmails offers22555", "offerId": "tK7kgW3yDfZoF3IWeedl", "price": 224555, "timestamp": "2025-06-09T10:50:02.539Z"}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": true, "isLoading": true, "postingDescription": "123Searching for basic fishing equipment - rod, reel, and tackle box. Must be in good working condition. ", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "Fishing Gear123", "timestamp": "2025-06-09T10:50:02.540Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": true, "showRefresh": true, "timestamp": "2025-06-09T10:50:02.541Z"}
 LOG  [PostingDetailsSection][RENDER_REFRESH_BUTTON] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "showRefresh": true, "timestamp": "2025-06-09T10:50:02.541Z", "visible": true}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": true, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offers22555", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "224555", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T10:50:02.550Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:02.577Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:02.577Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:02.577Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": true, "timestamp": "2025-06-09T10:50:02.578Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:02.578Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:02.578Z"}
 LOG  [OfferCache][GET] {"age": 174, "found": true, "isExpired": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:02.578Z"}
 LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"cachedStatus": "pending", "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:02.578Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:02.579Z"}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": true, "isLoading": true, "postingDescription": "123Searching for basic fishing equipment - rod, reel, and tackle box. Must be in good working condition. ", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "Fishing Gear123", "timestamp": "2025-06-09T10:50:02.581Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": true, "showRefresh": true, "timestamp": "2025-06-09T10:50:02.582Z"}
 LOG  [PostingDetailsSection][RENDER_REFRESH_BUTTON] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "showRefresh": true, "timestamp": "2025-06-09T10:50:02.582Z", "visible": true}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": true, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offers22555", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "224555", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T10:50:02.588Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:02.615Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:02.615Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:02.616Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": true, "timestamp": "2025-06-09T10:50:02.616Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:02.616Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:02.616Z"}
 LOG  [OfferCache][GET] {"age": 212, "found": true, "isExpired": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:02.616Z"}
 LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"cachedStatus": "pending", "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:02.617Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:02.617Z"}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": true, "isLoading": false, "postingDescription": "123Searching for basic fishing equipment - rod, reel, and tackle box. Must be in good working condition. ", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "Fishing Gear123", "timestamp": "2025-06-09T10:50:02.619Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": true, "showRefresh": true, "timestamp": "2025-06-09T10:50:02.619Z"}
 LOG  [PostingDetailsSection][RENDER_REFRESH_BUTTON] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "showRefresh": true, "timestamp": "2025-06-09T10:50:02.619Z", "visible": true}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offers22555", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "224555", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T10:50:02.630Z"}
 LOG  [PostingDetailsSection][MARKING_NOTIFICATIONS_READ] {"postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:50:02.656Z"}
 LOG  [notificationService][MARK_RELEVANT_READ] {"offerId": undefined, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:50:02.658Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:02.659Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:02.659Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:02.659Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": true, "timestamp": "2025-06-09T10:50:02.660Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:02.660Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:02.660Z"}
 LOG  [OfferCache][GET] {"age": 256, "found": true, "isExpired": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:02.660Z"}
 LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"cachedStatus": "pending", "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:02.661Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:02.662Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": "hotmails offers22555", "hookPrice": 224555, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offers22555", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "224555", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": true}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T10:50:02.664Z"}
 LOG  [OfferDetailsSection][OFFER_UPDATED] {"description": "hotmails offers22555", "offerId": "tK7kgW3yDfZoF3IWeedl", "price": 224555, "timestamp": "2025-06-09T10:50:02.667Z"}
 LOG  [OfferCache][SET] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:02.668Z"}
 LOG  [useOfferDetails][FETCH_OFFER_SUCCESS] {"offerId": "tK7kgW3yDfZoF3IWeedl", "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:50:02.668Z"}
 LOG  [OfferCache][SET] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:02.669Z"}
 LOG  [useOfferDetails][FETCH_OFFER_SUCCESS] {"offerId": "tK7kgW3yDfZoF3IWeedl", "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:50:02.669Z"}
 LOG  [notificationService][MARK_RELEVANT_READ] Found notifications: {"count": 1, "offerId": undefined, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:50:02.785Z"}
 LOG  [OfferDetail][NOTIFICATION_DB_UPDATE] {"count": 0, "timestamp": "2025-06-09T10:50:02.792Z"}
 LOG  [notificationService][MARKED_READ] {"count": 1, "offerId": undefined, "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:50:03.067Z"}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": true, "isLoading": false, "postingDescription": "123Searching for basic fishing equipment - rod, reel, and tackle box. Must be in good working condition. ", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "Fishing Gear123", "timestamp": "2025-06-09T10:50:03.074Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T10:50:03.075Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offers22555", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "224555", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T10:50:03.098Z"}
 LOG  [PostingDetailsSection][REFRESH_COMPLETE] {"postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-09T10:50:03.141Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:03.142Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:03.142Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:03.142Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": true, "timestamp": "2025-06-09T10:50:03.143Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:03.143Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:03.145Z"}
 LOG  [OfferCache][GET] {"age": 476, "found": true, "isExpired": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:03.145Z"}
 LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"cachedStatus": "pending", "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:03.145Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-09T10:50:03.146Z"}
 LOG  OfferDetail refresh button states: {"showOfferRefresh": false, "showOfferRefreshExplicit": false, "showPostingRefresh": false, "showPostingRefreshExplicit": false, "timestamp": "2025-06-09T10:50:03.146Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": "hotmails offers22555", "hookPrice": 224555, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offers22555", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "224555", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": true}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-09T10:50:03.148Z"}
 LOG  [OfferDetailsSection][OFFER_UPDATED] {"description": "hotmails offers22555", "offerId": "tK7kgW3yDfZoF3IWeedl", "price": 224555, "timestamp": "2025-06-09T10:50:03.151Z"}

