step 1: user clicks on an offer in posting detail screen







 LOG  [PostingDetail][Navigation] Attempting safe navigation
 LOG  [PostingDetail][Navigation] Checking listeners: {"activeCount": 2, "byCollection": {"offers": 1, "postings": 1}, "timestamp": "2025-06-07T21:57:18.089Z"}
 LOG  [PostingDetail][Navigation] Cleaning up hooks
 LOG  [PostingDetail] Unsubscribing from posting
 LOG  [usePostingDetails][Unsubscribe] Cleaning up listener: {"listenerId": "postings_1749333371316_1749333371316", "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-07T21:57:18.090Z"}
 LOG  [ListenerRegistry][Component] Unregistered listener from component: {"componentId": "PostingDetail_Z7cW3OyoEsojfIreWcAD", "listenerId": "postings_1749333371316_1749333371316", "timestamp": "2025-06-07T21:57:18.091Z"}
 LOG  [PostingDetail] Successfully unsubscribed from posting
 LOG  [PostingDetail] Unsubscribing from offers
 LOG  [useOffers][Unsubscribe] Cleaning up subscription: {"postingId": "Z7cW3OyoEsojfIreWcAD", "subscriptionId": "offers_1749333371317_1749333371317", "timestamp": "2025-06-07T21:57:18.092Z"}
 LOG  [ListenerRegistry][Component] Unregistered listener from component: {"componentId": "PostingDetail_Z7cW3OyoEsojfIreWcAD", "listenerId": "offers_1749333371317_1749333371317", "timestamp": "2025-06-07T21:57:18.093Z"}
 LOG  [PostingDetail] Successfully unsubscribed from offers
 LOG  [ListenerRegistry] Cleanup metrics: {"groupId": null, "metrics": {"duration": 3, "startTime": 1749333438092, "success": true}, "subscriptionId": "postings_1749333371316_1749333371316", "timestamp": "2025-06-07T21:57:18.095Z"}
 LOG  [ListenerRegistry] Cleanup metrics: {"groupId": null, "metrics": {"duration": 7, "startTime": 1749333438093, "success": true}, "subscriptionId": "offers_1749333371317_1749333371317", "timestamp": "2025-06-07T21:57:18.100Z"}
 LOG  [PostingDetail][Navigation] Post-cleanup verification: {"activeListeners": 0, "byCollection": {"offers": 0, "postings": 0}, "timestamp": "2025-06-07T21:57:18.201Z"}
 LOG  [PostingDetail][Navigation] All listeners cleaned up successfully
 LOG  [Navigation] Configuring screen: LoginScreen
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: Home
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: PostingDetail
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: OfferDetail
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Rendering headerLeft for: PostingDetail
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Rendering headerLeft for: OfferDetail
 LOG  [Navigation] Can go back status: true
 LOG  [BackButton] Rendering back button
 LOG  [OfferDetail][STATUS_TRACKING] {"hasInitialOffer": false, "hasOfferDetails": false, "initialOfferStatus": undefined, "offerId": "tK7kgW3yDfZoF3IWeedl", "offerStatus": "active", "postingStatus": "Active", "timestamp": "2025-06-07T21:57:18.257Z"}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDetailsExpanded": false, "isLoading": true, "postingDescription": "", "postingOwnerId": null, "postingStatus": "Active", "postingTitle": "", "timestamp": "2025-06-07T21:57:18.258Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isCurrentUserPostingOwner": false, "postingOwnerId": null, "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T21:57:18.259Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "hookDescription": undefined, "hookPrice": undefined, "isLoading": true, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "", "offerOwnerId": null, "offerPrice": "0", "offerStatus": "active", "restrictionEndDate": null, "userScore": undefined, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isCurrentUserOfferOwner": false, "offerOwnerId": null, "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T21:57:18.265Z"}
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingOwnerId": null, "shouldShowAbusiveButton": false, "timestamp": "2025-06-07T21:57:18.276Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.277Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.277Z"}
 LOG  [OfferCache][GET] {"age": 878922, "found": true, "isExpired": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.277Z"}
 LOG  [useOfferDetails][FETCHING_DATA] {"fetchCount": 1, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.277Z"}
 LOG  [OfferCache][GET] {"age": 878922, "found": true, "isExpired": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.277Z"}
 LOG  [useOfferDetails][FETCH_OFFER_START] {"forceRefresh": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.277Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.278Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.278Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.278Z"}
 LOG  [OfferCache][GET] {"age": 878924, "found": true, "isExpired": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.279Z"}
 LOG  [useOfferDetails][FETCHING_DATA] {"fetchCount": 1, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.279Z"}
 LOG  [OfferCache][GET] {"age": 878924, "found": true, "isExpired": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.279Z"}
 LOG  [useOfferDetails][FETCH_OFFER_START] {"forceRefresh": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.279Z"}
 LOG  [useAuthUser] Setting up auth state listener {"currentUser": {"_redirectEventId": undefined, "apiKey": "AIzaSyBOHewmZjlRWGu1jeEI-uBSkAn56xLMlec", "appName": "[DEFAULT]", "createdAt": "1731681254940", "displayName": undefined, "email": "<EMAIL>", "emailVerified": false, "isAnonymous": false, "lastLoginAt": "1749332552760", "phoneNumber": undefined, "photoURL": undefined, "providerData": [Array], "stsTokenManager": [Object], "tenantId": undefined, "uid": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "hasAuth": true, "hasOnAuthStateChanged": true}
 LOG  [OfferDetail][MOUNT] {"cleanupStarted": {"isCleaningUp": false, "isMounted": true}, "lastAction": "MOUNT", "mountCount": 0, "params": {"offerId": "tK7kgW3yDfZoF3IWeedl", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingId": "Z7cW3OyoEsojfIreWcAD", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "timestamp": "2025-06-07T21:57:18.282Z"}
 LOG  [Messages Subscription] Setting up subscription for offerId: tK7kgW3yDfZoF3IWeedl
 LOG  === OfferDetail Screen Mounted ===
 LOG  Route params: {"offerId": "tK7kgW3yDfZoF3IWeedl", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingId": "Z7cW3OyoEsojfIreWcAD", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  Setting owner IDs from route params: {"offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [OfferDetail][MOUNT] {"cleanupStarted": {"isCleaningUp": false, "isMounted": true}, "lastAction": "MOUNT", "mountCount": 1, "params": {"offerId": "tK7kgW3yDfZoF3IWeedl", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingId": "Z7cW3OyoEsojfIreWcAD", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "timestamp": "2025-06-07T21:57:18.283Z"}
 LOG  Authenticated user: JIPkxLOiOrMcavtTItGBID0HMqH3
 LOG  [OfferDetail] Initializing with params: {"hasInitialOffer": false, "hasInitialPosting": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.283Z"}
 LOG  [OfferDetail] Setting postingOwnerId from route params: {"postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [OfferDetail] Setting offerOwnerId from route params: {"offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2"}
 LOG  OfferDetail refresh button states: {"showOfferRefresh": false, "showOfferRefreshExplicit": false, "showPostingRefresh": false, "showPostingRefreshExplicit": false, "timestamp": "2025-06-07T21:57:18.284Z"}
 LOG  [OfferDetail][NOTIFICATION_LISTENER_SETUP] {"offerId": "tK7kgW3yDfZoF3IWeedl", "postingId": undefined, "timestamp": "2025-06-07T21:57:18.284Z"}
 LOG  [OfferDetail][NOTIFICATION_DB_LISTENER_SETUP] {"offerId": "tK7kgW3yDfZoF3IWeedl", "postingId": undefined, "timestamp": "2025-06-07T21:57:18.284Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [usePostingDetails][Focus] Screen unfocused, cleaning up
 LOG  [ListenerRegistry][Component] Unregistered listener from component: {"componentId": "PostingDetail_Z7cW3OyoEsojfIreWcAD", "listenerId": "postings_1749333371316_1749333371316", "timestamp": "2025-06-07T21:57:18.285Z"}
 LOG  [ListenerRegistry] No entry found for postings_1749333371316_1749333371316
 LOG  [useOffers][Focus] Screen unfocused, cleaning up
 LOG  [ListenerRegistry][Component] Unregistered listener from component: {"componentId": "PostingDetail_Z7cW3OyoEsojfIreWcAD", "listenerId": "offers_1749333371317_1749333371317", "timestamp": "2025-06-07T21:57:18.285Z"}
 LOG  [ListenerRegistry] No entry found for offers_1749333371317_1749333371317
 LOG  [PostingDetailsSection] Render: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDetailsExpanded": false, "isLoading": true, "postingDescription": "", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "", "timestamp": "2025-06-07T21:57:18.287Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isCurrentUserPostingOwner": true, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T21:57:18.287Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "hookDescription": undefined, "hookPrice": undefined, "isLoading": true, "isOfferDetailsExpanded": false, "isPostingOwner": true, "offerDescription": "", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "0", "offerStatus": "active", "restrictionEndDate": null, "userScore": undefined, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isCurrentUserOfferOwner": false, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T21:57:18.291Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.298Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.299Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.299Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": false, "timestamp": "2025-06-07T21:57:18.299Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.299Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.299Z"}
 LOG  [OfferCache][GET] {"age": 878945, "found": true, "isExpired": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.300Z"}
 LOG  [useOfferDetails][FETCHING_DATA] {"fetchCount": 1, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.300Z"}
 LOG  [OfferCache][GET] {"age": 878945, "found": true, "isExpired": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.300Z"}
 LOG  [useOfferDetails][FETCH_OFFER_START] {"forceRefresh": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.300Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.300Z"}
 LOG  [useAuthUser] Auth state changed {"user": {"_redirectEventId": undefined, "apiKey": "AIzaSyBOHewmZjlRWGu1jeEI-uBSkAn56xLMlec", "appName": "[DEFAULT]", "createdAt": "1731681254940", "displayName": undefined, "email": "<EMAIL>", "emailVerified": false, "isAnonymous": false, "lastLoginAt": "1749332552760", "phoneNumber": undefined, "photoURL": undefined, "providerData": [Array], "stsTokenManager": [Object], "tenantId": undefined, "uid": "JIPkxLOiOrMcavtTItGBID0HMqH3"}}
 LOG  [OfferDetail][NOTIFICATION_DB_UPDATE] {"count": 0, "timestamp": "2025-06-07T21:57:18.308Z"}
 LOG  [Navigation] Rendering headerLeft for: PostingDetail
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Rendering headerLeft for: OfferDetail
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Rendering headerLeft for: OfferDetail
 LOG  [Navigation] Can go back status: true
 LOG  Region changing: {"latitude": 37.78769141684092, "latitudeDelta": 0.04788825627440474, "longitude": -122.43664143699868, "longitudeDelta": 0.034503704621840825}
 LOG  [Messages Subscription] Received snapshot update: {"empty": false, "metadata": {"fromCache": false, "hasPendingWrites": false}, "size": 1}
 LOG  [Messages Subscription] Processing discussion: {"id": "uFPwMJYrZ5wEt4cqPi35", "messageCount": 16}
 LOG  [Messages Subscription] Raw messages: {"count": 16, "messageKeys": ["timestamp", "id", "readAt", "read", "text", "senderId"], "sampleMessage": {"id": "1737359768014_ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "read": true, "readAt": [Object], "senderId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "text": "hotmails offer", "timestamp": [Object]}}
 LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["timestamp", "id", "readAt", "read", "text", "senderId"], "timestampType": "object"}
 LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["readAt", "timestamp", "senderId", "id", "read", "text"], "timestampType": "object"}
 LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["readAt", "senderId", "timestamp", "id", "read", "text"], "timestampType": "object"}
 LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["id", "readAt", "timestamp", "text", "read", "senderId"], "timestampType": "object"}
 LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["id", "readAt", "read", "senderId", "text", "timestamp"], "timestampType": "object"}
 LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["timestamp", "readAt", "senderId", "read", "id", "text"], "timestampType": "object"}
 LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["text", "read", "id", "timestamp", "readAt", "senderId"], "timestampType": "object"}
 LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["read", "id", "senderId", "readAt", "timestamp", "text"], "timestampType": "object"}
 LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["timestamp", "id", "readAt", "text", "read", "senderId"], "timestampType": "object"}
 LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["id", "read", "text", "senderId", "timestamp", "readAt"], "timestampType": "object"}
 LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["read", "timestamp", "id", "readAt", "text", "senderId"], "timestampType": "object"}
 LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["text", "read", "senderId", "readAt", "timestamp", "id"], "timestampType": "object"}
 LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["read", "text", "timestamp", "senderId", "readAt", "id"], "timestampType": "object"}
 LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["readAt", "id", "timestamp", "senderId", "read", "text"], "timestampType": "object"}
 LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["senderId", "text", "read", "id", "readAt", "timestamp"], "timestampType": "object"}
 LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["senderId", "readAt", "text", "timestamp", "read", "id"], "timestampType": "object"}
 LOG  [Messages Subscription] Validation summary: {"firstValidMessage": {"id": "1737359768014_ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "senderId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "timestamp": [Object]}, "invalidMessages": 0, "totalMessages": 16, "validMessages": 16}
 LOG  [Messages Subscription] Processing messages: {"hasLegacyMessages": false, "messageTypes": [{"hasId": true, "hasTimestamp": true, "timestampType": "object"}, {"hasId": true, "hasTimestamp": true, "timestampType": "object"}, {"hasId": true, "hasTimestamp": true, "timestampType": "object"}, {"hasId": true, "hasTimestamp": true, "timestampType": "object"}, {"hasId": true, "hasTimestamp": true, "timestampType": "object"}, {"hasId": true, "hasTimestamp": true, "timestampType": "object"}, {"hasId": true, "hasTimestamp": true, "timestampType": "object"}, {"hasId": true, "hasTimestamp": true, "timestampType": "object"}, {"hasId": true, "hasTimestamp": true, "timestampType": "object"}, {"hasId": true, "hasTimestamp": true, "timestampType": "object"}, {"hasId": true, "hasTimestamp": true, "timestampType": "object"}, {"hasId": true, "hasTimestamp": true, "timestampType": "object"}, {"hasId": true, "hasTimestamp": true, "timestampType": "object"}, {"hasId": true, "hasTimestamp": true, "timestampType": "object"}, {"hasId": true, "hasTimestamp": true, "timestampType": "object"}, {"hasId": true, "hasTimestamp": true, "timestampType": "object"}], "newCount": 16, "prevCount": 0}
 LOG  [Messages Subscription] Updated messages state: {"firstMessage": {"id": "1746608040263_JIPkxLOiOrMcavtTItGBID0HMqH3", "senderId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "text": "kou", "timestamp": [Object]}, "hasLegacyMessages": false, "lastMessage": {"id": "1737359768014_ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "senderId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "text": "hotmails offer", "timestamp": [Object]}, "newCount": 16, "previousCount": 0}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDetailsExpanded": false, "isLoading": true, "postingDescription": "", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "", "timestamp": "2025-06-07T21:57:18.428Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isCurrentUserPostingOwner": true, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T21:57:18.429Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "hookDescription": undefined, "hookPrice": undefined, "isLoading": true, "isOfferDetailsExpanded": false, "isPostingOwner": true, "offerDescription": "", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "0", "offerStatus": "active", "restrictionEndDate": null, "userScore": undefined, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isCurrentUserOfferOwner": false, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T21:57:18.433Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.472Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.473Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.473Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": false, "timestamp": "2025-06-07T21:57:18.473Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.474Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.474Z"}
 LOG  [OfferCache][GET] {"age": 879119, "found": true, "isExpired": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.474Z"}
 LOG  [useOfferDetails][FETCHING_DATA] {"fetchCount": 1, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.474Z"}
 LOG  [OfferCache][GET] {"age": 879119, "found": true, "isExpired": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.474Z"}
 LOG  [useOfferDetails][FETCH_OFFER_START] {"forceRefresh": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.475Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.475Z"}
 LOG  Marking messages as read for authorized user: {"isOfferOwner": false, "isPostingOwner": true, "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  Initial load, scrolling to end: 16
 LOG  [PostingDetailsSection] Render: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDetailsExpanded": false, "isLoading": true, "postingDescription": "", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "", "timestamp": "2025-06-07T21:57:18.478Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isCurrentUserPostingOwner": true, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T21:57:18.478Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "hookDescription": undefined, "hookPrice": undefined, "isLoading": true, "isOfferDetailsExpanded": false, "isPostingOwner": true, "offerDescription": "", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "0", "offerStatus": "active", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isCurrentUserOfferOwner": false, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T21:57:18.510Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.531Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.532Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.532Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": false, "timestamp": "2025-06-07T21:57:18.532Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.532Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.532Z"}
 LOG  [OfferCache][GET] {"age": 879177, "found": true, "isExpired": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.532Z"}
 LOG  [useOfferDetails][FETCHING_DATA] {"fetchCount": 1, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.533Z"}
 LOG  [OfferCache][GET] {"age": 879178, "found": true, "isExpired": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.533Z"}
 LOG  [useOfferDetails][FETCH_OFFER_START] {"forceRefresh": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.533Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.533Z"}
 LOG  [OfferCache][SET] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.540Z"}
 LOG  [useOfferDetails][FETCH_OFFER_SUCCESS] {"offerId": "tK7kgW3yDfZoF3IWeedl", "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-07T21:57:18.540Z"}
 LOG  [OfferCache][SET] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.541Z"}
 LOG  [useOfferDetails][FETCH_OFFER_SUCCESS] {"offerId": "tK7kgW3yDfZoF3IWeedl", "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-07T21:57:18.541Z"}
 LOG  [OfferDetail][STATUS_TRACKING] {"hasInitialOffer": false, "hasOfferDetails": true, "initialOfferStatus": undefined, "offerId": "tK7kgW3yDfZoF3IWeedl", "offerStatus": "pending", "postingStatus": "Active", "timestamp": "2025-06-07T21:57:18.542Z"}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDetailsExpanded": false, "isLoading": true, "postingDescription": "Searching for basic fishing equipment - rod, reel, and tackle box. Must be in good working condition. ", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "Fishing Gear7777", "timestamp": "2025-06-07T21:57:18.542Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isCurrentUserPostingOwner": true, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T21:57:18.543Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "hookDescription": undefined, "hookPrice": undefined, "isLoading": true, "isOfferDetailsExpanded": false, "isPostingOwner": true, "offerDescription": "hotmails offers22", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "22", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isCurrentUserOfferOwner": false, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T21:57:18.548Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.567Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.567Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.567Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [OfferDetail][NOTIFICATION_LISTENER_CLEANUP] {"offerId": "tK7kgW3yDfZoF3IWeedl", "postingId": undefined, "timestamp": "2025-06-07T21:57:18.567Z"}
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": false, "timestamp": "2025-06-07T21:57:18.567Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.568Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.568Z"}
 LOG  [OfferCache][GET] {"age": 27, "found": true, "isExpired": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.568Z"}
 LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"cachedStatus": "pending", "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.568Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.568Z"}
 LOG  [OfferDetail][NOTIFICATION_LISTENER_SETUP] {"offerId": "tK7kgW3yDfZoF3IWeedl", "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-07T21:57:18.568Z"}
 LOG  [OfferDetail][NOTIFICATION_DB_LISTENER_SETUP] {"offerId": "tK7kgW3yDfZoF3IWeedl", "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-07T21:57:18.569Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDetailsExpanded": false, "isLoading": true, "postingDescription": "Searching for basic fishing equipment - rod, reel, and tackle box. Must be in good working condition. ", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "Fishing Gear7777", "timestamp": "2025-06-07T21:57:18.570Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isCurrentUserPostingOwner": true, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T21:57:18.571Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "hookDescription": undefined, "hookPrice": undefined, "isLoading": true, "isOfferDetailsExpanded": false, "isPostingOwner": true, "offerDescription": "hotmails offers22", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "22", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isCurrentUserOfferOwner": false, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T21:57:18.574Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.594Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.594Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.594Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": false, "timestamp": "2025-06-07T21:57:18.595Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.595Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.595Z"}
 LOG  [OfferCache][GET] {"age": 54, "found": true, "isExpired": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.595Z"}
 LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"cachedStatus": "pending", "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.595Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.595Z"}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDetailsExpanded": false, "isLoading": false, "postingDescription": "Searching for basic fishing equipment - rod, reel, and tackle box. Must be in good working condition. ", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "Fishing Gear7777", "timestamp": "2025-06-07T21:57:18.597Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isCurrentUserPostingOwner": true, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T21:57:18.597Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "hookDescription": undefined, "hookPrice": undefined, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": true, "offerDescription": "hotmails offers22", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "22", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isCurrentUserOfferOwner": false, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T21:57:18.600Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.620Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.620Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.620Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": false, "timestamp": "2025-06-07T21:57:18.620Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.621Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.621Z"}
 LOG  [OfferCache][GET] {"age": 80, "found": true, "isExpired": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.621Z"}
 LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"cachedStatus": "pending", "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.621Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.621Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "hookDescription": "hotmails offers22", "hookPrice": 22, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": true, "offerDescription": "hotmails offers22", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "22", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": true}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isCurrentUserOfferOwner": false, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T21:57:18.622Z"}
 LOG  [OfferDetailsSection][OFFER_UPDATED] {"description": "hotmails offers22", "offerId": "tK7kgW3yDfZoF3IWeedl", "price": 22, "timestamp": "2025-06-07T21:57:18.627Z"}
 LOG  [OfferCache][SET] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.628Z"}
 LOG  [useOfferDetails][FETCH_OFFER_SUCCESS] {"offerId": "tK7kgW3yDfZoF3IWeedl", "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-07T21:57:18.628Z"}
 LOG  FlatList layout complete
 LOG  [OfferDetail][NOTIFICATION_DB_UPDATE] {"count": 0, "timestamp": "2025-06-07T21:57:18.634Z"}
 LOG  Debounced region changed: {"latitude": 37.78769141684092, "latitudeDelta": 0.04788825627440474, "longitude": -122.43664143699868, "longitudeDelta": 0.034503704621840825}
 LOG  Fetching postings for new region
 LOG  [useControlledPostings] Fetching postings for region: {"latitude": 37.78769141684092, "latitudeDelta": 0.04788825627440474, "longitude": -122.43664143699868, "longitudeDelta": 0.034503704621840825}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDetailsExpanded": false, "isLoading": false, "postingDescription": "Searching for basic fishing equipment - rod, reel, and tackle box. Must be in good working condition. ", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "Fishing Gear7777", "timestamp": "2025-06-07T21:57:18.753Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isCurrentUserPostingOwner": true, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T21:57:18.756Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "hookDescription": undefined, "hookPrice": undefined, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": true, "offerDescription": "hotmails offers22", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "22", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isCurrentUserOfferOwner": false, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T21:57:18.760Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.785Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.785Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.785Z"}
 LOG  [useOfferDetails][CLEANUP] Already cleaning up, skipping
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": false, "timestamp": "2025-06-07T21:57:18.786Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.786Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.786Z"}
 LOG  [OfferCache][GET] {"age": 158, "found": true, "isExpired": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.786Z"}
 LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"cachedStatus": "pending", "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.786Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.786Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "hookDescription": "hotmails offers22", "hookPrice": 22, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": true, "offerDescription": "hotmails offers22", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "22", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": true}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isCurrentUserOfferOwner": false, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-07T21:57:18.788Z"}
 LOG  [OfferDetailsSection][OFFER_UPDATED] {"description": "hotmails offers22", "offerId": "tK7kgW3yDfZoF3IWeedl", "price": 22, "timestamp": "2025-06-07T21:57:18.791Z"}
 LOG  [Navigation] Rendering headerLeft for: PostingDetail
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Rendering headerLeft for: OfferDetail
 LOG  [Navigation] Can go back status: true
 LOG  [OfferCache][SET] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.866Z"}
 LOG  [useOfferDetails][FETCH_OFFER_SUCCESS] {"offerId": "tK7kgW3yDfZoF3IWeedl", "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-07T21:57:18.866Z"}
 LOG  [OfferCache][SET] {"offerId": "tK7kgW3yDfZoF3IWeedl", "timestamp": "2025-06-07T21:57:18.867Z"}
 LOG  [useOfferDetails][FETCH_OFFER_SUCCESS] {"offerId": "tK7kgW3yDfZoF3IWeedl", "postingId": "Z7cW3OyoEsojfIreWcAD", "timestamp": "2025-06-07T21:57:18.867Z"}
 LOG  [fetchPostingsBySearch] Query results: 10
 LOG  [useControlledPostings] Fetched postings: 10
