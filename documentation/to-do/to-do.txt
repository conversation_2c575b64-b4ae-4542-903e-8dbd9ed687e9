# Potential File Additions for Improved Architecture

## Location Management
- utils/locationUtils.ts
  Purpose: Centralize location-related helper functions
  Benefits: Better code organization, reusable location formatting and validation

- types/location.ts
  Purpose: Define TypeScript interfaces for location data structures
  Benefits: Type safety, better IDE support, documentation

## Map Components
- components/MapView.tsx
  Purpose: Custom wrapper around react-native-maps
  Benefits: Consistent map styling, reusable map functionality

## Discussion/Messages
- hooks/useDiscussionMessages.ts
  Purpose: Manage message state and operations
  Benefits: Better separation of concerns from OfferDetail screen

- types/message.ts
  Purpose: Define TypeScript interfaces for message structures
  Benefits: Type safety for message operations

## Settings
- hooks/useSettings.ts
  Purpose: Centralize settings management logic
  Benefits: Reusable settings operations across screens

Note: These files are suggestions for future improvements and are not currently referenced in the codebase. 

TODO: Test iOS bounce behavior
- Verify ScrollableContent bounce behavior
- Test with various content lengths
- Check interaction with keyboard 

TODO: Replace Expo Vector Icons with React Native Vector Icons
- Current: import { Ionicons } from '@expo/vector-icons'
- Replace with: import MaterialIcons from 'react-native-vector-icons/MaterialIcons'
- Update all icon usages in components
- Update icon mocks in tests

TODO: File Naming Conventions
- Mock files should include "Mock" in the name (e.g., firebaseMock.ts)
- Test files should include "Test" in the name (e.g., OfferDetail.test.tsx)
- Test utility files should include "Test" in the name (e.g., setupTest.ts)

TODO: Standardize File Format to TypeScript
- Convert .js files to .ts/.tsx for better type safety and IDE support
- Priority: Medium
- Benefits:
  - Type safety
  - Better IDE support
  - Consistent codebase
- Steps:
  - Identify all .js files
  - Create type definitions
  - Convert files one at a time
  - Update imports 

TODO: Test Coverage Improvements
- Priority: High (After completing all screen tests)
- Areas needing attention:
  1. Components (offer-detail/*):
     - PostingDetailsSection.tsx (55.55% coverage)
     - StatusBanner.tsx (75% coverage)
     - MessageItem.tsx (80% coverage)
     - Improve branch coverage in ScrollableContent.tsx
  
  2. Hooks:
     - useFormValidation.js (36.36% coverage)
     - useWithdrawOffer.js (22.22% coverage)
     - Improve branch coverage in useOfferDetails.js
  
  3. Services:
     - notificationService.ts (9.61% coverage)
     - firebaseService.js (18.52% coverage)
     - cacheManager.ts (33.33% coverage)
  
  4. Utils:
     - cacheManager.ts (20% coverage)
     - firebaseCleanup.ts (20% coverage)
     - Improve branch coverage in dateUtils.ts

- Action Items:
  1. Add comprehensive tests for utility functions
  2. Increase branch coverage in hooks
  3. Add integration tests for services
  4. Improve component test coverage with more edge cases
  5. Add error handling test cases

Note: These coverage improvements should be addressed after completing test implementation for all screens to maintain consistent test coverage across the application. 

TODO: Message Data Structure Migration
- Priority: High (Before Production)
- Current State:
  - Legacy messages don't have 'id' field
  - New messages include 'id' field for better tracking
- Required Changes:
  1. Add migration script to update legacy messages with IDs
  2. Update message validation to require IDs in production
  3. Add data integrity checks for message structure
  4. Document message format changes
- Note: Currently allowing legacy format for test data only 