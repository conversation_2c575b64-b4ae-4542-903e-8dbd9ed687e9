# Execution Protocol for AI Agent


This execution protocol provides:
1. Precise step-by-step instructions
2. Verification points
3. Error recovery procedures
4. Success criteria
5. Rollback procedures
6. Logging requirements

## Pre-Migration Commands
```bash
# 1. Create backup of current state
git checkout -b firebase-listener-migration-backup
git add .
git commit -m "backup: pre-firebase-listener-migration"

# 2. Create working branch
git checkout -b feat/firebase-listener-migration
```

## Execution Order

### Phase 1: Infrastructure Setup
HALT_POINT: "INFRASTRUCTURE"
1. Create directory structure exactly as specified
2. Implement base interfaces and types
3. Create empty files for all services
4. Verify structure:
```bash
find ./src/services/listeners -type f
find ./src/services/firebase -type f
```

### Phase 2: Base Implementation
HALT_POINT: "BASE_IMPLEMENTATION"
1. Implement BaseListener class
2. Implement ListenerRegistry
3. Implement ListenerStateManager
4. Run type checks:
```bash
tsc --noEmit
```

### Phase 3: Collection-Specific Services
HALT_POINT: "SERVICES"
1. Implement in this exact order:
   - PostingListener
   - OfferListener
   - NotificationListener
   - MessageListener
2. Verify each implementation against interfaces
3. Log implementation status:
```typescript
console.log('[Migration] Service implementation complete:', {
  service: 'PostingListener',
  methods: ['setup', 'cleanup', 'handleSnapshot'],
  timestamp: new Date().toISOString()
});
```

### Phase 4: Migration Execution
HALT_POINT: "MIGRATION"

For each file in Required Files list:
1. Create backup:
```bash
cp {file} {file}.bak
```

2. Update imports:
```typescript
// Search pattern
grep -l "firebaseService" {file}

// Replace pattern
import { 
  PostingListener,
  OfferListener,
  NotificationListener 
} from '../services/listeners';
```

3. Replace listener implementation:
```typescript
// Search pattern
createAndRegisterListener(

// Replace with
const listener = new PostingListener(componentId);
const { unsubscribe, listenerId } = await listener.setup(
```

4. Verify file:
```bash
# Type check
tsc {file} --noEmit

# Lint check
eslint {file}
```

5. Log migration:
```typescript
console.log('[Migration] File migration complete:', {
  file: '{file}',
  changes: ['imports', 'listener_implementation'],
  timestamp: new Date().toISOString()
});
```

### Phase 5: Cleanup
HALT_POINT: "CLEANUP"

1. Verify all files migrated:
```bash
# Should return 0 matches
grep -r "createAndRegisterListener" ./src
```

2. Remove safe-to-delete code:
```typescript
// Search and remove patterns matching Safe-to-Remove section
// PRESERVE all patterns matching Must-Preserve section
```

3. Log cleanup:
```typescript
console.log('[Migration] Cleanup complete:', {
  removedPatterns: ['createAndRegisterListener', 'unregisterListener'],
  preservedHooks: ['usePosting', 'useOffer', 'useNotifications'],
  timestamp: new Date().toISOString()
});
```

### Phase 6: Verification
HALT_POINT: "VERIFICATION"

1. Run all checks:
```bash
# Type checks
tsc --noEmit

# Lint checks
eslint ./src

# Test runs
npm test
```

2. Verify imports:
```bash
# Should match Required Files count
grep -r "from '../services/listeners'" ./src | wc -l
```

3. Verify hook preservation:
```bash
# Should match original count
find ./src/hooks -type f | wc -l
```

4. Log verification:
```typescript
console.log('[Migration] Verification complete:', {
  typeChecks: 'passed',
  lintChecks: 'passed',
  tests: 'passed',
  preservedHooks: ['usePosting', 'useOffer', 'useNotifications'],
  timestamp: new Date().toISOString()
});
```

## Error Recovery Protocol

If any HALT_POINT fails:
1. Log error state
2. Revert to last successful HALT_POINT
3. Retry phase with increased logging

## Success Criteria

Migration is complete when:
1. All HALT_POINTs passed
2. All verification checks passed
3. No old patterns found
4. All Required Files updated
5. All hooks preserved
6. All tests passing

## Rollback Protocol

If migration fails:
```bash
# Restore from backup branch
git checkout firebase-listener-migration-backup
git checkout -b firebase-listener-migration-retry
```
```





# Firebase Listener Services Migration Plan

## 0. Prerequisites and Context

### Project Structure
Required source files and their locations:
```
src/
  ├── services/
  │   └── firebaseService.js     # Current implementation
  ├── hooks/
  │   ├── usePosting.ts         # Current posting hooks
  │   ├── useOffer.ts           # Current offer hooks
  │   └── useNotifications.ts   # Current notification hooks
  ├── screens/
  │   ├── PostingDetail.tsx     # Uses posting listeners
  │   └── OfferDetail.tsx       # Uses offer listeners
  └── components/
      └── NotificationBell.tsx  # Uses notification listeners
```

### Current Implementation Patterns

1. Listener Creation Pattern:
```typescript
// Current pattern used throughout the codebase
const unsubscribe = createAndRegisterListener(
  'collection_name',
  query,
  handleSnapshot,
  componentId
);
```

2. Cleanup Pattern:
```typescript
useEffect(() => {
  const unsubscribe = createAndRegisterListener(...);
  return () => unsubscribe();
}, [dependencies]);
```

### Data Structures

1. Posting Document Structure:
```typescript
interface Posting {
  id: string;
  userId: string;
  title: string;
  description: string;
  status: 'active' | 'closed';
  createdAt: Timestamp;
  updatedAt: Timestamp;
}
```

2. Offer Document Structure:
```typescript
interface Offer {
  id: string;
  postingId: string;
  userId: string;
  status: 'pending' | 'accepted' | 'rejected';
  createdAt: Timestamp;
}
```

3. Notification Document Structure:
```typescript
interface Notification {
  id: string;
  userId: string;
  type: 'offer' | 'message' | 'system';
  read: boolean;
  createdAt: Timestamp;
  data: Record<string, any>;
}
```

### Required Dependencies
```json
{
  "firebase": "^9.x.x",
  "react": "^18.x.x",
  "typescript": "^4.x.x"
}
```

### Error Handling Requirements

1. Error Types to Handle:
```typescript
type FirebaseError = {
  code: string;
  message: string;
  details?: any;
};

type ListenerError = {
  type: 'setup' | 'snapshot' | 'cleanup';
  error: FirebaseError;
  context: {
    collection: string;
    documentId?: string;
    componentId: string;
  };
};
```

2. Error Recovery Strategy:
```typescript
interface ErrorRecoveryStrategy {
  maxRetries: number;
  backoffMs: number;
  timeoutMs: number;
}
```

### Logging Format

1. Listener Events:
```typescript
interface ListenerEvent {
  timestamp: string;
  type: 'setup' | 'update' | 'cleanup';
  collection: string;
  documentId?: string;
  componentId: string;
  duration?: number;
  error?: ListenerError;
}
```

### State Management Requirements

1. Listener States:
```typescript
type ListenerState = 
  | 'initializing'
  | 'active'
  | 'error'
  | 'cleanup_pending'
  | 'cleanup_complete';
```

2. State Transitions:
```typescript
interface StateTransition {
  from: ListenerState;
  to: ListenerState;
  timestamp: number;
  metadata?: Record<string, any>;
}
```

### Migration Validation Rules

1. Performance Thresholds:
```typescript
const PERFORMANCE_THRESHOLDS = {
  setupTime: 500, // ms
  cleanupTime: 200, // ms
  memoryLimit: 1024 * 1024, // 1MB
  maxListenersPerComponent: 5
};
```

2. Validation Checks:
```typescript
interface ValidationCheck {
  name: string;
  check: () => Promise<boolean>;
  errorMessage: string;
  severity: 'warning' | 'error';
}
```

## 1. New Service Structure (Firebase-Only)

Create the following service files:
```
services/
  ├── listeners/
  │   ├── index.ts               # Exports all Firebase listener services
  │   ├── baseFirebaseListener.ts # Base Firebase listener class/utilities
  │   ├── postingListener.ts     # Posting-specific listeners
  │   ├── offerListener.ts       # Offer-specific listeners
  │   ├── notificationListener.ts # Notification-specific listeners
  │   └── messageListener.ts     # Message/Discussion listeners
  └── firebase/
      └── listenerRegistry.ts    # Core registry functionality
```

## 2. Migration Steps

### Phase 1: Setup Base Infrastructure

1. Create `baseFirebaseListener.ts`:
   - Move Firebase listener registration logic
   - Implement Firebase-specific utilities
   - Port state management and cleanup coordination

2. Create `listenerRegistry.ts`:
   - Move Firebase registry data structures
   - Implement collection-specific tracking
   - Port debugging and monitoring tools

### Phase 2: Implement Collection Services

For each Firebase collection service:
1. Create collection-specific listener class
2. Implement standard Firebase listener methods:
   - setupListener()
   - cleanupListener()
   - handleSnapshot()
   - handleError()

### Phase 3: Code Migration

1. Update imports in all files:
```typescript
// Before
import { createAndRegisterListener } from '../services/firebaseService';

// After
import { PostingListener } from '../services/listeners';
```

2. Replace listener creation:
```typescript
// Before
const { unsubscribe, listenerId } = createAndRegisterListener('postings', ...);

// After
const listener = new PostingListener(componentId);
const { unsubscribe, listenerId } = await listener.setup(postingId);
```

### Phase 4: Cleanup & Testing

1. Remove old listener code from:
   - firebaseService.js
   - notificationService.ts
   - existing hooks

2. Test migration:
   - Verify listener registration
   - Check cleanup processes
   - Validate component tracking
   - Monitor memory usage

## 3. Implementation Details

### Base Listener Interface
```typescript
interface BaseListener {
  setup(id: string, options?: ListenerOptions): Promise<ListenerResult>;
  cleanup(): Promise<void>;
  getStats(): ListenerStats;
}
```

### Collection-Specific Implementation
```typescript
class PostingListener implements BaseListener {
  private registry: ListenerRegistry;
  private componentId: string;

  constructor(componentId: string) {
    this.componentId = componentId;
    this.registry = getListenerRegistry();
  }

  async setup(postingId: string): Promise<ListenerResult> {
    // Implementation
  }
}
```

### Implementation Examples

#### Base Listener Implementation
```typescript
abstract class BaseListener {
  protected registry: ListenerRegistry;
  protected componentId: string;
  protected listenerStateManager: ListenerStateManager;

  constructor(componentId: string) {
    this.componentId = componentId;
    this.registry = getListenerRegistry();
    this.listenerStateManager = new ListenerStateManager();
  }

  protected async registerListener(
    collection: string,
    id: string,
    cleanup: () => void,
    metadata: ListenerMetadata
  ): Promise<string> {
    const registryId = `${collection}_${id}_${Date.now()}`;
    
    if (!this.listenerStateManager.startTransition(registryId, 'inactive', 'initializing')) {
      throw new Error(`Registration blocked for ${registryId}`);
    }

    try {
      // Implementation from existing firebaseService.js
      return registryId;
    } finally {
      this.listenerStateManager.completeTransition(registryId);
    }
  }
}
```

#### Collection-Specific Implementation
```typescript
class PostingListener extends BaseListener {
  async setup(postingId: string, options?: ListenerOptions): Promise<ListenerResult> {
    const q = query(collection(db, 'postings'), where('id', '==', postingId));
    
    const unsubscribe = onSnapshot(q, 
      (snapshot) => this.handleSnapshot(snapshot),
      (error) => this.handleError(error)
    );

    const listenerId = await this.registerListener('postings', postingId, unsubscribe, {
      collection: 'postings',
      type: 'detail',
      source: this.componentId
    });

    return { unsubscribe, listenerId };
  }

  private handleSnapshot(snapshot: QuerySnapshot): void {
    // Implementation
  }
}
```

### State Management Migration
```typescript
// Current state management from firebaseService.js
const listenerStateManager = {
  transitions: new Map<string, TransitionState>(),
  locks: new Set<string>(),
  // ... existing implementation
};

// New TypeScript class implementation
class ListenerStateManager {
  private transitions = new Map<string, TransitionState>();
  private locks = new Set<string>();

  // Port existing methods with type safety
}
```

### Cleanup Coordination
```typescript
class CleanupCoordinator {
  private groups = new Map<string, CleanupGroup>();
  private activeCleanups = new Set<string>();

  // Port existing methods from cleanupCoordinator
}
```

### Migration Steps Details

1. File Structure Creation:
```bash
mkdir -p services/listeners
mkdir -p services/firebase

# Create base files
touch services/listeners/index.ts
touch services/listeners/baseListener.ts
touch services/listeners/postingListener.ts
touch services/listeners/offerListener.ts
touch services/listeners/notificationListener.ts
touch services/listeners/messageListener.ts
touch services/firebase/listenerRegistry.ts
```

2. Component Migration Example:
```typescript
// Before (in PostingDetail.tsx)
const { unsubscribe } = createAndRegisterListener(
  'postings',
  postingId,
  handleSnapshot,
  componentId
);

// After
const postingListener = new PostingListener(componentId);
const { unsubscribe, listenerId } = await postingListener.setup(postingId, {
  onData: handleSnapshot,
  logging: { enabled: true, level: 'debug' }
});
```

### Testing Requirements

1. Unit Tests:
```typescript
describe('PostingListener', () => {
  test('setup should register listener correctly', async () => {
    // Test implementation
  });

  test('cleanup should remove all references', async () => {
    // Test implementation
  });
});
```

2. Integration Tests:
```typescript
describe('Listener Migration Integration', () => {
  test('component should receive updates', async () => {
    // Test implementation
  });
});
```

## 4. Migration Checklist

- [ ] Create new directory structure
- [ ] Implement base listener functionality
- [ ] Create collection-specific services
- [ ] Update all listener references
- [ ] Add comprehensive logging
- [ ] Test all listener scenarios
- [ ] Remove old implementation
- [ ] Document new listener usage

## 5. Rollback Plan

1. Keep old implementation files with `.old` extension
2. Maintain version mapping of changed files
3. Create restore script for emergency rollback

## Current Implementation Reference

### Existing Listener Creation
```typescript
// Current implementation in firebaseService.js
export const createAndRegisterListener = (
  collection: string,
  query: Query,
  onSnapshot: (snapshot: QuerySnapshot) => void,
  componentId: string
): ListenerRegistration => {
  // Implementation details here
};
```

### Required Interfaces

```typescript
interface ListenerOptions {
  query?: Query;
  onData?: (data: any) => void;
  onError?: (error: Error) => void;
  metadata: {
    collection: string;
    type: string;
    source: string;
    startTime?: number;
    componentId?: string;
  };
  logging?: {
    enabled: boolean;
    level: 'debug' | 'info' | 'error';
  };
}

interface ListenerStats {
  componentId: string;
  listenerId: string;
  collection: string;
  startTime: number;
  updateCount: number;
  lastUpdateTime: number;
  active: boolean;
  createdAt: number;
  lastActive: number;
  errors: Array<{
    timestamp: number;
    message: string;
  }>;
}

interface ListenerResult {
  unsubscribe: () => void;
  listenerId: string;
}

interface ListenerRegistry {
  cleanup: Map<string, {
    cleanup: () => void;
    createdAt: number;
    lastActive: number;
    active: boolean;
    collection: string;
    id: string;
  }>;
  stats: {
    totalListeners: number;
    activeScreens: Set<string>;
    lastCleanup: number | null;
  };
}

interface ListenerLog {
  registryId: string;
  collection: string;
  id: string;
  state: 'active' | 'inactive' | 'error';
  timestamp: string;
  metadata?: {
    componentId?: string;
    source?: string;
    error?: any;
  };
}
```

### Logging Requirements
```typescript
// Required log format
interface ListenerLog {
  timestamp: string;
  level: 'debug' | 'info' | 'error';
  component: string;
  action: string;
  details: {
    listenerId?: string;
    collection?: string;
    documentId?: string;
    error?: any;
  };
}
```

### Performance Requirements
- Maximum listener setup time: 500ms
- Memory usage per listener: < 1MB
- Cleanup timeout: 2000ms

### Error Handling Strategy
1. Listener Setup Failures
2. Network Disconnection
3. Permission Changes
4. Data Structure Changes


6. Migration Checklist Updates:

### Migration Validation Checklist
- [ ] Verify listener registration timing
- [ ] Confirm cleanup of all resources
- [ ] Check memory usage patterns
- [ ] Validate error handling paths
- [ ] Test concurrent listener scenarios
- [ ] Verify component tracking
- [ ] Test cleanup coordination
- [ ] Validate state transitions
- [ ] Check logging compliance
- [ ] Performance metrics validation

### Listener Type Mapping

1. Firebase Listeners
   - Collection Listeners (`offers`, `postings`, )
   - Document Listeners (postingId, offerId, discussionId)
   
2. React Native Event Listeners
   - Navigation Events
   - Push Notification Events

3. Custom Event Listeners
   - Notification Bell
   - Message Updates
   - Auth State

### Migration Mapping Table

| Source Listener Type | Target Implementation | Migration Path |
|---------------------|----------------------|----------------|
| Direct `onSnapshot` | `createAndRegisterListener` | Replace direct calls with wrapped version |
| Push Notification | Enhanced Error Handling | Add to notification registry |
| Navigation Events | Coordinate with Cleanup | Add pre-navigation checks |

### Component-Specific Migrations

1. PostingDetail Screen
   - Primary: Posting document listener
   - Secondary: Offers collection listener
   - Cleanup: Coordinated group cleanup

2. NotificationBell Component
   - Primary: User notification listener
   - Cleanup: Clear on profile actions

3. OfferDetail Screen
   - Primary: Message listener
   - Secondary: Offer status listener
   - Cleanup: Individual cleanup

### Listener Registry Structure

```typescript
interface ListenerRegistry {
  offers: Map<string, ListenerEntry>;
  postings: Map<string, ListenerEntry>;
  cleanup: Map<string, CleanupFunction>;
  stats: {
    totalListeners: number;
    activeScreens: Set<string>;
  }
}

interface ListenerEntry {
  count: number;
  timestamp: number;
  listeners: Map<string, Function>;
  metadata: Record<string, any>;
}
```

### Migration Steps Per Component

1. Identify all direct Firebase listeners
2. Replace with `createAndRegisterListener`
3. Add component tracking
4. Implement cleanup coordination
5. Add error boundaries
6. Verify cleanup timing
7. Add performance monitoring
8. Update logging

### Logging Requirements

All migrated listeners must include:
- Setup/teardown logs
- Error state logs
- Performance metrics
- Cleanup confirmation
- State transition logs

### Testing Scenarios

1. Normal Flow
   - Setup verification
   - Cleanup timing
   - Resource release

2. Error Cases
   - Network disconnection
   - Permission changes
   - Concurrent operations

3. Edge Cases
   - Rapid mount/unmount
   - Multiple listeners
   - Navigation interrupts

## Code Safety and Dependencies

### Protected Code Patterns
DO NOT REMOVE these patterns when found:
```typescript
// Protected Pattern 1: Hook Definition
export function usePosting(postingId: string) {
  // Keep hook structure, only replace listener implementation
}

// Protected Pattern 2: Component Logic
function PostingDetail({ postingId }) {
  // Keep component logic, only replace listener creation
}
```

### Required Code Replacements

1. Files Requiring Import Updates:
```typescript
// REQUIRED - Update ALL these files:
src/
  ├── screens/
  │   ├── PostingDetail.tsx        // REQUIRED
  │   ├── PostingList.tsx         // REQUIRED
  │   ├── OfferDetail.tsx         // REQUIRED
  │   └── OfferList.tsx          // REQUIRED
  ├── components/
  │   ├── NotificationBell.tsx    // REQUIRED
  │   └── PostingCard.tsx        // REQUIRED
  └── hooks/
      ├── usePosting.ts          // REQUIRED
      ├── useOffer.ts           // REQUIRED
      └── useNotifications.ts   // REQUIRED
```

2. Import Replacement Patterns:
```typescript
// OLD IMPORTS TO REPLACE
import { 
  createAndRegisterListener, 
  unregisterListener 
} from '../services/firebaseService';

// NEW REQUIRED IMPORTS
import { 
  PostingListener,
  OfferListener,
  NotificationListener 
} from '../services/listeners';
```

### Code Removal Safety Checklist

1. Safe to Remove:
```typescript
// ✅ Safe to remove: Direct Firebase listener calls
const unsubscribe = onSnapshot(query, ...);

// ✅ Safe to remove: Old listener registration
const { listenerId } = createAndRegisterListener(...);

// ✅ Safe to remove: Deprecated cleanup calls
unregisterListener(listenerId);
```

2. Must Preserve:
```typescript
// 🚫 DO NOT REMOVE: Hook business logic
const transformData = (data) => {...};
const validatePosting = (posting) => {...};

// 🚫 DO NOT REMOVE: Error handling
const handleError = (error) => {...};

// 🚫 DO NOT REMOVE: Custom event handlers
const onPostingUpdate = (posting) => {...};
```

### Import Verification Matrix

| File Path | Required Imports | Required Methods |
|-----------|-----------------|------------------|
| `screens/PostingDetail.tsx` | `PostingListener` | `setup(), cleanup()` |
| `screens/OfferDetail.tsx` | `OfferListener` | `setup(), cleanup()` |
| `components/NotificationBell.tsx` | `NotificationListener` | `setup(), cleanup()` |

### Migration Verification Steps

1. Import Check:
```bash
# Required grep patterns to verify imports
grep -r "from '../services/listeners'" src/screens/
grep -r "from '../services/listeners'" src/components/
grep -r "from '../services/listeners'" src/hooks/
```

2. Hook Safety Check:
```typescript
// Verify these structures remain intact
export function usePosting(postingId: string) {
  // Only listener implementation should change
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // Business logic must remain
  useEffect(() => {
    // Only this part should be updated
    const listener = new PostingListener();
    ...
  }, [postingId]);

  return { data, loading, error };
}
```

3. Component Safety Check:
```typescript
// Verify component structure preservation
function PostingDetail({ postingId }) {
  // State management must remain
  const [localState, setLocalState] = useState();
  
  // Business logic must remain
  const handleUpdate = (data) => {
    setLocalState(data);
  };

  // Only listener setup should change
  useEffect(() => {
    const listener = new PostingListener();
    ...
  }, [postingId]);
}
```
