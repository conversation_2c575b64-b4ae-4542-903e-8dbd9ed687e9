TEST PLAN: Offer Detail Screen

+ for pass
- for fail

1. Container Layout Tests
   a. Initial View
   - [+] Verify all three containers are visible (Posting Details, Offer Details, Messaging)
   - [+] Verify default collapsed/expanded states
   - [+] Check if messaging container fills available space when others are collapsed
   
   b. Scrolling Behavior
   - [+] Try scrolling when all containers are collapsed
   - [+] Try scrolling when posting details is expanded
   - [+] Try scrolling when offer details is expanded
   - [+] Try scrolling when both containers are expanded

2. Posting Details Container Tests
   a. Expansion/Collapse
   - [+] Click to expand posting details
   - [+] Verify content is visible and scrollable 
   - [+] Verify header stays at top while scrolling 
   - [+] Click to collapse posting details
   
   b. Navigation
   - [+] Click on chevron right to navigate to Posting Detail Screen 
   - [+] Verify correct posting data is passed to Posting Detail Screen 

3. Offer Details Container Tests
   a. Expansion/Collapse
   - [+] Click to expand offer details
   - [+] Verify content is visible and scrollable 
   - [+] Verify header stays at top while scrolling 
   - [+] Click to collapse offer details
   
   b. Owner Actions (if user is offer owner)
   - [+] Verify Edit and Withdraw buttons are visible
   - [+] Click Edit button navigates to Edit Offer screen
   - [+] Click Withdraw button shows confirmation dialog

4. Messaging Container Tests
   a. Message List
   - [+] Verify messages are displayed in correct order
   - [+] Check if read receipts are visible
   - [+] Verify timestamps are displayed correctly
   - [+] Test pull-to-refresh functionality
   - [+] Test loading more messages on scroll 
   
   b. Message Input
   - [+] Verify input field is visible for authorized users
   - [+] Test sending a new message 
   - [+] Verify message appears in the list
   - [-] Check if keyboard handling works correctly > partial pass, when the message container displays the most recent message at the bottom, i click the input field to activate the keyboard. when the keyboard is activated, the message container snaps to the top of the messages and the user request to scroll to the bottom to see the most recent message.keyboard activation action should not affect the displayed messages.

5. Permission-Based Tests
   a. Offer Owner View
   - [+] Verify Edit/Withdraw buttons are visible
   - [+] Verify can send messages > can send messages however just like the keyboard activation issue, the message container snaps to the top of the messages and the user request to scroll to the bottom to see the most recent message.
   
   b. Posting Owner View
   - [+] Verify Edit/Withdraw buttons are not visible
   - [+] Verify can send messages > can send messages however just like the keyboard activation issue, the message container snaps to the top of the messages and the user request to scroll to the bottom to see the most recent message.
   
   c. Third Person View
   - [+] Verify Edit/Withdraw buttons are not visible
   - [+] Verify cannot send messages
   - [+] Verify can view discussion 

6. Status-Based Tests
   a. Active Offer
   - [+] Verify no status banner
   - [+] Verify messaging is enabled
   
   b. Withdrawn Offer
   - [+] Verify withdrawal banner is displayed 
   - [+] Verify messaging is disabled 
   - [+] Verify correct message for withdrawn state 

Please test each scenario and provide:
1. Pass/Fail status
2. Any error messages or console logs
3. Screenshots if possible
4. Steps to reproduce any failures