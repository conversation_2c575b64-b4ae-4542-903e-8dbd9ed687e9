FILE STRUCTURE:
  Core Application Files
    App.tsx - Main application component, sets up navigation and providers
    index.js - Entry point of the application, initializes gesture handler and app registration
    firebase.ts - Firebase initialization and configuration
    firebaseConfig.ts - Firebase credentials and configuration constants

  Navigation
    navigation/AppNavigator.tsx - Main navigation configuration, defines all app routes and screens

  Screens
  1. Authentication
    screens/LoginScreen.tsx - Initial login screen
    screens/SignUp.tsx - User registration screen
    screens/SignUpConfirmation.tsx - Verification screen after signup
    screens/ForgotPassword.tsx - Password recovery screen
  2. Main Features
    screens/HomeScreen.tsx - Main map view with postings
    screens/PostingDetail.tsx - Detailed view of a posting
    screens/CreatePostingScreen.tsx - Create new posting form
    screens/EditPostingScreen.tsx - Edit existing posting
    screens/MakeOffer.tsx - Create offer for a posting
    screens/OfferDetail.tsx - Detailed view of an offer
    screens/EditOffer.tsx - Edit existing offer
  3. User Management
    screens/ProfileScreen.tsx - User profile management
    screens/MyPostingsScreen.tsx - List of user's postings
    screens/MyOffersScreen.tsx - List of user's offers
    screens/MyFavoritesScreen.tsx - Saved/favorite postings
    screens/NotificationsScreen.tsx - User notifications
    screens/NotificationSettingsScreen.tsx - Notification preferences
    screens/LocationSettings.tsx - Location preferences

  4. Components
    components/MapMarker.tsx - Custom map marker component
    components/FormInput.tsx - Reusable form input component
    components/SearchInput.tsx - Search input with clear functionality
    components/NotificationBell.tsx - Notification indicator component
    components/DraggableListContainer.tsx - Draggable list implementation
    components/ListItem.tsx - Reusable list item component

  5. Services
    services/firebaseService.js - Firebase operations (CRUD)
    services/notificationService.ts - Notification handling
    services/fcmService.ts - Firebase Cloud Messaging implementation
    services/locationService.js - Location services
    services/authService.ts - Authentication operations
    services/notificationQueueService.ts - Notification queue management

  6. Hooks
    hooks/useNotifications.ts - Notification management
    hooks/useNotificationSettings.ts - Notification preferences management
    hooks/useOffers.js - Offer data management
    hooks/usePostingDetails.js - Posting data fetching
    hooks/useFirebaseSubscriptions.js - Firebase real-time updates
    hooks/useWithdrawOffer.js - Offer withdrawal functionality
    hooks/useAuthUser.js - Authentication state management
    hooks/useFavorites.js - Favorites management
    hooks/useAddPosting.js - Posting creation
    hooks/useFormValidation.js - Form validation logic
    hooks/useGestureHandler.js - Gesture handling
    hooks/useLocationInitialization.js - Location setup
    hooks/useNotificationSetup.ts - Notification initialization
    hooks/useOfferSubmission.js - Offer submission logic
    hooks/useProfileActions.js - Profile action handlers

  7. Utils
    utils/dateUtils.ts - Date formatting utilities
    utils/errorLogger.ts - Error logging functionality
    utils/fcmHelper.ts - FCM helper functions
    utils/notificationHelper.ts - Notification utility functions
    utils/cacheManager.ts - Data caching implementation
    utils/errorRecovery.ts - Error handling utilities
    utils/retryUtils.ts - Retry mechanism for failed operations
    utils/firebaseCleanup.ts - Firebase subscription cleanup
    utils/notificationManager.ts - Notification state management
    utils/notificationTester.ts - Notification testing utilities
    utils/pushNotifications.ts - Push notification registration
    utils/notificationCategories.ts - Notification type definitions
    utils/notificationActionHandler.ts - Notification action handling
    utils/notificationConfig.ts - Notification configuration

  8. Types
    types/notifications.ts - Notification type definitions
    types/navigation.ts - Navigation type definitions
    types/posting.ts - Posting interface definitions
    types/offer.ts - Offer interface definitions
    types/firebase.ts - Firebase type definitions
    types/fcm.ts - FCM type definitions
    types/env.d.ts - Environment variable types

  9. iOS Configuration
    ios/satbana/AppDelegate.mm - iOS app delegate implementation
    ios/satbana/AppDelegate.h - iOS app delegate header
    ios/satbana/AppDelegate.m - iOS app delegate implementation (Objective-C)
    ios/Podfile - iOS dependencies configuration
    ios/GoogleService-Info.plist - Firebase iOS configuration

  10. Configuration Files
    .env - Environment variables
    babel.config.js - Babel configuration
    tsconfig.json - TypeScript configuration
    firestore.rules - Firestore security rules
    EXPO_TO_RN_MIGRATION.md - Migration tracking
    eas.json - Expo Application Services config

  11. Firebase Functions
    functions/index.js - Cloud Functions implementation for notifications
    functions/firebase.json - Firebase configuration
    
  This index represents the main components of your codebase. Each file has a specific purpose in the application's architecture, following a modular and maintainable structure.