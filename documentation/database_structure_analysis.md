Omers-MBP:satbana-admin omeryazici$ node databaseStructureScanner.mjs
Starting database structure analysis...

(node:47077) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
Found 7 root collections

▸ Analyzing collection: discussions
   Found 146 documents

   Collection: discussions
   Schema variations: 5
   Unique fields: 7

   Schema (81 docs):
   Fields: createdAt, messages, offerId, offerOwnerId, postingOwnerId
   Example ID: 006Ta8KNCE8XJMtjkHLu

   Schema (9 docs):
   Fields: createdAt, lastUpdated, messages, offerId, offerOwnerId, postingOwnerId
   Example ID: 0X2mdTx7jpBoB9C4XpGu

   Schema (49 docs):
   Fields: messages, offerId, offerOwnerId, postingOwnerId
   Example ID: 15N3LnCYSgnmw6UuJPH3

   Schema (3 docs):
   Fields: lastUpdated, messages, offerId, offerOwnerId, postingOwnerId
   Example ID: Dr782UGIMNRmIJpDbpzr

   Schema (4 docs):
   Fields: createdAt, lastUpdated, messages, offerId, offerOwnerId, postingOwnerId, status
   Example ID: IO2ArGtNKNBn1c3y65hT

   Field Type Distribution:
   createdAt: timestamp (94)
   messages: array (146)
   offerId: string (146)
   offerOwnerId: string (146)
   postingOwnerId: string (146)
   lastUpdated: timestamp (16)
   status: string (4)

▸ Analyzing collection: notifications
   Found 385 documents

   Collection: notifications
   Schema variations: 21
   Unique fields: 27

   Schema (156 docs):
   Fields: body, createdAt, delivered, deliveredAt, fcmMessageId, offerId, read, recipientId, senderId, title, type
   Example ID: 0384OAhWNDHaTuKTZuet

   Schema (76 docs):
   Fields: body, createdAt, delivered, deliveredAt, fcmMessageId, offerId, postingId, read, recipientId, senderId, title, type
   Example ID: 04ZAdqgT6xry91UrzPsM

   Schema (57 docs):
   Fields: body, createdAt, data.currentDescription, data.currentPrice, data.currentStatus, data.timestamp, delivered, deliveredAt, fcmMessageId, offerId, postingId, read, recipientId, senderId, title, type
   Example ID: 0ATQwSwVfk7YgIgEjs0D

   Schema (4 docs):
   Fields: body, createdAt, delivered, error, errorAt, offerId, postingId, read, recipientId, senderId, title, type, updatedAt
   Example ID: 1wDsuO9iwBotZxmJ8zw8

   Schema (2 docs):
   Fields: body, createdAt, delivered, error, errorAt, offerId, postingId, read, recipientId, senderId, title, type
   Example ID: 2NaKU1z7PilcfNimpQrM

   Schema (14 docs):
   Fields: body, createdAt, data.currentStatus, data.postingDeleted, data.postingTitle, data.timestamp, delivered, deliveredAt, fcmMessageId, offerId, postingId, read, recipientId, senderId, title, type
   Example ID: 2jgHApTMkEbnkJTgPnKx

   Schema (2 docs):
   Fields: body, createdAt, data.currentDescription, data.currentPrice, data.currentStatus, data.timestamp, delivered, offerId, postingId, read, recipientId, senderId, title, type
   Example ID: 3FJd8TzM8NgbaH2KNm3u

   Schema (1 docs):
   Fields: body, createdAt, data.currentStatus, data.timestamp, delivered, deliveredAt, fcmMessageId, offerId, postingId, read, recipientId, senderId, title, type
   Example ID: 3RWcifJYsV6AJL4V2COM

   Schema (8 docs):
   Fields: body, createdAt, offerId, read, recipientId, senderId, title, type
   Example ID: 4k9ic1QJprLbbUWbWi0n

   Schema (21 docs):
   Fields: body, createdAt, offerId, postingId, read, recipientId, senderId, title, type, updatedAt
   Example ID: 6CMYh2upgUatfLrEArgP

   Schema (6 docs):
   Fields: body, createdAt, delivered, deliveredAt, fcmMessageId, postingId, read, recipientId, senderId, title, type
   Example ID: 6vt4NqZ2bBc9EbccxEDG

   Schema (6 docs):
   Fields: body, createdAt, delivered, deliveredAt, fcmMessageId, offerId, postingId, read, recipientId, senderId, title, type, updatedAt
   Example ID: 7BUigURIJvTOTkyFIBmW

   Schema (6 docs):
   Fields: body, createdAt, offerId, read, recipientId, senderId, title, type, updatedAt
   Example ID: 7bDyW8VAb3itzmPgBKXn

   Schema (1 docs):
   Fields: body, createdAt, delivered, offerId, postingId, read, recipientId, senderId, title, type
   Example ID: HJ5mshZT0nEsqyNmrCQf

   Schema (5 docs):
   Fields: body, createdAt, data.currentDescription, data.currentPrice, data.currentStatus, data.offerId, data.postingId, data.timestamp, data.type, delivered, deliveredAt, fcmMessageId, offerId, postingId, read, recipientId, senderId, title, type
   Example ID: IsHvFgwLVziFmVIHBEhb

   Schema (8 docs):
   Fields: body, createdAt, offerId, postingId, read, recipientId, senderId, title, type
   Example ID: L8Tbs5njFvcLYTQxiipO

   Schema (6 docs):
   Fields: body, createdAt, delivered, error, errorAt, offerId, read, recipientId, senderId, title, type
   Example ID: LCXWx6kamrr35IDS7ep2

   Schema (1 docs):
   Fields: body, createdAt, delivered, deliveredAt, fcmMessageId, offerId, read, recipientId, senderId, title, type, updatedAt
   Example ID: MSIhIiugMLwJ5DFXLz2O

   Schema (2 docs):
   Fields: body, createdAt, data.currentStatus, data.postingDeleted, data.postingTitle, data.timestamp.nanoseconds, data.timestamp.seconds, data.withdrawnBy, delivered, deliveredAt, fcmMessageId, offerId, postingId, read, recipientId, senderId, title, type
   Example ID: atVdbP3dm251t5uPsYCv

   Schema (1 docs):
   Fields: body, createdAt, delivered, offerId, postingId, read, recipientId, senderId, title, type, updatedAt
   Example ID: e7ACgdRBQvoUkPdyZ9a4

   Schema (2 docs):
   Fields: body, createdAt, error, errorAt, offerId, read, recipientId, senderId, title, type
   Example ID: eITu4nNJ3IH8p0rKl8Xh

   Field Type Distribution:
   body: string (385)
   createdAt: timestamp (385)
   delivered: boolean (340)
   deliveredAt: timestamp (324)
   fcmMessageId: string (324)
   offerId: string (379)
   read: boolean (385)
   recipientId: string (385)
   senderId: string (385)
   title: string (385)
   type: string (385)
   postingId: string (206)
   data.currentDescription: string (64)
   data.currentPrice: number (64)
   data.currentStatus: string (81)
   data.timestamp: number (74)
   error: string (14)
   errorAt: timestamp (14)
   updatedAt: timestamp (39)
   data.postingDeleted: boolean (16)
   data.postingTitle: string (16)
   data.offerId: string (5)
   data.postingId: string (5)
   data.timestamp: timestamp (5)
   data.type: string (5)
   data.timestamp.nanoseconds: number (2)
   data.timestamp.seconds: number (2)
   data.withdrawnBy: string (2)

▸ Analyzing collection: offers
   Found 146 documents

   Collection: offers
   Schema variations: 11
   Unique fields: 12

   Schema (35 docs):
   Fields: description, postingId, price, status, timestamp, userId
   Example ID: 0CwU3pzxuf901bKjT8m6

   Schema (53 docs):
   Fields: createdAt, description, postingId, price, status, timestamp, userId
   Example ID: 0SohkRpCY13UUXGXMVUP

   Schema (13 docs):
   Fields: createdAt, description, lastUpdated, postingDeleted, postingId, price, status, timestamp, userId
   Example ID: 17orf7dNAarVxvvYvFzb

   Schema (22 docs):
   Fields: createdAt, description, lastUpdated, postingId, price, status, timestamp, userId
   Example ID: 4C0YsALRIMVUOF1QPjfT

   Schema (12 docs):
   Fields: description, postingId, price, status, timestamp, userId, withdrawnAt
   Example ID: 4gYV6kXiVLFbRW0s8t4c

   Schema (1 docs):
   Fields: createdAt, description, postingId, price, status, timestamp, updatedAt, userId
   Example ID: 8epnjEVerq7O07XKm6CI

   Schema (3 docs):
   Fields: description, lastUpdated, postingId, price, status, timestamp, userId
   Example ID: AJQ3nN0Pe7ciutO5b07L

   Schema (4 docs):
   Fields: createdAt, description, lastUpdated, postingDeleted, postingId, price, status, timestamp, userId, withdrawnBy
   Example ID: DfuMhyRJVc0aO0vBbIXn

   Schema (1 docs):
   Fields: description, lastUpdated, postingId, price, status, timestamp, updatedAt, userId
   Example ID: S7ZA517oO9z96uBo3lzm

   Schema (1 docs):
   Fields: description, lastUpdated, postingDeleted, postingId, price, status, timestamp, userId
   Example ID: cKja76f0AIzUw8rfsKzz

   Schema (1 docs):
   Fields: createdAt, description, lastUpdated, postingId, price, status, timestamp, updatedAt, userId
   Example ID: yEmLZB0HQzmABLEX6kOk

   Field Type Distribution:
   description: string (146)
   postingId: string (146)
   price: string (45)
   status: string (146)
   timestamp: timestamp (146)
   userId: string (146)
   createdAt: timestamp (94)
   price: number (101)
   lastUpdated: timestamp (45)
   postingDeleted: boolean (18)
   withdrawnAt: timestamp (12)
   updatedAt: timestamp (3)
   withdrawnBy: string (4)

▸ Analyzing collection: postings
   Found 74 documents

   Collection: postings
   Schema variations: 3
   Unique fields: 11

   Schema (46 docs):
   Fields: createdAt, description, lastUpdated, latitude, longitude, postingStatus, searchTerms, title, titleLower, userId
   Example ID: 01emTVYf0Au1Sj8OOuld

   Schema (27 docs):
   Fields: createdAt, deletedAt, description, lastUpdated, latitude, longitude, postingStatus, searchTerms, title, titleLower, userId
   Example ID: 0QWDoX61uaEt8s0wqapn

   Schema (1 docs):
   Fields: createdAt, description, latitude, longitude, postingStatus, searchTerms, title, titleLower, userId
   Example ID: nzBOGm0YIJu59tI5P9RT

   Field Type Distribution:
   createdAt: timestamp (74)
   description: string (74)
   lastUpdated: timestamp (73)
   latitude: number (74)
   longitude: number (74)
   postingStatus: string (74)
   searchTerms: array (74)
   title: string (74)
   titleLower: string (74)
   userId: string (74)
   deletedAt: timestamp (27)

▸ Analyzing collection: userLocations
   Found 46 documents

   Collection: userLocations
   Schema variations: 1
   Unique fields: 4

   Schema (46 docs):
   Fields: latitude, longitude, timestamp, userId
   Example ID: 0YzkzvOfXD9idlqmZ1gL

   Field Type Distribution:
   latitude: number (46)
   longitude: number (46)
   timestamp: timestamp (46)
   userId: string (46)

▸ Analyzing collection: userSettings
   Found 1 documents

   Collection: userSettings
   Schema variations: 1
   Unique fields: 8

   Schema (1 docs):
   Fields: createdAt, notifications.FAVORITE_NEW_MESSAGE, notifications.FAVORITE_POSTING_UPDATE, notifications.NEW_LOWER_OFFER, notifications.NEW_MESSAGE, notifications.NEW_OFFER, notifications.OFFER_STATUS_CHANGE, userId
   Example ID: ybEyGDxbvleCEXdTvrA2CgVTJ1y2

   Field Type Distribution:
   createdAt: timestamp (1)
   notifications.FAVORITE_NEW_MESSAGE: boolean (1)
   notifications.FAVORITE_POSTING_UPDATE: boolean (1)
   notifications.NEW_LOWER_OFFER: boolean (1)
   notifications.NEW_MESSAGE: boolean (1)
   notifications.NEW_OFFER: boolean (1)
   notifications.OFFER_STATUS_CHANGE: boolean (1)
   userId: string (1)

▸ Analyzing collection: users
   Found 10 documents

   Collection: users
   Schema variations: 7
   Unique fields: 14

   Schema (2 docs):
   Fields: favorites, favourites, score
   Example ID: 78UyAL4LEiSjwZn8VuoBbp4D85j2

   Schema (2 docs):
   Fields: favorites, fcmToken, fcmTokenUpdatedAt, platform, score
   Example ID: EozdXr1vCDXz1Whsc2ZE57RsQy03

   Schema (1 docs):
   Fields: deviceTokens, expoPushToken, favorites, fcmToken, fcmTokenUpdatedAt, lastTokenUpdate, notificationSettings.FAVORITE_POSTING_UPDATE, notificationSettings.NEW_MESSAGE, notificationSettings.NEW_OFFER, notificationSettings.OFFER_STATUS_CHANGE, platform, score
   Example ID: JIPkxLOiOrMcavtTItGBID0HMqH3

   Schema (2 docs):
   Fields: favorites, score
   Example ID: Msg9BpESAebCT8hXmGyppGubbts2

   Schema (1 docs):
   Fields: expoPushToken, favorites, fcmToken, lastTokenUpdate, platform, score
   Example ID: OMgBXtldnSQhmqiZbJHIrast7PU2

   Schema (1 docs):
   Fields: favorites, fcmToken, lastTokenUpdate, platform, score
   Example ID: fcYeWrztRZhb1VGKt66lHTLyHBP2

   Schema (1 docs):
   Fields: expoPushToken, favorites, fcmToken, fcmTokenUpdatedAt, lastTokenUpdate, notificationSettings.FAVORITE_POSTING_UPDATE, notificationSettings.NEW_MESSAGE, notificationSettings.NEW_OFFER, notificationSettings.OFFER_STATUS_CHANGE, notificationSettings.messages, platform, score
   Example ID: ybEyGDxbvleCEXdTvrA2CgVTJ1y2

   Field Type Distribution:
   favorites: array (10)
   favourites: array (2)
   score: number (10)
   fcmToken: string (6)
   fcmTokenUpdatedAt: timestamp (4)
   platform: string (6)
   deviceTokens: array (1)
   expoPushToken: string (3)
   lastTokenUpdate: timestamp (4)
   notificationSettings.FAVORITE_POSTING_UPDATE: boolean (2)
   notificationSettings.NEW_MESSAGE: boolean (2)
   notificationSettings.NEW_OFFER: boolean (2)
   notificationSettings.OFFER_STATUS_CHANGE: boolean (2)
   notificationSettings.messages: boolean (1)