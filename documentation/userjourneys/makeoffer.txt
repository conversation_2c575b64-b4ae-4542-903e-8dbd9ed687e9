User Journey: Making an Offer

Summary: This user journey outlines the steps a user takes to make an offer on a posting, including navigating through the app, filling out the offer details, and submitting the offer.

    User Launches the App: The user opens the application and is presented with the Home Screen.
    Navigating to Posting Detail: The user clicks on a map marker to view the posting title or finds a posting from the list container. The user navigates to the Posting Detail Screen by clicking the callout bubble on the map marker or the posting in the list container.
    Viewing Posting Options: If the user is the posting owner, they see the following buttons: "Edit Posting" "Delete Posting" "Navigate to Posting Location" Posting owners cannot submit offers to their own postings. If the user is not the posting owner and does not have an offer on the posting, they see the following buttons: "Make Offer" "Add to Favorites" "Navigate to Posting Location" Offer owners cannot submit offers to a posting where they've already made an offer.
    Clicking the Make Offer Button: The user clicks the "Make Offer" button. The application navigates to the Make Offer Screen.
    Filling Out Offer Details: The user fills out the offer details: Enters a number in the price field. Enters a string in the description field. The fields cannot be empty.
    Submitting the Offer: The user clicks the "Submit Offer" button. The application processes the offer submission and adds the offer to the database. The offer is added to the posting's offers array and displayed in the Posting Detail Screen. The offer is automatically added to the offer owner's favorites.
    Navigating Back to Posting Detail: After successfully submitting the offer, the user is navigated back to the Posting Detail Screen.

Notification Flow:

    The posting owner receives a push notification about the new offer
    The notification bell shows an unread notification badge
    The notification appears in the iOS notification center
    When the posting owner taps the ios notification, they are taken to the OfferDetail screen (not implemented yet)
    The notification badge count decreases when the posting owner views the offer

Relevant Files Involved in the Make Offer User Journey

    Screens: screens/HomeScreen.tsx: The main screen where the user starts their journey. screens/PostingDetail.tsx: The screen that shows detailed information about the selected posting. screens/MakeOffer.tsx: The screen where the user fills out the offer details.
    Components: components/FormInput.tsx: A reusable input component used for entering the offer price and description.
    Functions: addOfferAndDiscussion: A function from the firebaseService that handles adding the offer to the database and initializing discussions. addToFavorites: A function from the firebaseService that handles adding the offer to the user's favorites.
    Hooks: hooks/useOfferSubmission.js: A custom hook that manages the offer submission process, including validation and state management. hooks/useFavorites.js: A custom hook that manages the favorite status of postings.
    Services: services/firebaseService.js: Contains functions for interacting with Firebase, including adding offers and managing favorites.
    Types: types/offer.ts: Defines the structure of an offer, which includes fields like id, price, description, userId, and postingId. types/posting.ts: Defines the structure of a posting, which may include fields like id, title, description, and offers.
