User Journey: My Postings Screen

Summary: This user journey outlines the steps a user takes to interact with the My Postings Screen, including navigating through the app, viewing postings, editing or withdrawing postings, and handling notifications.

User Launches the App: 
    The user opens the application and is presented with the Home Screen.
Navigating to My Postings Screen:
    The user taps on the profile button located in the upper right corner of the Home Screen.
    The application navigates to the Profile Screen.
    From the Profile Screen, the user selects the "My Postings" option.
    The application navigates to the My Postings Screen.

Viewing My Postings:
    The My Postings Screen displays a list of postings created by the user.
    Each posting is displayed with relevant details, including the title and description.
    Description text is displayed in a truncated format which only displays on line of text, and shows a "Read More" button at the end of text to allow user to expand the description.

Interacting with Postings:
    The user can click on a specific posting from the list to view its details.
    The application navigates to the Posting Detail Screen, displaying detailed information about the selected posting.

Active and Deleted Postings:
    Active and Deleted Postings are separated under corresponding tabs and users can switch between these two tabs.

Viewing Deleted Postings:
    The user can view withdrawn postings by clicking on a "Deleted" tab within the My Postings Screen.
    The application displays a list of deleted postings created by the user.

Viewing Active Postings:
    The user can view active postings by clicking on a "Active" tab within the My Postings Screen.

Navigating Back:
    The user can navigate back to the Profile Screen or Home Screen at any time using the navigation options available.

Relevant Files Involved in the My Postings User Journey

    Screens: screens/MyPostingsScreen.tsx: The screen that displays the list of postings created by the user.
    Components:
    Functions: fetchUserPostings: A function from the firebaseService that retrieves the postings created by the user.
    Hooks: hooks/fetchUserPostings.js: A custom hook that manages fetching and displaying the user's postings.
    Services: services/firebaseService.js: Contains functions for interacting with Firebase, including fetching user postings and withdrawing postings.
    Types: types/posting.ts: Defines the structure of a posting, which includes fields like id, title, description, and status.
