User Journey: Adding/Removing a Posting to/from Favorites

Sumary: This user journey outlines the steps a user takes to add or remove a posting from their favorites, including navigating through the app, interacting with the favorites button, and handling success or error messages.

    User Launches the App: The user opens the application and is presented with the Home Screen.
    Navigating to Posting Detail: The user clicks on a map marker to view the posting title or finds a posting from the list container. The user navigates to the Posting Detail Screen by clicking the callout bubble on the map marker or the posting in the list container.
    Viewing Posting Options: If the user is the posting owner, they see the following buttons: "Edit Posting" "Delete Posting" "Navigate to Posting Location" If the user is not the posting owner, they see the following buttons: "Make Offer" "Add to Favorites" "Navigate to Posting Location"
    Adding a Posting to Favorites: The user clicks the heart-shaped "Add to Favorites" button. The application processes the request to add the posting to the user's favorites. The button is activated to indicate that this posting is now in the user's favorites.
    Removing a Posting from Favorites: If the user clicks on the already favorited posting, the application processes the request to remove the posting from the user's favorites. The button is deactivated to indicate that this posting is no longer in the user's favorites.
    Handling Success or Error: If the addition or removal of the posting from favorites is successful, the application may display a success message. If there is an error during the process, an alert is shown informing the user of the failure.

Notification Flow for Favorited Postings:

    When a favorited posting is updated, the user receives a push notification
    The notification bell shows an unread notification badge
    The notification appears in the iOS notification center
    When the user taps the ios notification, they are taken to the PostingDetail screen (not implemented yet)
    The notification badge count decreases when the user views the updated posting
    Users receive notifications for:
    Title or description changes
    Posting deletion
    Location changes
    New discussion messages in the offer submitted to the posting
    New offers on the posting

Relevant Files Involved in the Add/Remove Posting to/from Favorites User Journey

    Screens: screens/HomeScreen.tsx: The main screen where the user starts their journey. screens/PostingDetail.tsx: The screen that shows detailed information about the selected posting.
    Components: components/MapMarker.tsx: The component that represents each posting on the map as a marker, including the callout bubble functionality.
    Functions: addToFavorites: A function from the firebaseService that handles adding a posting to the user's favorites. removeFromFavorites: A function from the firebaseService that handles removing a posting from the user's favorites.
    Hooks: hooks/useFavorites.js: A custom hook that manages the favorite status of postings, including adding and removing favorites. hooks/useFavoritesData.js: A custom hook that fetches the user's favorite postings.
    Services: services/firebaseService.js: Contains functions for interacting with Firebase, including adding and removing postings from favorites.
    Types: types/offer.ts: Defines the structure of an offer, which may include fields related to the posting but is not directly related to favorites. types/posting.ts: Defines the structure of a posting, which may include fields like id, title, description, and location. types/navigation.ts: Defines the types for navigation, which may include parameters for the Posting Detail Screen.