User Journey: Posting Detail Screen

Summary: This user journey outlines the steps a user takes to interact with the Posting Detail Screen, including navigating through the app, expanding/collapsing containers,

User Navigates to the Posting Detail Screen: 
There are three ways that the user can navigate to the Posting Detail Screen:
    By clicking on a map marker in the Home Screen. 
    By clicking on a posting in the list container in the Home Screen. 
    By clicking on a posting in My Postings Screen
    By clicking on the right chevron button in the Posting Details Container in Offer Detail Screen.

Containers Layout: The screen consists of two separate containers stacked in the following order:
    Posting Details Container: displays the posting title, description, and navigation buttons.
    Offers Container: displays the list of offers for the posting ordered by price in ascending order, and the active and deleted tabs. Whenever a new offer is submitted, edited, or deleted, the offers container is updated immediately to reflect the new offer.
  

Expanding/Collapsing Containers: 
    Expanding/Collapsing Containers is not available in this screen.

Content Display: 
    Viewing Posting Details Screen as Posting owner:
        At the top of the screen user sees the Posting Title, Edit Posting Button, Delete Posting Button, and Navigate to Posting on the Map Button.
        Below the posting title, user sees the Posting Description.
        Below the Posting Description, user sees the Offers header.
        Under the Offers header, user sees the Active and Deleted tabs.
        Under the Active tab, user sees the list of active offers ordered by price in ascending order.
        Under the Deleted tab, user sees the list of deleted offers ordered by price in ascending order.
    
    Viewing Posting Details Screen as Offer owner:
        At the top of the screen user sees the Posting Title, Edit Offer Button, Active Favorite Button, and Navigate to Posting on the Map Button.
        Below the posting title, user sees the Posting Description.
        Below the Posting Desctiption, user sees the Offers header.
        Under the Offers header, user sees the Active and Deleted tabs.
        Under the Active tab, user sees the list of active offers ordered by price in ascending order.
        Under the Deleted tab, user sees the list of deleted offers ordered by price in ascending order.

    Viewing Posting Details Screen as Third Party User:
        At the top of the screen user sees the Posting Title, Make Offer Button, Favorite Button, and Navigate to Posting on the Map Button.
        Below the posting title, user sees the Posting Description.
        Below the Posting Desctiption, user sees the Offers header.
        Under the Offers header, user sees the Active and Deleted tabs.
        Under the Active tab, user sees the list of active offers ordered by price in ascending order.
        Under the Deleted tab, user sees the list of deleted offers ordered by price in ascending order.





Relevant Files Involved in the Posting Detail Screen User Journey

    Screens: screens/PostingDetail.tsx: The screen that shows detailed information about the selected posting, including the posting details, offer details, and messaging sections.
   

    Hooks: hooks/usePostingDetails.js: A custom hook that manages fetching and displaying the posting details. hooks/useOffers.js: A custom hook that manages fetching and displaying the offers for the posting.
            hooks/useFavorites.js: A custom hook that manages the favorite status of postings.
            hooks/useOffers.js: A custom hook that manages fetching and displaying the offers for the posting.

    Services: services/firebaseService.js: Contains functions for interacting with Firebase, including sending messages and retrieving discussion messages.
  