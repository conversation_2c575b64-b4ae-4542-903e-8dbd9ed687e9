User Journey: Sign Up

Summary: The Sign Up user journey is supported by various hooks, services, and components that facilitate user input, validation, and interaction with Firebase for authentication and Firestore for data storage.

    User Launches the App: The user opens the application and is presented with the Landing Page.
    Navigating to Sign Up: The user sees options to sign in or sign up. The user taps on the Sign Up button.
    Filling Out the Sign-Up Form: The user is taken to the Sign Up screen. The user enters their email address, password, and confirms the password. The user taps the Sign Up button.
    Validation: The application checks if the password and confirm password fields match. If they do not match, an error message is logged, and the user is prompted to correct it.
    Creating the User Account: If the passwords match, the application attempts to create a new user account using Firebase Authentication. Upon successful account creation, a document for the new user is created in the Firestore database.
    Confirmation Code Generation: A random confirmation code is generated and logged for testing purposes. The user is navigated to the Sign Up Confirmation screen with the confirmation code.
    Confirmation of Sign Up: The user is prompted to enter the confirmation code received. The user enters the code and taps the Confirm button. Final Validation: The application checks if the entered confirmation code matches the generated code. If the code is correct, the user is navigated to the Home screen, completing the sign-up process. If the code is incorrect, an error message is displayed.

Relevant files:

    App.tsx - Main application component that handles initial notification setup and permissions
    firebase.ts - Firebase initialization and authentication configuration
    firebaseConfig.ts - Firebase credentials and configuration constants screens/LoginScreen.tsx - Initial screen with navigation to sign up screens/SignUp.tsx - Sign up form screen with email/password inputs screens/SignUpConfirmation.tsx - Confirmation code verification screen
    services/firebaseService.ts - Service handling user creation and database operations components/FormInput.tsx - Reusable form input component