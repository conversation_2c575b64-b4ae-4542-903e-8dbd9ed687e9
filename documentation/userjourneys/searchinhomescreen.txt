User Journey: Search Functionality in Home Screen

Summary: This user journey outlines the steps a user takes to utilize the search functionality in the Home Screen, including typing in the search field, performing searches, and interacting with the search results.

    User Launches the App: The user opens the application and is presented with the Home Screen, which displays a map and a search input field.
    Initial Map and Postings: The map is centered on the user's current location, and postings within the displayed map area are retrieved automatically.
    Typing in the Search Field: The user types a search term (e.g., "test") into the search input field. As the user types, the "Search This Area" button appears, indicating that a search can be performed.
    Clicking the Search Button: The user clicks the "Search This Area" button. The application retrieves results for the displayed map area based on the search term, respecting the numberOfSearchResults parameter. The "Search This Area" button disappears after the search is executed.
    Deleting Characters from the Search Term: The user deletes one letter from the search term (e.g., "tes"). The "Search This Area" button reappears, but no automatic queries are sent to the database.
    Performing a New Search: The user clicks the "Search This Area" button again. The application retrieves results for the displayed map area based on the updated search term.
    Clearing the Search Field: The user clears the search field completely. The application retrieves results for a blank search, respecting the numberOfSearchResults parameter.
    Panning the Map with a Search Term: The user pans the map while having a search term in the search field. The "Search This Area" button appears, and no automatic queries are sent to the database.
    Maintaining Previous Search Results: The results from the previous search are maintained in the list view while the user pans the map.

Relevant Files Involved in the Search Functionality User Journey

    Screens: screens/HomeScreen.tsx: The main screen where the map and search input are displayed.
    Components: components/SearchInput.tsx: The component that represents the search input field, allowing users to type and clear their search terms.
    Functions: fetchPostingsBySearch: A function that retrieves postings based on the current map region and search term.
    Hooks: hooks/useFirebaseSubscriptions.js: A hook that manages subscriptions to Firebase for real-time updates of postings, which is used to populate the list view based on search results.
    Services: services/firebaseService.js: Contains functions for interacting with Firebase, including fetching postings based on the current map's region and search term.
    Types: types/posting.ts: Defines the structure of a posting, which may include fields like id, title, description, and location. types/navigation.ts: Defines the types for navigation, which may include parameters for the Posting Detail Screen.