User Journey: Editing a Posting

This user journey outlines the steps a user takes to edit a posting, including navigating through the app, modifying the posting details, and saving the changes.

    User Launches the App: The user opens the application and is presented with the Home Screen.
    Navigating to Profile Screen: The user taps on the profile button located in the upper right corner of the Home Screen. The application navigates to the Profile Screen.
    Accessing My Postings: The user selects the "My Postings" option from the Profile Screen. The application displays a list of postings created by the user.
    Selecting a Posting to View: The user taps on a specific posting from the list. The application navigates to the Posting Detail Screen, displaying detailed information about the selected posting.
    Navigating to Edit Posting: If the user is the owner of the posting, a edit posting icon is displayed on the Posting Detail Screen. The user clicks the edit button on the Posting Detail Screen. The application navigates to the Edit Posting Screen.
    Viewing Existing Data: The Edit Posting Screen displays the existing data (title, description, and location) in editable fields.
    Modifying Posting Details: The user modifies the existing title and description as necessary. The user selects a new location on the map by tapping on the desired area.
    Saving Changes: The user clicks the "Save Changes" button. The application processes the update and saves the modified posting details to the database. After successfully saving, the user is navigated back to the Posting Detail Screen.

    Refresh Button for Offer Owners: When a posting is updated, the changes are not reflected immediately as there's no active listener for postings collection. To maintain an acceptable user experience while reducing firebase costs, a refresh button appears in the Posting Details Container Header in the Offer Detail Screen for all users who have made offers on this posting when the posting is updated. 

    Refresh Button Interaction: When an offer owner clicks the refresh button, the latest posting details are fetched and displayed, and the refresh button disappears.

    UI Transitions: The system prevents UI flicker by ensuring smooth transitions when refreshing data.

    Canceling Edits: Alternatively, the user can click the back button at any point to cancel the edits and return to the Posting Detail Screen without saving changes.

Notification Flow:

    Users which have favorited the posting will receive a notification with type "FAVORITE_POSTING_UPDATE" when the posting is updated
    Users who have made offers on the posting will receive a notification when the posting is updated
    Notification bell shows unread badge and badge count is updated to reflect the total number of unread notifications
    Notification appears in iOS notification center with the changes made
    When the user taps the iOS notification, they are taken to the appropriate screen (Posting Detail or Offer Detail)
    Badge count decreases when user views the updated posting
    The refresh button appears automatically when an offer owner navigates to the OfferDetail screen with unread notifications
    Notifications are automatically cleared when the user views and refreshes the posting data
    The system handles both foreground and background notifications appropriately

Relevant Files Involved in the Edit Posting User Journey

    Screens:
        screens/HomeScreen.tsx: The main screen where the user starts their journey.
        screens/ProfileScreen.tsx: The screen where the user accesses the "My Postings" option.
        screens/MyPostingsScreen.tsx: The screen that displays the list of postings created by the user.
        screens/PostingDetail.tsx: The screen that shows detailed information about the selected posting.
        screens/EditPostingScreen.tsx: The screen where the user edits the posting details.
        screens/OfferDetail.tsx: The screen that displays offer details with posting refresh functionality.

    Components:
        components/FormInput.tsx: A reusable input component used for entering the posting title and description.
        components/MapMarker.tsx: The component that represents the posting's location on the map.
        components/NotificationBell.tsx: The component that shows the notification bell in the top right corner of the Home Screen.
        components/offer-detail/RefreshButton.tsx: Button component that appears when updates are available, with loading animation during refresh.
        components/offer-detail/PostingDetailsSection.tsx: Component that displays posting information with refresh functionality.

    Functions:
        updatePostingDetails: A function from the firebaseService that handles updating the posting details in the database.
        functions/index.js: Cloud Functions for notification handling.
        refreshPostingData: A function that fetches fresh posting data when the refresh button is clicked.
        handleNotification: A function that processes incoming notifications and updates UI accordingly.
        createNotification: A function that creates notifications for users who have favorited or made offers on the posting.

    Hooks:
        hooks/usePostingDetails.js: A custom hook that fetches the details of the posting to be edited and provides refresh functionality.
        hooks/useAddPosting.js: A custom hook that may also be used for updating posting details.
        hooks/useFormValidation.js: A custom hook that validates the input fields for the posting.
        hooks/useOfferDetails.js: A custom hook that manages fetching offer details and provides refresh functionality for associated postings.

    Services:
        services/firebaseService.js: Contains functions for interacting with Firebase, including updating a posting.
        services/notificationService.ts: Service managing notification creation, delivery, and processing for posting updates.

    Types:
        types/posting.ts: Defines the structure of a posting, which may include fields like id, title, description, and location.
        types/navigation.ts: Defines the types for navigation, which may include parameters for the Edit Posting Screen and Posting Detail Screen.
        types/notifications.ts: Defines the structure of notifications, including types and payloads related to posting updates.
        types/firebase.ts: Contains Firebase-specific type definitions for data structures and listeners.

    Utils:
        utils/notificationHelper.ts: Helper functions for notifications.
        utils/listenerRegistry.js: Utility for centralized management of Firebase listeners to optimize performance.
        utils/notificationUtils.js: Helper functions for processing notifications and managing refresh button visibility.