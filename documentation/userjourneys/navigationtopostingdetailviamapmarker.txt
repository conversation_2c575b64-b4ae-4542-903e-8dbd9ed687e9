User Journey: Navigating to Posting Detail via Map Markers

This user journey outlines the steps a user takes to navigate to the Posting Detail Screen via map markers on the Home Screen.

    User Launches the App: The user opens the application and is presented with the Home Screen, which displays a map with markers representing various postings.
    Viewing Map Markers: The user sees multiple markers on the map, each representing a different posting.
    Interacting with a Map Marker: The user taps on one of the map markers. A callout bubble appears, displaying the posting's title and a truncated part of the posting description.
    Clicking on the Callout Bubble: The user taps on the callout bubble. The application navigates to the Posting Detail Screen, where detailed information about the selected posting is displayed.
    Viewing Posting Details: The user can now view the full details of the posting, including the complete description, offers, and options to make an offer or edit the posting.

Relevant Files Involved in the User Journey

    Screens: screens/HomeScreen.tsx: The main screen where the map is displayed and user interactions occur. screens/PostingDetail.tsx: The screen that shows detailed information about the selected posting.
    Components: components/MapMarker.tsx: The component that represents each posting on the map as a marker, including the callout bubble functionality.
    Functions: fetchPostingsBySearch: A function that retrieves postings based on the current map region and zoom level, which may be used to populate the map markers.
    Hooks: hooks/useFirebaseSubscriptions.js: A hook that manages subscriptions to Firebase for real-time updates of postings, which is used to populate the map markers.
    Services: services/firebaseService.js: Contains functions for interacting with Firebase, including fetching postings based on the current map's region.
    Types: types/posting.ts: Defines the structure of a posting, which includes fields like id, title, description, and location. types/navigation.ts: Defines the types for navigation, which may include parameters for the Posting Detail Screen.