User Journey: Forgot Password

Summary: This user journey outlines the steps a user takes to reset their password, including navigating to the Forgot Password screen, entering their email, and receiving a confirmation alert.

    User Launches the App: The user opens the application and is presented with the Landing Page.
    Navigating to Forgot Password: The user sees the option to sign in or sign up. The user taps on the Forgot Password? link.
    Entering Email Address: The user is taken to the Forgot Password screen. The user enters their registered email address in the input field.
    Submitting the Request: The user taps the Reset Password button.
    Validation: The application checks if the email field is not empty. If the email is empty, an alert is shown prompting the user to enter a valid email address.
    Sending Password Reset Email: If the email is valid, the application attempts to send a password reset email using Firebase Authentication. Upon successful sending, an alert is displayed confirming that a password reset link has been sent to the user's email.
    Navigating Back: The user is navigated back to the Landing Page after the confirmation.

Relevant Files Involved in the Forgot Password User Journey

    Screens: screens/ForgotPassword.tsx: The screen where the user enters their email to reset their password. screens/LoginScreen.tsx: The initial screen where the user can navigate to the Forgot Password screen.
    Components: components/FormInput.tsx: A reusable input component used for entering the email address.
    Functions: sendPasswordResetEmail: A function from Firebase Authentication used to send the password reset email
    Services: firebase.ts: Contains the Firebase configuration and initialization, which includes the authentication service. firebaseService.ts - Password reset operations