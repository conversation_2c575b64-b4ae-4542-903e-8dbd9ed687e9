User Journey: Launching the App for the first time

Summary: Initial app launch process including notification setup and Firebase initialization.

Steps:

    User opens the app for the first time
    App checks for notification permissions
    A<PERSON> requests notification permissions if not granted
    App initializes Firebase configuration
    App sets up notification handlers

Notification Flow:

    App requests notification permissions
    User is prompted to allow notifications
    If allowed, app registers device for push notifications
    Notification bell appears in top right corner
    Initial notification badge count is set to 0

Relevant Files:

    App.tsx - Main application component handling initialization and permissions
    firebase.ts - Firebase initialization and configuration
    firebaseConfig.ts - Firebase credentials and settings
    hooks/useNotificationSetup.ts - Notification initialization logic
    services/notificationService.ts - Notification permission and token management
    utils/notificationHelper.ts - Notification setup utilities
    components/NotificationBell.tsx - Notification UI component
    functions/index.js - Cloud Functions for notification handling