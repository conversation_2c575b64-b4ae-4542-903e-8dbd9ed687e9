User Journey: Deleting a Posting

This user journey outlines the steps a user takes to delete a posting, including navigating through the app, confirming the deletion, and handling success or error messages.

    User Launches the App: The user opens the application and is presented with the Home Screen.
    Navigating to Profile Screen: The user taps on the profile button located in the upper right corner of the Home Screen. The application navigates to the Profile Screen.
    Selecting a Posting to View: The user selects a specific posting from the "My Postings" option. The application navigates to the Posting Detail Screen, displaying detailed information about the selected posting.
    Viewing Delete Option: If the user is the owner of the posting, a trash bin icon is displayed on the Posting Detail Screen. Other users do not see this icon.
    Clicking the Trash Bin Icon: The user clicks the trash bin icon to initiate the deletion process. An alert is displayed to confirm the deletion of the posting.
    Confirming Deletion: If the user confirms the deletion, the application processes the deletion request. The posting is removed from the database.
    Handling Success or Error: If the deletion is successful, the user is navigated back to the My Postings screen or the Home Screen, and a success message is displayed. If there is an error during deletion, an alert is shown informing the user of the failure.
    Deleting a Posting automatically withdraws any offers on the posting When a posting is deleted, the application automatically withdraws any offers submitted by offer owners on the posting.

Notification Flow:

    Users which have favorited the posting or have an offer on the posting will receive a notification when the posting is deleted (submitting an offer on a posting automatically favorites the posting)
    Notification bell shows unread badge and badge count is updated to reflect the total number of unread notifications
    Notification appears in iOS notification center
    Badge count decreases when user views updated posting


Relevant Files Involved in the Delete Posting User Journey

    Screens: screens/HomeScreen.tsx: The main screen where the user starts their journey. screens/ProfileScreen.tsx: The screen where the user accesses their postings. screens/MyPostingsScreen.tsx: The screen that displays the list of postings created by the user. screens/PostingDetail.tsx: The screen that shows detailed information about the selected posting.
    Components: components/NotificationBell.tsx - Component showing notification updates
    Functions: deletePosting: A function from the firebaseService that handles the deletion of a posting from the database.
    Hooks: hooks/usePostingDetails.js: A custom hook that fetches the details of the posting, which may include the logic to determine if the current user is the owner. hooks/useWithdrawOffer.js: A custom hook that handles the withdrawal of offers.
    Services: services/firebaseService.js: Contains functions for interacting with Firebase, including deleting a posting.
    Types: types/posting.ts: Defines the structure of a posting, which may include fields like id, title, description, and location. types/navigation.ts: Defines the types for navigation, which may include parameters for the Posting Detail Screen.