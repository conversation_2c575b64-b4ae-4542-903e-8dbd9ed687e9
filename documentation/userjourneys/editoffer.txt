User Journey: Editing an Offer

Summary: This user journey outlines the steps a user takes to edit an offer on a posting, including navigating through the app, modifying the offer details, and submitting the updated offer.

    User Launches the App: The user opens the application and is presented with the Home Screen.
    Navigating to Posting Detail: The user clicks on a map marker to view the posting title or finds a posting from the list container. The user navigates to the Posting Detail Screen by clicking the callout bubble on the map marker or the posting in the list container.
    Viewing Posting Options: If the user is the posting owner, they see the following buttons: "Edit Posting" "Delete Posting" "Navigate to Posting Location" If the user is not the posting owner and has an offer on the posting, they see the following buttons: "Edit Offer" "Add to Favorites" "Navigate to Posting Location"
    Clicking the Edit Offer Button: The user clicks the "Edit Offer" button. The application navigates to the Edit Offer Screen.
    Viewing Existing Offer Details: The existing offer details (price and description) are displayed in the editable form.
    Modifying Offer Details: The user modifies the offer details: Updates the price field with a new number. Updates the description field with new text. fields cannot be empty.
    Submitting the Updated Offer: The user clicks the "Update Offer" button. The application processes the update and saves the changes to the database.
    Navigating Back to Offer Detail Screen: After successfully updating the offer, the user is navigated back to the Offer Detail Screen and Offer Detail Screen reflects the updated offer. The updated offer is displayed in the Posting Detail Screen.
    A refresh button appears on the Offer Details Container Header only visible to the Posting Owner if the Posting Owner is viewing the Offer Details Screen by the time the offer is updated.
    When the Posting Owner clicks the refresh button, the latest offer details are fetched and displayed, and the refresh button disappears.
    The system prevents UI flicker by ensuring smooth transitions when refreshing data.

Notification Flow:

    The posting owner receives a push notification about the offer update with type "OFFER_STATUS_CHANGE"
    The notification bell in the homescreen shows an unread notification badge
    The notification appears in the iOS notification center with the changes made
    When the posting owner taps the iOS notification, they are taken to the OfferDetail screen
    The notification badge count decreases when the posting owner views the updated offer
    The refresh button appears automatically when the posting owner navigates to the OfferDetail screen with unread notifications
    Notifications are automatically cleared when the user views and refreshes the offer data
    The system handles both foreground and background notifications appropriately

Relevant Files Involved in the Edit Offer User Journey

    Screens:
        screens/HomeScreen.tsx: The main screen where the user starts their journey.
        screens/PostingDetail.tsx: The screen that shows detailed information about the selected posting.
        screens/EditOffer.tsx: The screen where the user edits the offer details.
        screens/OfferDetail.tsx: The screen that displays offer details with refresh functionality.

    Components:
        components/FormInput.tsx: A reusable input component used for entering the offer price and description.
        components/offer-detail/RefreshButton.tsx: Button component that appears when updates are available, with loading animation during refresh.
        components/offer-detail/OfferDetailsSection.tsx: Component that displays offer information with refresh functionality.

    Functions:
        updateDoc: A function from Firebase that handles updating the offer in the database.
        createNotification: A function from the firebaseService that handles creating notifications for the posting owner.
        refreshOfferData: A function that fetches fresh offer data when the refresh button is clicked.
        handleNotification: A function that processes incoming notifications and updates UI accordingly.

    Hooks:
        hooks/useOfferDetails.js: A custom hook that fetches the details of the offer to be edited and provides refresh functionality.
        hooks/useWithdrawOffer.js: A custom hook that manages the withdrawal of offers.
        hooks/useAuthUser.js: A custom hook that manages the authentication state of the user.

    Services:
        services/firebaseService.js: Contains functions for interacting with Firebase, including updating offers and creating notifications.
        services/notificationService.js: Manages notification creation, delivery, and processing for offer updates.

    Types:
        types/offer.ts: Defines the structure of an offer, which includes fields like id, price, description, userId, and postingId.
        types/notifications.ts: Defines the structure of notifications, including types and payloads.
        types/firebase.ts: Contains Firebase-specific type definitions for data structures and listeners.

    Utils:
        utils/listenerRegistry.js: Utility for centralized management of Firebase listeners to optimize performance.
        utils/notificationUtils.js: Helper functions for processing notifications and managing refresh button visibility.

