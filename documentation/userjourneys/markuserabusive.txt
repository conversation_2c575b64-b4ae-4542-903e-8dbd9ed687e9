User Journey: Marking a User as Abusive

Summary: This user journey outlines the steps involved when a user marks another user as abusive, including the different scenarios for posting owners and offer owners, confirmation flows, and system responses.

1. User Launches the App:
   - The user opens the application and is presented with the Home Screen.

2. Navigating to Offer Detail:
   - For posting owners:
     - User navigates to their posting detail screen
     - Views offers received for their posting
     - Selects an offer to view details
   - For offer owners/third party users:
     - User navigates to a posting detail screen
     - Views their own offer or another user's offer
     - Selects an offer to view details

3. Viewing User Score and Options:
   - User sees the offer owner's score displayed prominently
   - If user is the posting owner:
     - Sees "Mark as Abusive" button below offer details
   - If user is the offer owner or third party:
     - Sees "Mark as Abusive" button in posting details section

4. Initiating Mark as Abusive:
   - User clicks the "Mark as Abusive" button
   - System displays confirmation alert:
     - Title: "Mark User as Abusive?"
     - Message: "Are you sure you want to mark this user as abusive? This action will restrict their account from submitting offers to your postings for 30 days."
     - Options: "Cancel" or "Confirm"

5. Confirming Action:
   - If user selects "Cancel":
     - <PERSON>ert is dismissed
     - User remains on current screen
   - If user selects "Confirm":
     - System displays "Work in Progress" alert:
       - Title: "Processing Request"
       - Message: "Your request is being processed. The user will be restricted if confirmed."
     - System logs the action with timestamp and user IDs
     - System navigates back to offer detail screen

6. System Response:
   - If successful:
     - User score is updated in the system
     - Marked user's account is restricted for 30 days
     - Restricted user cannot submit new offers during restriction period
   - If error occurs:
     - System displays error alert
     - Action is logged for review
     - User remains on current screen

7. Handling Restricted Users:
   - Restricted users see a restriction message:
     - "Your account is restricted until [date] due to abusive behavior"
   - Restricted users cannot submit new offers
   - Restriction status is visible to other users

Notification Flow for Abusive Marking:

1. When a user is marked as abusive:
   - Marked user receives push notification about restriction
   - Notification appears in iOS notification center
   - Notification includes restriction details and duration

2. When restriction period ends:
   - User receives notification about restriction lift
   - Account status is automatically updated
   - User can resume normal activities

Relevant Files Involved in the Mark User as Abusive Journey:

1. Screens:
   - screens/OfferDetail.tsx: Main screen for viewing offer details and marking users
   - screens/PostingDetail.tsx: Screen for viewing posting details and offers

2. Components:
   - components/AbusiveUserButton.tsx: Button component for marking users
   - components/RestrictionMessage.tsx: Component for displaying restriction status
   - components/UserScore.tsx: Component for displaying user scores

3. Functions:
   - markUserAsAbusive: Handles the abusive marking API call
   - handleRestriction: Manages user restriction status
   - logAbusiveAction: Logs all marking actions

4. Hooks:
   - useUserScore.js: Manages user score data
   - useRestriction.js: Handles restriction status and duration

5. Services:
   - services/userScoreService.js: Manages user score related API calls
   - services/restrictionService.js: Handles restriction related functionality

6. Types:
   - types/userScore.ts: Defines user score structure
   - types/restriction.ts: Defines restriction status and duration
   - types/abusiveAction.ts: Defines abusive marking action structure
