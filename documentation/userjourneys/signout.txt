User Journey: Signing Out

This user journey outlines the steps a user takes to sign out of the application, including navigating to the Profile Screen, tapping the sign-out option, and handling the sign-out process.

    User Launches the App: The user opens the application and is presented with the Home Screen.
    Navigating to Profile Screen: The user sees a profile button located in the upper right corner of the Home Screen. The user taps on the profile button.
    Viewing Profile Options: The user is taken to the Profile Screen. The screen displays various options, including the option to sign out.
    Tapping the Sign Out Button: The user taps the Sign Out option.
    Handling Sign Out: The application triggers the sign-out process. Before signing out, the application unsubscribes from all Firebase listeners to prevent memory leaks.
    Successful Sign Out: The user is signed out of the application. A success message is logged, and the user is navigated back to the Landing Page or the initial screen.
    Error Handling: If there is an error during the sign-out process, an alert is displayed to inform the user of the failure.

Relevant Files Involved in the Sign Out User Journey

    Screens: screens/ProfileScreen.tsx: The screen where the user can sign out and view other profile options.
    Functions: handleSignOut: A function defined in the Profile Screen that manages the sign-out process.
    Hooks: hooks/useProfileActions.ts: A custom hook that may manage profile-related actions, including handling the sign-out option.
    Services: firebase.ts: Contains the Firebase configuration and initialization, which includes the authentication service used for signing out. firebaseService.ts - Sign-out operations
    Utils: utils/firebaseCleanup.ts: Contains the unsubscribeAll function used to unsubscribe from all Firebase listeners before signing out.