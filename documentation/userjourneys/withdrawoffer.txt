User Journey: Withdrawing an Offer

Summary: This user journey outlines the steps a user takes to withdraw an offer, including navigating through the app, confirming the withdrawal, and viewing the updated offer status.

    User Launches the App: The user opens the application and is presented with the Home Screen.
    Navigating to Offer Detail: The user navigates to the My Offers Screen and clicks on an offer to view its details. Alternatively, the user can navigate to the Posting Detail Screen if they have an offer on that posting and click on the offer.
    Viewing Offer Options: As the user is the offer owner, they see the "Withdraw Offer" button in the offer details section.
    Clicking the Withdraw Offer Button: The user clicks the "Withdraw Offer" button. A confirmation alert is displayed, asking the user to confirm the withdrawal.
    Confirming Withdrawal: The user clicks the "Confirm" button in the alert. The application processes the withdrawal of the offer and marks the offer as withdrawn in the database.
    Viewing Success Message: The user sees a success message indicating that the offer has been successfully withdrawn.
    Updating Offer Detail Page: The offer detail page now shows a banner stating, "This Offer has been Withdrawn." Users can no longer send discussion messages in the offer detail screen. A message is displayed stating, "Messages cannot be sent in withdrawn offers." The withdrawn offer is no longer displayed in the Posting Detail Screen.
    Viewing Withdrawn Offers: The user can view withdrawn offers in the My Offers Screen by clicking on the "Withdrawn" tab. Alternatively, the user can navigate to the Edit Offer Screen by clicking the "Edit Offer" button in the offer details section to withdraw the offer.

Notification Flow:

    The posting owner receives a push notification about the withdrawn offer
    The notification bell shows an unread notification badge
    The notification appears in the iOS notification center
    When the posting owner taps the ios notification, they are taken to the OfferDetail screen (not implemented yet)
    The notification shows "This offer has been withdrawn"
    The notification badge count decreases when the posting owner views the withdrawn offer

Relevant Files Involved in the Withdraw Offer User Journey

    Screens: screens/MyOffersScreen.tsx: The screen that displays the list of offers created by the user, including the withdrawn tab. screens/OfferDetail.tsx: The screen that shows detailed information about the selected offer, including the withdraw option. screens/EditOffer.tsx: The screen where the user can edit the offer details, including the option to withdraw.
    Components: components/FormInput.tsx: A reusable input component used for entering the offer price and description, but not directly related to withdrawal.
    Functions: withdrawOffer: A function from the firebaseService that handles the withdrawal of the offer in the database. createNotification: A function from the firebaseService that handles creating notifications for the posting owner when an offer is withdrawn.
    Hooks: hooks/useWithdrawOffer.js: A custom hook that manages the withdrawal of offers, including state management and API calls. hooks/useAuthUser.js: A custom hook that manages the authentication state of the user.
    Services: services/firebaseService.js: Contains functions for interacting with Firebase, including withdrawing offers and creating notifications. 6.Types: types/offer.ts: Defines the structure of an offer, which includes fields like id, price, description, userId, and postingId. types/notifications.ts: Defines the structure of notifications, including types and payloads related to offer status changes.