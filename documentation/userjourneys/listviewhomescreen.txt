User Journey: List View in Home Screen

This user journey outlines the steps a user takes to interact with the list view in the Home Screen, including expanding the list, loading more postings, and navigating to the Posting Detail Screen.

    User Launches the App: The user opens the application and is presented with the Home Screen, which displays a map and a collapsed list view at the bottom.
    Viewing the Map: The user sees the map centered on their current location with markers for postings.
    Expanding the List View: The user drags the handle of the list container to expand it. The list view expands without overlapping the search field at the top of the screen.
    Interacting with the List: The user scrolls through the list of postings displayed in the list view. The list shows a maximum of 10 postings, which are fetched based on the current map area.
    Loading More Postings: As the user scrolls down, more postings are loaded automatically when reaching the end of the list. The application fetches additional postings from the database, maintaining the limit set by the parameter.
    Navigating to Posting Detail: The user taps on a posting in the list. The application navigates to the Posting Detail Screen, displaying detailed information about the selected posting.

Relevant Files Involved in the List View User Journey

    Screens: screens/HomeScreen.tsx: The main screen where the map and list view are displayed. screens/PostingDetail.tsx: The screen that shows detailed information about a selected posting.
    Components: components/ListItem.tsx: The component that represents each posting in the list view. components/DraggableListContainer.tsx: The component that manages the draggable and expandable list view container.
    Functions: fetchPostingsBySearch: A function that retrieves postings based on the current map region and zoom level, which also affects the list view.
    Hooks: hooks/useFirebaseSubscriptions.js: A hook that manages subscriptions to Firebase for real-time updates of postings, which is used to populate the list view.
    Services: services/firebaseService.js: Contains functions for interacting with Firebase, including fetching postings based on the current map's region.
    Types: types/posting.ts: Defines the structure of a posting, which may include latitude, longitude, title, and description. types/navigation.ts: Defines the types for navigation, which may include parameters for the Posting Detail Screen.