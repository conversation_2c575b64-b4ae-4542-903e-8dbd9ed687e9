User Journey: Location Settings (incomplete)

Summary: User location preference management including permission requests and updates.

Steps:

User navigates to Settings screen
User selects Location Settings
User views current location on map
User updates preferred location
System validates location selection
System saves location preferences
Relevant Files:

screens/LocationSettings.tsx - Location preference management
screens/SettingsScreen.tsx - Main settings navigation
services/firebaseService.ts - Location data storage