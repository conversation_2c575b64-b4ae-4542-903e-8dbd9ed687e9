User Journey: Sign In

This user journey outlines the steps a user takes to sign in to the application, including entering their credentials, authenticating, and handling success or failure. The relevant files involved in this journey have been listed, providing a clear overview of the components and services utilized.

    User Launches the App: The user opens the application and is presented with the Landing Page. Entering Credentials: The user sees input fields for their email address and password. The user enters their registered email address and password.
    Submitting the Sign-In Request: The user taps the Sign In button.
    Validation: The application checks if the email and password fields are filled. If either field is empty, an alert is shown prompting the user to fill in the required fields.
    Authenticating the User: If the fields are filled, the application attempts to authenticate the user using Firebase Authentication. The handleSignIn function is called, which handles the sign-in process.
    Successful Sign-In: If authentication is successful, the user is navigated to the Home Screen. A success message is logged, and notifications are initialized for the user. Notification bell updates to reflect new state Failed Sign-In: If authentication fails (e.g., incorrect password), an alert is displayed with an error message indicating the failure reason.

Relevant Files Involved in the Sign In User Journey

    Screens: screens/LoginScreen.tsx: The screen where the user enters their email and password to sign in screens/HomeScreen.tsx: The main screen where the user can interact with the app after successful sign-in
    Components: components/FormInput.tsx: A reusable input component used for entering the email and password components/NotificationBell.tsx: The component that displays the notification bell in the top right corner of the Home Screen
    Functions: signInWithEmailAndPassword: A function from Firebase Authentication used to authenticate the user
    Hooks: hooks/useSignIn.js: A custom hook that manages the sign-in process, including loading state and error handling
    Services: firebase.ts: Contains the Firebase configuration and initialization, which includes the authentication service. firebaseService.ts - Authentication operations
    Types: types/navigation.ts: Defines the types for navigation, which may include the parameters for the sign-in process.