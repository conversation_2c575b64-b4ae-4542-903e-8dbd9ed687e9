User Journey: Map Navigation on Home Screen

This user journey outlines how a user interacts with the map on the Home Screen, including panning, zooming, and rotating the map, while the postings are updated in real-time based on their interactions.

    User Launches the App: The user opens the application and is presented with the Home Screen, which displays a map centered on their current location.
    Initial Map View: The user sees the map centered on their current location with a default zoom level. The map displays markers for postings within the initial viewable area. Initial number of postings in the viewable area is defined by the numberOfSearchResults parameter.
    Panning the Map: The user drags the map to pan to a different area. As the user pans, the map updates in real-time, and the postings are updated to reflect the new area.
    Zooming In/Out: The user uses pinch gestures to zoom in or out on the map. The postings on the map are updated to reflect the new zoom level and the area currently in view.
    Map Rotation: The user rotates the map to view it from different angles. The postings remain visible and are updated based on the new orientation and zoom level.
    Automatic Updates: The application automatically fetches and updates the postings displayed on the map when the user pans, zooms, or rotates the map. The updates occur only once after the user stops interacting with the map for a brief period (e.g., 1 second buffer).
    Maintaining Postings: The postings that are currently displayed on the map are maintained in the view until the user performs another interaction that requires a new fetch. User Experience: The user enjoys a smooth and responsive experience as they navigate the map, with postings updating rapidly based on their interactions.

Relevant Files Involved in the Map Navigation User Journey

    Screens: screens/HomeScreen.tsx: The main screen where the map is displayed and user interactions occur.
    Components: components/MapMarker.tsx: The component that represents each posting on the map as a marker.
    Functions: firebaseService/fetchPostingsBySearch: A function that retrieves postings based on the current map region and zoom level.
    Hooks: hooks/useFirebaseSubscriptions.js: A hook that manages subscriptions to Firebase for real-time updates of postings. Services: services/firebaseService.js: Contains functions for interacting with Firebase, including fetching postings based on the map's current region. Types: types/posting.ts: Defines the structure of a posting, which may include latitude, longitude, title, and description. types/navigation.ts: Defines the types for navigation, which may include parameters for the Home Screen.
