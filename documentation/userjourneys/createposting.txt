User Journey: Creating a Posting

This user journey outlines the steps a user takes to create a posting, including navigating through the app, filling out the necessary fields, and submitting the posting.

User Launches the App: The user opens the application and is presented with the Home Screen.
    Navigating to Profile Screen: The user taps on the profile button located in the upper right corner of the Home Screen. The application navigates to the Profile Screen.
    Accessing Create Posting: The user sees various options on the Profile Screen, including "Create Posting." The user taps on the "Create Posting" option.
    Filling Out Posting Details: The application navigates to the Create Posting Screen. The user types the posting title in the designated input field. The user types the posting description in the designated input field. The user selects a location on the map by tapping on the desired area.
    Validation of Input Fields: The user attempts to submit the posting. The application checks if the title and description fields are not empty. If any fields are empty, an alert is displayed indicating that all fields must be filled.
    Submitting the Posting: Once all fields are filled, the user clicks the "Submit Posting" button. The application processes the submission and adds the posting to the database.
    Success or Error Handling: If the posting is successfully created, an alert is displayed confirming the success, and the user is navigated back to the Home Screen. If there is an error during submission, an alert is displayed informing the user of the failure.

Relevant Files Involved in the Create Posting User Journey

    Screens: screens/HomeScreen.tsx: The main screen where the user starts their journey. screens/ProfileScreen.tsx: The screen where the user accesses the "Create Posting" option. screens/CreatePostingScreen.tsx: The screen where the user fills out the posting details.
    Components: components/FormInput.tsx: A reusable input component used for entering the posting title and description.
    Functions: addPosting: A function from the firebaseService that handles the addition of a new posting to the database.
    Hooks: hooks/useAddPosting.js: A custom hook that manages the posting submission process, including loading state. hooks/useFormValidation.js: A custom hook that validates the input fields for the posting.
    Services: services/firebaseService.js: Contains functions for interacting with Firebase, including adding a new posting.
    Types: types/posting.ts: Defines the structure of a posting, which may include fields like id, title, description, and location. types/navigation.ts: Defines the types for navigation, which may include parameters for the Create Posting Screen.