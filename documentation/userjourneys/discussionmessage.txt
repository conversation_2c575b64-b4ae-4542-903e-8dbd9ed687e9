User Journey: Sending Discussion Messages in the Offer Detail Screen

Summary: This user journey outlines the steps a user takes to send discussion messages in the offer detail screen, including navigating through the app, sending messages, and viewing the discussion.

    User Launches the App: The user opens the application and is presented with the Home Screen.
    Navigating to Offer Detail: The user navigates to the My Offers Screen and clicks on an offer to view its details. Alternatively, the user can navigate to the Posting Detail Screen if they have an offer on that posting and click on the offer.
    Viewing Offer Options: If the user is the offer owner or posting owner, they see the text input field and the "Send Message" button. If the user is not the offer owner or posting owner, they see the discussion messages section but do not see the text input field or the "Send Message" button. Instead, they see a message stating, "Only the posting owner and offer owner can send messages in this discussion."
    Sending a Message (for Offer Owner or Posting Owner): The user types a message in the text input field. The user clicks the "Send Message" button. The message is processed and added to the discussion messages section of the Offer Detail Screen.
    Viewing Discussion Messages: All users (offer owner, posting owner, and third persons) can view the discussion messages in the Offer Detail Screen. The messages appear in the discussion messages section, showing the conversation between the Offer Owner and Posting Owner. Users can see read receipts for messages. Users can see the timestamp of when the message was sent. Users can see relative read receipt timestamps for messages.

Notification Flow for New Messages:

    When a message is sent, the recipient receives a push notification
    The notification bell shows an unread notification badge
    The notification appears in the iOS notification center
    When the recipient taps the ios notification, they are taken to the OfferDetail screen (not implemented yet)
    The notification badge count decreases when the recipient reads the message
    Messages show read receipts once viewed by the recipient
    Messages show read receipts with:
    Single checkmark for sent messages
    Double checkmark for read messages
    Timestamp when the message was read
    Read status updates in real-time

Relevant Files Involved in the Sending Discussion Messages User Journey

    Screens: screens/MyOffersScreen.tsx: The screen that displays the list of offers created by the user. screens/OfferDetail.tsx: The screen that shows detailed information about the selected offer, including the discussion messages section.
    Components: components/FormInput.tsx: A reusable input component used for entering the message text.
    Functions: sendMessage: A function from the firebaseService that handles sending messages and storing them in the database. fetchMessages: A function that retrieves the discussion messages for the offer.
    Hooks: hooks/useAuthUser.js: A custom hook that manages the authentication state of the user.
    Services: services/firebaseService.js: Contains functions for interacting with Firebase, including sending messages and retrieving discussion messages.