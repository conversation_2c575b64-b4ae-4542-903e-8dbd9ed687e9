---
description: agent to confirm that given context is read
globs: *
---
- check the code base before responding, 
- do not ask user to check the code that you can already read,
- ask user to provide the context you want to read only if you are unable to access it,
- respond to confirm that you've read all the given files in the context.
- when user reports an issue, always include debug logs in the proposed solution and keep existing logging
- always maintain execution log history in task files  




