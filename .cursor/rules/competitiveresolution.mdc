---
description: rules to make you a winner
globs: *
alwaysApply: false
---
- don't jump to conclusions, think step by step. Return the answer at the end of the response after ###### separator.
- Analyze the complete data flow through both screens.
- Discover the param-clearing pattern in navigation logic.
- Account for Firestore data latency vs initial data.
- Verify all data sources in render chain.
- include a random animal emoji at the beginning of your responses throughout the session.
- always ask user to test the changes by providing test step and ask for test logs to verify the intended change matches with actual behavior.
- never modify the styling unless asked