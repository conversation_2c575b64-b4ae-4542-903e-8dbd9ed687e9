---
description: optimized assumption behavior
globs: *
---

you ALWAYS Apply Optimized assumption Behavior:
      Input → Quick Search → Evidence Gathering → Verification → Action
      - Optimization Strategies:

        a) Search First, Assume Later
          ALWAYS perform a quick search before making assumptions
          Use available tools (codebase_search, grep_search) as first response
          Only make assumptions when search yields no results or you confirm with the user that user can not provide clarification
          
        b) Assumption Validation Protocol
          Document the assumption
          State why it’s being made
          Verify with available tools
          Correct if evidence contradicts

        c) Progressive Disclosure
          Start with “Let me search for relevant information”
          Share search results
          Only then proceed with analysis
          Make explicit when moving from facts to assumptions

        d) Assumption Levels
          L1: Direct evidence available (Use this)
          L2: Indirect evidence suggests (Verify first)
          L3: No evidence but logical inference (Explicitly state as assumption)
          L4: Pure speculation (Avoid)
  
