import { auth } from '../firebase';
import { unsubscribeAll, clearSubscriptions } from '../utils/firebaseCleanup';
import { Platform } from 'react-native';
import messaging from '@react-native-firebase/messaging';
import PushNotificationIOS from '@react-native-community/push-notification-ios';
import { listenerRegistry } from '../utils/listenerRegistry';
import { clearNotificationListeners } from '../services/notificationService';

export const signOut = async () => {
  try {
    console.log('[AuthService] Starting sign out process');
    
    // Step 1: Debug current listener state
    console.log('[AuthService] Current listenerRegistry state:', {
      activeListenersCount: listenerRegistry.activeListeners.size,
      keys: Array.from(listenerRegistry.activeListeners.keys())
    });
    
    // Add debug for Firebase active listeners
    console.log('[AuthService] Checking Firebase active listeners...');
    
    // Step 2: Clean up all registered listeners
    console.log('[AuthService] Cleaning up listenerRegistry listeners');
    listenerRegistry.cleanupAll();
    
    // Step 3: Clean up firebaseCleanup tracked listeners
    console.log('[AuthService] Cleaning up firebaseCleanup tracked listeners');
    unsubscribeAll();
    
    // Step 4: Explicitly clear notification listeners
    console.log('[AuthService] Explicitly clearing notification listeners');
    await clearNotificationListeners();
    
    // Step 5: Force clear any remaining subscriptions without calling unsubscribe
    console.log('[AuthService] Force clearing any remaining subscriptions');
    clearSubscriptions();
    
    // Step 6: Small delay to allow cleanup operations to complete
    console.log('[AuthService] Waiting for cleanup to finalize');
    await new Promise(resolve => setTimeout(resolve, 300));
    
    // Step 7: Remove FCM token
    console.log('[AuthService] Removing FCM token');
    await removeFCMToken(false); // Pass false to skip redundant unsubscribeAll
    
    // Step 8: Sign out from Firebase
    console.log('[AuthService] Signing out from Firebase');
    await auth.signOut();
    
    // After all cleanup steps, add a final check
    console.log('[AuthService] Final check for any remaining Firebase listeners');
    
    // Step 9: Debug final state after cleanup
    console.log('[AuthService] Final listenerRegistry state:', {
      activeListenersCount: listenerRegistry.activeListeners.size,
      keys: Array.from(listenerRegistry.activeListeners.keys())
    });
    
    console.log('[AuthService] Sign out successful');
    return true;
  } catch (error) {
    console.error('[AuthService][SIGN_OUT_ERROR]', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : 'No stack available',
      timestamp: new Date().toISOString()
    });
    throw error;
  }
};

const removeFCMToken = async (shouldUnsubscribe = true) => {
  console.log('=== Starting FCM Token Removal Process ===');
  try {
    // Step 1: Unsubscribe from Firebase listeners (skip if already done)
    if (shouldUnsubscribe) {
      console.log('Step 1: Unsubscribing from Firebase listeners...');
      unsubscribeAll();
      console.log('Firebase listeners unsubscribed successfully');
    } else {
      console.log('Step 1: Skipping Firebase listeners unsubscribe (already done)');
    }

    // Step 2: Clean up notifications
    console.log('Step 2: Cleaning up notifications...');
    if (Platform.OS === 'ios') {
      console.log('Removing iOS notifications...');
      await PushNotificationIOS.removeAllDeliveredNotifications();
      await PushNotificationIOS.setApplicationIconBadgeNumber(0);
      console.log('iOS notifications cleaned up');
    }

    // Step 3: Remove FCM token
    console.log('Step 3: Removing FCM token...');
    try {
      const token = await messaging().getToken();
      console.log('Current FCM token:', token ? 'exists' : 'none');
      await messaging().deleteToken();
      console.log('FCM token deleted successfully');
    } catch (error) {
      console.warn('Error removing FCM token:', error);
      // Continue with sign out even if FCM cleanup fails
    }
    
    console.log('=== FCM Token Removal Process Complete ===');
  } catch (error) {
    console.error('Error during FCM token removal process:', error instanceof Error ? error.message : error);
    console.error('Error stack:', error instanceof Error ? error.stack : 'No stack available');
    throw error;
  }
}; 
