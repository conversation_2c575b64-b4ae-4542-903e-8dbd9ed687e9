import { NotificationType } from '../types/notifications';
import { db } from '../firebase';
import { doc, getDoc } from 'firebase/firestore';
import { fcmService } from './fcmService';

export const sendNewOfferNotification = async (
  postingOwnerId: string,
  postingId: string,
  offerId: string,
  offerPrice: number
) => {
  try {
    // Get posting details
    const postingDoc = await getDoc(doc(db, 'postings', postingId));
    if (!postingDoc.exists()) return;

    // Get user's FCM token
    const userDoc = await getDoc(doc(db, 'users', postingOwnerId));
    if (!userDoc.exists()) return;

    const fcmToken = userDoc.data().fcmToken;
    if (!fcmToken) return;

    // Prepare notification payload
    const payload = {
      token: fcmToken,
      notification: {
        title: 'New Offer Received',
        body: `You received a new offer of $${offerPrice} for "${postingDoc.data().title}"`,
      },
      data: {
        type: NotificationType.NEW_OFFER,
        postingId,
        offerId,
      },
    };

    // Send to Firebase Cloud Function
    await fetch('YOUR_CLOUD_FUNCTION_URL/sendNotification', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
    });
  } catch (error) {
    console.error('Error sending new offer notification:', error);
  }
}; 