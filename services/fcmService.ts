import messaging from '@react-native-firebase/messaging';
import { Platform, NativeEventEmitter, NativeModules } from 'react-native';

class FCMService {
  private messagingEmitter: NativeEventEmitter | undefined;

  constructor() {
    if (Platform.OS === 'ios') {
      this.messagingEmitter = new NativeEventEmitter(NativeModules.RNFBMessagingModule);
    }
  }

  async requestUserPermission() {
    if (Platform.OS === 'ios') {
      const authStatus = await messaging().requestPermission();
      return authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
             authStatus === messaging.AuthorizationStatus.PROVISIONAL;
    }
    return true;
  }

  async getFCMToken() {
    try {
      const token = await messaging().getToken();
      console.log('FCM Token:', token);
      return token;
    } catch (error) {
      console.error('Error getting FCM token:', error);
      return null;
    }
  }

  onMessage(callback: (message: any) => void) {
    return messaging().onMessage(callback);
  }

  onBackgroundMessage(callback: (message: any) => Promise<void>) {
    return messaging().setBackgroundMessageHandler(callback);
  }

  async subscribeToTopic(topic: string) {
    try {
      await messaging().subscribeToTopic(topic);
      console.log('Subscribed to topic:', topic);
    } catch (error) {
      console.error('Error subscribing to topic:', error);
    }
  }

  async unsubscribeFromTopic(topic: string) {
    try {
      await messaging().unsubscribeFromTopic(topic);
      console.log('Unsubscribed from topic:', topic);
    } catch (error) {
      console.error('Error unsubscribing from topic:', error);
    }
  }
}

export const fcmService = new FCMService(); 