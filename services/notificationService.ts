import { db, auth } from '../firebase';
import { collection, query, where, orderBy, onSnapshot, updateDoc, doc, getDocs, addDoc, serverTimestamp, getDoc, limit as firestoreLimit, getCountFromServer } from 'firebase/firestore';
import { NotificationType, NotificationSettings, NOTIFICATION_CATEGORIES } from '../types/notifications';
import messaging from '@react-native-firebase/messaging';
import PushNotificationIOS from '@react-native-community/push-notification-ios';
import { NavigationContainerRef } from '@react-navigation/native';
import { Platform } from 'react-native';
import { addSubscription } from '../utils/firebaseCleanup';
import { listenerRegistry } from '../utils/listenerRegistry';

declare global {
  var _notificationListenerCount: number | undefined;
}

interface UnsubscribeFunction extends Function {
  _userId?: string;
}

let notificationListeners: UnsubscribeFunction[] = [];

export const clearNotificationListeners = async () => {
  console.log(`Clearing ${notificationListeners.length} notification listeners...`);

  // Unsubscribe each listener
  for (let i = 0; i < notificationListeners.length; i++) {
    try {
      console.log(`Unsubscribing notification listener ${i + 1}`);
      if (typeof notificationListeners[i] === 'function') {
        notificationListeners[i]();
      }
    } catch (error) {
      console.error(`Error unsubscribing listener ${i + 1}:`, error);
    }
  }

  // Clear the array
  notificationListeners.length = 0;

  // Reset any internal counters used for tracking listeners
  if (typeof global._notificationListenerCount !== 'undefined') {
    global._notificationListenerCount = 0;
  }

  console.log('All notification listeners cleared');
};

export const setupNotificationListener = (userId: string, onUnreadCountUpdate: (count: number) => void) => {
  console.log('Setting up notification listener...');

  // Check if we already have an active listener for this user
  const existingListenerIndex = notificationListeners.findIndex(
    listener => listener._userId === userId
  );

  if (existingListenerIndex >= 0) {
    console.log(`Found existing listener for user ${userId}, cleaning up first`);
    if (typeof notificationListeners[existingListenerIndex] === 'function') {
      notificationListeners[existingListenerIndex]();
    }
    notificationListeners.splice(existingListenerIndex, 1);
  }

  // Get current count for logging
  const currentCount = notificationListeners.length;

  // Create a unique subscription ID
  const subscriptionId = `notification_listener_${userId}`;
  console.log(`Adding new notification listener subscription (${subscriptionId}), current count: ${currentCount}`);

  // Set up the new listener
  const q = query(
    collection(db, 'notifications'),
    where('recipientId', '==', userId),
    where('read', '==', false)
  );

  const unsubscribe = onSnapshot(q,
    snapshot => {
      const count = snapshot.docs.length;
      onUnreadCountUpdate(count);
    },
    error => {
      console.error('Error in notification listener:', error);
    }
  ) as UnsubscribeFunction;

  // Add user ID to the unsubscribe function for tracking
  unsubscribe._userId = userId;

  // Add to our tracking array
  notificationListeners.push(unsubscribe);
  console.log(`Subscription added, new count: ${notificationListeners.length}`);

  // Register with listenerRegistry directly
  try {
    listenerRegistry.addListener(subscriptionId, () => unsubscribe());
    console.log(`Added to listenerRegistry with ID: ${subscriptionId}`);
  } catch (error) {
    console.error('Error registering with listenerRegistry:', error);
  }

  console.log('Notification listener setup complete');
  return unsubscribe;
};

export const fetchNotifications = async (limitCount = 20): Promise<Notification[]> => {
  const user = auth.currentUser;
  if (!user) throw new Error('User not authenticated');

  const q = query(
    collection(db, 'notifications'),
    where('recipientId', '==', user.uid),
    orderBy('createdAt', 'desc'),
    firestoreLimit(limitCount)
  );

  const snapshot = await getDocs(q);
  return snapshot.docs.map(doc => ({
    id: doc.id,
    ...doc.data()
  })) as Notification[];
};

export const markNotificationAsRead = async (notificationId: string): Promise<void> => {
  const notificationRef = doc(db, 'notifications', notificationId);
  await updateDoc(notificationRef, { read: true });
};

export const markRelevantNotificationsAsRead = async (offerId?: string, postingId?: string): Promise<void> => {
  console.log('[notificationService][MARK_RELEVANT_READ]', {
    offerId,
    postingId,
    timestamp: new Date().toISOString()
  });

  if (!offerId && !postingId) {
    console.warn('[notificationService][MARK_RELEVANT_READ] No offerId or postingId provided');
    return;
  }

  try {
    const user = auth.currentUser;
    if (!user) {
      console.warn('[notificationService][MARK_RELEVANT_READ] No authenticated user');
      return;
    }

    // Build query conditions
    const conditions = [];
    if (offerId) conditions.push(where('offerId', '==', offerId));
    if (postingId) conditions.push(where('postingId', '==', postingId));

    // Create query for unread notifications related to this offer/posting
    const q = query(
      collection(db, 'notifications'),
      where('recipientId', '==', user.uid),
      where('read', '==', false),
      ...conditions
    );

    const snapshot = await getDocs(q);
    const count = snapshot.docs.length;

    console.log('[notificationService][MARK_RELEVANT_READ] Found notifications:', {
      count,
      offerId,
      postingId,
      timestamp: new Date().toISOString()
    });

    // Mark all matching notifications as read
    const updatePromises = snapshot.docs.map(doc =>
      updateDoc(doc.ref, { read: true })
    );

    await Promise.all(updatePromises);

    console.log('[notificationService][MARKED_READ]', {
      count,
      offerId,
      postingId,
      timestamp: new Date().toISOString()
    });
  } catch (error: any) {
    console.error('[notificationService][MARK_RELEVANT_READ_ERROR]', {
      error: error.message,
      stack: error.stack,
      offerId,
      postingId,
      timestamp: new Date().toISOString()
    });
  }
};

export const getUnreadNotificationCount = async (settings = null) => {
  try {
    const user = auth.currentUser;
    if (!user) return 0;

    console.log('Getting unread count with settings:', settings);

    let q = query(
      collection(db, 'notifications'),
      where('recipientId', '==', user.uid),
      where('read', '==', false)
    );

    // If settings provided, only count enabled notification types
    if (settings) {
      const enabledTypes = Object.entries(settings)
        .filter(([_, enabled]) => enabled)
        .map(([type]) => type);

      console.log('Enabled notification types:', enabledTypes);

      if (enabledTypes.length > 0) {
        q = query(q, where('type', 'in', enabledTypes));
      } else {
        console.log('All notifications disabled');
        return 0;
      }
    }

    const snapshot = await getCountFromServer(q);
    const count = snapshot.data().count;
    console.log('Unread count from server:', count);
    return count;
  } catch (error) {
    console.error('Error getting unread count:', error);
    return 0;
  }
};

export const subscribeToNotifications = async (callback: (notifications: Notification[]) => void) => {
  console.log('[notificationService] Setting up notification subscription');
  try {
    const currentUser = auth.currentUser;
    if (!currentUser) {
      console.warn('No user logged in for notifications subscription');
      return () => {};
    }

    const notificationsRef = collection(db, 'notifications');
    const q = query(
      notificationsRef,
      where('recipientId', '==', currentUser.uid),
      orderBy('createdAt', 'desc')
    );

    const unsubscribe = onSnapshot(q, (snapshot) => {
      const notifications = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
      })) as Notification[];

      console.log('Notification snapshot received, count:', notifications.length);
      callback(notifications);
    }, (error) => {
      console.error('Error in notifications snapshot:', error);
    });

    return unsubscribe;
  } catch (error) {
    console.error('[notificationService] Subscription error:', error);
    throw error;
  }
};

export const createOfferNotification = async (
  recipientId: string,
  offerId: string,
  postingId: string,
  offerAmount: string
) => {
  const notification = {
    type: NotificationType.NEW_OFFER,
    offerId,
    recipientId,
    senderId: auth.currentUser?.uid,
    data: { postingId },
    title: 'New Offer',
    body: `You received a new offer of $${offerAmount}`,
    read: false,
    createdAt: serverTimestamp(),
  };

  await addDoc(collection(db, 'notifications'), notification);
};

export const getNotificationSettings = async (): Promise<NotificationSettings> => {
  const user = auth.currentUser;
  if (!user) throw new Error('User not authenticated');

  try {
    const userDoc = await getDoc(doc(db, 'users', user.uid));
    const settings = userDoc.data()?.notificationSettings || {
      [NotificationType.NEW_OFFER]: true,
      [NotificationType.NEW_MESSAGE]: true,
      [NotificationType.FAVORITE_POSTING_UPDATE]: true,
      [NotificationType.FAVORITE_NEW_MESSAGE]: true,
      [NotificationType.OFFER_STATUS_CHANGE]: true,
      [NotificationType.NEW_LOWER_OFFER]: true,
    };
    return settings;
  } catch (error) {
    console.error('Error fetching notification settings:', error);
    throw error;
  }
};

export const updateNotificationSettings = async (updates: Partial<NotificationSettings>): Promise<void> => {
  const user = auth.currentUser;
  if (!user) throw new Error('User not authenticated');

  try {
    const userRef = doc(db, 'users', user.uid);
    await updateDoc(userRef, {
      'notificationSettings': updates
    });
  } catch (error) {
    console.error('Error updating notification settings:', error);
    throw error;
  }
};

export const setupForegroundMessageHandler = () => {
  return messaging().onMessage(async remoteMessage => {
    console.log('Received foreground message:', remoteMessage);

    // Show iOS notification
    PushNotificationIOS.addNotification({
      alertTitle: remoteMessage.notification?.title,
      alertBody: remoteMessage.notification?.body,
      userInfo: remoteMessage.data,
      soundName: 'default',
      category: remoteMessage.data?.type,
    });
  });
};

export const setupNotificationOpenedHandler = (navigationRef: NavigationContainerRef<any>) => {
  return messaging().onNotificationOpenedApp(remoteMessage => {
    console.log('Notification opened app:', remoteMessage);
    if (navigationRef) {
      handleNotificationNavigation(remoteMessage, navigationRef);
    }
  });
};

const handleNotificationNavigation = (
  notification: any,
  navigationRef: NavigationContainerRef<any>
) => {
  try {
    console.log('[notificationService] Handling notification navigation:', {
      type: notification.type,
      hasData: !!notification.data
    });

    const notificationData = notification.data || notification.getData?.() || {};
    const {
      type,
      offerId,
      postingId,
      postingOwnerId,
      offerOwnerId
    } = notificationData;

    if (!type || !offerId) {
      console.warn('[notificationService] Missing required data:', {
        type,
        offerId,
        notification
      });
      return;
    }

    console.log('[notificationService] Navigating with:', {
      type,
      offerId,
      postingId
    });

    switch (type) {
      case NotificationType.NEW_OFFER:
      case NotificationType.OFFER_STATUS_CHANGE:
      case NotificationType.NEW_MESSAGE:
        navigationRef.navigate('OfferDetail', {
          offerId,
          postingId: postingId || '',
          postingOwnerId: postingOwnerId || '',
          offerOwnerId: offerOwnerId || ''
          // Don't pass initialOffer/initialPosting
        });
        break;
      // Add other cases as needed
      default:
        console.warn('[notificationService] Unhandled notification type:', type);
    }
  } catch (error) {
    console.error('[notificationService] Navigation error:', {
      error: error.message,
      stack: error.stack
    });
  }
};

export const initializeNotifications = async () => {
  console.log('=== Starting Notification Initialization ===');

  if (Platform.OS !== 'ios') {
    console.log('Platform is not iOS, skipping notification setup');
    return { success: true, platform: Platform.OS };
  }

  try {
    console.log('Requesting notification permission...');
    const authStatus = await messaging().requestPermission();
    console.log('Permission status received:', authStatus);

    const enabled =
      authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
      authStatus === messaging.AuthorizationStatus.PROVISIONAL;

    if (!enabled) {
      console.log('Notifications not enabled. Status:', authStatus);
      return { success: false, reason: 'permissions_denied' };
    }

    console.log('Notifications enabled, getting FCM token...');
    const token = await messaging().getToken();
    console.log('FCM Token received:', token ? 'success' : 'failed');

    if (token && auth.currentUser) {
      console.log('Updating device token for user:', auth.currentUser.uid);
      await updateUserDeviceToken(auth.currentUser.uid, token);
      console.log('Device token updated successfully');
    }

    // Safely check if the function exists before calling
    console.log('Checking foreground presentation options capability...');
    if (typeof messaging().setForegroundPresentationOptions === 'function') {
      console.log('Configuring foreground presentation options...');
      await messaging().setForegroundPresentationOptions({
        alert: true,
        badge: true,
        sound: true,
      });
      console.log('Foreground presentation options configured successfully');
    } else {
      console.log('Foreground presentation options not available on this platform/version');
    }

    console.log('=== Notification Initialization Complete ===');
    return { success: true, token };

  } catch (error) {
    console.error('Notification initialization failed:', {
      error: error.message,
      stack: error.stack
    });
    return { success: false, error };
  }
};

export const showLocalNotification = (notification: {
  title: string;
  body: string;
  type: NotificationType;
  data?: any;
}) => {
  if (Platform.OS !== 'ios') return;

  try {
    console.log('=== Showing Local Notification ===');

    // Create notification details
    const details = {
      alertTitle: notification.title,
      alertBody: notification.body,
      category: notification.type,
      userInfo: {
        type: notification.type,
        offerId: notification.data?.offerId,
        postingId: notification.data?.postingId,
        senderId: notification.data?.senderId,
      },
      applicationIconBadgeNumber: 1,
      soundName: 'default',
    };

    console.log('Creating notification with details:', details);

    // Use presentLocalNotification for both simulator and device
    PushNotificationIOS.presentLocalNotification(details);
    console.log('Local notification presented successfully');
  } catch (error) {
    console.error('Error showing local notification:', error);
    console.error('Error details:', {
      name: error.name,
      message: error.message,
      stack: error.stack
    });
  }
};

export const handleForegroundNotification = (remoteMessage: any) => {
  if (Platform.OS !== 'ios') return;

  try {
    console.log('Handling foreground message:', remoteMessage);

    const notification = {
      title: remoteMessage.notification?.title || '',
      body: remoteMessage.notification?.body || '',
      type: remoteMessage.data?.type || 'DEFAULT',
      data: {
        offerId: remoteMessage.data?.offerId,
        postingId: remoteMessage.data?.postingId,
        senderId: remoteMessage.data?.senderId,
      }
    };

    showLocalNotification(notification);
  } catch (error) {
    console.error('Error handling foreground notification:', error);
  }
};

export const requestNotificationPermissions = async () => {
  if (Platform.OS !== 'ios') return false;

  try {
    const authStatus = await PushNotificationIOS.requestPermissions({
      alert: true,
      badge: true,
      sound: true,
    });

    console.log('Notification permissions status:', authStatus);
    return !!authStatus;
  } catch (error) {
    console.error('Error requesting notification permissions:', error);
    return false;
  }
};

export const setupNotificationHandlers = (navigationRef: any) => {
  if (Platform.OS !== 'ios') return;

  // Handle notification when app is in foreground
  PushNotificationIOS.addEventListener('localNotification', (notification) => {
    console.log('Received local notification:', notification);
    // Add your navigation logic here if needed
  });

  // Handle when app is opened from notification
  PushNotificationIOS.addEventListener('notification', (notification) => {
    console.log('Opened app from notification:', notification);
    // Add your navigation logic here if needed
  });

  // Clear badge number when app is opened
  PushNotificationIOS.setApplicationIconBadgeNumber(0);
};

export const clearBadgeCount = () => {
  if (Platform.OS === 'ios') {
    PushNotificationIOS.setApplicationIconBadgeNumber(0);
  }
};

export const sendNotification = async (notificationData: NotificationData) => {
  try {
    console.log('[notificationService][SEND_START]', {
      recipientId: notificationData.recipientId,
      type: notificationData.data?.type,
      notificationType: notificationData.type, // Log both data.type and direct type
      title: notificationData.title,
      timestamp: new Date().toISOString()
    });

    // Get recipient's token
    const userDoc = await getDoc(doc(db, 'users', notificationData.recipientId));

    if (!userDoc.exists()) {
      console.warn('[notificationService][USER_NOT_FOUND]', {
        recipientId: notificationData.recipientId,
        notificationType: notificationData.type,
        timestamp: new Date().toISOString()
      });
      return;
    }

    const pushToken = userDoc.data().expoPushToken;

    if (!pushToken) {
      console.warn('[notificationService][NO_PUSH_TOKEN]', {
        recipientId: notificationData.recipientId,
        notificationType: notificationData.type,
        timestamp: new Date().toISOString()
      });
      return;
    }

    console.log('[notificationService][SENDING_PUSH]', {
      token: pushToken,
      recipientId: notificationData.recipientId,
      notificationType: notificationData.type,
      timestamp: new Date().toISOString()
    });

    // Send push notification
    const message = {
      to: pushToken,
      title: notificationData.title,
      body: notificationData.body,
      data: {
        ...notificationData.data,
        _notificationType: notificationData.type, // Ensure type is included in data
        _timestamp: Date.now()
      }
    };

    console.log('[notificationService][PUSH_MESSAGE]', {
      message,
      timestamp: new Date().toISOString()
    });

    const response = await fetch('https://exp.host/--/api/v2/push/send', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(message)
    });

    const result = await response.json();

    console.log('[notificationService][PUSH_SENT]', {
      success: result.data?.status === 'ok',
      recipientId: notificationData.recipientId,
      notificationType: notificationData.type,
      response: result,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('[notificationService][SEND_ERROR]', {
      error: error.message,
      stack: error.stack,
      code: error.code,
      name: error.name,
      recipientId: notificationData.recipientId,
      timestamp: new Date().toISOString()
    });
    throw error;
  }
};

export const updateUserDeviceToken = async (userId: string, token: string) => {
  console.log('=== Updating Device Token ===');
  console.log('User ID:', userId);
  console.log('Token Length:', token?.length);

  try {
    const userRef = doc(db, 'users', userId);
    await updateDoc(userRef, {
      fcmToken: token,
      lastTokenUpdate: serverTimestamp(),
      platform: Platform.OS
    });
    console.log('Device token updated successfully');
    return true;
  } catch (error) {
    console.error('Error updating device token:', {
      error: error.message,
      stack: error.stack
    });
    return false;
  }
};

interface Notification {
  id: string;
  type: NotificationType;
  recipientId: string;
  senderId?: string;
  data?: any;
  title: string;
  body: string;
  read: boolean;
  createdAt: any;
}

