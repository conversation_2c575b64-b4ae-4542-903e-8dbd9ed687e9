import AsyncStorage from '@react-native-async-storage/async-storage';

const QUEUE_KEY = '@notification_queue';

interface QueuedNotification {
  recipientId: string;
  notification: {
    title: string;
    body: string;
    data?: any;
  };
  timestamp: number;
  retryCount: number;
}

export const NotificationQueueService = {
  async addToQueue(notification: QueuedNotification) {
    try {
      const queue = await this.getQueue();
      queue.push(notification);
      await AsyncStorage.setItem(QUEUE_KEY, JSON.stringify(queue));
    } catch (error) {
      console.error('Error adding to notification queue:', error);
    }
  },

  async getQueue(): Promise<QueuedNotification[]> {
    try {
      const queue = await AsyncStorage.getItem(QUEUE_KEY);
      return queue ? JSON.parse(queue) : [];
    } catch (error) {
      console.error('Error getting notification queue:', error);
      return [];
    }
  },

  async removeFromQueue(notification: QueuedNotification) {
    try {
      const queue = await this.getQueue();
      const newQueue = queue.filter(n => 
        n.timestamp !== notification.timestamp || 
        n.recipientId !== notification.recipientId
      );
      await AsyncStorage.setItem(QUEUE_KEY, JSON.stringify(newQueue));
    } catch (error) {
      console.error('Error removing from notification queue:', error);
    }
  },

  async processQueue(processNotification: (recipientId: string, notification: any) => Promise<void>) {
    const queue = await this.getQueue();
    for (const notification of queue) {
      try {
        await processNotification(notification.recipientId, notification.notification);
        await this.removeFromQueue(notification);
      } catch (error) {
        if (notification.retryCount < 3) {
          notification.retryCount++;
          await this.addToQueue(notification);
        }
        console.error('Error processing queued notification:', error);
      }
    }
  }
};

export type { QueuedNotification }; 