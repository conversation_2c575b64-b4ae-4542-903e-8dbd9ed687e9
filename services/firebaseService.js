// /Users/<USER>/Desktop/satbana/services/firebaseService.js

import { db, auth } from '../firebase';
import {
  collection,
  query,
  where,
  orderBy,
  limit,
  startAfter,
  onSnapshot,
  doc,
  getDoc,
  getDocs,
  addDoc,
  updateDoc,
  arrayUnion,
  arrayRemove,
  serverTimestamp,
  documentId,
  QuerySnapshot,
  DocumentData,
  writeBatch,
  setDoc,
  increment
} from 'firebase/firestore';
import { NotificationType } from '../types/notifications';
import messaging from '@react-native-firebase/messaging';
import PushNotificationIOS from '@react-native-community/push-notification-ios';
import { Platform } from 'react-native';

// Enhanced Listener Registry
const listenerRegistry = {
  offers: new Map(), // postingId -> { count: number, timestamp: number, listeners: Map<string, Function> }
  postings: new Map(),
  cleanup: new Map(), // subscriptionId -> { cleanup: Function, createdAt: number, lastActive: number }
  stats: {
    totalListeners: 0,
    activeScreens: new Set(),
    lastCleanup: null
  }
};

// Add new state management for listener transitions
const listenerStateManager = {
  transitions: new Map(), // registryId -> { status: 'initializing' | 'active' | 'cleaning' | 'inactive', timestamp: number }
  locks: new Set(), // Set of locked registryIds

  startTransition(registryId, fromState, toState) {
    console.log(`[ListenerStateManager] Starting transition for ${registryId}: ${fromState} -> ${toState}`);

    if (this.locks.has(registryId)) {
      console.log(`[ListenerStateManager] Transition blocked - ${registryId} is locked`);
      return false;
    }

    this.locks.add(registryId);
    this.transitions.set(registryId, {
      status: toState,
      timestamp: Date.now(),
      previousState: fromState
    });

    return true;
  },

  completeTransition(registryId) {
    console.log(`[ListenerStateManager] Completing transition for ${registryId}`);
    this.locks.delete(registryId);
    return true;
  },

  getState(registryId) {
    return this.transitions.get(registryId)?.status || 'inactive';
  },

  async rollback(registryId, previousState) {
    console.log(`[ListenerStateManager] Rolling back ${registryId} to ${previousState}`);

    this.transitions.set(registryId, {
      status: previousState,
      timestamp: Date.now(),
      rollback: true
    });

    this.locks.delete(registryId);

    console.log(`[ListenerStateManager] Rollback complete for ${registryId}`);
  },

  getTransitionMetrics(registryId) {
    const transition = this.transitions.get(registryId);
    if (!transition) return null;

    return {
      currentState: transition.status,
      previousState: transition.previousState,
      duration: Date.now() - transition.timestamp,
      isRollback: !!transition.rollback
    };
  }
};

export const getListenerRegistry = () => {
  return {
    cleanup: listenerRegistry.cleanup,
    stats: listenerRegistry.stats
  };
};

// Enhanced registration with metadata
export const registerListener = (collection, id, cleanup, metadata = {}) => {
  const registryId = `${collection}_${id}_${Date.now()}`;

  if (!listenerStateManager.startTransition(registryId, 'inactive', 'initializing')) {
    console.warn(`[ListenerRegistry] Registration blocked for ${registryId} - state transition in progress`);
    return null;
  }

  try {
    listenerRegistry[collection].set(id, {
      count: 1,
      timestamp: Date.now(),
      listeners: new Map([[registryId, cleanup]]),
      metadata
    });

    listenerRegistry.cleanup.set(registryId, {
      cleanup,
      createdAt: Date.now(),
      lastActive: Date.now(),
      active: true,
      collection,
      id
    });

    listenerRegistry.stats.totalListeners++;

    listenerStateManager.transitions.set(registryId, {
      status: 'active',
      timestamp: Date.now()
    });

    console.log(`[ListenerRegistry][${collection}] Registered:`, {
      registryId,
      collection,
      id,
      state: 'active',
      timestamp: new Date().toISOString()
    });

    return registryId;
  } finally {
    listenerStateManager.completeTransition(registryId);
  }
};

// Add component tracking after listenerRegistry
const componentListeners = new Map();

export const registerListenerWithComponent = (componentId, listenerId) => {
  if (!componentListeners.has(componentId)) {
    componentListeners.set(componentId, new Set());
  }
  componentListeners.get(componentId).add(listenerId);

  console.log(`[ListenerRegistry][Component] Registered listener with component:`, {
    componentId,
    listenerId,
    timestamp: new Date().toISOString()
  });
};

export const unregisterListenerFromComponent = (componentId, listenerId) => {
  if (componentListeners.has(componentId)) {
    componentListeners.get(componentId).delete(listenerId);

    console.log(`[ListenerRegistry][Component] Unregistered listener from component:`, {
      componentId,
      listenerId,
      timestamp: new Date().toISOString()
    });
  }
};

export const getActiveListenersForComponent = (componentId) => {
  if (!componentListeners.has(componentId)) {
    return {
      total: 0,
      listeners: [],
      byCollection: {
        offers: 0,
        postings: 0
      }
    };
  }

  const listenerIds = Array.from(componentListeners.get(componentId));
  const listeners = listenerIds
    .map(id => {
      const entry = listenerRegistry.cleanup.get(id);
      return entry ? {
        id,
        collection: entry.collection,
        active: entry.active,
        createdAt: entry.createdAt,
        lastActive: entry.lastActive
      } : null;
    })
    .filter(Boolean);

  const byCollection = listeners.reduce((acc, listener) => {
    acc[listener.collection] = (acc[listener.collection] || 0) + 1;
    return acc;
  }, {});

  return {
    total: listeners.length,
    listeners,
    byCollection
  };
};

// Update createAndRegisterListener function
export const createAndRegisterListener = (collectionName, queryOrRef, onNext, onError = null, componentId = null) => {
  const listenerId = `${collectionName}_${Date.now()}`;

  try {
    const unsubscribe = onSnapshot(
      queryOrRef,
      {
        next: (snapshot) => {
          const entry = listenerRegistry.cleanup.get(listenerId);
          if (entry) {
            entry.lastActive = Date.now();
          }
          onNext(snapshot);
        },
        error: (error) => {
          console.error(`[ListenerRegistry][${collectionName}] Error:`, {
            error: error.message,
            listenerId,
            timestamp: new Date().toISOString()
          });
          if (onError) onError(error);
        }
      }
    );

    const registryId = registerListener(collectionName, listenerId.split('_')[1], unsubscribe, {
      createdAt: Date.now(),
      source: 'hooks'
    });

    if (componentId) {
      registerListenerWithComponent(componentId, registryId);
    }

    console.log(`[ListenerRegistry][${collectionName}] Created:`, {
      listenerId,
      componentId: componentId || 'none',
      timestamp: new Date().toISOString()
    });

    return {
      unsubscribe: () => {
        if (componentId) {
          unregisterListenerFromComponent(componentId, registryId);
        }
        unregisterListener(registryId);
      },
      listenerId: registryId
    };
  } catch (error) {
    console.error(`[ListenerRegistry][${collectionName}] Creation failed:`, {
      error: error.message,
      timestamp: new Date().toISOString()
    });
    return {
      unsubscribe: () => {},
      listenerId: null
    };
  }
};

// Add a function to get active listener count by type
export const getActiveListenerCount = (collectionType = null) => {
  if (collectionType) {
    return (listenerRegistry[collectionType]?.size || 0);
  }

  return {
    total: listenerRegistry.stats.totalListeners,
    offers: listenerRegistry.offers.size,
    postings: listenerRegistry.postings.size,
    cleanup: listenerRegistry.cleanup.size
  };
};

// Add Cleanup Coordination
const cleanupCoordinator = {
  groups: new Map(), // groupId -> CleanupGroup
  activeCleanups: new Set(),

  createGroup(groupId, listeners) {
    console.log('[CleanupCoordinator] Creating group:', {
      groupId,
      listeners,
      timestamp: new Date().toISOString()
    });

    this.groups.set(groupId, {
      id: groupId,
      listeners,
      status: 'pending',
      startTime: Date.now(),
      completedListeners: new Set(),
      metrics: {
        duration: 0,
        attempts: 0,
        errors: 0
      }
    });
    return groupId;
  },

  async coordinateCleanup(groupId) {
    const group = this.groups.get(groupId);
    if (!group || this.activeCleanups.has(groupId)) {
      return false;
    }

    console.log('[CleanupCoordinator] Starting cleanup:', {
      groupId,
      listeners: group.listeners,
      timestamp: new Date().toISOString()
    });

    this.activeCleanups.add(groupId);
    group.status = 'in_progress';

    try {
      await Promise.all(group.listeners.map(async (listenerId) => {
        if (group.completedListeners.has(listenerId)) {
          return;
        }

        try {
          await this.cleanupListener(listenerId, groupId);
          group.completedListeners.add(listenerId);
        } catch (error) {
          group.metrics.errors++;
          console.error('[CleanupCoordinator] Listener cleanup failed:', {
            groupId,
            listenerId,
            error: error.message,
            timestamp: new Date().toISOString()
          });
        }
      }));

      group.status = 'completed';
      group.metrics.duration = Date.now() - group.startTime;

      console.log('[CleanupCoordinator] Group cleanup completed:', {
        groupId,
        metrics: group.metrics,
        timestamp: new Date().toISOString()
      });

      return true;
    } finally {
      this.activeCleanups.delete(groupId);
    }
  },

  async cleanupListener(listenerId, groupId) {
    const group = this.groups.get(groupId);
    if (!group) return;

    group.metrics.attempts++;

    // Start state transition with rollback support
    const previousState = listenerStateManager.getState(listenerId);
    const transitionSuccess = listenerStateManager.startTransition(
      listenerId,
      previousState,
      'cleaning'
    );

    if (!transitionSuccess) {
      throw new Error(`State transition failed for ${listenerId}`);
    }

    try {
      await unregisterListener(listenerId);
    } catch (error) {
      // Rollback on failure
      await listenerStateManager.rollback(listenerId, previousState);
      throw error;
    }
  }
};

// Enhance unregisterListener with coordinator support
const unregisterQueue = new Map(); // registryId -> timestamp
const CLEANUP_DEBOUNCE_MS = 100;

export const unregisterListener = async (subscriptionId, groupId = null) => {
  if (!subscriptionId) {
    console.warn('[ListenerRegistry] Attempted to unregister null subscriptionId');
    return;
  }

  const now = Date.now();
  const lastAttempt = unregisterQueue.get(subscriptionId);

  if (lastAttempt && (now - lastAttempt < CLEANUP_DEBOUNCE_MS)) {
    console.log(`[ListenerRegistry] Skipping duplicate cleanup for ${subscriptionId}`);
    return;
  }

  unregisterQueue.set(subscriptionId, now);

  const metrics = {
    startTime: now,
    duration: 0,
    success: false
  };

  try {
    const [collection, id] = subscriptionId.split('_');
    const registry = listenerRegistry[collection];
    const entry = registry?.get(id);

    if (!entry) {
      console.log(`[ListenerRegistry] No entry found for ${subscriptionId}`);
      return;
    }

    await new Promise(resolve => setTimeout(resolve, 0)); // Ensure async context

    entry.count--;
    listenerRegistry.stats.totalListeners--;

    const cleanupEntry = listenerRegistry.cleanup.get(subscriptionId);
    if (cleanupEntry) {
      await Promise.resolve(cleanupEntry.cleanup());
      listenerRegistry.cleanup.delete(subscriptionId);
    }

    if (entry.count <= 0) {
      registry.delete(id);
    }

    metrics.success = true;
    metrics.duration = Date.now() - metrics.startTime;

    console.log('[ListenerRegistry] Cleanup metrics:', {
      subscriptionId,
      groupId,
      metrics,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('[ListenerRegistry] Cleanup failed:', {
      subscriptionId,
      groupId,
      error: error.message,
      metrics,
      timestamp: new Date().toISOString()
    });
    throw error;
  } finally {
    setTimeout(() => unregisterQueue.delete(subscriptionId), CLEANUP_DEBOUNCE_MS * 2);
  }
};

// New: Automatic cleanup for orphaned listeners
export const cleanupOrphanedListeners = (maxAge = 300000) => { // 5 minutes default
  const now = Date.now();
  let cleanedCount = 0;

  listenerRegistry.cleanup.forEach((entry, subscriptionId) => {
    if (now - entry.lastActive > maxAge) {
      try {
        entry.cleanup();
        listenerRegistry.cleanup.delete(subscriptionId);
        cleanedCount++;

        console.log('[ListenerRegistry] Cleaned orphaned listener:', {
          subscriptionId,
          age: now - entry.createdAt,
          metadata: entry.metadata,
          timestamp: new Date().toISOString()
        });
      } catch (error) {
        console.error('[ListenerRegistry] Orphaned cleanup failed:', {
          error: error.message,
          subscriptionId,
          timestamp: new Date().toISOString()
        });
      }
    }
  });

  listenerRegistry.stats.lastCleanup = now;

  return cleanedCount;
};

// New: Get active listeners stats
export const getListenerStats = () => {
  return {
    ...listenerRegistry.stats,
    byCollection: {
      offers: listenerRegistry.offers.size,
      postings: listenerRegistry.postings.size
    },
    activeCleanups: listenerRegistry.cleanup.size,
    timestamp: new Date().toISOString()
  };
};

const getActiveListeners = (collection) => {
  return Array.from(listenerRegistry.entries())
    .filter(([key, count]) => key.startsWith(collection) && count > 0)
    .map(([key, count]) => ({ key, count }));
};

// // Fetch postings that are not deleted
// export const fetchActivePostings = (callback) => {
//   const q = query(collection(db, 'postings'), where('postingStatus', '!=', 'Deleted'));
//   return onSnapshot(q, (snapshot) => {
//     const updatedPostings = snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
//     callback(updatedPostings);
//   });
// };

export const fetchAllPostings = async (numberOfResults = 10) => {
  try {
    const postingsRef = collection(db, 'postings');
    const q = query(
      postingsRef,
      where('postingStatus', '!=', 'Deleted'),
      limit(numberOfResults)
    );

    const querySnapshot = await getDocs(q);
    const postings = querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      latitude: Number(doc.data().latitude),
      longitude: Number(doc.data().longitude)
    }));

    return postings.filter(posting =>
      !isNaN(posting.latitude) &&
      !isNaN(posting.longitude)
    );
  } catch (error) {
    console.error('Error fetching all postings:', error);
    return [];
  }
};

export const fetchPostingsPreview = async (numberOfResults = 10) => {
  try {
    const postingsRef = collection(db, 'postings');
    const q = query(
      postingsRef,
      where('postingStatus', '!=', 'Deleted'),
      limit(numberOfResults)
    );

    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      title: doc.data().title,
      userId: doc.data().userId,
      latitude: Number(doc.data().latitude),
      longitude: Number(doc.data().longitude)
    })).filter(posting =>
      !isNaN(posting.latitude) &&
      !isNaN(posting.longitude)
    );
  } catch (error) {
    console.error('Error fetching postings preview:', error);
    throw error;
  }
};

// Function to fetch user offers (keep this version)
export const fetchUserOffers = (userId, callback) => {
  const offersRef = collection(db, 'offers');
  const q = query(offersRef, where('userId', '==', userId));

  return onSnapshot(q, async (snapshot) => {
    const postingIds = [...new Set(
      snapshot.docs.map(doc => doc.data().postingId).filter(Boolean)
    )];

    const postingsData = postingIds.length > 0
      ? await fetchPostingsBatch(postingIds)
      : {};

    const offersData = snapshot.docs.map(offerDoc => {
      const offerData = { id: offerDoc.id, ...offerDoc.data() };
      const postingData = postingsData[offerData.postingId] || {
        title: 'Untitled',
        description: 'No description available',
        postingStatus: 'Deleted'
      };

      return {
        ...offerData,
        title: postingData.title,
        description: postingData.description,
        postingStatus: postingData.postingStatus
      };
    });

    callback(offersData);
  });
};

// Helper function to batch fetch postings
const MAX_RETRIES = 3;
const RETRY_DELAY = 1000; // 1 second

const fetchPostingsBatch = async (postingIds) => {
  let retryCount = 0;

  const attemptBatchFetch = async () => {
    try {
      console.log('Starting batch fetch for postings:', postingIds);

      const batches = [];
      const batchSize = 10;

      for (let i = 0; i < postingIds.length; i += batchSize) {
        const batch = postingIds.slice(i, i + batchSize);
        batches.push(batch);
      }

      console.log('Created batches:', batches.length);

      const results = await Promise.all(
        batches.map(async (batch, index) => {
          console.log(`Fetching batch ${index + 1}/${batches.length}`);
          const q = query(
            collection(db, 'postings'),
            where(documentId(), 'in', batch)
          );
          const snapshot = await getDocs(q);
          return snapshot.docs;
        })
      );

      const lookupObject = results.flat().reduce((acc, doc) => {
        acc[doc.id] = doc.data();
        return acc;
      }, {});

      console.log('Successfully created lookup object with keys:', Object.keys(lookupObject));
      return lookupObject;
    } catch (error) {
      if (retryCount < MAX_RETRIES) {
        retryCount++;
        console.log(`Batch fetch failed, attempt ${retryCount}/${MAX_RETRIES}. Retrying...`);
        await new Promise(resolve => setTimeout(resolve, RETRY_DELAY * retryCount));
        return attemptBatchFetch();
      }
      throw error;
    }
  };

  try {
    return await attemptBatchFetch();
  } catch (error) {
    console.error('Error in fetchPostingsBatch after all retries:', error);
    return {}; // Return empty object after all retries fail
  }
};

// Function to get posting details
export const getPostingDetails = async (postingId) => {
  const postingRef = doc(db, 'postings', postingId);
  const postingSnapshot = await getDoc(postingRef);
  return postingSnapshot.exists() ? postingSnapshot.data() : null;
};

// Function to fetch postidng owner by posting ID
export const getPostingOwner = async (postingId) => {
  const postingRef = doc(db, 'postings', postingId);
  const postingSnapshot = await getDoc(postingRef);
  if (postingSnapshot.exists()) {
    return postingSnapshot.data().userId || null;
  }
  return null;
};

// Function to get offer details
export const getOfferDetails = async (offerId) => {
  throw new Error('getOfferDetails is deprecated. Use useOfferDetails hook instead');
};

// Function to fetch messages for a specific offer discussion with pagination
export const fetchMessagesForOffer = async (offerId, lastTimestamp) => {
  try {
    const currentUser = auth.currentUser;
    if (!currentUser) {
      throw new Error('User not authenticated');
    }

    const discussionsQuery = query(
      collection(db, 'discussions'),
      where('offerId', '==', offerId)
    );

    const discussionSnapshot = await getDocs(discussionsQuery);

    if (!discussionSnapshot.empty) {
      const discussionDoc = discussionSnapshot.docs[0];
      const discussionData = discussionDoc.data();

      if (discussionData.messages) {
        // If lastTimestamp provided, only return newer messages
        if (lastTimestamp) {
          const newMessages = discussionData.messages.filter(msg =>
            msg.timestamp.toDate().getTime() > lastTimestamp
          );

          return {
            hasNewMessages: newMessages.length > 0,
            newMessages: newMessages,
            lastDoc: null,
            hasMore: false
          };
        }

        // Otherwise return all messages (initial load)
        return {
          messages: discussionData.messages,
          lastDoc: null,
          hasMore: false
        };
      }
    }

    return {
      messages: [],
      newMessages: [],
      hasNewMessages: false,
      lastDoc: null,
      hasMore: false
    };
  } catch (error) {
    console.error('Error fetching messages:', error);
    throw error;
  }
};

// Simplify fetchPaginatedMessages to use the same logic
export const fetchPaginatedMessages = fetchMessagesForOffer;

// Function to add an offer and initialize a discussion
export const addOfferAndDiscussion = async (offerData, postingOwnerId) => {
  try {
    console.log('[addOfferAndDiscussion] Starting with data:', {
      postingId: offerData.postingId,
      userId: offerData.userId,
      price: offerData.price,
      postingOwnerId,
      currentUser: auth.currentUser?.uid
    });

    // Step 1: Create the offer document
    const offerRef = await addDoc(collection(db, 'offers'), {
      ...offerData,
      createdAt: serverTimestamp(),
      status: 'pending'
    });
    console.log('[addOfferAndDiscussion] Offer created:', { offerId: offerRef.id });

    // Step 2: Create the initial discussion
    const discussionData = {
      offerId: offerRef.id,
      postingOwnerId,
      offerOwnerId: offerData.userId,
      messages: [],
      createdAt: serverTimestamp()
    };

    const discussionRef = await addDoc(collection(db, 'discussions'), discussionData);
    console.log('[addOfferAndDiscussion] Discussion created:', { discussionId: discussionRef.id });

    // Step 3: Create notification
    try {
      const notificationData = {
        type: 'NEW_OFFER',
        recipientId: postingOwnerId,
        senderId: offerData.userId,
        postingId: offerData.postingId,
        offerId: offerRef.id,
        title: 'New Offer',
        body: `You received a new offer of $${offerData.price}`,
        createdAt: serverTimestamp(),
        read: false
      };

      console.log('[addOfferAndDiscussion] Creating notification:', notificationData);

      // Add directly to notifications collection
      const notificationRef = collection(db, 'notifications');
      await addDoc(notificationRef, notificationData);
      console.log('[addOfferAndDiscussion] Notification created successfully');

    } catch (notificationError) {
      // Log but don't throw notification errors
      console.error('[addOfferAndDiscussion] Error creating notification:', {
        error: notificationError instanceof Error ? {
          message: notificationError.message,
          stack: notificationError.stack
        } : notificationError,
        offerId: offerRef.id
      });
    }

    // Step 4: Update user's favorites
    if (auth.currentUser) {
      try {
        const userRef = doc(db, 'users', auth.currentUser.uid);
        await updateDoc(userRef, {
          favorites: arrayUnion(offerData.postingId)
        });
        console.log('[addOfferAndDiscussion] Updated user favorites');
      } catch (favoritesError) {
        // Log but don't throw favorites errors
        console.error('[addOfferAndDiscussion] Error updating favorites:', {
          error: favoritesError instanceof Error ? {
            message: favoritesError.message,
            stack: favoritesError.stack
          } : favoritesError,
          userId: auth.currentUser.uid,
          postingId: offerData.postingId
        });
      }
    }

    return offerRef.id;
  } catch (error) {
    console.error('[addOfferAndDiscussion] Critical error:', {
      error: error instanceof Error ? {
        message: error.message,
        stack: error.stack
      } : error,
      offerData: {
        postingId: offerData.postingId,
        userId: offerData.userId,
        price: offerData.price
      }
    });
    throw error;
  }
};

// Function to add a new message to an offer discussion
export const addMessageToDiscussion = async (offerId, messageData) => {
  try {
    console.log('[addMessageToDiscussion] Starting:', {
      offerId,
      messageData: {
        ...messageData,
        timestampType: typeof messageData.timestamp,
        hasTimestamp: !!messageData.timestamp,
        timestampKeys: messageData.timestamp ? Object.keys(messageData.timestamp) : []
      }
    });

    const discussionsQuery = query(
      collection(db, 'discussions'),
      where('offerId', '==', offerId)
    );

    console.log('[addMessageToDiscussion] Fetching discussion');
    const discussionSnapshot = await getDocs(discussionsQuery);
    let discussionId;

    if (!discussionSnapshot.empty) {
      console.log('[addMessageToDiscussion] Found existing discussion');
      const discussionDoc = discussionSnapshot.docs[0];
      discussionId = discussionDoc.id;
      const discussionRef = doc(db, 'discussions', discussionId);

      const discussionData = discussionDoc.data();
      console.log('[addMessageToDiscussion] Current discussion data:', {
        id: discussionDoc.id,
        messageCount: discussionData.messages?.length || 0
      });

      // Create a clean message object
      const cleanMessage = {
        id: messageData.id,
        text: messageData.text,
        senderId: messageData.senderId,
        read: false,
        timestamp: messageData.timestamp
      };

      console.log('[addMessageToDiscussion] Clean message:', {
        ...cleanMessage,
        timestampType: typeof cleanMessage.timestamp,
        timestampKeys: Object.keys(cleanMessage.timestamp)
      });

      const currentMessages = discussionData.messages || [];
      const updatedMessages = [...currentMessages, cleanMessage];

      console.log('[addMessageToDiscussion] Updating discussion:', {
        discussionId,
        currentMessageCount: currentMessages.length,
        newMessageCount: updatedMessages.length
      });

      await updateDoc(discussionRef, {
        messages: updatedMessages,
        lastUpdated: serverTimestamp()
      });

      console.log('[addMessageToDiscussion] Discussion updated successfully');
    } else {
      console.log('[addMessageToDiscussion] Creating new discussion');

      const cleanMessage = {
        id: messageData.id,
        text: messageData.text,
        senderId: messageData.senderId,
        read: false,
        timestamp: messageData.timestamp
      };

      const newDiscussion = {
        offerId,
        messages: [cleanMessage],
        lastUpdated: serverTimestamp()
      };

      console.log('[addMessageToDiscussion] New discussion data:', {
        ...newDiscussion,
        messageCount: 1,
        firstMessageTimestamp: cleanMessage.timestamp
      });

      const discussionRef = await addDoc(collection(db, 'discussions'), newDiscussion);
      discussionId = discussionRef.id;
      console.log('[addMessageToDiscussion] New discussion created:', { discussionId });
    }

    // Create notification with enhanced error handling
    try {
      console.log('[addMessageToDiscussion] Creating notification - Start', {
        offerId,
        senderId: messageData.senderId
      });

      const offerDoc = await getDoc(doc(db, 'offers', offerId));
      console.log('[addMessageToDiscussion] Offer document fetched:', {
        exists: offerDoc.exists(),
        offerId
      });

      if (offerDoc.exists()) {
        const offerData = offerDoc.data();
        console.log('[addMessageToDiscussion] Offer data retrieved:', {
          postingId: offerData.postingId,
          offerUserId: offerData.userId
        });

        const postingDoc = await getDoc(doc(db, 'postings', offerData.postingId));
        console.log('[addMessageToDiscussion] Posting document fetched:', {
          exists: postingDoc.exists(),
          postingId: offerData.postingId
        });

        if (!postingDoc.exists()) {
          console.warn('[addMessageToDiscussion] Posting document not found');
          return discussionId;
        }

        const postingData = postingDoc.data();
        const recipientId = messageData.senderId === offerData.userId
          ? postingData.userId
          : offerData.userId;

        console.log('[addMessageToDiscussion] Preparing notification:', {
          recipientId,
          senderId: messageData.senderId,
          messageLength: messageData.text.length
        });

        // Create a clean notification object
        const notificationData = {
          type: 'NEW_MESSAGE',
          data: {
            offerOwnerId: offerData.userId,
            postingOwnerId: postingData.userId,
            messagePreview: messageData.text.substring(0, 100),
            timestamp: {
              seconds: Math.floor(Date.now() / 1000),
              nanoseconds: 0
            }
          },
          recipientId,
          senderId: messageData.senderId,
          offerId,
          title: 'New Message',
          body: messageData.text.substring(0, 100) + (messageData.text.length > 100 ? '...' : ''),
          createdAt: serverTimestamp(),
          read: false
        };

        // Add directly to notifications collection instead of using createSystemNotification
        const notificationRef = collection(db, 'notifications');
        await addDoc(notificationRef, notificationData);

        console.log('[addMessageToDiscussion] Notification created successfully:', {
          recipientId,
          type: 'NEW_MESSAGE'
        });
      }
    } catch (notificationError) {
      console.error('[addMessageToDiscussion] Error creating notification:', {
        error: notificationError instanceof Error ? {
          message: notificationError.message,
          stack: notificationError.stack,
          name: notificationError.name
        } : notificationError,
        offerId,
        senderId: messageData.senderId
      });
      // Don't throw here as the message was sent successfully
    }

    return discussionId;
  } catch (error) {
    console.error('[addMessageToDiscussion] Error:', {
      error: error instanceof Error ? {
        message: error.message,
        stack: error.stack
      } : error,
      offerId,
      messageData: {
        id: messageData.id,
        senderId: messageData.senderId
      }
    });
    throw error;
  }
};

// Function to withdraw user offer
export const withdrawUserOffer = async (offerId) => {
    const offerRef = doc(db, 'offers', offerId);
    await updateDoc(offerRef, {
      status: 'withdrawn',
    });
  };

// Helper function to generate search terms
const generateSearchTerms = (title) => {
  const words = title.toLowerCase().split(/\s+/);
  const searchTerms = new Set();

  // Process each word
  words.forEach(word => {
    // Add the full word
    searchTerms.add(word);

    // Add substrings of length 3 or more
    for (let i = 0; i < word.length - 2; i++) {
      for (let j = i + 3; j <= word.length; j++) {
        searchTerms.add(word.slice(i, j));
      }
    }
  });

  // Add combinations of consecutive words
  for (let i = 0; i < words.length - 1; i++) {
    const phrase = `${words[i]} ${words[i + 1]}`;
    searchTerms.add(phrase);
  }

  // Add the full title
  searchTerms.add(title.toLowerCase());

  return Array.from(searchTerms);
};

// Update the addPosting function
export const addPosting = async (postingData) => {
  try {
    const postingsRef = collection(db, 'postings');

    // Add titleLower and searchTerms fields
    const enhancedPostingData = {
      ...postingData,
      titleLower: postingData.title.toLowerCase(),
      searchTerms: generateSearchTerms(postingData.title),
      createdAt: serverTimestamp(),
      postingStatus: 'Active'
    };

    const docRef = await addDoc(postingsRef, enhancedPostingData);
    console.log('Posting added with ID:', docRef.id);
    return docRef.id;
  } catch (error) {
    console.error('Error adding posting:', error);
    throw error;
  }
};

// Function to fetch postings for a specific user
export const fetchUserPostings = (userId, callback) => {
    const q = query(collection(db, 'postings'), where('userId', '==', userId), where('postingStatus', '!=', 'Deleted'));
    return onSnapshot(q, (snapshot) => {
      const userPostings = snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
      callback(userPostings);
    });
  };

  // Function to fetch offers for a specific posting
export const fetchOffersForPosting = (postingId, callback) => {
  const subscriptionId = `offers_${postingId}_${Date.now()}`;
  const startTime = Date.now();

  try {
    // First try with ordered query
    const q = query(
      collection(db, 'offers'),
      where('postingId', '==', postingId),
      orderBy('createdAt', 'desc')
    );

    return onSnapshot(q,
      (snapshot) => {
        console.log('[fetchOffersForPosting] Query success:', {
          postingId,
          count: snapshot.size,
          elapsedMs: Date.now() - startTime
        });

        const offers = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));

        callback(offers);
      },
      (error) => {
        // If index error, fall back to unordered query
        if (error.message?.includes('requires an index')) {
          console.warn('[fetchOffersForPosting] Falling back to unordered query:', {
            postingId,
            error: error.message
          });

          const fallbackQuery = query(
            collection(db, 'offers'),
            where('postingId', '==', postingId)
          );

          return onSnapshot(fallbackQuery, (snapshot) => {
            const offers = snapshot.docs.map(doc => ({
              id: doc.id,
              ...doc.data()
            }));
            callback(offers);
          });
        }

        throw error; // Re-throw other errors
      }
    );
  } catch (error) {
    console.error('[fetchOffersForPosting] Fatal error:', {
      postingId,
      error: error.message,
      elapsedMs: Date.now() - startTime
    });
    throw error;
  }
};

// Function to delete a posting (mark as deleted)
export const deletePosting = async (postingId) => {
  console.log('[deletePosting] Starting deletion process', {
    postingId,
    timestamp: new Date().toISOString(),
    stack: new Error().stack
  });

  const batch = writeBatch(db);
  let notificationPromises = [];
  let discussionPromises = [];

  try {
    // Get all active offers for this posting
    console.log('[deletePosting] Fetching active offers...');
    const offersQuery = query(
      collection(db, 'offers'),
      where('postingId', '==', postingId),
      where('status', 'in', ['pending', 'active', 'updated'])
    );

    const offersSnapshot = await getDocs(offersQuery);
    console.log('[deletePosting] Found offers:', {
      count: offersSnapshot.size,
      postingId,
      timestamp: new Date().toISOString()
    });

    // Update posting status
    const postingRef = doc(db, 'postings', postingId);
    const postingDoc = await getDoc(postingRef);

    if (!postingDoc.exists()) {
      console.error('[deletePosting] Posting not found:', {
        postingId,
        timestamp: new Date().toISOString()
      });
      throw new Error('Posting not found');
    }

    const postingData = postingDoc.data();
    const postingOwnerId = postingData.userId;
    const postingTitle = postingData.title || 'Untitled Posting';

    // Update posting status
    console.log('[deletePosting] Marking posting as deleted:', {
      postingId,
      postingTitle,
      timestamp: new Date().toISOString()
    });

    batch.update(postingRef, {
      postingStatus: 'Deleted',
      deletedAt: serverTimestamp()
    });

    // Process each offer
    for (const offerDoc of offersSnapshot.docs) {
      try {
        const offerData = offerDoc.data();
        const offerRef = doc(db, 'offers', offerDoc.id);

        console.log('[deletePosting] Processing offer:', {
          offerId: offerDoc.id,
          postingId,
          timestamp: new Date().toISOString()
        });

        // Update offer status
        batch.update(offerRef, {
          status: 'withdrawn',
          lastUpdated: serverTimestamp(),
          postingDeleted: true,
          withdrawnBy: 'system'
        });

        // Create notification for offer owner
        const notificationData = {
          type: NotificationType.SYSTEM_WITHDRAWAL,
          recipientId: offerData.userId,
          senderId: postingOwnerId,
          offerId: offerDoc.id,
          postingId,
          title: 'Posting Deleted',
          body: `The posting "${postingTitle}" has been deleted. Your offer has been automatically withdrawn.`,
          data: {
            currentStatus: 'withdrawn',
            postingDeleted: true,
            timestamp: {
              seconds: Math.floor(Date.now() / 1000),
              nanoseconds: 0
            },
            postingTitle,
            withdrawnBy: 'system'
          }
        };

        // Add system message to discussion
        const discussionQuery = query(
          collection(db, 'discussions'),
          where('offerId', '==', offerDoc.id)
        );

        const discussionSnapshot = await getDocs(discussionQuery);
        if (!discussionSnapshot.empty) {
          const discussionDoc = discussionSnapshot.docs[0];
          const discussionRef = doc(db, 'discussions', discussionDoc.id);

          const systemMessage = {
            text: `This offer has been automatically withdrawn because the posting was deleted.`,
            senderId: 'system',
            timestamp: {
              seconds: Math.floor(Date.now() / 1000),
              nanoseconds: 0
            },
            type: 'system',
            read: true
          };

          batch.update(discussionRef, {
            messages: arrayUnion(systemMessage),
            lastUpdated: serverTimestamp(),
            status: 'withdrawn'
          });

          console.log('[deletePosting] Added system message to discussion:', {
            discussionId: discussionDoc.id,
            offerId: offerDoc.id,
            timestamp: new Date().toISOString()
          });
        }

        // Create notification directly with enhanced logging
        console.log('[deletePosting] Creating notification for offer owner:', {
          recipientId: offerData.userId,
          offerId: offerDoc.id,
          type: NotificationType.SYSTEM_WITHDRAWAL,
          timestamp: new Date().toISOString()
        });

        notificationPromises.push(
          addDoc(collection(db, 'notifications'), {
            ...notificationData,
            createdAt: serverTimestamp(),
            read: false,
            delivered: false
          }).then(docRef => {
            console.log('[deletePosting] Notification document created:', {
              notificationId: docRef.id,
              recipientId: offerData.userId,
              offerId: offerDoc.id,
              timestamp: new Date().toISOString()
            });
            return docRef;
          }).catch(error => {
            console.error('[deletePosting] Failed to create notification document:', {
              error: error.message,
              stack: error.stack,
              recipientId: offerData.userId,
              offerId: offerDoc.id,
              timestamp: new Date().toISOString()
            });
            throw error; // Re-throw to be caught by Promise.allSettled
          })
        );
      } catch (offerError) {
        console.error('[deletePosting] Error processing offer:', {
          error: offerError.message,
          stack: offerError.stack,
          offerId: offerDoc.id,
          postingId,
          timestamp: new Date().toISOString()
        });
        // Continue with other offers even if one fails
      }
    }

    // Commit the batch
    console.log('[deletePosting] Committing batch updates...');
    await batch.commit();

    // Send notifications after successful batch commit
    console.log('[deletePosting] Sending notifications to offer owners...', {
      notificationCount: notificationPromises.length,
      timestamp: new Date().toISOString()
    });

    // Enhanced logging for notification promises
    const notificationResults = await Promise.allSettled(notificationPromises);

    // Log detailed results of notification sending
    console.log('[deletePosting] Notification results:', {
      total: notificationResults.length,
      fulfilled: notificationResults.filter(r => r.status === 'fulfilled').length,
      rejected: notificationResults.filter(r => r.status === 'rejected').length,
      timestamp: new Date().toISOString()
    });

    // Log individual notification results for debugging
    notificationResults.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        console.log(`[deletePosting] Notification ${index + 1} succeeded:`, {
          notificationId: result.value?.id || 'unknown',
          timestamp: new Date().toISOString()
        });
      } else {
        console.error(`[deletePosting] Notification ${index + 1} failed:`, {
          error: result.reason?.message || 'Unknown error',
          stack: result.reason?.stack,
          timestamp: new Date().toISOString()
        });
      }
    });

    console.log('[deletePosting] Process completed successfully:', {
      postingId,
      offersProcessed: offersSnapshot.size,
      notificationsSent: notificationResults.filter(r => r.status === 'fulfilled').length,
      timestamp: new Date().toISOString()
    });

    return true;
  } catch (error) {
    console.error('[deletePosting] Error during deletion:', {
      error: error.message,
      stack: error.stack,
      postingId,
      timestamp: new Date().toISOString()
    });

    // Attempt rollback if batch failed
    try {
      console.log('[deletePosting] Attempting rollback...');
      const rollbackBatch = writeBatch(db);

      // Revert posting status
      const postingRef = doc(db, 'postings', postingId);
      rollbackBatch.update(postingRef, {
        postingStatus: 'Active',
        deletedAt: null
      });

      await rollbackBatch.commit();
      console.log('[deletePosting] Rollback successful');
    } catch (rollbackError) {
      console.error('[deletePosting] Rollback failed:', {
        error: rollbackError.message,
        stack: rollbackError.stack,
        postingId,
        timestamp: new Date().toISOString()
      });
    }

    throw error;
  }
};


// Function to add a posting to user's favorites
export const addToFavorites = async (userId, postingId) => {
  try {
    const userRef = doc(db, 'users', userId);
    await updateDoc(userRef, {
      favorites: arrayUnion(postingId),
    });
    console.log('Posting added to favorites:', postingId);
  } catch (error) {
    console.error('Error adding to favorites:', error);
    throw error;
  }
};

// Function to remove a posting from user's favorites
export const removeFromFavorites = async (userId, postingId) => {
  try {
    const userRef = doc(db, 'users', userId);
    await updateDoc(userRef, {
      favorites: arrayRemove(postingId),
    });
    console.log('Posting removed from favorites:', postingId);
  } catch (error) {
    console.error('Error removing from favorites:', error);
    throw error;
  }
};

// Function to get user's favorites list
export const getUserFavorites = async (userId) => {
  try {
    const userRef = doc(db, 'users', userId);
    const userDoc = await getDoc(userRef);
    if (userDoc.exists()) {
      return userDoc.data().favorites || [];
    }
    return [];
  } catch (error) {
    console.error('Error fetching user favorites:', error);
    throw error;
  }
};

// Update the updatePostingDetails function
export const updatePostingDetails = async (postingId, updatedData) => {
  try {
    console.log('[updatePostingDetails] Starting update:', { postingId, updatedData });

    const postingRef = doc(db, 'postings', postingId);
    const originalData = (await getDoc(postingRef)).data();

    // Track changes for notification
    const changes = [];
    if (updatedData.title && updatedData.title !== originalData.title) {
      console.log('[updatePostingDetails] Detected title change:', {
        from: originalData.title,
        to: updatedData.title
      });
      changes.push('title');
    }
    if (updatedData.description && updatedData.description !== originalData.description) {
      console.log('[updatePostingDetails] Detected description change');
      changes.push('description');
    }
    if (updatedData.latitude !== originalData.latitude || updatedData.longitude !== originalData.longitude) {
      console.log('[updatePostingDetails] Detected location change');
      changes.push('location');
    }

    // Update the posting
    await updateDoc(postingRef, updatedData);
    console.log('[updatePostingDetails] Posting updated successfully');

    // Notification logic
    if (changes.length > 0 || updatedData.postingStatus === 'Deleted') {
      console.log('[updatePostingDetails] Preparing notifications for changes:', changes);

      // Get all users who favorited this posting
      const usersQuery = query(
        collection(db, 'users'),
        where('favorites', 'array-contains', postingId)
      );

      const userSnapshots = await getDocs(usersQuery);
      console.log(`[updatePostingDetails] Found ${userSnapshots.size} users with favorite`);

      const notificationPromises = userSnapshots.docs.map(async (userDoc) => {
        try {
          console.log(`[updatePostingDetails] Creating notification for user ${userDoc.id}`);

          const notificationData = {
            type: NotificationType.FAVORITE_POSTING_UPDATE,
            recipientId: userDoc.id,
            senderId: auth.currentUser?.uid,
            postingId,
            title: 'Posting Updated',
            body: getUpdateNotificationBody(changes, originalData.title),
            createdAt: serverTimestamp(),
            read: false
          };

          console.log('[updatePostingDetails] Notification payload:', notificationData);

          // Add notification to Firestore
          const notificationRef = collection(db, 'notifications');
          const result = await addDoc(notificationRef, notificationData);

          // Update user's unread notification count
          const userRef = doc(db, 'users', userDoc.id);
          await updateDoc(userRef, {
            unreadNotificationCount: increment(1)
          });

          console.log(`[updatePostingDetails] Notification created for user ${userDoc.id}`, {
            notificationId: result.id,
            unreadCountUpdated: true
          });

          return result;
        } catch (error) {
          console.error(`[updatePostingDetails] Notification creation failed for user ${userDoc.id}:`, {
            error: error.message,
            stack: error.stack,
            notificationData
          });
          return null;
        }
      });

      console.log('[updatePostingDetails] Sending all notifications');
      await Promise.allSettled(notificationPromises);
      console.log('[updatePostingDetails] All notifications processed');
    }

    return true;
  } catch (error) {
    console.error('[updatePostingDetails] Error updating posting:', {
      error: error.message,
      stack: error.stack,
      postingId
    });
    throw error;
  }
};

// Helper function to generate notification body
const getUpdateNotificationBody = (changes, postingTitle) => {
  if (changes.includes('status')) {
    return `"${postingTitle}" has been deleted`;
  }

  const changeText = changes.join(' and ');
  return `"${postingTitle}" has been updated (${changeText})`;
};

// Subscribe to user's favorites
export const subscribeToUserFavorites = (userId, callback) => {
  const userRef = doc(db, 'users', userId);
  return onSnapshot(userRef, (userDoc) => {
    if (userDoc.exists()) {
      callback(userDoc.data().favorites || []);
    }
  });
};

// Function to save user location
export const saveUserLocation = async (location) => {
  try {
    const user = auth.currentUser;
    if (!user) {
      throw new Error('User not logged in');
    }

    const userLocationsRef = collection(db, 'userLocations');
    await addDoc(userLocationsRef, {
      userId: user.uid,
      latitude: location.latitude,
      longitude: location.longitude,
      timestamp: serverTimestamp(),
    });
    console.log('Location saved successfully');
  } catch (error) {
    console.error('Error saving location:', error);
    throw error;
  }
};

// Function to retrieve user location

export const fetchUserLocation = async () => {
  try {
    const user = auth.currentUser;
    if (!user) {
      throw new Error('User not logged in');
    }

    const userLocationsRef = collection(db, 'userLocations');
    const q = query(
      userLocationsRef,
      where('userId', '==', user.uid),
      orderBy('timestamp', 'desc'),
      limit(1)
    );

    const querySnapshot = await getDocs(q);
    if (!querySnapshot.empty) {
      const latestLocation = querySnapshot.docs[0].data();
      return {
        latitude: latestLocation.latitude,
        longitude: latestLocation.longitude,
      };
    } else {
      throw new Error('No location data found');
    }
  } catch (error) {
    console.error('Error fetching location:', error);
    return null;
  }
};


// Function to fetch postings by search


const fetchAdditionalPostings = async (searchTerm, lastVisibleDoc, mapBounds, remainingResults, isSearchMode) => {
  try {
    // Call the main fetch function with the given parameters
    const result = await fetchPostingsBySearch(
      searchTerm,
      lastVisibleDoc,
      mapBounds,
      remainingResults,
      isSearchMode
    );

    // Return only the postings from the result
    return result.postings || [];
  } catch (error) {
    console.error('Error fetching additional postings:', error);
    throw error;
  }
};



export const fetchPostingsBySearch = async (searchTerm, lastVisibleDoc = null, mapBounds, numberOfResults = 10, isSearchMode = false) => {
  try {
    const postingsRef = collection(db, 'postings');

    // Base query conditions for location and status
    const queryConditions = [
      where('postingStatus', '!=', 'Deleted'),
      where('latitude', '>=', mapBounds.south),
      where('latitude', '<=', mapBounds.north),
      where('longitude', '>=', mapBounds.west),
      where('longitude', '<=', mapBounds.east),
      orderBy('latitude'),
      orderBy('longitude'),
      limit(numberOfResults)  // Always fetch exactly 10 results
    ];

    // Add search term filtering if provided
    if (searchTerm.trim()) {
      const searchLower = searchTerm.toLowerCase();
      queryConditions.push(
        where('searchTerms', 'array-contains', searchLower)
      );
    }

    const postingsQuery = query(postingsRef, ...queryConditions);
    const querySnapshot = await getDocs(postingsQuery);
    console.log('[fetchPostingsBySearch] Query results:', querySnapshot.docs.length);

    const allPostings = querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      location: {
        latitude: doc.data().latitude,
        longitude: doc.data().longitude
      }
    }));

    return {
      postings: allPostings,
      lastVisible: querySnapshot.docs[querySnapshot.docs.length - 1] || null
    };
  } catch (error) {
    console.error('[fetchPostingsBySearch] Error:', error);
    throw error;
  }
};

// Add new function to mark messages as read
export const markMessagesAsRead = async (offerId, userId) => {
  try {
    // First get the offer details to check permissions
    const offerRef = doc(db, 'offers', offerId);
    const offerDoc = await getDoc(offerRef);

    if (!offerDoc.exists()) {
      throw new Error('Offer not found');
    }

    const offerData = offerDoc.data();

    // Get posting owner ID from posting document
    const postingRef = doc(db, 'postings', offerData.postingId);
    const postingDoc = await getDoc(postingRef);

    if (!postingDoc.exists()) {
      throw new Error('Posting not found');
    }

    const postingOwnerId = postingDoc.data().userId;

    // Check if user is either posting owner or offer owner
    if (userId !== offerData.userId && userId !== postingOwnerId) {
      console.log('User does not have permission to mark messages as read');
      return;
    }

    const q = query(collection(db, 'discussions'), where('offerId', '==', offerId));
    const querySnapshot = await getDocs(q);

    if (!querySnapshot.empty) {
      const discussionDoc = querySnapshot.docs[0];
      const discussionRef = doc(db, 'discussions', discussionDoc.id);
      const discussion = discussionDoc.data();

      const updatedMessages = discussion.messages.map(msg => {
        if (msg.senderId !== userId && !msg.read) {
          return { ...msg, read: true, readAt: new Date() };
        }
        return msg;
      });

      await updateDoc(discussionRef, { messages: updatedMessages });
    }
  } catch (error) {
    console.error('Error marking messages as read:', error);
    throw error;
  }
};

// Add this function to create notifications
export const createNotification = async (notificationData) => {
  try {
    console.log('=== Creating notification ===');
    console.log('Notification data:', notificationData);

    // Get recipient's notification settings
    const recipientRef = doc(db, 'users', notificationData.recipientId);
    const recipientDoc = await getDoc(recipientRef);
    const recipientSettings = recipientDoc.data()?.notificationSettings || {};

    // Check if this type of notification is enabled
    if (recipientSettings[notificationData.type] === false) {
      console.log(`Notification type ${notificationData.type} is disabled for recipient`);
      return null;
    }

    const notificationRef = collection(db, 'notifications');
    await addDoc(notificationRef, {
      ...notificationData,
      read: false,
      delivered: false,
      createdAt: serverTimestamp()
    });

    console.log('Notification added to Firestore with ID:', notificationRef.id);
    return notificationRef.id;
  } catch (error) {
    console.error('Error in createNotification:', error);
    throw error;
  }
};

// Batch read function for multiple offers
export const getMultipleOfferDetails = async (offerIds) => {
  const results = new Map();
  const uncachedOfferIds = [];

  // Check cache first
  for (const offerId of offerIds) {
    const cached = cacheManager.get(`offer_${offerId}`);
    if (cached) {
      results.set(offerId, cached);
    } else {
      uncachedOfferIds.push(offerId);
    }
  }

  // Batch size of 10 for Firestore limitations
  const BATCH_SIZE = 10;
  const batches = [];

  for (let i = 0; i < uncachedOfferIds.length; i += BATCH_SIZE) {
    const batch = uncachedOfferIds.slice(i, i + BATCH_SIZE);
    batches.push(batch);
  }

  // Process batches
  for (const batch of batches) {
    const offerRefs = batch.map(id => doc(db, 'offers', id));
    const offerDocs = await getDocs(query(collection(db, 'offers'), where(documentId(), 'in', batch)));

    offerDocs.forEach(doc => {
      const data = doc.data();
      results.set(doc.id, data);
      cacheManager.set(`offer_${doc.id}`, data);
    });
  }

  return results;
};

// Add this function to get postingId from an offer
export const getPostingIdForOffer = async (offerId) => {
  try {
    const offerRef = doc(db, 'offers', offerId);
    const offerDoc = await getDoc(offerRef);

    if (!offerDoc.exists()) {
      throw new Error('Offer not found');
    }

    const offerData = offerDoc.data();
    return offerData.postingId;
  } catch (error) {
    console.error('Error getting postingId for offer:', error);
    throw error;
  }
};

// Add updateOffer function with comprehensive logging
export const updateOffer = async (offerId, updates) => {
  try {
    // 1. Get offer data first to get postingId
    const offerSnap = await getDoc(doc(db, 'offers', offerId));
    if (!offerSnap.exists()) {
      throw new Error('Offer not found');
    }

    const offerData = offerSnap.data();
    const postingId = offerData.postingId;

    // 2. Get posting data
    const postingSnap = await getDoc(doc(db, 'postings', postingId));

    // 3. Create batch for atomic operations
    const batch = writeBatch(db);
    const offerRef = doc(db, 'offers', offerId);

    // 4. Add offer update to batch
    batch.update(offerRef, {
      ...updates,
      updatedAt: serverTimestamp()
    });

    // 5. Add notification if posting exists
    if (postingSnap.exists()) {
      const postingOwnerId = postingSnap.data().userId;

      const notificationData = {
        type: NotificationType.OFFER_STATUS_CHANGE,
        recipientId: postingOwnerId,
        senderId: auth.currentUser?.uid,
        offerId,
        postingId,
        title: 'Offer Updated',
        body: `Offer price updated to $${updates.price}`,
        data: {
          offerId,
          postingId,
          type: NotificationType.OFFER_STATUS_CHANGE,
          currentPrice: updates.price,
          currentDescription: updates.description,
          currentStatus: offerData.status || 'pending',
          timestamp: serverTimestamp()
        },
        read: false,
        createdAt: serverTimestamp()
      };

      await createNotification(notificationData);
    }

    // 6. Commit batch
    await batch.commit();

    // Return the updated offer data instead of just the reference
    const updatedOfferSnap = await getDoc(offerRef);
    return {
      id: updatedOfferSnap.id,
      ...updatedOfferSnap.data()
    };
  } catch (error) {
    console.error('[firebaseService][UPDATE_OFFER_ERROR]', {
      error: error instanceof Error ? {
        message: error.message,
        stack: error.stack
      } : error,
      offerId,
      updates
    });
    throw error;
  }
};

// Export coordinator for component use
export const getCleanupCoordinator = () => cleanupCoordinator;

