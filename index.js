import 'react-native-gesture-handler';
import { enableScreens } from 'react-native-screens';
import {AppRegistry, NativeModules, Platform, LogBox} from 'react-native';
import {gestureHandlerRootHOC} from 'react-native-gesture-handler';
import App from './App';
import { app } from './firebase';

// Enable screens before anything else
enableScreens();

// Debug configuration
const DEBUG = true;
const debug = (...args) => DEBUG && console.log(...args);

// Ignore specific warnings
LogBox.ignoreLogs([
  '`new NativeEventEmitter()`',
  'EventEmitter.removeListener',
]);

try {
  debug('=== Starting App Initialization ===');
  debug('Imports loaded successfully');
  
  // Check if all required functions exist
  debug('gestureHandlerRootHOC:', typeof gestureHandlerRootHOC);
  debug('AppRegistry.registerComponent:', typeof AppRegistry.registerComponent);
  
  // Register immediately without setTimeout
  const APP_NAME = 'com.company.satbana';
  
  // Check if App is loaded properly
  debug('App component:', typeof App);
  
  // Create wrapped component
  const AppWithGesture = gestureHandlerRootHOC(App);
  debug('AppWithGesture created successfully');
  
  // Register with React Native first
  debug('Registering with React Native');
  AppRegistry.registerComponent(APP_NAME, () => AppWithGesture);
  debug('React Native registration successful');
  
  // Then try Expo registration
  try {
    const { registerRootComponent } = require('expo');
    if (typeof registerRootComponent === 'function') {
      registerRootComponent(AppWithGesture);
      debug('Expo registration successful');
    }
  } catch (expoError) {
    debug('Expo registration skipped:', expoError.message);
  }
  
} catch (error) {
  console.error('Critical initialization error:', error);
  throw error;
}
