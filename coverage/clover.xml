<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1736818350345" clover="3.2.0">
  <project timestamp="1736818350345" name="All files">
    <metrics statements="714" coveredstatements="105" conditionals="198" coveredconditionals="12" methods="126" coveredmethods="5" elements="1038" coveredelements="122" complexity="0" loc="714" ncloc="714" packages="7" files="9" classes="9"/>
    <package name="hooks">
      <metrics statements="13" coveredstatements="1" conditionals="4" coveredconditionals="0" methods="4" coveredmethods="0"/>
      <file name="useFetchUserPostings.js" path="/Users/<USER>/Desktop/satbana/hooks/useFetchUserPostings.js">
        <metrics statements="13" coveredstatements="1" conditionals="4" coveredconditionals="0" methods="4" coveredmethods="0"/>
        <line num="7" count="1" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="25" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
      </file>
    </package>
    <package name="jest.mocks">
      <metrics statements="24" coveredstatements="12" conditionals="0" coveredconditionals="0" methods="11" coveredmethods="0"/>
      <file name="firebaseMock.ts" path="/Users/<USER>/Desktop/satbana/jest/mocks/firebaseMock.ts">
        <metrics statements="24" coveredstatements="12" conditionals="0" coveredconditionals="0" methods="11" coveredmethods="0"/>
        <line num="20" count="1" type="stmt"/>
        <line num="21" count="1" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="32" count="1" type="stmt"/>
        <line num="35" count="1" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="48" count="1" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="57" count="1" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="63" count="1" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="90" count="1" type="stmt"/>
        <line num="92" count="1" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="104" count="1" type="stmt"/>
        <line num="107" count="1" type="stmt"/>
        <line num="108" count="1" type="stmt"/>
      </file>
    </package>
    <package name="jest.mocks.firebase">
      <metrics statements="32" coveredstatements="22" conditionals="4" coveredconditionals="0" methods="11" coveredmethods="0"/>
      <file name="firestore.ts" path="/Users/<USER>/Desktop/satbana/jest/mocks/firebase/firestore.ts">
        <metrics statements="32" coveredstatements="22" conditionals="4" coveredconditionals="0" methods="11" coveredmethods="0"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="38" count="1" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="51" count="1" type="stmt"/>
        <line num="58" count="1" type="stmt"/>
        <line num="59" count="1" type="stmt"/>
        <line num="60" count="1" type="stmt"/>
        <line num="61" count="1" type="stmt"/>
        <line num="62" count="1" type="stmt"/>
        <line num="65" count="1" type="stmt"/>
        <line num="66" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="71" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="77" count="1" type="stmt"/>
        <line num="78" count="1" type="stmt"/>
        <line num="79" count="1" type="stmt"/>
        <line num="81" count="1" type="stmt"/>
        <line num="82" count="1" type="stmt"/>
        <line num="83" count="1" type="stmt"/>
        <line num="84" count="1" type="stmt"/>
        <line num="85" count="1" type="stmt"/>
        <line num="86" count="1" type="stmt"/>
        <line num="87" count="1" type="stmt"/>
        <line num="90" count="1" type="stmt"/>
        <line num="136" count="1" type="stmt"/>
      </file>
    </package>
    <package name="screens">
      <metrics statements="9" coveredstatements="9" conditionals="12" coveredconditionals="12" methods="5" coveredmethods="5"/>
      <file name="MyPostingsScreen.tsx" path="/Users/<USER>/Desktop/satbana/screens/MyPostingsScreen.tsx">
        <metrics statements="9" coveredstatements="9" conditionals="12" coveredconditionals="12" methods="5" coveredmethods="5"/>
        <line num="18" count="10" type="stmt"/>
        <line num="19" count="10" type="stmt"/>
        <line num="21" count="10" type="stmt"/>
        <line num="22" count="2" type="stmt"/>
        <line num="31" count="10" type="stmt"/>
        <line num="37" count="10" type="stmt"/>
        <line num="39" count="10" type="stmt"/>
        <line num="40" count="2" type="stmt"/>
        <line num="60" count="1" type="stmt"/>
      </file>
    </package>
    <package name="services">
      <metrics statements="605" coveredstatements="54" conditionals="175" coveredconditionals="0" methods="87" coveredmethods="0"/>
      <file name="firebaseService.js" path="/Users/<USER>/Desktop/satbana/services/firebaseService.js">
        <metrics statements="407" coveredstatements="34" conditionals="98" coveredconditionals="0" methods="54" coveredmethods="0"/>
        <line num="73" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="102" count="1" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="111" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="115" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="117" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="123" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="136" count="1" type="stmt"/>
        <line num="137" count="1" type="stmt"/>
        <line num="139" count="1" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="176" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="177" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="195" count="1" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="198" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="202" count="1" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="205" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="206" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="208" count="0" type="stmt"/>
        <line num="212" count="1" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="217" count="1" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="220" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="221" count="0" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="229" count="0" type="stmt"/>
        <line num="231" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="232" count="0" type="stmt"/>
        <line num="233" count="0" type="stmt"/>
        <line num="235" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="237" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="238" count="0" type="stmt"/>
        <line num="239" count="0" type="stmt"/>
        <line num="242" count="0" type="stmt"/>
        <line num="251" count="0" type="stmt"/>
        <line num="259" count="0" type="stmt"/>
        <line num="267" count="0" type="stmt"/>
        <line num="268" count="0" type="stmt"/>
        <line num="273" count="1" type="stmt"/>
        <line num="276" count="1" type="stmt"/>
        <line num="277" count="0" type="stmt"/>
        <line num="278" count="0" type="stmt"/>
        <line num="279" count="0" type="stmt"/>
        <line num="280" count="0" type="stmt"/>
        <line num="283" count="0" type="stmt"/>
        <line num="288" count="0" type="stmt"/>
        <line num="291" count="0" type="stmt"/>
        <line num="299" count="0" type="stmt"/>
        <line num="300" count="0" type="stmt"/>
        <line num="303" count="0" type="stmt"/>
        <line num="313" count="0" type="stmt"/>
        <line num="314" count="0" type="stmt"/>
        <line num="317" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="318" count="0" type="stmt"/>
        <line num="319" count="0" type="stmt"/>
        <line num="322" count="0" type="stmt"/>
        <line num="325" count="0" type="stmt"/>
        <line num="327" count="0" type="stmt"/>
        <line num="328" count="0" type="stmt"/>
        <line num="333" count="1" type="stmt"/>
        <line num="334" count="0" type="stmt"/>
        <line num="335" count="0" type="stmt"/>
        <line num="336" count="0" type="stmt"/>
        <line num="337" count="0" type="stmt"/>
        <line num="339" count="0" type="stmt"/>
        <line num="344" count="0" type="stmt"/>
        <line num="347" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="349" count="0" type="stmt"/>
        <line num="350" count="0" type="stmt"/>
        <line num="351" count="0" type="stmt"/>
        <line num="352" count="0" type="stmt"/>
        <line num="355" count="0" type="stmt"/>
        <line num="361" count="0" type="stmt"/>
        <line num="367" count="0" type="stmt"/>
        <line num="368" count="0" type="stmt"/>
        <line num="378" count="0" type="stmt"/>
        <line num="379" count="0" type="stmt"/>
        <line num="383" count="0" type="stmt"/>
        <line num="384" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="385" count="0" type="stmt"/>
        <line num="386" count="0" type="stmt"/>
        <line num="387" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="391" count="0" type="stmt"/>
        <line num="401" count="0" type="stmt"/>
        <line num="403" count="0" type="stmt"/>
        <line num="404" count="0" type="stmt"/>
        <line num="409" count="1" type="stmt"/>
        <line num="410" count="0" type="stmt"/>
        <line num="411" count="0" type="stmt"/>
        <line num="417" count="1" type="stmt"/>
        <line num="418" count="0" type="stmt"/>
        <line num="419" count="0" type="stmt"/>
        <line num="422" count="0" type="stmt"/>
        <line num="424" count="0" type="stmt"/>
        <line num="427" count="0" type="stmt"/>
        <line num="428" count="0" type="stmt"/>
        <line num="429" count="0" type="stmt"/>
        <line num="435" count="0" type="stmt"/>
        <line num="436" count="0" type="stmt"/>
        <line num="437" count="0" type="stmt"/>
        <line num="441" count="0" type="stmt"/>
        <line num="443" count="0" type="stmt"/>
        <line num="447" count="1" type="stmt"/>
        <line num="448" count="0" type="stmt"/>
        <line num="449" count="0" type="stmt"/>
        <line num="452" count="0" type="stmt"/>
        <line num="460" count="0" type="stmt"/>
        <line num="461" count="0" type="stmt"/>
        <line num="462" count="0" type="stmt"/>
        <line num="464" count="0" type="stmt"/>
        <line num="465" count="0" type="stmt"/>
        <line num="470" count="1" type="stmt"/>
        <line num="471" count="0" type="stmt"/>
        <line num="472" count="0" type="stmt"/>
        <line num="473" count="0" type="stmt"/>
        <line num="474" count="0" type="stmt"/>
        <line num="479" count="1" type="stmt"/>
        <line num="480" count="0" type="stmt"/>
        <line num="481" count="0" type="stmt"/>
        <line num="482" count="0" type="stmt"/>
        <line num="483" count="0" type="stmt"/>
        <line num="484" count="0" type="stmt"/>
        <line num="489" count="1" type="stmt"/>
        <line num="490" count="0" type="stmt"/>
        <line num="491" count="0" type="stmt"/>
        <line num="493" count="0" type="stmt"/>
        <line num="495" count="0" type="stmt"/>
        <line num="497" count="0" type="stmt"/>
        <line num="498" count="0" type="stmt"/>
        <line num="504" count="0" type="stmt"/>
        <line num="505" count="0" type="stmt"/>
        <line num="508" count="0" type="stmt"/>
        <line num="509" count="0" type="stmt"/>
        <line num="511" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="512" count="0" type="stmt"/>
        <line num="515" count="0" type="stmt"/>
        <line num="517" count="0" type="stmt"/>
        <line num="518" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="521" count="0" type="stmt"/>
        <line num="522" count="0" type="stmt"/>
        <line num="528" count="0" type="stmt"/>
        <line num="529" count="0" type="stmt"/>
        <line num="530" count="0" type="stmt"/>
        <line num="531" count="0" type="stmt"/>
        <line num="533" count="0" type="stmt"/>
        <line num="536" count="0" type="stmt"/>
        <line num="543" count="0" type="stmt"/>
        <line num="559" count="0" type="stmt"/>
        <line num="563" count="0" type="stmt"/>
        <line num="564" count="0" type="stmt"/>
        <line num="567" count="0" type="stmt"/>
        <line num="568" count="0" type="stmt"/>
        <line num="570" count="0" type="stmt"/>
        <line num="571" count="0" type="stmt"/>
        <line num="573" count="0" type="stmt"/>
        <line num="574" count="0" type="stmt"/>
        <line num="580" count="1" type="stmt"/>
        <line num="581" count="0" type="stmt"/>
        <line num="582" count="0" type="stmt"/>
        <line num="583" count="0" type="stmt"/>
        <line num="586" count="0" type="stmt"/>
        <line num="588" count="0" type="stmt"/>
        <line num="589" count="0" type="stmt"/>
        <line num="594" count="1" type="stmt"/>
        <line num="595" count="0" type="stmt"/>
        <line num="596" count="0" type="stmt"/>
        <line num="597" count="0" type="stmt"/>
        <line num="600" count="0" type="stmt"/>
        <line num="602" count="0" type="stmt"/>
        <line num="603" count="0" type="stmt"/>
        <line num="608" count="1" type="stmt"/>
        <line num="609" count="0" type="stmt"/>
        <line num="610" count="0" type="stmt"/>
        <line num="611" count="0" type="stmt"/>
        <line num="612" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="613" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="615" count="0" type="stmt"/>
        <line num="617" count="0" type="stmt"/>
        <line num="618" count="0" type="stmt"/>
        <line num="623" count="1" type="stmt"/>
        <line num="624" count="0" type="stmt"/>
        <line num="625" count="0" type="stmt"/>
        <line num="628" count="0" type="stmt"/>
        <line num="629" count="0" type="stmt"/>
        <line num="632" count="0" type="stmt"/>
        <line num="638" count="0" type="stmt"/>
        <line num="639" count="0" type="stmt"/>
        <line num="640" count="0" type="stmt"/>
        <line num="643" count="0" type="stmt"/>
        <line num="644" count="0" type="stmt"/>
        <line num="645" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="646" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="647" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="649" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="650" count="0" type="stmt"/>
        <line num="659" count="0" type="stmt"/>
        <line num="663" count="0" type="stmt"/>
        <line num="664" count="0" type="stmt"/>
        <line num="666" count="0" type="stmt"/>
        <line num="667" count="0" type="stmt"/>
        <line num="672" count="1" type="stmt"/>
        <line num="673" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="674" count="0" type="stmt"/>
        <line num="677" count="0" type="stmt"/>
        <line num="678" count="0" type="stmt"/>
        <line num="682" count="1" type="stmt"/>
        <line num="683" count="0" type="stmt"/>
        <line num="684" count="0" type="stmt"/>
        <line num="685" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="686" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="692" count="1" type="stmt"/>
        <line num="693" count="0" type="stmt"/>
        <line num="694" count="0" type="stmt"/>
        <line num="695" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="696" count="0" type="stmt"/>
        <line num="699" count="0" type="stmt"/>
        <line num="700" count="0" type="stmt"/>
        <line num="706" count="0" type="stmt"/>
        <line num="708" count="0" type="stmt"/>
        <line num="709" count="0" type="stmt"/>
        <line num="715" count="1" type="stmt"/>
        <line num="716" count="0" type="stmt"/>
        <line num="717" count="0" type="stmt"/>
        <line num="718" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="719" count="0" type="stmt"/>
        <line num="722" count="0" type="stmt"/>
        <line num="723" count="0" type="stmt"/>
        <line num="730" count="0" type="stmt"/>
        <line num="731" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="732" count="0" type="stmt"/>
        <line num="733" count="0" type="stmt"/>
        <line num="738" count="0" type="stmt"/>
        <line num="741" count="0" type="stmt"/>
        <line num="742" count="0" type="stmt"/>
        <line num="750" count="1" type="stmt"/>
        <line num="757" count="0" type="stmt"/>
        <line num="759" count="0" type="stmt"/>
        <line num="768" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="770" count="0" type="stmt"/>
        <line num="771" count="0" type="stmt"/>
        <line num="777" count="1" type="stmt"/>
        <line num="784" count="0" type="stmt"/>
        <line num="785" count="0" type="stmt"/>
        <line num="788" count="0" type="stmt"/>
        <line num="799" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="800" count="0" type="stmt"/>
        <line num="801" count="0" type="stmt"/>
        <line num="806" count="0" type="stmt"/>
        <line num="812" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="813" count="0" type="stmt"/>
        <line num="816" count="0" type="stmt"/>
        <line num="817" count="0" type="stmt"/>
        <line num="819" count="0" type="stmt"/>
        <line num="828" count="0" type="stmt"/>
        <line num="834" count="0" type="stmt"/>
        <line num="835" count="0" type="stmt"/>
        <line num="840" count="1" type="stmt"/>
        <line num="841" count="0" type="stmt"/>
        <line num="843" count="0" type="stmt"/>
        <line num="844" count="0" type="stmt"/>
        <line num="846" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="847" count="0" type="stmt"/>
        <line num="850" count="0" type="stmt"/>
        <line num="853" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="854" count="0" type="stmt"/>
        <line num="855" count="0" type="stmt"/>
        <line num="858" count="0" type="stmt"/>
        <line num="859" count="0" type="stmt"/>
        <line num="861" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="862" count="0" type="stmt"/>
        <line num="863" count="0" type="stmt"/>
        <line num="864" count="0" type="stmt"/>
        <line num="866" count="0" type="stmt"/>
        <line num="867" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="868" count="0" type="stmt"/>
        <line num="870" count="0" type="stmt"/>
        <line num="873" count="0" type="stmt"/>
        <line num="876" count="0" type="stmt"/>
        <line num="877" count="0" type="stmt"/>
        <line num="882" count="1" type="stmt"/>
        <line num="883" count="0" type="stmt"/>
        <line num="884" count="0" type="stmt"/>
        <line num="885" count="0" type="stmt"/>
        <line num="888" count="0" type="stmt"/>
        <line num="889" count="0" type="stmt"/>
        <line num="890" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="893" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="894" count="0" type="stmt"/>
        <line num="895" count="0" type="stmt"/>
        <line num="898" count="0" type="stmt"/>
        <line num="905" count="0" type="stmt"/>
        <line num="906" count="0" type="stmt"/>
        <line num="908" count="0" type="stmt"/>
        <line num="909" count="0" type="stmt"/>
        <line num="914" count="1" type="stmt"/>
        <line num="915" count="0" type="stmt"/>
        <line num="916" count="0" type="stmt"/>
        <line num="917" count="0" type="stmt"/>
        <line num="919" count="0" type="stmt"/>
        <line num="920" count="0" type="stmt"/>
        <line num="921" count="0" type="stmt"/>
        <line num="926" count="0" type="stmt"/>
        <line num="927" count="0" type="stmt"/>
        <line num="929" count="0" type="stmt"/>
        <line num="933" count="0" type="stmt"/>
        <line num="938" count="1" type="stmt"/>
        <line num="939" count="0" type="stmt"/>
        <line num="940" count="0" type="stmt"/>
        <line num="943" count="0" type="stmt"/>
        <line num="944" count="0" type="stmt"/>
        <line num="945" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="946" count="0" type="stmt"/>
        <line num="948" count="0" type="stmt"/>
        <line num="953" count="0" type="stmt"/>
        <line num="954" count="0" type="stmt"/>
        <line num="956" count="0" type="stmt"/>
        <line num="957" count="0" type="stmt"/>
        <line num="958" count="0" type="stmt"/>
        <line num="962" count="0" type="stmt"/>
        <line num="963" count="0" type="stmt"/>
        <line num="964" count="0" type="stmt"/>
        <line num="966" count="0" type="stmt"/>
        <line num="967" count="0" type="stmt"/>
        <line num="968" count="0" type="stmt"/>
        <line num="969" count="0" type="stmt"/>
        <line num="973" count="0" type="stmt"/>
        <line num="977" count="1" type="stmt"/>
        <line num="978" count="0" type="stmt"/>
        <line num="979" count="0" type="stmt"/>
        <line num="980" count="0" type="stmt"/>
        <line num="982" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="983" count="0" type="stmt"/>
        <line num="986" count="0" type="stmt"/>
        <line num="987" count="0" type="stmt"/>
        <line num="989" count="0" type="stmt"/>
        <line num="990" count="0" type="stmt"/>
        <line num="995" count="1" type="stmt"/>
        <line num="996" count="0" type="stmt"/>
        <line num="998" count="0" type="stmt"/>
        <line num="999" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1000" count="0" type="stmt"/>
        <line num="1003" count="0" type="stmt"/>
        <line num="1004" count="0" type="stmt"/>
        <line num="1007" count="0" type="stmt"/>
        <line num="1010" count="0" type="stmt"/>
        <line num="1011" count="0" type="stmt"/>
        <line num="1012" count="0" type="stmt"/>
        <line num="1015" count="0" type="stmt"/>
        <line num="1021" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1022" count="0" type="stmt"/>
        <line num="1024" count="0" type="stmt"/>
        <line num="1046" count="0" type="stmt"/>
        <line num="1049" count="0" type="stmt"/>
        <line num="1050" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="1051" count="0" type="stmt"/>
        <line num="1061" count="0" type="stmt"/>
        <line num="1072" count="0" type="stmt"/>
        <line num="1081" count="0" type="stmt"/>
        <line num="1087" count="0" type="stmt"/>
      </file>
      <file name="notificationService.ts" path="/Users/<USER>/Desktop/satbana/services/notificationService.ts">
        <metrics statements="198" coveredstatements="20" conditionals="77" coveredconditionals="0" methods="33" coveredmethods="0"/>
        <line num="11" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="27" count="1" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="40" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="49" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="50" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="64" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="68" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="82" count="1" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="87" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="92" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="101" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="108" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="109" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="126" count="1" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="131" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="161" count="1" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="182" count="1" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="184" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="186" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="188" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="196" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="203" count="1" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="205" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="207" count="0" type="stmt"/>
        <line num="208" count="0" type="stmt"/>
        <line num="209" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="214" count="0" type="stmt"/>
        <line num="218" count="1" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
        <line num="233" count="1" type="stmt"/>
        <line num="234" count="0" type="stmt"/>
        <line num="235" count="0" type="stmt"/>
        <line num="236" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="237" count="0" type="stmt"/>
        <line num="242" count="1" type="stmt"/>
        <line num="246" count="0" type="stmt"/>
        <line num="247" count="0" type="stmt"/>
        <line num="252" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="259" count="0" type="stmt"/>
        <line num="261" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="262" count="0" type="stmt"/>
        <line num="267" count="0" type="stmt"/>
        <line num="270" count="0" type="stmt"/>
        <line num="276" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="280" count="0" type="stmt"/>
        <line num="287" count="0" type="stmt"/>
        <line num="290" count="0" type="stmt"/>
        <line num="293" count="0" type="stmt"/>
        <line num="300" count="1" type="stmt"/>
        <line num="301" count="0" type="stmt"/>
        <line num="303" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="304" count="0" type="stmt"/>
        <line num="305" count="0" type="stmt"/>
        <line num="308" count="0" type="stmt"/>
        <line num="309" count="0" type="stmt"/>
        <line num="310" count="0" type="stmt"/>
        <line num="311" count="0" type="stmt"/>
        <line num="314" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="317" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="318" count="0" type="stmt"/>
        <line num="319" count="0" type="stmt"/>
        <line num="322" count="0" type="stmt"/>
        <line num="323" count="0" type="stmt"/>
        <line num="324" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="326" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="327" count="0" type="stmt"/>
        <line num="328" count="0" type="stmt"/>
        <line num="329" count="0" type="stmt"/>
        <line num="333" count="0" type="stmt"/>
        <line num="334" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="335" count="0" type="stmt"/>
        <line num="336" count="0" type="stmt"/>
        <line num="341" count="0" type="stmt"/>
        <line num="343" count="0" type="stmt"/>
        <line num="346" count="0" type="stmt"/>
        <line num="347" count="0" type="stmt"/>
        <line num="350" count="0" type="stmt"/>
        <line num="354" count="0" type="stmt"/>
        <line num="358" count="1" type="stmt"/>
        <line num="364" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="366" count="0" type="stmt"/>
        <line num="367" count="0" type="stmt"/>
        <line num="370" count="0" type="stmt"/>
        <line num="384" count="0" type="stmt"/>
        <line num="387" count="0" type="stmt"/>
        <line num="388" count="0" type="stmt"/>
        <line num="390" count="0" type="stmt"/>
        <line num="391" count="0" type="stmt"/>
        <line num="399" count="1" type="stmt"/>
        <line num="400" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="402" count="0" type="stmt"/>
        <line num="403" count="0" type="stmt"/>
        <line num="405" count="0" type="stmt"/>
        <line num="416" count="0" type="stmt"/>
        <line num="418" count="0" type="stmt"/>
        <line num="422" count="1" type="stmt"/>
        <line num="423" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="425" count="0" type="stmt"/>
        <line num="426" count="0" type="stmt"/>
        <line num="432" count="0" type="stmt"/>
        <line num="433" count="0" type="stmt"/>
        <line num="435" count="0" type="stmt"/>
        <line num="436" count="0" type="stmt"/>
        <line num="440" count="1" type="stmt"/>
        <line num="441" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="444" count="0" type="stmt"/>
        <line num="445" count="0" type="stmt"/>
        <line num="450" count="0" type="stmt"/>
        <line num="451" count="0" type="stmt"/>
        <line num="456" count="0" type="stmt"/>
        <line num="459" count="1" type="stmt"/>
        <line num="460" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="461" count="0" type="stmt"/>
        <line num="465" count="1" type="stmt"/>
        <line num="466" count="0" type="stmt"/>
        <line num="467" count="0" type="stmt"/>
        <line num="474" count="0" type="stmt"/>
        <line num="476" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="477" count="0" type="stmt"/>
        <line num="481" count="0" type="stmt"/>
        <line num="484" count="0" type="stmt"/>
        <line num="486" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="487" count="0" type="stmt"/>
        <line num="491" count="0" type="stmt"/>
        <line num="494" count="0" type="stmt"/>
        <line num="501" count="0" type="stmt"/>
        <line num="508" count="0" type="stmt"/>
        <line num="516" count="0" type="stmt"/>
        <line num="518" count="0" type="stmt"/>
        <line num="526" count="0" type="stmt"/>
        <line num="534" count="0" type="stmt"/>
      </file>
    </package>
    <package name="types">
      <metrics statements="1" coveredstatements="1" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="notifications.ts" path="/Users/<USER>/Desktop/satbana/types/notifications.ts">
        <metrics statements="1" coveredstatements="1" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="23" count="1" type="stmt"/>
      </file>
    </package>
    <package name="utils">
      <metrics statements="30" coveredstatements="6" conditionals="3" coveredconditionals="0" methods="8" coveredmethods="0"/>
      <file name="cacheManager.ts" path="/Users/<USER>/Desktop/satbana/utils/cacheManager.ts">
        <metrics statements="5" coveredstatements="1" conditionals="2" coveredconditionals="0" methods="4" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="5" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="9" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
      </file>
      <file name="firebaseCleanup.ts" path="/Users/<USER>/Desktop/satbana/utils/firebaseCleanup.ts">
        <metrics statements="25" coveredstatements="5" conditionals="1" coveredconditionals="0" methods="4" coveredmethods="0"/>
        <line num="7" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="10" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="24" count="1" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="43" count="1" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
      </file>
    </package>
  </project>
</coverage>
