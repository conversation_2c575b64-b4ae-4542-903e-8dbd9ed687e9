const functions = require('firebase-functions');
const admin = require('firebase-admin');
admin.initializeApp();

exports.sendNotification = functions.firestore
  .document('notifications/{notificationId}')
  .onCreate(async (snap, context) => {
    try {
      const notification = snap.data();
      console.log('Processing notification:', notification);

      // Get recipient's FCM token
      const userDoc = await admin.firestore()
        .collection('users')
        .doc(notification.recipientId)
        .get();
      
      const fcmToken = userDoc.data()?.fcmToken;
      
      if (!fcmToken) {
        console.log('No FCM token found for user:', notification.recipientId);
        return null;
      }

      // Log the token for debugging
      console.log('Sending to FCM token:', fcmToken);

      const message = {
        token: fcmToken,
        notification: {
          title: notification.title,
          body: notification.body,
        },
        data: {
          type: notification.type,
          postingId: notification.postingId || '',
          offerId: notification.offerId || '',
          postingOwnerId: notification.recipientId || '',
          offerOwnerId: notification.senderId || '',
        },
        apns: {
          headers: {
            'apns-priority': '10',
            'apns-push-type': 'alert'
          },
          payload: {
            aps: {
              alert: {
                title: notification.title,
                body: notification.body,
              },
              sound: 'default',
              badge: 1,
              'content-available': 1,
              'mutable-content': 1,
              category: notification.type,
            },
            data: {
              type: notification.type,
              postingId: notification.postingId || '',
              offerId: notification.offerId || '',
              postingOwnerId: notification.recipientId || '',
              offerOwnerId: notification.senderId || '',
            },
            notificationId: context.params.notificationId,
          },
        },
      };

      console.log('Sending message:', JSON.stringify(message, null, 2));

      const response = await admin.messaging().send(message);
      console.log('Successfully sent message:', response);

      // Update the notification document with delivery status
      await snap.ref.update({
        delivered: true,
        deliveredAt: admin.firestore.FieldValue.serverTimestamp(),
        fcmMessageId: response
      });

      return response;
    } catch (error) {
      console.error('Error sending notification:', error);
      console.error('Error details:', {
        code: error.code,
        message: error.message,
        stack: error.stack,
        errorInfo: error.errorInfo
      });
      
      // Update the notification document with error status
      await snap.ref.update({
        error: {
          code: error.code,
          message: error.message,
          timestamp: admin.firestore.FieldValue.serverTimestamp()
        }
      });
      
      return null;
    }
  });