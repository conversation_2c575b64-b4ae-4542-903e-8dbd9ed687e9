const { withDangerousMod, withPlugins } = require('@expo/config-plugins');
const fs = require('fs');
const path = require('path');

async function readFile(filePath) {
    return fs.promises.readFile(filePath, 'utf8');
}

async function saveFile(filePath, content) {
    return fs.promises.writeFile(filePath, content, 'utf8');
}

module.exports = (config) => withPlugins(config, [
    (config) => {
        return withDangerousMod(config, [
            'ios',
            async (config) => {
                const filePath = path.join(config.modRequest.platformProjectRoot, 'Podfile');
                let contents = await readFile(filePath);

                // Add required pods
                const podLines = [
                    "pod 'RNGestureHandler', :path => '../node_modules/react-native-gesture-handler'",
                    "pod 'RNCPushNotificationIOS', :path => '../node_modules/@react-native-community/push-notification-ios'"
                ];

                // Remove existing pod lines if they exist
                podLines.forEach(podLine => {
                    contents = contents.replace(new RegExp(podLine, 'g'), '');
                });

                // Find insertion point
                const insertPosition = contents.indexOf('use_react_native!(');
                if (insertPosition === -1) {
                    throw new Error("Couldn't find the position to insert the pod lines.");
                }

                // Insert pod lines
                contents = contents.slice(0, insertPosition) + 
                          podLines.join('\n') + '\n\n' + 
                          contents.slice(insertPosition);

                await saveFile(filePath, contents);
                return config;
            },
        ]);
    },
]); 