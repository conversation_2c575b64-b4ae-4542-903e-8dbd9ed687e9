# SpecStory Artifacts Directory

This directory is automatically created and maintained by the SpecStory extension to preserve your Cursor composer and chat history.

## What's Here?

- `.specstory/history`: Contains markdown files of your AI coding sessions
- Each file represents a separate chat or composer session
- Files are automatically updated as you work

## Version Control

We recommend keeping this directory under version control to maintain a history of your AI interactions. However, if you prefer not to version these files, you can exclude them by adding this to your `.gitignore`:

```
.specstory/**
```

## Searching Your Codebase

When searching your codebase in Cursor, search results may include your previous AI coding interactions. To focus solely on your actual code files, you can exclude the AI interaction history from search results.

To exclude AI interaction history:

1. Open the "Find in Files" search in Cursor (Cmd/Ctrl + Shift + F)
2. Navigate to the "files to exclude" section
3. Add the following pattern:

```
.specstory/*
```

This will ensure your searches only return results from your working codebase files.

## Settings

You can control auto-saving behavior in Cursor:

1. Open Cursor → Settings → VS Code Settings (Cmd/Ctrl + ,)
2. Search for "SpecStory"
3. Find "Auto Save" setting to enable/disable

Auto-save when changes are detected in Cursor's sqlite database, or every 5 minutes as a safety net.