import { Timestamp } from 'firebase/firestore';
import { logError, createErrorContext } from './errorLogger';

/**
 * Type guard to check if a value is a Firestore Timestamp
 */
export function isFirestoreTimestamp(value: unknown): value is Timestamp {
  return value instanceof Timestamp;
}

/**
 * Validates and normalizes a timestamp value to ensure it's a proper Firestore Timestamp
 * @param value - The value to validate/normalize
 * @param context - Additional context for error logging
 * @returns Firestore Timestamp or null if invalid
 */
export function validateTimestamp(value: unknown, fieldName: string = 'timestamp'): Timestamp | null {
  try {
    if (!value) return null;

    // Already a Firestore Timestamp
    if (isFirestoreTimestamp(value)) {
      return value;
    }

    // Handle seconds/nanoseconds object format
    if (typeof value === 'object' && value !== null) {
      const obj = value as Record<string, unknown>;
      if ('seconds' in obj && typeof obj.seconds === 'number') {
        return new Timestamp(
          obj.seconds,
          (obj.nanoseconds as number) || 0
        );
      }
    }

    // Handle ISO string format
    if (typeof value === 'string') {
      const date = new Date(value);
      if (!isNaN(date.getTime())) {
        return Timestamp.fromDate(date);
      }
    }

    // Handle Unix timestamp (milliseconds)
    if (typeof value === 'number') {
      return Timestamp.fromMillis(value);
    }

    logError(new Error(`Invalid timestamp format`), createErrorContext(
      'TimestampValidation',
      'validateTimestamp',
      {
        severity: 'medium',
        data: {
          fieldName,
          value: JSON.stringify(value),
          valueType: typeof value
        }
      }
    ));
    return null;

  } catch (error) {
    logError(error as Error, createErrorContext(
      'TimestampValidation',
      'validateTimestamp',
      {
        severity: 'medium',
        data: {
          fieldName,
          value: JSON.stringify(value)
        }
      }
    ));
    return null;
  }
}

/**
 * Creates a new Firestore Timestamp for the current time
 */
export function getCurrentTimestamp(): Timestamp {
  return Timestamp.now();
}

/**
 * Type guard to ensure an object has valid timestamp fields
 * @param obj - Object to validate
 * @param fields - Array of field names to check
 * @returns True if all specified fields are valid timestamps
 */
export function hasValidTimestamps<T extends Record<string, unknown>>(
  obj: T,
  fields: (keyof T)[]
): boolean {
  return fields.every(field => {
    const value = obj[field];
    return value === undefined || value === null || isFirestoreTimestamp(value);
  });
} 