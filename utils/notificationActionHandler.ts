import { FirebaseMessagingTypes } from '@react-native-firebase/messaging';
import { NavigationContainerRef } from '@react-navigation/native';
import { NotificationType } from '../types/notifications';

export class NotificationActionHandler {
  private navigation: NavigationContainerRef<any>;

  constructor(navigation: NavigationContainerRef<any>) {
    this.navigation = navigation;
  }

  handleNotificationOpen = (message: FirebaseMessagingTypes.RemoteMessage) => {
    const type = message.data?.type as NotificationType;
    const notificationType = message.data?._notificationType as NotificationType;

    console.log('[NotificationActionHandler] Handling notification:', {
      type,
      notificationType,
      data: message.data,
      timestamp: new Date().toISOString()
    });

    // Use either type from data or the explicit _notificationType we added
    const effectiveType = type || notificationType;

    switch (effectiveType) {
      case NotificationType.NEW_OFFER:
        this.navigateToOffer(message.data);
        break;
      case NotificationType.NEW_MESSAGE:
        this.navigateToDiscussion(message.data);
        break;
      case NotificationType.FAVORITE_POSTING_UPDATE:
        this.navigateToPosting(message.data);
        break;
      case NotificationType.FAVORITE_NEW_MESSAGE:
        this.navigateToDiscussion(message.data);
        break;
      case NotificationType.OFFER_STATUS_CHANGE:
        this.navigateToOffer(message.data);
        break;
      case NotificationType.NEW_LOWER_OFFER:
        this.navigateToPosting(message.data);
        break;
      case NotificationType.SYSTEM_WITHDRAWAL:
        console.log('[NotificationActionHandler] Handling SYSTEM_WITHDRAWAL notification', {
          offerId: message.data?.offerId,
          postingId: message.data?.postingId,
          timestamp: new Date().toISOString()
        });
        this.navigateToOffer(message.data);
        break;
      default:
        console.warn('[NotificationActionHandler] Unhandled notification type:', {
          type: effectiveType,
          data: message.data,
          timestamp: new Date().toISOString()
        });
    }
  };

  private navigateToOffer(data: any) {
    this.navigation.navigate('OfferDetail', {
      offerId: data.offerId,
      postingId: data.postingId,
      postingOwnerId: data.postingOwnerId,
      offerOwnerId: data.offerOwnerId,
    });
  }

  private navigateToDiscussion(data: any) {
    this.navigation.navigate('OfferDetail', {
      offerId: data.offerId,
      postingId: data.postingId,
      postingOwnerId: data.postingOwnerId,
      offerOwnerId: data.offerOwnerId,
    });
  }

  private navigateToPosting(data: any) {
    this.navigation.navigate('PostingDetail', {
      postingId: data.postingId,
      itemLocation: data.itemLocation,
      userId: data.userId,
    });
  }
}