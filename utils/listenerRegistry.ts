interface Listener {
  cleanup: () => void;
  createdAt: string;
  lastActive: string;
}

export const listenerRegistry = {
  activeListeners: new Map<string, Listener>(),
  addListener: function(id: string, cleanupFn: () => void) {
    this.activeListeners.set(id, {
      cleanup: cleanupFn,
      createdAt: new Date().toISOString(),
      lastActive: new Date().toISOString()
    });
  },
  removeListener: function(id: string) {
    if (this.activeListeners.has(id)) {
      this.activeListeners.delete(id);
    }
  },
  cleanupAll: function() {
    this.activeListeners.forEach((listener) => {
      try {
        listener.cleanup();
      } catch (error) {
        console.error('Error cleaning up listener:', error);
      }
    });
    this.activeListeners.clear();
  }
}; 