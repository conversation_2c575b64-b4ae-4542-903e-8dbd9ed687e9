import { NotificationType } from '../types/notifications';

export const NotificationCategories = {
  [NotificationType.NEW_OFFER]: {
    id: 'new_offer',
    actions: [
      {
        id: 'view_offer',
        title: 'View Offer',
        foreground: true,
      },
      {
        id: 'dismiss',
        title: 'Dismiss',
        destructive: true,
      },
    ],
  },
  [NotificationType.NEW_MESSAGE]: {
    id: 'new_message',
    actions: [
      {
        id: 'reply',
        title: 'Reply',
        textInput: {
          buttonTitle: 'Send',
          placeholder: 'Type your reply...',
        },
      },
      {
        id: 'view',
        title: 'View',
        foreground: true,
      },
    ],
  },
  // ... define other categories ...
}; 