// Define error types
export type AppError = {
  message: string;
  stack?: string;
  code?: string;
  name?: string;
  cause?: unknown;
};

// Define context types
export type ErrorSeverity = 'low' | 'medium' | 'high' | 'critical';

export type ErrorContext = {
  component: string;
  action: string;
  severity?: ErrorSeverity;
  data?: Record<string, unknown>;
  userId?: string;
  timestamp?: string;
};

export type ErrorDetails = {
  timestamp: string;
  component: string;
  action: string;
  severity: ErrorSeverity;
  data?: Record<string, unknown>;
  error: {
    message: string;
    stack?: string;
    code?: string;
    name?: string;
  };
  userId?: string;
};

export const logError = (error: unknown, context: ErrorContext): void => {
  // Ensure error is properly typed
  const appError: AppError = error instanceof Error 
    ? {
        message: error.message,
        stack: error.stack,
        name: error.name,
        // Handle Firebase/API errors that might have a code
        code: (error as any).code,
      }
    : {
        message: String(error),
      };

  const errorDetails: ErrorDetails = {
    timestamp: context.timestamp || new Date().toISOString(),
    component: context.component,
    action: context.action,
    severity: context.severity || 'medium',
    data: context.data,
    error: appError,
    userId: context.userId,
  };

  // Log to console in development
  if (__DEV__) {
    console.error('Error Details:', errorDetails);
  }

  // TODO: Add remote error logging service integration here
  // e.g., Firebase Crashlytics, Sentry, etc.
};

// Helper function to create error context
export const createErrorContext = (
  component: string,
  action: string,
  options?: Partial<Omit<ErrorContext, 'component' | 'action'>>
): ErrorContext => ({
  component,
  action,
  severity: options?.severity || 'medium',
  data: options?.data,
  userId: options?.userId,
  timestamp: options?.timestamp,
}); 