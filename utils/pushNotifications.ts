import { fcmHelper } from './fcmHelper';
import { doc, updateDoc } from 'firebase/firestore';
import { auth, db } from '../firebase';

export const registerForPushNotificationsAsync = async () => {
  try {
    const token = await fcmHelper.getFCMToken();
    
    if (token && auth.currentUser) {
      const userRef = doc(db, 'users', auth.currentUser.uid);
      await updateDoc(userRef, {
        fcmToken: token
      });
      return token;
    }
    
    return null;
  } catch (error) {
    console.error('Error registering for push notifications:', error);
    return null;
  }
}; 