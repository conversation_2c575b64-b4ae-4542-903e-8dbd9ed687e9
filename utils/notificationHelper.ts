import { NotificationType } from '../types/notifications';
import { fcmService } from '../services/fcmService';

interface NotificationData {
  type: NotificationType;
  postingId?: string;
  offerId?: string;
  messageId?: string;
  title: string;
  body: string;
}

export const formatNotification = (data: NotificationData) => {
  return {
    title: data.title,
    body: data.body,
    data: {
      type: data.type,
      postingId: data.postingId,
      offerId: data.offerId,
      messageId: data.messageId,
    },
    ios: {
      categoryId: data.type,
      foregroundPresentationOptions: {
        badge: true,
        sound: true,
        banner: true,
        list: true,
      },
    },
  };
};

export const notificationHelper = {
  async setupNotifications() {
    try {
      const hasPermission = await fcmService.requestUserPermission();
      if (hasPermission) {
        const token = await fcmService.getFCMToken();
        return token;
      }
      return null;
    } catch (error) {
      console.error('Error setting up notifications:', error);
      return null;
    }
  },

  async subscribeToOffers(userId: string) {
    await fcmService.subscribeToTopic(`offers_${userId}`);
  },

  async subscribeToMessages(userId: string) {
    await fcmService.subscribeToTopic(`messages_${userId}`);
  }
}; 