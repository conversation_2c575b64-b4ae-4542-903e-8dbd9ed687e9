interface Subscription {
  id: string;
  unsubscribe: () => void;
  type: string;
}

let subscriptions: Subscription[] = [];
let subscriptionCounter = 0;

export const addSubscription = (unsubscribe: () => void, type: string = 'unknown') => {
  const id = `sub_${++subscriptionCounter}`;
  console.log(`Adding new ${type} subscription (${id}), current count: ${subscriptions.length}`);
  
  subscriptions.push({
    id,
    unsubscribe,
    type
  });
  
  console.log(`Subscription added, new count: ${subscriptions.length}`);
  return id;
};

export const unsubscribeAll = () => {
  console.log(`=== Starting Cleanup of ${subscriptions.length} Subscriptions ===`);
  
  subscriptions.forEach((sub, index) => {
    try {
      console.log(`Cleaning up ${sub.type} subscription ${index + 1}/${subscriptions.length} (${sub.id})`);
      sub.unsubscribe();
      console.log(`Successfully cleaned up subscription ${sub.id}`);
    } catch (error) {
      console.warn(`Error cleaning up subscription ${sub.id}:`, error);
    }
  });
  
  const oldCount = subscriptions.length;
  subscriptions = [];
  subscriptionCounter = 0;
  console.log(`=== Cleanup Complete: Cleared ${oldCount} Subscriptions ===`);
};

export const clearSubscriptions = () => {
  console.log(`Clearing ${subscriptions.length} subscriptions without calling unsubscribe`);
  subscriptions = [];
  subscriptionCounter = 0;
  console.log('Subscriptions array cleared');
}; 