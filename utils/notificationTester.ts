import { messaging } from '../firebase';
import { Platform } from 'react-native';

export const testNotification = async () => {
  console.log('=== Testing Notification Setup ===');
  
  try {
    if (Platform.OS === 'ios') {
      const authStatus = await messaging().hasPermission();
      console.log('Current permission status:', authStatus);

      const token = await messaging().getToken();
      console.log('FCM Token available:', !!token);

      // Test foreground handler
      messaging().onMessage(async remoteMessage => {
        console.log('Received foreground message:', remoteMessage);
      });

      console.log('Notification test complete - all systems operational');
      return true;
    }
    return false;
  } catch (error) {
    console.error('Notification test failed:', error);
    return false;
  }
}; 