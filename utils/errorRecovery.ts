export class RetryError extends Error {
  constructor(message: string, public readonly attempts: number) {
    super(message);
    this.name = '<PERSON>tryError';
  }
}

export async function withRetry<T>(
  operation: () => Promise<T>,
  maxAttempts: number = 3,
  delayMs: number = 1000
): Promise<T> {
  let lastError: Error;
  
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error as Error;
      console.warn(`Attempt ${attempt} failed:`, error);
      
      if (attempt < maxAttempts) {
        await new Promise(resolve => setTimeout(resolve, delayMs * attempt));
      }
    }
  }
  
  throw new RetryError(`Failed after ${maxAttempts} attempts`, maxAttempts);
}

export function withErrorBoundary<T>(
  operation: () => Promise<T>,
  fallback: T
): Promise<T> {
  return operation().catch(error => {
    console.error('Operation failed, using fallback:', error);
    return fallback;
  });
} 