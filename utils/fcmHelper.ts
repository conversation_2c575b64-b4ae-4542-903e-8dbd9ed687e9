import messaging from '@react-native-firebase/messaging';
import { Platform } from 'react-native';
import { FirebaseMessagingTypes } from '@react-native-firebase/messaging';

// Initialize messaging as a function to ensure it's called when needed
const getMessaging = () => {
  try {
    return messaging();
  } catch (error) {
    console.error('Error initializing messaging:', error);
    return null;
  }
};

export async function requestUserPermission() {
  if (Platform.OS === 'ios') {
    const messagingInstance = getMessaging();
    if (!messagingInstance) return;

    const authStatus = await messagingInstance.requestPermission();
    const enabled =
      authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
      authStatus === messaging.AuthorizationStatus.PROVISIONAL;

    if (enabled) {
      console.log('Authorization status:', authStatus);
    }
  }
}

export async function getFCMToken() {
  try {
    const messagingInstance = getMessaging();
    if (!messagingInstance) return null;

    const fcmToken = await messagingInstance.getToken();
    if (fcmToken) {
      console.log('FCM Token:', fcmToken);
      return fcmToken;
    }
  } catch (error) {
    console.error('Failed to get FCM token:', error);
    return null;
  }
}

export const onMessageReceived = (callback: (message: FirebaseMessagingTypes.RemoteMessage) => void) => {
  const messagingInstance = getMessaging();
  if (!messagingInstance) return () => {};

  return messagingInstance.onMessage(callback);
};

export const onBackgroundMessage = () => {
  const messagingInstance = getMessaging();
  if (!messagingInstance) return;

  return messagingInstance.setBackgroundMessageHandler(async remoteMessage => {
    console.log('Message handled in the background!', remoteMessage);
  });
};