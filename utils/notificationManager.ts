import messaging, { FirebaseMessagingTypes } from '@react-native-firebase/messaging';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';
import { auth } from '../firebase';

const FCM_TOKEN_KEY = '@fcm_token';

class NotificationManager {
  async init() {
    // Only initialize notifications if user is logged in
    if (!auth.currentUser) {
      console.log('No user logged in, skipping notification setup');
      return true;
    }

    try {
      if (Platform.OS === 'ios') {
        const authStatus = await messaging().requestPermission();
        const enabled =
          authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
          authStatus === messaging.AuthorizationStatus.PROVISIONAL;

        if (!enabled) {
          console.log('User notification permissions denied');
          return true; // Still return true to not block the app
        }
      }

      const token = await this.getFCMToken();
      console.log('FCM Token:', token);
      
      return true;
    } catch (error) {
      console.error('Error initializing notifications:', error);
      return true;
    }
  }

  async getFCMToken() {
    try {
      const savedToken = await AsyncStorage.getItem(FCM_TOKEN_KEY);
      
      // Get new token from Firebase
      const token = await messaging().getToken();
      
      // If token has changed, save the new one
      if (token !== savedToken) {
        await AsyncStorage.setItem(FCM_TOKEN_KEY, token);
      }
      
      return token;
    } catch (error) {
      console.error('Error getting FCM token:', error);
      return null;
    }
  }

  onMessage(callback: (message: FirebaseMessagingTypes.RemoteMessage) => void) {
    try {
      return messaging().onMessage(callback);
    } catch (error) {
      console.error('Error setting up onMessage listener:', error);
      // Return a no-op unsubscribe function
      return () => {};
    }
  }

  onBackgroundMessage(callback: (message: FirebaseMessagingTypes.RemoteMessage) => Promise<void>) {
    try {
      return messaging().setBackgroundMessageHandler(callback);
    } catch (error) {
      console.error('Error setting up background message handler:', error);
      // Return a no-op unsubscribe function
      return () => {};
    }
  }

  onNotificationOpenedApp(callback: (message: FirebaseMessagingTypes.RemoteMessage) => void) {
    try {
      return messaging().onNotificationOpenedApp(callback);
    } catch (error) {
      console.error('Error setting up onNotificationOpenedApp listener:', error);
      // Return a no-op unsubscribe function
      return () => {};
    }
  }

  async getInitialNotification(): Promise<FirebaseMessagingTypes.RemoteMessage | null> {
    try {
      return await messaging().getInitialNotification();
    } catch (error) {
      console.error('Error getting initial notification:', error);
      return null;
    }
  }
}

export default new NotificationManager(); 