import { logError, createErrorContext, ErrorContext } from './errorLogger';

interface RetryConfig {
  maxAttempts: number;
  delayMs: number;
  backoffFactor: number;
}

export const DEFAULT_RETRY_CONFIG: RetryConfig = {
  maxAttempts: 3,
  delayMs: 1000,
  backoffFactor: 2,
};

export async function withRetry<T>(
  operation: () => Promise<T>,
  config: RetryConfig = DEFAULT_RETRY_CONFIG,
  context: Omit<ErrorContext, 'severity'>
): Promise<T> {
  let lastError: unknown;
  let delay = config.delayMs;

  for (let attempt = 1; attempt <= config.maxAttempts; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;
      
      // Log with severity based on attempt number
      const severity = attempt === config.maxAttempts ? 'high' : 'medium';
      
      logError(error, createErrorContext(
        context.component,
        context.action,
        {
          severity,
          data: {
            ...context.data,
            attempt,
            maxAttempts: config.maxAttempts,
            remainingAttempts: config.maxAttempts - attempt,
          },
        }
      ));

      if (attempt === config.maxAttempts) {
        throw error;
      }

      await new Promise(resolve => setTimeout(resolve, delay));
      delay *= config.backoffFactor;
    }
  }

  throw lastError;
} 