import messaging from '@react-native-firebase/messaging';
import { updateUserDeviceToken } from '../services/notificationService';
import { auth } from '../firebase';
import PushNotificationIOS from '@react-native-community/push-notification-ios';
import { NotificationType, NotificationData } from '../types/notifications';

// Notification formatter
export const formatNotification = (data: NotificationData, badge?: number) => {
  return {
    title: data.title,
    body: data.body,
    data: {
      type: data.type,
      postingId: data.postingId,
      offerId: data.offerId,
      messageId: data.messageId,
    },
    ios: {
      categoryId: data.type,
      badge,
      foregroundPresentationOptions: {
        badge: true,
        sound: true,
        banner: true,
        list: true,
      },
    },
  };
};

// Badge counter management
let currentBadgeCount = 0;

export const incrementBadge = async () => {
  currentBadgeCount += 1;
  PushNotificationIOS.setApplicationIconBadgeNumber(currentBadgeCount);
  return currentBadgeCount;
};

export const decrementBadge = async () => {
  currentBadgeCount = Math.max(0, currentBadgeCount - 1);
  PushNotificationIOS.setApplicationIconBadgeNumber(currentBadgeCount);
  return currentBadgeCount;
};

export const resetBadge = async () => {
  currentBadgeCount = 0;
  PushNotificationIOS.setApplicationIconBadgeNumber(0);
};

// Add FCM token refresh handling
export const initializeNotifications = async () => {
  try {
    const authStatus = await messaging().requestPermission();
    const enabled =
      authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
      authStatus === messaging.AuthorizationStatus.PROVISIONAL;

    if (enabled) {
      // Get the FCM token
      const token = await messaging().getToken();
      console.log('FCM Token:', token);
      
      // Save it to the user's document if logged in
      if (auth.currentUser) {
        try {
          await updateUserDeviceToken(auth.currentUser.uid, token);
          console.log('Device token updated successfully');
        } catch (error) {
          console.error('Failed to update device token:', error);
        }
      }

      // Listen for token refresh
      messaging().onTokenRefresh(async (newToken) => {
        console.log('Token refreshed:', newToken);
        if (auth.currentUser) {
          try {
            await updateUserDeviceToken(auth.currentUser.uid, newToken);
            console.log('Refreshed token updated successfully');
          } catch (error) {
            console.error('Failed to update refreshed token:', error);
          }
        }
      });

      return true;
    }
    
    return false;
  } catch (error) {
    console.error('Failed to initialize notifications:', error);
    return false;
  }
}; 